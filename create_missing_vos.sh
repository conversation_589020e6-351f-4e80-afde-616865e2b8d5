#!/bin/bash

# 基础路径
BASE_PATH="/Users/<USER>/workspace_yitong/yitong-octopus-admin-dev/yitong-module-distribution/yitong-module-distribution-biz/src/main/java/com/yitong/octopus/module/distribution/controller/admin/goods/vo"

# 创建目录
mkdir -p "$BASE_PATH"

# 创建 DistProductCommissionCalculationDetailVO
cat > "$BASE_PATH/DistProductCommissionCalculationDetailVO.java" << 'EOF'
package com.yitong.octopus.module.distribution.controller.admin.goods.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 商品佣金计算明细 VO")
@Data
public class DistProductCommissionCalculationDetailVO {
    @Schema(description = "商品ID", example = "1")
    private Long productId;
    
    @Schema(description = "商品名称", example = "测试商品")
    private String productName;
    
    @Schema(description = "销售价格", example = "100.00")
    private BigDecimal salePrice;
    
    @Schema(description = "佣金比例", example = "10")
    private BigDecimal commissionRate;
    
    @Schema(description = "佣金金额", example = "10.00")
    private BigDecimal commissionAmount;
    
    @Schema(description = "计算公式", example = "销售价格 × 佣金比例")
    private String formula;
}
EOF

# 创建 DistProductCommissionComparisonVO
cat > "$BASE_PATH/DistProductCommissionComparisonVO.java" << 'EOF'
package com.yitong.octopus.module.distribution.controller.admin.goods.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 商品佣金对比 VO")
@Data
public class DistProductCommissionComparisonVO {
    @Schema(description = "等级ID", example = "1")
    private Long levelId;
    
    @Schema(description = "等级名称", example = "初级代理")
    private String levelName;
    
    @Schema(description = "佣金金额", example = "100.00")
    private BigDecimal commissionAmount;
    
    @Schema(description = "佣金比例", example = "10")
    private BigDecimal commissionRate;
}
EOF

# 创建 DistProductCommissionOptimizationVO
cat > "$BASE_PATH/DistProductCommissionOptimizationVO.java" << 'EOF'
package com.yitong.octopus.module.distribution.controller.admin.goods.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 商品佣金优化建议 VO")
@Data
public class DistProductCommissionOptimizationVO {
    @Schema(description = "建议类型", example = "提高佣金")
    private String suggestionType;
    
    @Schema(description = "当前佣金", example = "100.00")
    private BigDecimal currentCommission;
    
    @Schema(description = "建议佣金", example = "120.00")
    private BigDecimal suggestedCommission;
    
    @Schema(description = "预期收益提升", example = "20%")
    private String expectedImprovement;
    
    @Schema(description = "建议原因", example = "同类商品平均佣金较高")
    private String reason;
}
EOF

# 创建 DistProductCommissionPreviewVO
cat > "$BASE_PATH/DistProductCommissionPreviewVO.java" << 'EOF'
package com.yitong.octopus.module.distribution.controller.admin.goods.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

@Schema(description = "管理后台 - 商品佣金预览 VO")
@Data
public class DistProductCommissionPreviewVO {
    @Schema(description = "商品ID", example = "1")
    private Long productId;
    
    @Schema(description = "商品名称", example = "测试商品")
    private String productName;
    
    @Schema(description = "各等级佣金")
    private List<LevelCommission> levelCommissions;
    
    @Data
    public static class LevelCommission {
        @Schema(description = "等级ID", example = "1")
        private Long levelId;
        
        @Schema(description = "等级名称", example = "初级代理")
        private String levelName;
        
        @Schema(description = "佣金金额", example = "100.00")
        private BigDecimal amount;
    }
}
EOF

# 创建 DistProductCommissionRangeVO
cat > "$BASE_PATH/DistProductCommissionRangeVO.java" << 'EOF'
package com.yitong.octopus.module.distribution.controller.admin.goods.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 商品佣金范围 VO")
@Data
public class DistProductCommissionRangeVO {
    @Schema(description = "最低佣金", example = "10.00")
    private BigDecimal minCommission;
    
    @Schema(description = "最高佣金", example = "100.00")
    private BigDecimal maxCommission;
    
    @Schema(description = "平均佣金", example = "50.00")
    private BigDecimal avgCommission;
    
    @Schema(description = "建议佣金", example = "60.00")
    private BigDecimal suggestedCommission;
}
EOF

# 创建 DistProductCommissionRuleVO
cat > "$BASE_PATH/DistProductCommissionRuleVO.java" << 'EOF'
package com.yitong.octopus.module.distribution.controller.admin.goods.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 商品佣金规则 VO")
@Data
public class DistProductCommissionRuleVO {
    @Schema(description = "规则ID", example = "1")
    private Long ruleId;
    
    @Schema(description = "规则名称", example = "默认佣金规则")
    private String ruleName;
    
    @Schema(description = "计算方式：1-固定金额，2-比例", example = "1")
    private Integer calculateType;
    
    @Schema(description = "佣金值", example = "100.00")
    private BigDecimal commissionValue;
    
    @Schema(description = "优先级", example = "1")
    private Integer priority;
}
EOF

# 创建 DistProductCommissionStatisticsVO
cat > "$BASE_PATH/DistProductCommissionStatisticsVO.java" << 'EOF'
package com.yitong.octopus.module.distribution.controller.admin.goods.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 商品佣金统计 VO")
@Data
public class DistProductCommissionStatisticsVO {
    @Schema(description = "总佣金", example = "10000.00")
    private BigDecimal totalCommission;
    
    @Schema(description = "平均佣金", example = "100.00")
    private BigDecimal avgCommission;
    
    @Schema(description = "最高佣金", example = "500.00")
    private BigDecimal maxCommission;
    
    @Schema(description = "最低佣金", example = "10.00")
    private BigDecimal minCommission;
    
    @Schema(description = "商品数量", example = "100")
    private Integer productCount;
}
EOF

# 创建 DistProductCommissionTrendVO
cat > "$BASE_PATH/DistProductCommissionTrendVO.java" << 'EOF'
package com.yitong.octopus.module.distribution.controller.admin.goods.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Schema(description = "管理后台 - 商品佣金趋势 VO")
@Data
public class DistProductCommissionTrendVO {
    @Schema(description = "趋势数据")
    private List<TrendData> trendList;
    
    @Data
    public static class TrendData {
        @Schema(description = "日期")
        private LocalDate date;
        
        @Schema(description = "佣金金额", example = "1000.00")
        private BigDecimal amount;
        
        @Schema(description = "环比增长", example = "10.5")
        private BigDecimal growthRate;
    }
}
EOF

# 创建 DistProductConfigBatchUpdateStatusReqVO
cat > "$BASE_PATH/DistProductConfigBatchUpdateStatusReqVO.java" << 'EOF'
package com.yitong.octopus.module.distribution.controller.admin.goods.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 商品配置批量更新状态 Request VO")
@Data
public class DistProductConfigBatchUpdateStatusReqVO {
    @Schema(description = "商品ID列表", required = true, example = "[1,2,3]")
    @NotEmpty(message = "商品ID列表不能为空")
    private List<Long> productIds;
    
    @Schema(description = "状态：0-禁用，1-启用", required = true, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer status;
}
EOF

# 创建 DistProductConfigOptionVO
cat > "$BASE_PATH/DistProductConfigOptionVO.java" << 'EOF'
package com.yitong.octopus.module.distribution.controller.admin.goods.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 商品配置选项 VO")
@Data
public class DistProductConfigOptionVO {
    @Schema(description = "选项键", example = "enable_distribution")
    private String optionKey;
    
    @Schema(description = "选项值", example = "true")
    private String optionValue;
    
    @Schema(description = "选项名称", example = "启用分销")
    private String optionName;
    
    @Schema(description = "选项描述", example = "是否启用该商品的分销功能")
    private String optionDesc;
}
EOF

# 创建 DistProductConfigStatisticsVO
cat > "$BASE_PATH/DistProductConfigStatisticsVO.java" << 'EOF'
package com.yitong.octopus.module.distribution.controller.admin.goods.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 商品配置统计 VO")
@Data
public class DistProductConfigStatisticsVO {
    @Schema(description = "总商品数", example = "1000")
    private Integer totalCount;
    
    @Schema(description = "已启用数", example = "800")
    private Integer enabledCount;
    
    @Schema(description = "已禁用数", example = "200")
    private Integer disabledCount;
    
    @Schema(description = "启用率", example = "80.0")
    private Double enabledRate;
}
EOF

# 创建 DistProductDistributionConfigVO
cat > "$BASE_PATH/DistProductDistributionConfigVO.java" << 'EOF'
package com.yitong.octopus.module.distribution.controller.admin.goods.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 商品分销配置 VO")
@Data
public class DistProductDistributionConfigVO {
    @Schema(description = "是否启用分销", example = "true")
    private Boolean enabled;
    
    @Schema(description = "分销模式：1-统一比例，2-阶梯比例", example = "1")
    private Integer mode;
    
    @Schema(description = "基础佣金比例", example = "10")
    private BigDecimal baseCommissionRate;
    
    @Schema(description = "最低销售价格", example = "100.00")
    private BigDecimal minPrice;
}
EOF

# 创建 DistProductDistributionDataVO
cat > "$BASE_PATH/DistProductDistributionDataVO.java" << 'EOF'
package com.yitong.octopus.module.distribution.controller.admin.goods.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 商品分销数据 VO")
@Data
public class DistProductDistributionDataVO {
    @Schema(description = "分销订单数", example = "100")
    private Integer orderCount;
    
    @Schema(description = "分销金额", example = "10000.00")
    private BigDecimal salesAmount;
    
    @Schema(description = "佣金总额", example = "1000.00")
    private BigDecimal commissionAmount;
    
    @Schema(description = "参与分销员数", example = "50")
    private Integer agentCount;
}
EOF

# 创建 DistProductDistributionInfoVO
cat > "$BASE_PATH/DistProductDistributionInfoVO.java" << 'EOF'
package com.yitong.octopus.module.distribution.controller.admin.goods.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商品分销信息 VO")
@Data
public class DistProductDistributionInfoVO {
    @Schema(description = "商品ID", example = "1")
    private Long productId;
    
    @Schema(description = "商品名称", example = "测试商品")
    private String productName;
    
    @Schema(description = "分销状态：0-未开启，1-已开启", example = "1")
    private Integer status;
    
    @Schema(description = "开始时间")
    private LocalDateTime startTime;
    
    @Schema(description = "结束时间")
    private LocalDateTime endTime;
}
EOF

# 创建 DistProductDistributionLimitVO
cat > "$BASE_PATH/DistProductDistributionLimitVO.java" << 'EOF'
package com.yitong.octopus.module.distribution.controller.admin.goods.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 商品分销限制 VO")
@Data
public class DistProductDistributionLimitVO {
    @Schema(description = "每人限购数量", example = "10")
    private Integer perPersonLimit;
    
    @Schema(description = "每日限购数量", example = "100")
    private Integer dailyLimit;
    
    @Schema(description = "总限购数量", example = "1000")
    private Integer totalLimit;
    
    @Schema(description = "最低购买数量", example = "1")
    private Integer minQuantity;
}
EOF

# 创建 DistProductDistributionPermissionVO
cat > "$BASE_PATH/DistProductDistributionPermissionVO.java" << 'EOF'
package com.yitong.octopus.module.distribution.controller.admin.goods.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Schema(description = "管理后台 - 商品分销权限 VO")
@Data
public class DistProductDistributionPermissionVO {
    @Schema(description = "允许分销的等级ID列表", example = "[1,2,3]")
    private List<Long> allowedLevelIds;
    
    @Schema(description = "禁止分销的代理商ID列表", example = "[4,5,6]")
    private List<Long> blockedAgentIds;
    
    @Schema(description = "是否需要审核", example = "true")
    private Boolean requireApproval;
}
EOF

# 创建 DistProductDistributionRankingVO
cat > "$BASE_PATH/DistProductDistributionRankingVO.java" << 'EOF'
package com.yitong.octopus.module.distribution.controller.admin.goods.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 商品分销排名 VO")
@Data
public class DistProductDistributionRankingVO {
    @Schema(description = "排名", example = "1")
    private Integer rank;
    
    @Schema(description = "商品ID", example = "1")
    private Long productId;
    
    @Schema(description = "商品名称", example = "测试商品")
    private String productName;
    
    @Schema(description = "销售数量", example = "100")
    private Integer salesCount;
    
    @Schema(description = "销售金额", example = "10000.00")
    private BigDecimal salesAmount;
}
EOF

# 创建 DistProductDistributionRuleVO
cat > "$BASE_PATH/DistProductDistributionRuleVO.java" << 'EOF'
package com.yitong.octopus.module.distribution.controller.admin.goods.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 商品分销规则 VO")
@Data
public class DistProductDistributionRuleVO {
    @Schema(description = "规则类型：1-佣金规则，2-推广规则", example = "1")
    private Integer ruleType;
    
    @Schema(description = "规则内容")
    private String ruleContent;
    
    @Schema(description = "优先级", example = "1")
    private Integer priority;
    
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;
}
EOF

echo "所有VO类创建完成！"