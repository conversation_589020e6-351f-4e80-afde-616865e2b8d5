package com.yitong.octopus.framework.excel.core.convert;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.booleanconverter.BooleanBooleanConverter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.yitong.octopus.framework.common.util.string.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 金额转换器
 *
 * 金额单位：分
 *
 * <AUTHOR>
 */
@Slf4j
public class BooleanConvert implements Converter<Boolean> {
    @Override
    public Class<?> supportJavaTypeKey() {
        throw new UnsupportedOperationException("暂不支持，也不需要");
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        throw new UnsupportedOperationException("暂不支持，也不需要");
    }

    @Override
    public WriteCellData<String> convertToExcelData(Boolean value, ExcelContentProperty contentProperty,
                                                    GlobalConfiguration globalConfiguration) {
        String cellStr = "";
        if (ObjectUtil.isNotNull(value)){
            cellStr = value? "是":"否";
        }
        return new WriteCellData<>(cellStr);
    }

    @Override
    public Boolean convertToJavaData(ReadCellData readCellData, ExcelContentProperty contentProperty,
                                    GlobalConfiguration globalConfiguration) {
        // 使用字典解析
        String label = readCellData.getStringValue();
        if (ObjectUtil.isNull(label)) {
            log.error("[convertToJavaData][解析不掉 label({})]", label);
            return null;
        }
        if ("true".equalsIgnoreCase(label)){
            return true;
        }else if ("false".equalsIgnoreCase(label)){
            return false;
        }
        return null;
    }
}
