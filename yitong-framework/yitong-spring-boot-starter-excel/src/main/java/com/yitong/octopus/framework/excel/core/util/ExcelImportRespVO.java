package com.yitong.octopus.framework.excel.core.util;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 管理后台 - Excel导入 Response VO
 */
@Data
@Builder
public class ExcelImportRespVO {

    /**
     * 创建成功的用数组
     */
    private List<String> creates;

    /**
     * 更新成功的用数组
     */
    private List<String> updates;

    /**
     * 导入失败的用户集合,key 为名成，value 为失败原因
     */
    private Map<String, String> failures;

}
