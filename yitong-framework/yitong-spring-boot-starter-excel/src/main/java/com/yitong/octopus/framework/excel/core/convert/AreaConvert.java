package com.yitong.octopus.framework.excel.core.convert;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.yitong.octopus.framework.ip.core.Area;
import com.yitong.octopus.framework.ip.core.utils.AreaUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * Excel 数据字典转换器
 *
 * <AUTHOR>
 */
@Slf4j
public class AreaConvert implements Converter<Object> {

    @Override
    public Class<?> supportJavaTypeKey() {
        throw new UnsupportedOperationException("暂不支持，也不需要");
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        throw new UnsupportedOperationException("暂不支持，也不需要");
    }

    @Override
    public Object convertToJavaData(ReadCellData readCellData, ExcelContentProperty contentProperty,
                                    GlobalConfiguration globalConfiguration) {
        // 使用字典解析
        String label = readCellData.getStringValue();
        Area value = AreaUtils.getAreaByName(label);
        if (ObjectUtil.isNull(value)) {
            log.error("[convertToJavaData][解析不掉 label({})]", label);
            return null;
        }
        // 将 String 的 value 转换成对应的属性
        Class<?> fieldClazz = contentProperty.getField().getType();
        return Convert.convert(fieldClazz, value.getId());
    }

    @Override
    public WriteCellData<String> convertToExcelData(Object object, ExcelContentProperty contentProperty,
                                                    GlobalConfiguration globalConfiguration) {
        // 空时，返回空
        if (object == null) {
            return new WriteCellData<>("");
        }

        // 使用字典格式化
        String value = String.valueOf(object);
        Area label = AreaUtils.getArea(Integer.valueOf(value));
        if (ObjectUtil.isNull(label)) {
            log.error("[convertToExcelData][转换不了 label({})]", value);
            return new WriteCellData<>("");
        }
        // 生成 Excel 小表格
        return new WriteCellData<>(label.getName());
    }

}
