package com.yitong.octopus.framework.excel.core.convert;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 金额转换器
 *
 * 金额单位：分
 *
 * <AUTHOR>
 */
@Slf4j
public class MoneyConvert implements Converter<Object> {
    @Override
    public Class<?> supportJavaTypeKey() {
        throw new UnsupportedOperationException("暂不支持，也不需要");
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        throw new UnsupportedOperationException("暂不支持，也不需要");
    }

    @Override
    public WriteCellData<String> convertToExcelData(Object value, ExcelContentProperty contentProperty,
                                                    GlobalConfiguration globalConfiguration) {
        if (NumberUtil.isNumber(value.toString())){
            if (BigDecimal.ZERO.longValue() != Long.parseLong(value.toString())){
                BigDecimal result = BigDecimal.valueOf(Long.parseLong(value.toString())).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                return new WriteCellData<>(result.toString());
            }
        }
        return new WriteCellData<>("0.00");
    }

    @Override
    public Object convertToJavaData(ReadCellData readCellData, ExcelContentProperty contentProperty,
                                    GlobalConfiguration globalConfiguration) {
        // 使用字典解析
        String label = readCellData.getStringValue();
        if (ObjectUtil.isNull(label)) {
            log.error("[convertToJavaData][解析不掉 label({})]", label);
            return null;
        }
        BigDecimal result = BigDecimal.valueOf(Double.parseDouble(label)).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
        return result.longValue();
    }
}
