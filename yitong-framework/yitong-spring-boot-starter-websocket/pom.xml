<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.yitong.octopus</groupId>
        <artifactId>yitong-framework</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yitong-spring-boot-starter-websocket</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>WebSocket</description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>


    <dependencies>

        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>io.undertow</groupId>
            <artifactId>undertow-servlet</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.websocket</groupId>
            <artifactId>javax.websocket-api</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-mq</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-rabbit</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <!-- 为什么要依赖 tenant 组件？
                因为广播某个类型的用户时候，需要根据租户过滤下，避免广播到别的租户！
            -->
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-biz-tenant</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>