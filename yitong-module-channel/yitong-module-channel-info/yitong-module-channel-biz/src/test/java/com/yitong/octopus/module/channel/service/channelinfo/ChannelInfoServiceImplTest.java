package com.yitong.octopus.module.channel.service.channelinfo;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import com.yitong.octopus.framework.test.core.ut.BaseDbUnitTest;

import com.yitong.octopus.module.channel.controller.admin.channelinfo.vo.*;
import com.yitong.octopus.module.channel.dal.dataobject.channelinfo.ChannelInfoDO;
import com.yitong.octopus.module.channel.dal.mysql.channelinfo.ChannelInfoMapper;
import com.yitong.octopus.framework.common.pojo.PageResult;

import org.springframework.context.annotation.Import;
import java.util.*;
import static com.yitong.octopus.module.channel.enums.ErrorCodeConstants.*;
import static com.yitong.octopus.framework.test.core.util.AssertUtils.*;
import static com.yitong.octopus.framework.test.core.util.RandomUtils.*;
import static com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils.*;
import static com.yitong.octopus.framework.common.util.object.ObjectUtils.*;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link ChannelInfoServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(ChannelInfoServiceImpl.class)
public class ChannelInfoServiceImplTest extends BaseDbUnitTest {

    @Resource
    private ChannelInfoServiceImpl infoService;

    @Resource
    private ChannelInfoMapper infoMapper;

    @Test
    public void testCreateInfo_success() {
        // 准备参数
        ChannelInfoCreateReqVO reqVO = randomPojo(ChannelInfoCreateReqVO.class);

        // 调用
        Long infoId = infoService.createInfo(reqVO);
        // 断言
        assertNotNull(infoId);
        // 校验记录的属性是否正确
        ChannelInfoDO info = infoMapper.selectById(infoId);
        assertPojoEquals(reqVO, info);
    }

    @Test
    public void testUpdateInfo_success() {
        // mock 数据
        ChannelInfoDO dbInfo = randomPojo(ChannelInfoDO.class);
        infoMapper.insert(dbInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        ChannelInfoUpdateReqVO reqVO = randomPojo(ChannelInfoUpdateReqVO.class, o -> {
            o.setId(dbInfo.getId()); // 设置更新的 ID
        });

        // 调用
        infoService.updateInfo(reqVO);
        // 校验是否更新正确
        ChannelInfoDO info = infoMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, info);
    }

    @Test
    public void testUpdateInfo_notExists() {
        // 准备参数
        ChannelInfoUpdateReqVO reqVO = randomPojo(ChannelInfoUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> infoService.updateInfo(reqVO), INFO_NOT_EXISTS);
    }

    @Test
    public void testDeleteInfo_success() {
        // mock 数据
        ChannelInfoDO dbInfo = randomPojo(ChannelInfoDO.class);
        infoMapper.insert(dbInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbInfo.getId();

        // 调用
        infoService.deleteInfo(id);
       // 校验数据不存在了
       assertNull(infoMapper.selectById(id));
    }

    @Test
    public void testDeleteInfo_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> infoService.deleteInfo(id), INFO_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetInfoPage() {
       // mock 数据
       ChannelInfoDO dbInfo = randomPojo(ChannelInfoDO.class, o -> { // 等会查询到
           o.setName(null);
           o.setStatus(null);
           o.setCreateTime(null);
       });
       infoMapper.insert(dbInfo);
       // 测试 name 不匹配
       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setName(null)));
       // 测试 status 不匹配
       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setStatus(null)));
       // 测试 createTime 不匹配
       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setCreateTime(null)));
       // 准备参数
       ChannelInfoPageReqVO reqVO = new ChannelInfoPageReqVO();
       reqVO.setName(null);
       reqVO.setStatus(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<ChannelInfoDO> pageResult = infoService.getInfoPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbInfo, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetInfoList() {
       // mock 数据
       ChannelInfoDO dbInfo = randomPojo(ChannelInfoDO.class, o -> { // 等会查询到
           o.setName(null);
           o.setStatus(null);
           o.setCreateTime(null);
       });
       infoMapper.insert(dbInfo);
       // 测试 name 不匹配
       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setName(null)));
       // 测试 status 不匹配
       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setStatus(null)));
       // 测试 createTime 不匹配
       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setCreateTime(null)));
       // 准备参数
       ChannelInfoExportReqVO reqVO = new ChannelInfoExportReqVO();
       reqVO.setName(null);
       reqVO.setStatus(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<ChannelInfoDO> list = infoService.getInfoList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbInfo, list.get(0));
    }

}
