package com.yitong.octopus.module.channel.service.channelcategory;

import com.yitong.octopus.module.channel.controller.admin.channelcategory.vo.ChannelCategoryCreateReqVO;
import com.yitong.octopus.module.channel.controller.admin.channelcategory.vo.ChannelCategoryListReqVO;
import com.yitong.octopus.module.channel.controller.admin.channelcategory.vo.ChannelCategoryUpdateReqVO;
import com.yitong.octopus.module.channel.dal.dataobject.channelcategory.ChannelCategoryDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 商品分类 Service 接口
 *
 * <AUTHOR>
 */
public interface ChannelCategoryService {

    /**
     * 创建商品分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createCategory(@Valid ChannelCategoryCreateReqVO createReqVO);

    /**
     * 更新商品分类
     *
     * @param updateReqVO 更新信息
     */
    void updateCategory(@Valid ChannelCategoryUpdateReqVO updateReqVO);

    /**
     * 删除商品分类
     *
     * @param id 编号
     */
    void deleteCategory(String id);

    /**
     * 获得商品分类
     *
     * @param id 编号
     * @return 商品分类
     */
    ChannelCategoryDO getCategory(String id);

    /**
     * 校验商品分类
     *
     * @param id 分类编号
     */
    void validateCategory(String id);

    /**
     * 获得商品分类的层级
     *
     * @param id 编号
     * @return 商品分类的层级
     */
    Integer getCategoryLevel(String id);

    /**
     * 获得商品分类列表
     *
     * @param listReqVO 查询条件
     * @return 商品分类列表
     */
    List<ChannelCategoryDO> getEnableCategoryList(ChannelCategoryListReqVO listReqVO);

    /**
     * 获得开启状态的商品分类列表
     *
     * @return 商品分类列表
     */
    List<ChannelCategoryDO> getEnableCategoryList();

}
