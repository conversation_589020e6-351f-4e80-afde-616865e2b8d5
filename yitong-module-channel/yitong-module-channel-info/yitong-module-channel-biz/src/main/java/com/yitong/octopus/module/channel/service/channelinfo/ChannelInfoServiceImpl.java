package com.yitong.octopus.module.channel.service.channelinfo;

import cn.hutool.core.util.ObjectUtil;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.yitong.octopus.module.channel.controller.admin.channelinfo.vo.*;
import com.yitong.octopus.module.channel.dal.dataobject.channelinfo.ChannelInfoDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.channel.convert.channelinfo.ChannelInfoConvert;
import com.yitong.octopus.module.channel.dal.mysql.channelinfo.ChannelInfoMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.channel.enums.ErrorCodeConstants.*;

/**
 * 渠道信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ChannelInfoServiceImpl implements ChannelInfoService {

    @Resource
    private ChannelInfoMapper infoMapper;

    @Override
    public Long createInfo(ChannelInfoCreateReqVO createReqVO) {
        // 插入
        ChannelInfoDO info = ChannelInfoConvert.INSTANCE.convert(createReqVO);
        validateInfoCodeExists(info.getCode(),info.getId());
        infoMapper.insert(info);
        // 返回
        return info.getId();
    }

    @Override
    public void updateInfo(ChannelInfoUpdateReqVO updateReqVO) {
        // 校验存在
        validateInfoExists(updateReqVO.getId());
        validateInfoCodeExists(updateReqVO.getCode(),updateReqVO.getId());
        // 更新
        ChannelInfoDO updateObj = ChannelInfoConvert.INSTANCE.convert(updateReqVO);
        infoMapper.updateById(updateObj);
    }

    @Override
    public void deleteInfo(Long id) {
        // 校验存在
        validateInfoExists(id);
        // 删除
        infoMapper.deleteById(id);
    }

    private void validateInfoExists(Long id) {
        if (infoMapper.selectById(id) == null) {
            throw exception(INFO_NOT_EXISTS);
        }
    }

    private void validateInfoCodeExists(String code,Long id) {
        ChannelInfoDO channelInfoDO = infoMapper.selectOne(new LambdaQueryWrapperX<ChannelInfoDO>().eq(ChannelInfoDO::getCode,code));
        if (ObjectUtil.isNotNull(channelInfoDO)){
            if (ObjectUtil.isNotNull(id) && !ObjectUtil.equals(channelInfoDO.getId(),id)){
                throw exception(INFO_CODE_SAME);
            }else if (ObjectUtil.isNull(id)){
                throw exception(INFO_CODE_SAME);
            }
        }
    }

    @Override
    public ChannelInfoDO getInfo(Long id) {
        return infoMapper.selectById(id);
    }

    @Override
    public ChannelInfoDO getInfoByCode(String code) {
        return infoMapper.selectOne(ChannelInfoDO::getCode,code);
    }

    @Override
    public List<ChannelInfoDO> getInfoList(Collection<Long> ids) {
        return infoMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ChannelInfoDO> getInfoPage(ChannelInfoPageReqVO pageReqVO) {
        return infoMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ChannelInfoDO> getInfoList(ChannelInfoExportReqVO exportReqVO) {
        return infoMapper.selectList(exportReqVO);
    }

}
