package com.yitong.octopus.module.channel.api.category;

import cn.hutool.core.bean.BeanUtil;
import com.yitong.octopus.module.channel.api.category.vo.ChannelCategoryVo;
import com.yitong.octopus.module.channel.service.channelcategory.ChannelCategoryService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class ChannelCategoryApiImpl implements ChannelCategoryApi {

    @Resource
    private ChannelCategoryService categoryService;

    @Override
    public ChannelCategoryVo getChannelCategoryById(String id) {
        return BeanUtil.toBean(categoryService.getCategory(id),ChannelCategoryVo.class);
    }
}
