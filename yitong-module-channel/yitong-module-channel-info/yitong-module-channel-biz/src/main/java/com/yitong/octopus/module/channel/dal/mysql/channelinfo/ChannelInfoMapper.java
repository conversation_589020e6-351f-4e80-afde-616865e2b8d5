package com.yitong.octopus.module.channel.dal.mysql.channelinfo;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.channel.dal.dataobject.channelinfo.ChannelInfoDO;
import org.apache.ibatis.annotations.Mapper;
import com.yitong.octopus.module.channel.controller.admin.channelinfo.vo.*;

/**
 * 渠道信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ChannelInfoMapper extends BaseMapperX<ChannelInfoDO> {

    default PageResult<ChannelInfoDO> selectPage(ChannelInfoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ChannelInfoDO>()
                .likeIfPresent(ChannelInfoDO::getName, reqVO.getName())
                .eqIfPresent(ChannelInfoDO::getCode, reqVO.getCode())
                .eqIfPresent(ChannelInfoDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(ChannelInfoDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ChannelInfoDO::getId));
    }

    default List<ChannelInfoDO> selectList(ChannelInfoExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ChannelInfoDO>()
                .likeIfPresent(ChannelInfoDO::getName, reqVO.getName())
                .eqIfPresent(ChannelInfoDO::getCode, reqVO.getCode())
                .eqIfPresent(ChannelInfoDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(ChannelInfoDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ChannelInfoDO::getId));
    }

}
