package com.yitong.octopus.module.channel.controller.admin.channelinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 渠道信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChannelInfoUpdateReqVO extends ChannelInfoBaseVO {

    @Schema(description = "id", required = true, example = "2654")
    @NotNull(message = "id不能为空")
    private Long id;

}
