package com.yitong.octopus.module.channel.dal.dataobject.channelcategory;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yitong.octopus.framework.common.enums.CommonStatusEnum;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 平台分类 DO
 *
 * 商品分类一共两类：
 * 1）一级分类：{@link #parentId} 等于 0
 * 2）二级 + 三级分类：{@link #parentId} 不等于 0
 *
 * <AUTHOR>
 */
@TableName("yt_channel_category")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChannelCategoryDO extends BaseDO {

    /**
     * 父分类编号 - 根分类
     */
    public static final String PARENT_ID_NULL = "0";

    /**
     * 分类编号
     */
    @TableId
    private String id;

    /**
     * 渠道编号
     */
    private Long channelId;

    /**
     * 父分类编号
     */
    private String parentId;
    /**
     * 分类名称
     */
    private String name;
    /**
     * 分类图片
     *
     * 一级分类：推荐 200 x 100 分辨率
     * 二级 + 三级分类：推荐 100 x 100 分辨率
     */
    private String icon;
    /**
     * 分类排序
     */
    private Integer sort;
    /**
     * 分类描述
     */
    private String description;
    /**
     * 开启状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;

}
