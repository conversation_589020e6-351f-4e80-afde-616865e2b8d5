package com.yitong.octopus.module.channel.service.channelcategory;

import com.yitong.octopus.framework.common.enums.CommonStatusEnum;
import com.yitong.octopus.module.channel.controller.admin.channelcategory.vo.ChannelCategoryCreateReqVO;
import com.yitong.octopus.module.channel.controller.admin.channelcategory.vo.ChannelCategoryListReqVO;
import com.yitong.octopus.module.channel.controller.admin.channelcategory.vo.ChannelCategoryUpdateReqVO;
import com.yitong.octopus.module.channel.convert.channelcategory.ChannelCategoryConvert;
import com.yitong.octopus.module.channel.dal.dataobject.channelcategory.ChannelCategoryDO;
import com.yitong.octopus.module.channel.dal.mysql.channelcategory.ChannelCategoryMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.channel.enums.ErrorCodeConstants.*;

/**
 * 商品分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ChannelCategoryServiceImpl implements ChannelCategoryService {

    @Resource
    private ChannelCategoryMapper channelCategoryMapper;

    @Override
    public String createCategory(ChannelCategoryCreateReqVO createReqVO) {
        // 校验父分类存在
        validateParentProductCategory(createReqVO.getParentId());

        // 插入
        ChannelCategoryDO category = ChannelCategoryConvert.INSTANCE.convert(createReqVO);
        channelCategoryMapper.insert(category);
        // 返回
        return category.getId();
    }

    @Override
    public void updateCategory(ChannelCategoryUpdateReqVO updateReqVO) {
        // 校验分类是否存在
        validateProductCategoryExists(updateReqVO.getId());
        // 校验父分类存在
        validateParentProductCategory(updateReqVO.getParentId());

        // 更新
        ChannelCategoryDO updateObj = ChannelCategoryConvert.INSTANCE.convert(updateReqVO);
        channelCategoryMapper.updateById(updateObj);
    }

    @Override
    public void deleteCategory(String id) {
        // 校验分类是否存在
        validateProductCategoryExists(id);
        // 校验是否还有子分类
        if (channelCategoryMapper.selectCountByParentId(id) > 0) {
            throw exception(CATEGORY_EXISTS_CHILDREN);
        }
        // TODO 芋艿 补充只有不存在商品才可以删除
        // 删除
        channelCategoryMapper.deleteById(id);
    }

    private void validateParentProductCategory(String id) {
        // 如果是根分类，无需验证
        if (Objects.equals(id, ChannelCategoryDO.PARENT_ID_NULL)) {
            return;
        }
        // 父分类不存在
        ChannelCategoryDO category = channelCategoryMapper.selectById(id);
        if (category == null) {
            throw exception(CATEGORY_PARENT_NOT_EXISTS);
        }
        // 父分类不能是二级分类
        if (Objects.equals(id, ChannelCategoryDO.PARENT_ID_NULL)) {
            throw exception(CATEGORY_PARENT_NOT_FIRST_LEVEL);
        }
    }

    private void validateProductCategoryExists(String id) {
        ChannelCategoryDO category = channelCategoryMapper.selectById(id);
        if (category == null) {
            throw exception(CATEGORY_NOT_EXISTS);
        }
    }

    @Override
    public ChannelCategoryDO getCategory(String id) {
        return channelCategoryMapper.selectById(id);
    }

    @Override
    public void validateCategory(String id) {
        ChannelCategoryDO category = channelCategoryMapper.selectById(id);
        if (category == null) {
            throw exception(CATEGORY_NOT_EXISTS);
        }
        if (Objects.equals(category.getStatus(), CommonStatusEnum.ENABLE.getStatus())) {
            throw exception(CATEGORY_DISABLED, category.getName());
        }
    }

    @Override
    public Integer getCategoryLevel(String id) {
        if (Objects.equals(id, ChannelCategoryDO.PARENT_ID_NULL)) {
            return 0;
        }
        int level = 1;
        for (int i = 0; i < 100; i++) {
            ChannelCategoryDO category = channelCategoryMapper.selectById(id);
            // 如果没有父节点，break 结束
            if (category == null
                    || Objects.equals(category.getParentId(), ChannelCategoryDO.PARENT_ID_NULL)) {
                break;
            }
            // 继续递归父节点
            level++;
            id = category.getParentId();
        }
        return level;
    }

    @Override
    public List<ChannelCategoryDO> getEnableCategoryList(ChannelCategoryListReqVO listReqVO) {
        return channelCategoryMapper.selectList(listReqVO);
    }

    @Override
    public List<ChannelCategoryDO> getEnableCategoryList() {
        return channelCategoryMapper.selectListByStatus(CommonStatusEnum.ENABLE.getStatus());
    }

}
