package com.yitong.octopus.module.channel.controller.app.channelinfo;

import cn.hutool.core.bean.BeanUtil;
import com.yitong.octopus.framework.common.enums.YTCommonStatusEnum;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.module.channel.controller.admin.channelinfo.vo.ChannelInfoPageReqVO;
import com.yitong.octopus.module.channel.controller.app.channelinfo.vo.AppChannelInfoPageReqVO;
import com.yitong.octopus.module.channel.controller.app.channelinfo.vo.AppChannelInfoRespVO;
import com.yitong.octopus.module.channel.convert.channelinfo.ChannelInfoConvert;
import com.yitong.octopus.module.channel.dal.dataobject.channelinfo.ChannelInfoDO;
import com.yitong.octopus.module.channel.service.channelinfo.ChannelInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

@Tag(name = "用户端 - 渠道信息")
@RestController
@RequestMapping("/channel/info")
@Validated
public class AppChannelInfoController {

    @Resource
    private ChannelInfoService infoService;

    @GetMapping("/get")
    @Operation(summary = "获得渠道信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppChannelInfoRespVO> getInfo(@RequestParam("id") Long id) {
        ChannelInfoDO info = infoService.getInfo(id);
        return success(ChannelInfoConvert.INSTANCE.convertApp(info));
    }

    @GetMapping("/page")
    @Operation(summary = "获得渠道信息分页")
    public CommonResult<PageResult<AppChannelInfoRespVO>> getInfoPage(@Valid AppChannelInfoPageReqVO pageVO) {
        ChannelInfoPageReqVO page = BeanUtil.toBean(pageVO,ChannelInfoPageReqVO.class);
        page.setStatus(YTCommonStatusEnum.ENABLE.getStatus());
        PageResult<ChannelInfoDO> pageResult = infoService.getInfoPage(page);
        return success(ChannelInfoConvert.INSTANCE.convertPageApp(pageResult));
    }
}
