package com.yitong.octopus.module.channel.convert.channelinfo;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.channel.controller.app.channelinfo.vo.AppChannelInfoRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yitong.octopus.module.channel.controller.admin.channelinfo.vo.*;
import com.yitong.octopus.module.channel.dal.dataobject.channelinfo.ChannelInfoDO;

/**
 * 渠道信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ChannelInfoConvert {

    ChannelInfoConvert INSTANCE = Mappers.getMapper(ChannelInfoConvert.class);

    ChannelInfoDO convert(ChannelInfoCreateReqVO bean);

    ChannelInfoDO convert(ChannelInfoUpdateReqVO bean);

    ChannelInfoRespVO convert(ChannelInfoDO bean);
    AppChannelInfoRespVO convertApp(ChannelInfoDO bean);

    List<ChannelInfoRespVO> convertList(List<ChannelInfoDO> list);

    List<AppChannelInfoRespVO> convertListApp(List<ChannelInfoDO> list);

    PageResult<ChannelInfoRespVO> convertPage(PageResult<ChannelInfoDO> page);

    PageResult<AppChannelInfoRespVO> convertPageApp(PageResult<ChannelInfoDO> page);

    List<ChannelInfoExcelVO> convertList02(List<ChannelInfoDO> list);

}
