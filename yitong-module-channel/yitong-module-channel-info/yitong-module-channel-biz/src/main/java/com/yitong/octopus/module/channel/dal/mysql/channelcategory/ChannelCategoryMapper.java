package com.yitong.octopus.module.channel.dal.mysql.channelcategory;

import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.module.channel.controller.admin.channelcategory.vo.ChannelCategoryListReqVO;
import com.yitong.octopus.module.channel.dal.dataobject.channelcategory.ChannelCategoryDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 商品分类 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ChannelCategoryMapper extends BaseMapperX<ChannelCategoryDO> {

    default List<ChannelCategoryDO> selectList(ChannelCategoryListReqVO listReqVO) {
        return selectList(new LambdaQueryWrapperX<ChannelCategoryDO>()
                .likeIfPresent(ChannelCategoryDO::getName, listReqVO.getName())
                .orderByDesc(ChannelCategoryDO::getId));
    }

    default Long selectCountByParentId(String parentId) {
        return selectCount(ChannelCategoryDO::getParentId, parentId);
    }

    default List<ChannelCategoryDO> selectListByStatus(Integer status) {
        return selectList(ChannelCategoryDO::getStatus, status);
    }

}
