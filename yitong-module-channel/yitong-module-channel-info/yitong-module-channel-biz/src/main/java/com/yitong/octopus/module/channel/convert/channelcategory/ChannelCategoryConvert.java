package com.yitong.octopus.module.channel.convert.channelcategory;

import com.yitong.octopus.module.channel.controller.admin.channelcategory.vo.ChannelCategoryCreateReqVO;
import com.yitong.octopus.module.channel.controller.admin.channelcategory.vo.ChannelCategoryRespVO;
import com.yitong.octopus.module.channel.controller.admin.channelcategory.vo.ChannelCategoryUpdateReqVO;
import com.yitong.octopus.module.channel.dal.dataobject.channelcategory.ChannelCategoryDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 商品分类 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ChannelCategoryConvert {

    ChannelCategoryConvert INSTANCE = Mappers.getMapper(ChannelCategoryConvert.class);

    ChannelCategoryDO convert(ChannelCategoryCreateReqVO bean);

    ChannelCategoryDO convert(ChannelCategoryUpdateReqVO bean);

    ChannelCategoryRespVO convert(ChannelCategoryDO bean);

    List<ChannelCategoryRespVO> convertList(List<ChannelCategoryDO> list);

}
