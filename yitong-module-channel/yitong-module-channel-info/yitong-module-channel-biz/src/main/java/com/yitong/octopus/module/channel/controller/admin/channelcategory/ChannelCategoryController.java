package com.yitong.octopus.module.channel.controller.admin.channelcategory;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import com.yitong.octopus.module.channel.convert.channelcategory.ChannelCategoryConvert;
import com.yitong.octopus.module.channel.dal.dataobject.channelcategory.ChannelCategoryDO;
import com.yitong.octopus.module.channel.service.channelcategory.ChannelCategoryService;
import com.yitong.octopus.module.channel.controller.admin.channelcategory.vo.ChannelCategoryCreateReqVO;
import com.yitong.octopus.module.channel.controller.admin.channelcategory.vo.ChannelCategoryListReqVO;
import com.yitong.octopus.module.channel.controller.admin.channelcategory.vo.ChannelCategoryRespVO;
import com.yitong.octopus.module.channel.controller.admin.channelcategory.vo.ChannelCategoryUpdateReqVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static com.yitong.octopus.framework.common.constant.TreeConstants.TREE_ROOT_ID;
import static com.yitong.octopus.framework.common.constant.TreeConstants.TREE_ROOT_NAME;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 渠道平台分类")
@RestController
@RequestMapping("/channel/category")
@Validated
public class ChannelCategoryController {

    @Resource
    private ChannelCategoryService categoryService;

    @PostMapping("/create")
    @Operation(summary = "创建商品分类")
    @PreAuthorize("@ss.hasPermission('channel:category:create')")
    public CommonResult<String> createCategory(@Valid @RequestBody ChannelCategoryCreateReqVO createReqVO) {
        return success(categoryService.createCategory(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商品分类")
    @PreAuthorize("@ss.hasPermission('channel:category:update')")
    public CommonResult<Boolean> updateCategory(@Valid @RequestBody ChannelCategoryUpdateReqVO updateReqVO) {
        categoryService.updateCategory(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品分类")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('channel:category:delete')")
    public CommonResult<Boolean> deleteCategory(@RequestParam("id") String id) {
        categoryService.deleteCategory(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品分类")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    public CommonResult<ChannelCategoryRespVO> getCategory(@RequestParam("id") String id) {
        ChannelCategoryDO category = categoryService.getCategory(id);
        return success(ChannelCategoryConvert.INSTANCE.convert(category));
    }

    @GetMapping("/list")
    @Operation(summary = "获得商品分类列表")
    @PermitAll
    public CommonResult<List<ChannelCategoryRespVO>> getCategoryList(@Valid ChannelCategoryListReqVO treeListReqVO) {
        List<ChannelCategoryDO> list = categoryService.getEnableCategoryList(treeListReqVO);
        list.sort(Comparator.comparing(ChannelCategoryDO::getSort));
        return success(ChannelCategoryConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/tree")
    @Operation(summary = "获得渠道分类树形列表")
    @PermitAll
    public CommonResult<Tree<String>> getCategoryTreeList(@Valid ChannelCategoryListReqVO treeListReqVO) {
        List<ChannelCategoryDO> list = categoryService.getEnableCategoryList(treeListReqVO);
        list.sort(Comparator.comparing(ChannelCategoryDO::getSort));
        //转换成树形
        List<TreeNode<String>> treeNodeList = list.stream().map(
                o ->new TreeNode<String>().setId(o.getId())
                        .setParentId(o.getParentId())
                        .setName(o.getName())
                        .setWeight(o.getSort())
        ).collect(Collectors.toList());
        Tree<String> tree = TreeUtil.buildSingle(treeNodeList,TREE_ROOT_ID);
        if (tree.getId().equals(TREE_ROOT_ID)){
            tree.setName(TREE_ROOT_NAME);
        }
        return success(tree);
    }

}
