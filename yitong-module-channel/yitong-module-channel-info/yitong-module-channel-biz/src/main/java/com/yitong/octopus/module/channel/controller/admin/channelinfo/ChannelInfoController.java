package com.yitong.octopus.module.channel.controller.admin.channelinfo;

import com.yitong.octopus.framework.common.pojo.PageParam;
import com.yitong.octopus.framework.common.util.object.BeanUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.channel.controller.admin.channelinfo.vo.*;
import com.yitong.octopus.module.channel.dal.dataobject.channelinfo.ChannelInfoDO;
import com.yitong.octopus.module.channel.convert.channelinfo.ChannelInfoConvert;
import com.yitong.octopus.module.channel.service.channelinfo.ChannelInfoService;

@Tag(name = "管理后台 - 渠道信息")
@RestController
@RequestMapping("/channel/info")
@Validated
public class ChannelInfoController {

    @Resource
    private ChannelInfoService infoService;

    @PostMapping("/create")
    @Operation(summary = "创建渠道信息")
    @PreAuthorize("@ss.hasPermission('channel:info:create')")
    public CommonResult<Long> createInfo(@Valid @RequestBody ChannelInfoCreateReqVO createReqVO) {
        return success(infoService.createInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新渠道信息")
    @PreAuthorize("@ss.hasPermission('channel:info:update')")
    public CommonResult<Boolean> updateInfo(@Valid @RequestBody ChannelInfoUpdateReqVO updateReqVO) {
        infoService.updateInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除渠道信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('channel:info:delete')")
    public CommonResult<Boolean> deleteInfo(@RequestParam("id") Long id) {
        infoService.deleteInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得渠道信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    public CommonResult<ChannelInfoRespVO> getInfo(@RequestParam("id") Long id) {
        ChannelInfoDO info = infoService.getInfo(id);
        return success(ChannelInfoConvert.INSTANCE.convert(info));
    }

    @GetMapping("/list")
    @Operation(summary = "获得渠道信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PermitAll
    public CommonResult<List<ChannelInfoRespVO>> getInfoList(@RequestParam("ids") Collection<Long> ids) {
        List<ChannelInfoDO> list = infoService.getInfoList(ids);
        return success(ChannelInfoConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得渠道信息分页")
    @PermitAll
    public CommonResult<PageResult<ChannelInfoRespVO>> getInfoPage(@Valid ChannelInfoPageReqVO pageVO) {
        PageResult<ChannelInfoDO> pageResult = infoService.getInfoPage(pageVO);
        return success(ChannelInfoConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/list-simple")
    @Operation(summary = "获得渠道信息分页")
    @PermitAll
    public CommonResult<List<ChannelInfoSimpleRespVO>> getInfoPageSample(@Valid ChannelInfoPageReqVO pageVO) {
        pageVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<ChannelInfoDO> pageResult = infoService.getInfoPage(pageVO);
        return success(BeanUtils.toBean(pageResult.getList(),ChannelInfoSimpleRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出渠道信息 Excel")
    @PreAuthorize("@ss.hasPermission('channel:info:export')")
    public void exportInfoExcel(@Valid ChannelInfoExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ChannelInfoDO> list = infoService.getInfoList(exportReqVO);
        // 导出 Excel
        List<ChannelInfoExcelVO> datas = ChannelInfoConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "渠道信息.xls", "数据", ChannelInfoExcelVO.class, datas);
    }

}
