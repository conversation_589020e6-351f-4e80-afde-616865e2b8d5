package com.yitong.octopus.module.channel.controller.admin.channelinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 渠道信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChannelInfoRespVO extends ChannelInfoBaseVO {

    @Schema(description = "id", required = true, example = "2654")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
