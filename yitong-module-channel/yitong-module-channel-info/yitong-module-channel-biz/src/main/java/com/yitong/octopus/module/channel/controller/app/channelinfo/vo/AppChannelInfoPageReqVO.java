package com.yitong.octopus.module.channel.controller.app.channelinfo.vo;

import com.yitong.octopus.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 渠道信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppChannelInfoPageReqVO extends PageParam {

    @Schema(description = "渠道名称", example = "王五")
    private String name;

    @Schema(description = "渠道编码", example = "XHS")
    private String code;

}
