package com.yitong.octopus.module.channel.controller.admin.channelinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 渠道信简单 Response VO")
@Data
@ToString(callSuper = true)
public class ChannelInfoSimpleRespVO {

    @Schema(description = "id", required = true, example = "2654")
    private Long id;

    @Schema(description = "渠道名称", example = "王五")
    private String name;

    @Schema(description = "code", example = "xhs")
    private String code;

    @Schema(description = "Logo")
    private String logo;

}
