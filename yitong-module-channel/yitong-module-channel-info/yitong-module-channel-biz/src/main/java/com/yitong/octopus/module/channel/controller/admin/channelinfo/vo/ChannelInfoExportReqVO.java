package com.yitong.octopus.module.channel.controller.admin.channelinfo.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 渠道信息 Excel 导出 Request VO，参数和 ChannelInfoPageReqVO 是一致的")
@Data
public class ChannelInfoExportReqVO {

    @Schema(description = "渠道名称", example = "王五")
    private String name;

    @Schema(description = "渠道编码", example = "王五")
    private String code;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
