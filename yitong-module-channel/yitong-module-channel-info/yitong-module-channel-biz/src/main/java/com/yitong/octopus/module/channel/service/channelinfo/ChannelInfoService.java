package com.yitong.octopus.module.channel.service.channelinfo;

import java.util.*;
import javax.validation.*;
import com.yitong.octopus.module.channel.controller.admin.channelinfo.vo.*;
import com.yitong.octopus.module.channel.dal.dataobject.channelinfo.ChannelInfoDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

/**
 * 渠道信息 Service 接口
 *
 * <AUTHOR>
 */
public interface ChannelInfoService {

    /**
     * 创建渠道信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInfo(@Valid ChannelInfoCreateReqVO createReqVO);

    /**
     * 更新渠道信息
     *
     * @param updateReqVO 更新信息
     */
    void updateInfo(@Valid ChannelInfoUpdateReqVO updateReqVO);

    /**
     * 删除渠道信息
     *
     * @param id 编号
     */
    void deleteInfo(Long id);

    /**
     * 获得渠道信息
     *
     * @param id 编号
     * @return 渠道信息
     */
    ChannelInfoDO getInfo(Long id);

    /**
     * 获得渠道信息
     *
     * @param code 编号
     * @return 渠道信息
     */
    ChannelInfoDO getInfoByCode(String code);

    /**
     * 获得渠道信息列表
     *
     * @param ids 编号
     * @return 渠道信息列表
     */
    List<ChannelInfoDO> getInfoList(Collection<Long> ids);

    /**
     * 获得渠道信息分页
     *
     * @param pageReqVO 分页查询
     * @return 渠道信息分页
     */
    PageResult<ChannelInfoDO> getInfoPage(ChannelInfoPageReqVO pageReqVO);

    /**
     * 获得渠道信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 渠道信息列表
     */
    List<ChannelInfoDO> getInfoList(ChannelInfoExportReqVO exportReqVO);

}
