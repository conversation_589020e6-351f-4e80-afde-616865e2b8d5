package com.yitong.octopus.module.channel.api.channel;

import cn.hutool.core.bean.BeanUtil;
import com.yitong.octopus.module.channel.api.channel.vo.ChannelInfoVo;
import com.yitong.octopus.module.channel.service.channelinfo.ChannelInfoService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class ChannelInfoApiImpl implements ChannelInfoApi{

    @Resource
    private ChannelInfoService channelInfoService;

    @Override
    public ChannelInfoVo getChannelInfoById(Long id) {
        return BeanUtil.toBean(channelInfoService.getInfo(id),ChannelInfoVo.class);
    }

    @Override
    public ChannelInfoVo getChannelInfoByChannelCode(String channelCode) {
        return BeanUtil.toBean(channelInfoService.getInfoByCode(channelCode),ChannelInfoVo.class);
    }

}
