package com.yitong.octopus.module.channel.dal.dataobject.channelinfo;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;

/**
 * 渠道信息 DO
 *
 * <AUTHOR>
 */
@TableName("yt_channel_info")
@KeySequence("yt_channel_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChannelInfoDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 渠道名称
     */
    private String name;
    /**
     * 渠道编码
     */
    private String code;
    /**
     * Logo
     */
    private String logo;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 状态
     *
     * 枚举 {@link TODO yt_common_status 对应的类}
     */
    private Integer status;

}
