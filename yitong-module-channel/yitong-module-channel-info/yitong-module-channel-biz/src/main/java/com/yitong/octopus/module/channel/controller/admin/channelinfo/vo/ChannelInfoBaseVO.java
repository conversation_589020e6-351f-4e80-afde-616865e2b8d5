package com.yitong.octopus.module.channel.controller.admin.channelinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 渠道信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class ChannelInfoBaseVO {

    @Schema(description = "渠道名称", example = "王五")
    private String name;

    @Schema(description = "code", example = "xhs")
    private String code;

    @Schema(description = "Logo")
    private String logo;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "状态", example = "2")
    private Integer status;

}
