package com.yitong.octopus.module.channel.controller.admin.channelinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;


/**
 * 渠道信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ChannelInfoExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("渠道名称")
    private String name;

    @ExcelProperty("Logo")
    private String logo;

    @ExcelProperty("排序")
    private Integer sort;

    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("yt_common_status") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer status;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
