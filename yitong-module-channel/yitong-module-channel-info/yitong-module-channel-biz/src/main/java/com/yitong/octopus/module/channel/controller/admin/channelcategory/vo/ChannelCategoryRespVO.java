package com.yitong.octopus.module.channel.controller.admin.channelcategory.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商品分类 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChannelCategoryRespVO extends ChannelCategoryBaseVO {

    @Schema(description = "分类编号", required = true, example = "2")
    @NotNull(message = "分类编号不能为空")
    private String id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
