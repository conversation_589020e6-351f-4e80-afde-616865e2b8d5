# 代理分销系统详细设计文档 v1.0

## 1. 项目概述

### 1.1 背景说明
根据业务需求，需要开发一套完整的代理分销系统，支持多级分销模式，实现产品推广、佣金计算、自动分账等功能。该系统将集成到现有的一筒科技平台中。

### 1.2 核心目标
- 支持三级分销体系管理
- 自动化佣金计算与结算
- 分销商等级与权益管理
- 分销数据统计与分析
- 小程序端分销功能支持

### 1.3 系统定位
代理分销系统作为独立的业务模块，与现有的会员系统、交易系统、财务系统等深度集成，为平台提供完整的分销解决方案。

## 2. 系统架构设计

### 2.1 总体架构
```
┌─────────────────────────────────────────────────────────────┐
│                        前端应用层                              │
├─────────────────────────┬────────────────┬──────────────────┤
│     管理后台            │   小程序端     │    H5/APP端      │
├─────────────────────────┴────────────────┴──────────────────┤
│                        网关层（Gateway）                      │
├─────────────────────────────────────────────────────────────┤
│                        业务服务层                             │
├──────────────┬──────────────┬──────────────┬────────────────┤
│  分销商服务  │  佣金服务    │  商品服务    │   订单服务     │
├──────────────┼──────────────┼──────────────┼────────────────┤
│  会员服务    │  财务服务    │  统计服务    │   海报服务     │
├──────────────┴──────────────┴──────────────┴────────────────┤
│                        数据访问层                             │
├─────────────────────────────────────────────────────────────┤
│                 MySQL │ Redis │ ElasticSearch               │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 模块设计
创建新模块：`yitong-module-distribution`

#### 2.2.1 模块结构
```
yitong-module-distribution/
├── yitong-module-distribution-api/       # API接口定义
│   └── src/main/java/cn/jianwoo/octopus/admin/module/distribution/
│       ├── api/                          # 对外API接口
│       ├── enums/                        # 枚举定义
│       └── dto/                          # 数据传输对象
├── yitong-module-distribution-biz/       # 业务实现
│   └── src/main/java/cn/jianwoo/octopus/admin/module/distribution/
│       ├── controller/                   # REST控制器
│       ├── service/                      # 业务服务
│       ├── dal/                          # 数据访问层
│       │   ├── dataobject/              # 数据对象
│       │   ├── mapper/                   # MyBatis映射
│       │   └── redis/                    # Redis操作
│       ├── convert/                      # 对象转换
│       └── job/                         # 定时任务
```

## 3. 数据库设计

### 3.1 分销商信息表（yt_dist_agent）
```sql
CREATE TABLE `yt_dist_agent` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分销商ID',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `agent_code` varchar(32) NOT NULL COMMENT '分销商编码',
  `agent_name` varchar(64) NOT NULL COMMENT '分销商名称',
  `agent_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '代理类型：1-普通代理，2-高级代理，3-合伙人',
  `scheme_id` bigint(20) DEFAULT NULL COMMENT '分销方案ID，关联yt_dist_scheme表',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '直接上级分销商ID',
  `path` varchar(512) DEFAULT NULL COMMENT '分销路径，如：1,2,3',
  `depth` int(11) DEFAULT '1' COMMENT '层级深度，顶级为1',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `total_commission` decimal(10,2) DEFAULT '0.00' COMMENT '累计佣金',
  `available_balance` decimal(10,2) DEFAULT '0.00' COMMENT '可提现余额',
  `frozen_balance` decimal(10,2) DEFAULT '0.00' COMMENT '冻结金额',
  `team_count` int(11) DEFAULT '0' COMMENT '团队人数（包含所有下级）',
  `direct_count` int(11) DEFAULT '0' COMMENT '直属下级人数',
  `total_sales` decimal(10,2) DEFAULT '0.00' COMMENT '累计销售额',
  `month_sales` decimal(10,2) DEFAULT '0.00' COMMENT '本月销售额',
  `join_time` datetime NOT NULL COMMENT '加入时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_member_id` (`member_id`),
  UNIQUE KEY `uk_agent_code` (`agent_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_agent_type` (`agent_type`),
  KEY `idx_depth` (`depth`),
  KEY `idx_scheme_id` (`scheme_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销商信息表';
```

### 3.2 分销关系表（yt_dist_relation）
```sql
CREATE TABLE `yt_dist_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `agent_id` bigint(20) NOT NULL COMMENT '分销商ID',
  `parent_id` bigint(20) NOT NULL COMMENT '上级分销商ID',
  `ancestor_id` bigint(20) NOT NULL COMMENT '祖先分销商ID',
  `level` tinyint(4) NOT NULL COMMENT '层级深度：1-直属，2-二级，3-三级',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agent_ancestor` (`agent_id`,`ancestor_id`),
  KEY `idx_ancestor_id` (`ancestor_id`),
  KEY `idx_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销关系表';
```

### 3.3 商品分销配置表（yt_goods_dist_config）
```sql
CREATE TABLE `yt_goods_dist_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `goods_id` bigint(20) NOT NULL COMMENT '商品ID，关联现有商品表',
  `goods_name` varchar(128) NOT NULL COMMENT '商品名称',
  `goods_type` varchar(32) NOT NULL COMMENT '商品类型',
  `enable_dist` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否启用分销：0-否，1-是',
  `default_scheme_id` bigint(20) DEFAULT NULL COMMENT '默认分销方案ID',
  `min_price` decimal(10,2) DEFAULT '0.00' COMMENT '最低销售价格',
  `stock_sync` tinyint(4) DEFAULT '1' COMMENT '是否同步库存：0-否，1-是',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_goods_id` (`goods_id`),
  KEY `idx_status` (`status`),
  KEY `idx_scheme_id` (`default_scheme_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分销配置表';
```

### 3.4 佣金记录表（yt_dist_commission_record）
```sql
CREATE TABLE `yt_dist_commission_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `agent_id` bigint(20) NOT NULL COMMENT '分销商ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_no` varchar(64) NOT NULL COMMENT '订单编号',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `product_name` varchar(128) NOT NULL COMMENT '商品名称',
  `order_amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `commission_amount` decimal(10,2) NOT NULL COMMENT '佣金金额',
  `commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '佣金比例（%）',
  `commission_level` tinyint(4) NOT NULL COMMENT '佣金层级：1-一级，2-二级，3-三级',
  `from_agent_id` bigint(20) NOT NULL COMMENT '来源分销商ID',
  `buyer_id` bigint(20) NOT NULL COMMENT '购买者ID',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待结算，1-已结算，2-已提现，3-已取消',
  `settle_time` datetime DEFAULT NULL COMMENT '结算时间',
  `remark` varchar(256) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_agent_level` (`order_id`,`agent_id`,`commission_level`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='佣金记录表';
```

### 3.5 提现记录表（yt_dist_withdraw_record）
```sql
CREATE TABLE `yt_dist_withdraw_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `withdraw_no` varchar(64) NOT NULL COMMENT '提现单号',
  `agent_id` bigint(20) NOT NULL COMMENT '分销商ID',
  `amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `fee` decimal(10,2) DEFAULT '0.00' COMMENT '手续费',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实际到账金额',
  `bank_name` varchar(64) DEFAULT NULL COMMENT '银行名称',
  `bank_account` varchar(64) DEFAULT NULL COMMENT '银行账号',
  `account_name` varchar(64) DEFAULT NULL COMMENT '账户名称',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待审核，1-审核通过，2-已打款，3-已到账，4-审核拒绝',
  `apply_time` datetime NOT NULL COMMENT '申请时间',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `pay_time` datetime DEFAULT NULL COMMENT '打款时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `audit_user` varchar(64) DEFAULT NULL COMMENT '审核人',
  `audit_remark` varchar(256) DEFAULT NULL COMMENT '审核备注',
  `pay_user` varchar(64) DEFAULT NULL COMMENT '打款人',
  `pay_remark` varchar(256) DEFAULT NULL COMMENT '打款备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_withdraw_no` (`withdraw_no`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_apply_time` (`apply_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现记录表';
```

### 3.6 分销统计表（yt_dist_statistics）
```sql
CREATE TABLE `yt_dist_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `agent_id` bigint(20) NOT NULL COMMENT '分销商ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `order_count` int(11) DEFAULT '0' COMMENT '订单数量',
  `order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '订单金额',
  `commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '佣金金额',
  `new_member_count` int(11) DEFAULT '0' COMMENT '新增下级人数',
  `active_member_count` int(11) DEFAULT '0' COMMENT '活跃下级人数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agent_date` (`agent_id`,`stat_date`),
  KEY `idx_stat_date` (`stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销统计表';
```

### 3.7 分销商等级表（yt_dist_agent_level）
```sql
CREATE TABLE `yt_dist_agent_level` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '等级ID',
  `level_code` varchar(32) NOT NULL COMMENT '等级编码',
  `level_name` varchar(64) NOT NULL COMMENT '等级名称',
  `level_grade` int(11) NOT NULL COMMENT '等级级别，数值越大等级越高',
  `icon_url` varchar(256) DEFAULT NULL COMMENT '等级图标',
  `color` varchar(32) DEFAULT NULL COMMENT '等级颜色',
  `min_sales` decimal(10,2) DEFAULT '0.00' COMMENT '升级所需最低销售额',
  `min_team_count` int(11) DEFAULT '0' COMMENT '升级所需最低团队人数',
  `min_direct_count` int(11) DEFAULT '0' COMMENT '升级所需最低直属人数',
  `auto_upgrade` tinyint(4) DEFAULT '1' COMMENT '是否自动升级：0-否，1-是',
  `auto_downgrade` tinyint(4) DEFAULT '0' COMMENT '是否自动降级：0-否，1-是',
  `benefits` text COMMENT '等级权益描述',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序序号',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_level_code` (`level_code`),
  KEY `idx_level_grade` (`level_grade`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销商等级表';
```

### 3.8 分销方案表（yt_dist_scheme）
```sql
CREATE TABLE `yt_dist_scheme` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '方案ID',
  `scheme_code` varchar(32) NOT NULL COMMENT '方案编码',
  `scheme_name` varchar(64) NOT NULL COMMENT '方案名称',
  `scheme_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '方案类型：1-通用方案，2-等级方案，3-个人方案',
  `level_id` bigint(20) DEFAULT NULL COMMENT '适用等级ID，当scheme_type=2时必填',
  `agent_id` bigint(20) DEFAULT NULL COMMENT '适用分销商ID，当scheme_type=3时必填',
  `dist_mode` tinyint(4) NOT NULL DEFAULT '1' COMMENT '分销模式：1-固定金额，2-百分比',
  `first_commission` decimal(10,2) DEFAULT '0.00' COMMENT '一级佣金（固定金额）',
  `second_commission` decimal(10,2) DEFAULT '0.00' COMMENT '二级佣金（固定金额）',
  `third_commission` decimal(10,2) DEFAULT '0.00' COMMENT '三级佣金（固定金额）',
  `first_rate` decimal(5,2) DEFAULT '0.00' COMMENT '一级佣金比例（%）',
  `second_rate` decimal(5,2) DEFAULT '0.00' COMMENT '二级佣金比例（%）',
  `third_rate` decimal(5,2) DEFAULT '0.00' COMMENT '三级佣金比例（%）',
  `min_order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最低订单金额',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金限制',
  `valid_days` int(11) DEFAULT '0' COMMENT '方案有效天数，0表示长期有效',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `priority` int(11) DEFAULT '0' COMMENT '优先级，数值越大优先级越高',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `remark` varchar(256) DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_code` (`scheme_code`),
  KEY `idx_scheme_type` (`scheme_type`),
  KEY `idx_level_id` (`level_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销方案表';
```

### 3.9 分销商品方案关联表（yt_dist_product_scheme）
```sql
CREATE TABLE `yt_dist_product_scheme` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `goods_id` bigint(20) NOT NULL COMMENT '商品ID',
  `scheme_id` bigint(20) NOT NULL COMMENT '方案ID',
  `priority` int(11) DEFAULT '0' COMMENT '优先级，同一商品多个方案时使用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_goods_scheme` (`goods_id`,`scheme_id`),
  KEY `idx_scheme_id` (`scheme_id`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销商品方案关联表';
```

### 3.10 分销邀请海报表（yt_dist_invite_poster）
```sql
CREATE TABLE `yt_dist_invite_poster` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `agent_id` bigint(20) NOT NULL COMMENT '分销商ID',
  `poster_code` varchar(32) NOT NULL COMMENT '海报编码',
  `poster_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '海报类型：1-通用海报，2-商品海报，3-活动海报',
  `target_level` tinyint(4) DEFAULT NULL COMMENT '目标代理级别：1-一级，2-二级，3-三级',
  `goods_id` bigint(20) DEFAULT NULL COMMENT '商品ID，商品海报时必填',
  `activity_id` bigint(20) DEFAULT NULL COMMENT '活动ID，活动海报时必填',
  `poster_url` varchar(512) NOT NULL COMMENT '海报图片URL',
  `qr_code_url` varchar(512) NOT NULL COMMENT '二维码图片URL',
  `invite_url` varchar(512) NOT NULL COMMENT '邀请链接',
  `invite_code` varchar(64) NOT NULL COMMENT '邀请码',
  `scan_count` int(11) DEFAULT '0' COMMENT '扫码次数',
  `register_count` int(11) DEFAULT '0' COMMENT '注册人数',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_poster_code` (`poster_code`),
  UNIQUE KEY `uk_invite_code` (`invite_code`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销邀请海报表';
```

## 4. 核心业务流程设计

### 4.1 成为分销商流程
```mermaid
graph TD
    A[用户申请成为分销商] --> B{是否满足条件}
    B -->|是| C[创建分销商账号]
    B -->|否| D[提示不满足条件]
    C --> E[绑定上级分销商]
    E --> F[建立分销关系链]
    F --> G[初始化分销商数据]
    G --> H[生成默认邀请海报]
```

### 4.2 分销下单流程
```mermaid
graph TD
    A[用户通过分销链接下单] --> B[识别分销来源]
    B --> C[创建订单]
    C --> D[记录分销信息]
    D --> E[订单支付]
    E --> F[计算各级佣金]
    F --> G[生成佣金记录]
    G --> H[更新分销商统计]
```

### 4.3 佣金结算流程
```mermaid
graph TD
    A[订单完成/售后期结束] --> B[触发佣金结算]
    B --> C[获取佣金记录]
    C --> D[计算实际佣金]
    D --> E[更新佣金状态]
    E --> F[增加可提现余额]
    F --> G[发送结算通知]
```

### 4.4 佣金提现流程
```mermaid
graph TD
    A[分销商申请提现] --> B{余额是否充足}
    B -->|是| C[创建提现申请]
    B -->|否| D[提示余额不足]
    C --> E[冻结提现金额]
    E --> F[后台审核]
    F -->|通过| G[财务打款]
    F -->|拒绝| H[解冻金额]
    G --> I[更新提现状态]
    I --> J[发送到账通知]
```

### 4.5 分销商等级升降级流程
```mermaid
graph TD
    A[定时任务/事件触发] --> B[获取分销商列表]
    B --> C[计算升级条件]
    C --> D{是否满足升级条件}
    D -->|是| E[获取下一等级信息]
    D -->|否| F{是否需要降级}
    E --> G[更新分销商等级]
    F -->|是| H[获取降级等级]
    F -->|否| I[保持当前等级]
    H --> G
    G --> J[更新分销方案]
    J --> K[发送等级变更通知]
    I --> L[结束]
    K --> L
```

### 4.6 分销方案匹配流程
```mermaid
graph TD
    A[订单创建] --> B[获取商品信息]
    B --> C[获取分销商信息]
    C --> D{是否有个人方案}
    D -->|是| E[使用个人方案]
    D -->|否| F[获取等级方案]
    F --> G{是否有等级方案}
    G -->|是| H[使用等级方案]
    G -->|否| I[使用通用方案]
    E --> J[计算佣金]
    H --> J
    I --> J
    J --> K[生成佣金记录]
```

### 4.7 邀请海报生成流程
```mermaid
graph TD
    A[选择海报类型] --> B{选择目标级别}
    B --> C[一级代理]
    B --> D[二级代理]
    B --> E[三级代理]
    C --> F[生成邀请码]
    D --> F
    E --> F
    F --> G[生成二维码]
    G --> H[合成海报图片]
    H --> I[保存海报记录]
    I --> J[返回海报信息]
```

## 5. API接口设计

### 5.1 分销商管理接口

#### 5.1.1 申请成为分销商
```
POST /api/distribution/agent/apply
Request:
{
    "realName": "张三",
    "phone": "13800138000",
    "inviteCode": "ABC123"  // 邀请码（可选）
}
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "agentId": 100001,
        "agentCode": "AG100001",
        "status": 1
    }
}
```

#### 5.1.2 获取分销商信息
```
GET /api/distribution/agent/info
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "agentId": 100001,
        "agentCode": "AG100001",
        "agentName": "张三",
        "levelInfo": {
            "levelId": 1,
            "levelName": "青铜分销商",
            "levelGrade": 1,
            "iconUrl": "https://xxx.com/level1.png",
            "color": "#CD7F32"
        },
        "schemeInfo": {
            "schemeId": 10,
            "schemeName": "青铜默认方案",
            "firstRate": "15%",
            "secondRate": "10%",
            "thirdRate": "5%"
        },
        "totalCommission": "1280.50",
        "availableBalance": "580.50",
        "totalSales": "12800.00",
        "monthSales": "3200.00",
        "teamCount": 25,
        "directCount": 8,
        "nextLevelProgress": {
            "nextLevelName": "白银分销商",
            "salesProgress": "12800/20000",
            "teamProgress": "25/50",
            "directProgress": "8/10"
        }
    }
}
```

#### 5.1.3 获取下级分销商列表
```
GET /api/distribution/agent/team?page=1&size=20
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "total": 25,
        "list": [
            {
                "agentId": 100002,
                "agentName": "李四",
                "level": 1,
                "joinTime": "2024-01-15 10:30:00",
                "orderCount": 15,
                "contribution": "380.00"
            }
        ]
    }
}
```

### 5.2 商品分销接口

#### 5.2.1 获取分销商品列表
```
GET /api/distribution/product/list?page=1&size=20
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "total": 50,
        "list": [
            {
                "productId": 1001,
                "productName": "潮州双人省钱卡",
                "price": "149.00",
                "originalPrice": "219.00",
                "commission": "39.90",
                "commissionRate": "26.7%",
                "salesCount": 128
            }
        ]
    }
}
```

#### 5.2.2 生成分销链接
```
POST /api/distribution/product/share
Request:
{
    "productId": 1001,
    "channel": "wechat"  // wechat/douyin/xhs
}
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "shareUrl": "https://xxx.com/p/1001?agent=AG100001",
        "shareCode": "ABC123",
        "qrCode": "base64...",
        "poster": "https://xxx.com/poster/xxx.jpg"
    }
}
```

### 5.3 佣金管理接口

#### 5.3.1 获取佣金记录
```
GET /api/distribution/commission/list?page=1&size=20&status=1
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "total": 156,
        "list": [
            {
                "recordId": 10001,
                "orderNo": "202401150001",
                "productName": "潮州双人省钱卡",
                "orderAmount": "149.00",
                "commission": "39.90",
                "level": 1,
                "fromAgent": "李四",
                "status": 1,
                "createTime": "2024-01-15 15:30:00"
            }
        ]
    }
}
```

#### 5.3.2 获取佣金统计
```
GET /api/distribution/commission/statistics?month=2024-01
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "totalCommission": "3580.00",
        "settledCommission": "2800.00",
        "unsettledCommission": "780.00",
        "orderCount": 89,
        "dailyStats": [
            {
                "date": "2024-01-15",
                "amount": "280.50",
                "count": 7
            }
        ]
    }
}
```

### 5.4 提现管理接口

#### 5.4.1 申请提现
```
POST /api/distribution/withdraw/apply
Request:
{
    "amount": "500.00",
    "bankName": "中国工商银行",
    "bankAccount": "6222021234567890123",
    "accountName": "张三"
}
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "withdrawNo": "WD202401150001",
        "status": 0
    }
}
```

#### 5.4.2 获取提现记录
```
GET /api/distribution/withdraw/list?page=1&size=20
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "total": 12,
        "list": [
            {
                "withdrawNo": "WD202401150001",
                "amount": "500.00",
                "fee": "2.00",
                "actualAmount": "498.00",
                "status": 2,
                "applyTime": "2024-01-15 10:30:00",
                "completeTime": "2024-01-16 15:20:00"
            }
        ]
    }
}
```

### 5.5 等级管理接口（管理端）

#### 5.5.1 创建/编辑等级
```
POST /admin/distribution/level/save
Request:
{
    "id": null,  // null为新增，有值为编辑
    "levelCode": "L001",
    "levelName": "青铜分销商",
    "levelGrade": 1,
    "iconUrl": "https://xxx.com/level1.png",
    "color": "#CD7F32",
    "minSales": "0",
    "minTeamCount": 0,
    "minDirectCount": 0,
    "autoUpgrade": 1,
    "autoDowngrade": 0,
    "benefits": "基础分销权益，享受标准佣金比例",
    "sortOrder": 1
}
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "levelId": 1
    }
}
```

#### 5.5.2 获取等级列表
```
GET /admin/distribution/level/list?page=1&size=20
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "total": 5,
        "list": [
            {
                "id": 1,
                "levelCode": "L001",
                "levelName": "青铜分销商",
                "levelGrade": 1,
                "agentCount": 1280,
                "defaultSchemeId": 10,
                "defaultSchemeName": "青铜默认方案"
            }
        ]
    }
}
```

### 5.6 分销方案管理接口（管理端）

#### 5.6.1 创建/编辑分销方案
```
POST /admin/distribution/scheme/save
Request:
{
    "id": null,
    "schemeCode": "SCH001",
    "schemeName": "青铜默认方案",
    "schemeType": 2,  // 等级方案
    "levelId": 1,
    "distMode": 2,  // 百分比
    "firstRate": 15,
    "secondRate": 10,
    "thirdRate": 5,
    "minOrderAmount": "0",
    "maxCommission": "1000",
    "validDays": 0,
    "priority": 100
}
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "schemeId": 10
    }
}
```

#### 5.6.2 为分销商设置个人方案
```
POST /admin/distribution/scheme/personal
Request:
{
    "agentId": 100001,
    "schemeId": 20,  // 特殊方案ID
    "validDays": 30,  // 有效期30天
    "remark": "优秀分销商特殊奖励"
}
Response:
{
    "code": 0,
    "msg": "success"
}
```

#### 5.6.3 批量设置商品分销方案
```
POST /admin/distribution/product/scheme/batch
Request:
{
    "goodsIds": [1001, 1002, 1003],
    "schemeIds": [10, 11, 12],  // 多个方案，按优先级生效
    "enableDist": 1
}
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "successCount": 3
    }
}
```

### 5.7 邀请海报管理接口

#### 5.7.1 生成邀请海报
```
POST /api/distribution/poster/generate
Request:
{
    "posterType": 1,  // 1-通用海报，2-商品海报，3-活动海报
    "targetLevel": 1,  // 目标代理级别：1-一级，2-二级，3-三级
    "goodsId": null,  // 商品ID，商品海报时必填
    "activityId": null  // 活动ID，活动海报时必填
}
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "posterId": 1001,
        "posterCode": "PST202401150001",
        "posterUrl": "https://xxx.com/poster/PST202401150001.jpg",
        "qrCodeUrl": "https://xxx.com/qr/PST202401150001.png",
        "inviteUrl": "https://xxx.com/invite?code=ABC123",
        "inviteCode": "ABC123"
    }
}
```

#### 5.7.2 获取我的海报列表
```
GET /api/distribution/poster/list?page=1&size=20&posterType=1
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "total": 15,
        "list": [
            {
                "posterId": 1001,
                "posterType": 1,
                "targetLevel": 1,
                "posterUrl": "https://xxx.com/poster/PST202401150001.jpg",
                "inviteCode": "ABC123",
                "scanCount": 128,
                "registerCount": 35,
                "createTime": "2024-01-15 10:30:00"
            }
        ]
    }
}
```

#### 5.7.3 获取海报统计数据
```
GET /api/distribution/poster/statistics?posterId=1001
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "posterId": 1001,
        "totalScan": 1280,
        "totalRegister": 156,
        "todayScan": 45,
        "todayRegister": 12,
        "registerRate": "12.2%",
        "dailyStats": [
            {
                "date": "2024-01-15",
                "scanCount": 45,
                "registerCount": 12
            }
        ]
    }
}
```

## 6. 关键功能实现

### 6.1 分销关系维护
```java
@Service
public class DistributionRelationServiceImpl implements DistributionRelationService {
    
    @Transactional
    public void buildRelation(Long agentId, Long parentId) {
        // 1. 建立直接上级关系
        YtDistRelationDO directRelation = new YtDistRelationDO();
        directRelation.setAgentId(agentId);
        directRelation.setParentId(parentId);
        directRelation.setAncestorId(parentId);
        directRelation.setLevel(1);
        relationMapper.insert(directRelation);
        
        // 2. 建立间接上级关系（最多三级）
        List<YtDistRelationDO> parentRelations = relationMapper.selectByAgentId(parentId);
        for (YtDistRelationDO parentRelation : parentRelations) {
            if (parentRelation.getLevel() < 3) {
                YtDistRelationDO indirectRelation = new YtDistRelationDO();
                indirectRelation.setAgentId(agentId);
                indirectRelation.setParentId(parentId);
                indirectRelation.setAncestorId(parentRelation.getAncestorId());
                indirectRelation.setLevel(parentRelation.getLevel() + 1);
                relationMapper.insert(indirectRelation);
            }
        }
        
        // 3. 更新上级的团队人数
        updateTeamCount(parentId);
    }
}
```

### 6.2 佣金计算引擎（支持灵活方案）
```java
@Service
public class CommissionCalculateServiceImpl implements CommissionCalculateService {
    
    @Autowired
    private DistSchemeService schemeService;
    
    @Autowired
    private DistAgentService agentService;
    
    public List<CommissionDTO> calculate(OrderDTO order) {
        List<CommissionDTO> commissions = new ArrayList<>();
        
        // 1. 获取分销链路
        List<YtDistRelationDO> relations = relationMapper.selectAncestorsByAgentId(order.getAgentId());
        
        // 2. 计算各级佣金
        for (YtDistRelationDO relation : relations) {
            // 获取上级分销商信息
            YtDistAgentDO ancestor = agentService.getById(relation.getAncestorId());
            if (ancestor == null || ancestor.getStatus() != 1) {
                continue;
            }
            
            // 获取适用的分销方案
            YtDistSchemeDO scheme = getApplicableScheme(order.getGoodsId(), ancestor);
            if (scheme == null) {
                continue;
            }
            
            // 计算佣金
            CommissionDTO commission = new CommissionDTO();
            commission.setAgentId(relation.getAncestorId());
            commission.setOrderId(order.getOrderId());
            commission.setLevel(relation.getLevel());
            commission.setSchemeId(scheme.getId());
            
            // 根据分销模式计算佣金
            BigDecimal commissionAmount = calculateCommissionAmount(
                order.getAmount(), scheme, relation.getLevel()
            );
            
            // 应用最高佣金限制
            if (scheme.getMaxCommission() != null && 
                commissionAmount.compareTo(scheme.getMaxCommission()) > 0) {
                commissionAmount = scheme.getMaxCommission();
            }
            
            commission.setAmount(commissionAmount);
            commissions.add(commission);
        }
        
        return commissions;
    }
    
    /**
     * 获取适用的分销方案
     * 优先级：个人方案 > 等级方案 > 通用方案
     */
    private YtDistSchemeDO getApplicableScheme(Long goodsId, YtDistAgentDO agent) {
        // 1. 检查是否有个人方案
        if (agent.getSchemeId() != null) {
            YtDistSchemeDO personalScheme = schemeService.getById(agent.getSchemeId());
            if (personalScheme != null && personalScheme.getStatus() == 1 
                && isSchemeValid(personalScheme)) {
                return personalScheme;
            }
        }
        
        // 2. 获取等级方案
        List<YtDistSchemeDO> levelSchemes = schemeService.getByLevelId(agent.getLevelId());
        YtDistSchemeDO applicableLevelScheme = findApplicableScheme(levelSchemes, goodsId);
        if (applicableLevelScheme != null) {
            return applicableLevelScheme;
        }
        
        // 3. 获取通用方案
        List<YtDistSchemeDO> generalSchemes = schemeService.getGeneralSchemes();
        return findApplicableScheme(generalSchemes, goodsId);
    }
    
    private BigDecimal calculateCommissionAmount(BigDecimal orderAmount, 
                                                YtDistSchemeDO scheme, 
                                                int level) {
        if (scheme.getDistMode() == 1) { // 固定金额
            switch (level) {
                case 1: return scheme.getFirstCommission();
                case 2: return scheme.getSecondCommission();
                case 3: return scheme.getThirdCommission();
                default: return BigDecimal.ZERO;
            }
        } else { // 百分比
            BigDecimal rate;
            switch (level) {
                case 1: rate = scheme.getFirstRate(); break;
                case 2: rate = scheme.getSecondRate(); break;
                case 3: rate = scheme.getThirdRate(); break;
                default: rate = BigDecimal.ZERO;
            }
            return orderAmount.multiply(rate).divide(new BigDecimal(100), 2, RoundingMode.DOWN);
        }
    }
}
```

### 6.3 分销商等级升降级服务
```java
@Service
public class AgentLevelServiceImpl implements AgentLevelService {
    
    @Autowired
    private YtDistAgentLevelMapper levelMapper;
    
    @Autowired
    private YtDistAgentMapper agentMapper;
    
    @Autowired
    private YtDistSchemeService schemeService;
    
    /**
     * 检查并执行等级升降级
     */
    @Transactional
    public void checkAndUpdateLevels() {
        // 获取所有启用的等级，按等级从高到低排序
        List<YtDistAgentLevelDO> levels = levelMapper.selectEnabledLevels();
        
        // 获取需要检查的分销商列表
        List<YtDistAgentDO> agents = agentMapper.selectActiveAgents();
        
        for (YtDistAgentDO agent : agents) {
            YtDistAgentLevelDO currentLevel = levelMapper.selectById(agent.getLevelId());
            YtDistAgentLevelDO targetLevel = determineTargetLevel(agent, levels, currentLevel);
            
            if (!targetLevel.getId().equals(currentLevel.getId())) {
                updateAgentLevel(agent, targetLevel);
            }
        }
    }
    
    /**
     * 确定分销商的目标等级
     */
    private YtDistAgentLevelDO determineTargetLevel(YtDistAgentDO agent, 
                                                  List<YtDistAgentLevelDO> levels,
                                                  YtDistAgentLevelDO currentLevel) {
        // 从高到低检查是否满足升级条件
        for (YtDistAgentLevelDO level : levels) {
            if (level.getLevelGrade() > currentLevel.getLevelGrade() 
                && checkUpgradeConditions(agent, level)) {
                return level; // 返回可以升级到的最高等级
            }
        }
        
        // 检查是否需要降级
        if (currentLevel.getAutoDowngrade() == 1) {
            for (int i = levels.size() - 1; i >= 0; i--) {
                YtDistAgentLevelDO level = levels.get(i);
                if (level.getLevelGrade() < currentLevel.getLevelGrade() 
                    && checkDowngradeConditions(agent, currentLevel)) {
                    return level;
                }
            }
        }
        
        return currentLevel; // 保持当前等级
    }
    
    /**
     * 更新分销商等级
     */
    @Transactional
    public void updateAgentLevel(YtDistAgentDO agent, YtDistAgentLevelDO newLevel) {
        Long oldLevelId = agent.getLevelId();
        
        // 更新分销商等级
        agent.setLevelId(newLevel.getId());
        agent.setLevelUpdateTime(new Date());
        agentMapper.updateById(agent);
        
        // 如果新等级有默认方案，更新分销方案
        List<YtDistSchemeDO> levelSchemes = schemeService.getByLevelId(newLevel.getId());
        if (!levelSchemes.isEmpty() && agent.getSchemeId() == null) {
            // 设置等级默认方案（优先级最高的）
            YtDistSchemeDO defaultScheme = levelSchemes.stream()
                .max(Comparator.comparing(YtDistSchemeDO::getPriority))
                .orElse(levelSchemes.get(0));
            agent.setSchemeId(defaultScheme.getId());
            agentMapper.updateById(agent);
        }
        
        // 发送等级变更通知
        sendLevelChangeNotification(agent, oldLevelId, newLevel.getId());
        
        // 记录等级变更日志
        saveLevelChangeLog(agent, oldLevelId, newLevel.getId());
    }
    
    private boolean checkUpgradeConditions(YtDistAgentDO agent, YtDistAgentLevelDO targetLevel) {
        // 检查销售额条件
        if (targetLevel.getMinSales() != null 
            && agent.getTotalSales().compareTo(targetLevel.getMinSales()) < 0) {
            return false;
        }
        
        // 检查团队人数条件
        if (targetLevel.getMinTeamCount() != null 
            && agent.getTeamCount() < targetLevel.getMinTeamCount()) {
            return false;
        }
        
        // 检查直属人数条件
        if (targetLevel.getMinDirectCount() != null 
            && agent.getDirectCount() < targetLevel.getMinDirectCount()) {
            return false;
        }
        
        return true;
    }
}
```

### 6.4 邀请海报生成服务
```java
@Service
public class InvitePosterServiceImpl implements InvitePosterService {
    
    @Autowired
    private YtDistInvitePosterMapper posterMapper;
    
    @Autowired
    private QrCodeService qrCodeService;
    
    @Autowired
    private ImageService imageService;
    
    @Transactional
    public InvitePosterDTO generatePoster(GeneratePosterRequest request) {
        // 1. 生成邀请码
        String inviteCode = generateInviteCode();
        
        // 2. 构建邀请链接
        String inviteUrl = buildInviteUrl(inviteCode, request.getTargetLevel());
        
        // 3. 生成二维码
        String qrCodeUrl = qrCodeService.generate(inviteUrl);
        
        // 4. 合成海报图片
        PosterTemplate template = getPosterTemplate(request.getPosterType());
        String posterUrl = imageService.composePoster(template, qrCodeUrl, request);
        
        // 5. 保存海报记录
        YtDistInvitePosterDO poster = new YtDistInvitePosterDO();
        poster.setAgentId(request.getAgentId());
        poster.setPosterCode(generatePosterCode());
        poster.setPosterType(request.getPosterType());
        poster.setTargetLevel(request.getTargetLevel());
        poster.setGoodsId(request.getGoodsId());
        poster.setPosterUrl(posterUrl);
        poster.setQrCodeUrl(qrCodeUrl);
        poster.setInviteUrl(inviteUrl);
        poster.setInviteCode(inviteCode);
        poster.setStatus(1);
        posterMapper.insert(poster);
        
        return convertToDTO(poster);
    }
    
    /**
     * 生成唯一邀请码
     */
    private String generateInviteCode() {
        // 生成6位唯一邀请码
        String code;
        do {
            code = RandomUtil.randomString(6).toUpperCase();
        } while (posterMapper.existsByInviteCode(code));
        return code;
    }
    
    /**
     * 构建邀请链接
     */
    private String buildInviteUrl(String inviteCode, Integer targetLevel) {
        return String.format("%s/invite?code=%s&level=%d", 
            getBaseUrl(), inviteCode, targetLevel);
    }
}
```

## 7. 性能优化方案

### 7.1 缓存策略
- 分销商基本信息缓存（Redis，TTL=1小时）
- 分销关系缓存（Redis，TTL=24小时）
- 商品分销配置缓存（本地缓存+Redis）
- 热门分销商品缓存（Redis，TTL=10分钟）

### 7.2 数据库优化
- 合理使用索引，特别是关系查询
- 佣金记录表按月分表
- 统计数据定时汇总，避免实时计算
- 使用读写分离，查询走从库

### 7.3 异步处理
- 订单佣金计算异步化
- 统计数据更新异步化
- 海报生成异步处理
- 批量任务队列化

## 8. 安全设计

### 8.1 权限控制
- 分销商只能查看自己的数据
- 敏感操作需要二次验证
- API接口限流防刷
- 管理后台权限细分

### 8.2 数据安全
- 银行账号等敏感信息加密存储
- 提现操作日志完整记录
- 金额计算使用BigDecimal
- 防止并发导致的数据不一致

### 8.3 防作弊机制
- IP地址监控
- 异常订单检测
- 刷单行为识别
- 黑名单机制

## 9. 监控告警

### 9.1 业务监控
- 每日新增分销商数量
- 佣金结算成功率
- 提现处理时效
- 异常订单占比

### 9.2 技术监控
- API接口响应时间
- 数据库慢查询
- 消息队列积压
- 缓存命中率

### 9.3 告警规则
- 佣金计算失败超过阈值
- 提现失败率异常
- 关键接口响应超时
- 数据库连接数告警

## 10. 部署方案

### 10.1 部署架构
- 应用服务器：2台（负载均衡）
- 数据库：主从架构
- Redis：哨兵模式
- 文件存储：OSS/MinIO

### 10.2 发布流程
1. 数据库脚本执行
2. 灰度发布（10%流量）
3. 监控观察30分钟
4. 全量发布
5. 回归测试

### 10.3 回滚方案
- 数据库脚本可回滚
- 应用版本可快速切换
- 配置中心支持热更新
- 完整的回滚测试流程

## 11. 分销商等级体系详细说明

### 11.1 等级体系设计理念
1. **灵活配置**：支持自定义等级数量、名称、升级条件
2. **差异化方案**：同一等级的分销商可以有不同的分销方案
3. **自动升降级**：根据业绩自动调整等级
4. **权益激励**：不同等级享受不同权益和佣金比例

### 11.2 方案优先级规则
```
个人方案 > 等级方案 > 通用方案
```
- **个人方案**：为特定分销商定制的专属方案
- **等级方案**：该等级分销商的默认方案
- **通用方案**：所有分销商的兜底方案

### 11.3 等级配置示例
```json
[
  {
    "levelName": "青铜分销商",
    "levelGrade": 1,
    "conditions": {
      "minSales": 0,
      "minTeamCount": 0,
      "minDirectCount": 0
    },
    "defaultScheme": {
      "firstRate": 15,
      "secondRate": 10,
      "thirdRate": 5
    }
  },
  {
    "levelName": "白银分销商",
    "levelGrade": 2,
    "conditions": {
      "minSales": 20000,
      "minTeamCount": 50,
      "minDirectCount": 10
    },
    "defaultScheme": {
      "firstRate": 18,
      "secondRate": 12,
      "thirdRate": 6
    }
  },
  {
    "levelName": "黄金分销商",
    "levelGrade": 3,
    "conditions": {
      "minSales": 100000,
      "minTeamCount": 200,
      "minDirectCount": 30
    },
    "defaultScheme": {
      "firstRate": 20,
      "secondRate": 15,
      "thirdRate": 8
    }
  }
]
```

### 11.4 使用场景
1. **新手扶持**：新加入的分销商自动获得新手专属方案，有效期30天
2. **业绩奖励**：月销售额超过目标的分销商，次月享受更高佣金比例
3. **特殊活动**：活动期间为参与分销商设置临时高佣金方案
4. **VIP定制**：为重要合作伙伴定制专属分销方案

## 12. 后续规划

### 12.1 功能扩展
- 团队管理功能增强
- 分销培训体系
- 分销商城功能
- 分销数据大屏

### 12.2 技术优化
- 引入图数据库优化关系查询
- 使用Flink进行实时统计
- 区块链存证佣金记录
- AI智能推荐分销商品

### 12.3 业务创新
- 社交裂变玩法
- 直播分销模式
- 内容分销体系
- 跨平台分销打通

## 13. 更新说明

### 13.1 主要调整
1. **表名规范化**：所有表名增加 `yt_` 前缀，保持与现有系统一致
2. **商品管理优化**：
   - 将 `dist_product_config` 改为 `yt_goods_dist_config`
   - 字段 `product_id` 改为 `goods_id`，关联现有商品库
   - 商品分销配置时从现有商品库选择
3. **移除消息服务**：改用异步任务和定时任务处理
4. **新增邀请海报功能**：
   - 新增 `yt_dist_invite_poster` 表
   - 支持生成不同级别的邀请海报
   - 海报统计和追踪功能

### 13.2 核心特性
1. **灵活的等级体系**：支持自定义分销商等级，同一等级可配置不同分销方案
2. **方案优先级机制**：个人方案 > 等级方案 > 通用方案
3. **自动升降级**：根据业绩自动调整分销商等级
4. **邀请体系**：完整的邀请海报生成、分享、统计功能
5. **积分管理**：与现有积分系统集成（待实现）

### 13.3 技术优化
- 使用现有的商品体系，避免重复建设
- 复用会员系统进行用户管理
- 与财务模块深度集成实现佣金结算
- 海报服务独立部署，支持高并发生成