# 分销代理系统详细设计文档 v2.0

## 1. 概述

本文档基于 v1.1 版本进行全面升级，主要改进点：
1. **多维度配置体系**：支持个人专属、标签、等级、商品等多维度配置
2. **智能归因机制**：建立清晰的配置优先级体系，自动处理配置冲突
3. **标签化管理**：支持人员标签配置，实现精细化分销策略
4. **实时缓存优化**：提升配置查询性能，支持实时生效
5. **完善监控体系**：全链路追踪，配置生效可视化

## 2. 核心需求分析

### 2.1 自定义分销等级
- **需求描述**：支持自定义分销等级，通过等级级别（level_grade）区分高低
- **设计要点**：
  - 等级可灵活配置（数量、名称、条件）
  - 通过 level_grade 数值大小判断等级高低
  - 支持升降级条件配置
  - 等级变更自动触发相关业务流程

### 2.2 分销员申请与绑定
- **需求描述**：人员申请成为分销员后，有自己的分销码。其他人员根据分销码，申请或者注册就成功绑定其为分销员的子分销员
- **设计要点**：
  - 每个分销员拥有唯一的分销码
  - 区分三种关系：
    - **上级**：管理关系，决定组织架构和团队归属
    - **等级**：决定分销员的分润比例和权益
    - **介绍人**：推荐关系，申请时谁介绍的（可有可无）
  - 支持通过分销码建立关系
  - **新注册用户**：通过分销码注册时，分销码拥有者同时成为其上级和介绍人
  - **已注册用户**：申请时可灵活选择绑定关系类型

### 2.3 多维度奖励配置体系【v2.0核心升级】
- **需求描述**：建立多维度、多层级的奖励配置体系，支持精细化分销策略
- **设计要点**：
  - **个人专属配置**：为特定分销员设置专属奖励政策
  - **标签配置**：基于人员标签（KOL、VIP、合作伙伴等）设置差异化奖励
  - **等级配置**：按分销等级设置不同的奖励标准
  - **商品配置**：按商品维度（SKU/SPU/类目）设置专项奖励
  - **全局配置**：系统默认的基础奖励配置
  - **智能归因**：自动选择最优配置，避免冲突

### 2.4 人员标签管理【v2.0新增】
- **需求描述**：支持为分销员打标签，实现人群分类和精准营销
- **设计要点**：
  - 支持多种标签类型：系统标签、业务标签、营销标签
  - 标签可设置有效期，支持临时性标签
  - 基于标签配置差异化奖励政策
  - 标签可视化管理，支持批量操作

### 2.5 配置归因机制【v2.0新增】
- **需求描述**：建立清晰的配置优先级体系，自动处理多维度配置冲突
- **设计要点**：
  - **优先级体系**：个人专属 > 标签配置 > 等级配置 > 商品配置 > 全局配置
  - **冲突处理**：自动选择最高优先级的有效配置
  - **决策追踪**：记录配置选择过程，支持审计和调试
  - **实时生效**：配置变更后立即生效，支持缓存刷新

## 3. 数据库设计

### 3.1 分销等级表（yt_dist_level）
```sql
CREATE TABLE `yt_dist_level` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '等级ID',
  `level_code` varchar(32) NOT NULL COMMENT '等级编码',
  `level_name` varchar(64) NOT NULL COMMENT '等级名称',
  `level_grade` int(11) NOT NULL COMMENT '等级级别，数值越大等级越高',
  `invite_code` varchar(32) DEFAULT NULL COMMENT '等级邀请码',
  `icon_url` varchar(256) DEFAULT NULL COMMENT '等级图标',
  `color` varchar(32) DEFAULT NULL COMMENT '等级颜色',
  `description` text COMMENT '等级说明',
  `benefits` text COMMENT '等级权益描述',
  `upgrade_conditions` json DEFAULT NULL COMMENT '升级条件（JSON格式）',
  `downgrade_conditions` json DEFAULT NULL COMMENT '降级条件（JSON格式）',
  `auto_upgrade` tinyint(1) DEFAULT '1' COMMENT '是否自动升级',
  `auto_downgrade` tinyint(1) DEFAULT '0' COMMENT '是否自动降级',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序序号',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_level_code` (`level_code`),
  UNIQUE KEY `uk_invite_code` (`invite_code`),
  KEY `idx_level_grade` (`level_grade`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销等级表';
```

### 3.2 分销员信息表（yt_dist_agent）
```sql
CREATE TABLE `yt_dist_agent` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分销员ID',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `agent_code` varchar(32) NOT NULL COMMENT '分销员编码（分销码）',
  `agent_name` varchar(64) NOT NULL COMMENT '分销员名称',
  `level_id` bigint(20) NOT NULL COMMENT '等级ID',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '直接上级分销员ID（管理关系）',
  `parent_code` varchar(32) DEFAULT NULL COMMENT '上级分销码',
  `referrer_id` bigint(20) DEFAULT NULL COMMENT '介绍人ID（推荐关系）',
  `agent_tags` varchar(512) DEFAULT NULL COMMENT '分销员标签（逗号分隔）',
  `referrer_code` varchar(32) DEFAULT NULL COMMENT '介绍人分销码',
  `referrer_time` datetime DEFAULT NULL COMMENT '介绍时间',
  `path` varchar(512) DEFAULT NULL COMMENT '分销路径，如：1,2,3',
  `depth` int(11) DEFAULT '1' COMMENT '层级深度，顶级为1',
  `bind_time` datetime DEFAULT NULL COMMENT '绑定上级时间',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `team_count` int(11) DEFAULT '0' COMMENT '团队人数（包含所有下级）',
  `direct_count` int(11) DEFAULT '0' COMMENT '直属下级人数',
  `referral_count` int(11) DEFAULT '0' COMMENT '介绍人数',
  `total_sales` decimal(10,2) DEFAULT '0.00' COMMENT '累计销售额',
  `month_sales` decimal(10,2) DEFAULT '0.00' COMMENT '本月销售额',
  `join_time` datetime NOT NULL COMMENT '成为分销员时间',
  `level_update_time` datetime DEFAULT NULL COMMENT '等级更新时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_member_id` (`member_id`),
  UNIQUE KEY `uk_agent_code` (`agent_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_parent_code` (`parent_code`),
  KEY `idx_referrer_id` (`referrer_id`),
  KEY `idx_referrer_code` (`referrer_code`),
  KEY `idx_level_id` (`level_id`),
  KEY `idx_status` (`status`),
  KEY `idx_depth` (`depth`),
  KEY `idx_agent_tags` (`agent_tags`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员信息表';
```

### 3.3 分销员标签表（yt_dist_agent_tag）【v2.0新增】
```sql
CREATE TABLE `yt_dist_agent_tag` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tag_code` varchar(32) NOT NULL COMMENT '标签编码（唯一）',
  `tag_name` varchar(64) NOT NULL COMMENT '标签名称',
  `tag_desc` varchar(256) DEFAULT NULL COMMENT '标签描述',
  `tag_type` tinyint(4) DEFAULT '1' COMMENT '标签类型：1-系统标签，2-业务标签，3-营销标签',
  `color` varchar(16) DEFAULT NULL COMMENT '标签颜色（用于前端展示）',
  `icon` varchar(64) DEFAULT NULL COMMENT '标签图标',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tag_code` (`tag_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员标签表';
```

### 3.4 分销员标签关联表（yt_dist_agent_tag_rel）【v2.0新增】
```sql
CREATE TABLE `yt_dist_agent_tag_rel` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  `attach_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '标签附加时间',
  `expire_time` datetime DEFAULT NULL COMMENT '标签过期时间（NULL表示永久）',
  `attach_reason` varchar(256) DEFAULT NULL COMMENT '附加原因',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agent_tag` (`agent_id`, `tag_id`),
  KEY `idx_tag_id` (`tag_id`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员标签关联表';
```

### 3.5 奖励方案主表（yt_dist_reward_scheme）
```sql
CREATE TABLE `yt_dist_reward_scheme` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '方案ID',
  `scheme_code` varchar(32) NOT NULL COMMENT '方案编码',
  `scheme_name` varchar(64) NOT NULL COMMENT '方案名称',
  `scheme_desc` varchar(256) DEFAULT NULL COMMENT '方案描述',
  `apply_scope` tinyint(4) NOT NULL DEFAULT '1' COMMENT '适用范围：1-全局，2-指定商品类目，3-指定商品',
  `category_id` bigint(20) DEFAULT NULL COMMENT '商品类目ID（apply_scope=2时使用）',
  `spu_id` bigint(20) DEFAULT NULL COMMENT 'SPU ID（apply_scope=3时使用）',
  `sku_id` bigint(20) DEFAULT NULL COMMENT 'SKU ID（apply_scope=3时使用，可选）',
  `enable_sales_reward` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用销售奖励',
  `enable_profit_sharing` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用分润奖励',
  `profit_target` tinyint(4) DEFAULT '1' COMMENT '分润对象：1-上级，2-介绍人，3-两者都有',
  `max_trace_level` int(11) DEFAULT '3' COMMENT '最大追溯层级',
  `level_config_mode` tinyint(4) NOT NULL DEFAULT '1' COMMENT '等级配置模式：1-统一配置，2-分等级配置',
  `default_sales_mode` tinyint(4) DEFAULT '2' COMMENT '默认销售佣金模式：1-固定金额，2-百分比',
  `default_sales_rate` decimal(5,2) DEFAULT '0.00' COMMENT '默认销售佣金比例（%）',
  `default_sales_amount` decimal(10,2) DEFAULT '0.00' COMMENT '默认销售固定佣金',
  `default_profit_mode` tinyint(4) DEFAULT '2' COMMENT '默认分润佣金模式：1-固定金额，2-百分比',
  `default_profit_rate` decimal(5,2) DEFAULT '0.00' COMMENT '默认分润佣金比例（%）',
  `default_profit_amount` decimal(10,2) DEFAULT '0.00' COMMENT '默认分润固定佣金',
  `min_order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最低订单金额',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金限制',
  `trigger_stage` tinyint(4) DEFAULT '1' COMMENT '触发阶段：1-订单支付后，2-券码核销后，3-订单完成后',
  `effective_type` tinyint(4) DEFAULT '1' COMMENT '生效类型：1-长期有效，2-限时有效',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `priority` int(11) DEFAULT '0' COMMENT '优先级，数值越大优先级越高',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_code` (`scheme_code`),
  KEY `idx_apply_scope` (`apply_scope`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_spu_id` (`spu_id`),
  KEY `idx_sku_id` (`sku_id`),
  KEY `idx_level_config_mode` (`level_config_mode`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='奖励方案主表';
```

### 3.6 等级奖励配置表（yt_dist_reward_level_config）
```sql
CREATE TABLE `yt_dist_reward_level_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `scheme_id` bigint(20) NOT NULL COMMENT '方案ID',
  `level_id` bigint(20) NOT NULL COMMENT '分销等级ID',
  `level_name` varchar(64) DEFAULT NULL COMMENT '等级名称（冗余）',
  `sales_commission_mode` tinyint(4) DEFAULT '2' COMMENT '销售佣金模式：1-固定金额，2-百分比',
  `sales_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '销售佣金比例（%）',
  `sales_commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '销售固定佣金金额',
  `parent_profit_config` json DEFAULT NULL COMMENT '上级分润配置（JSON格式，包含各级分润比例）',
  `referrer_profit_config` json DEFAULT NULL COMMENT '介绍人分润配置（JSON格式，包含各级分润比例）',
  `condition_type` tinyint(4) DEFAULT '1' COMMENT '条件类型：1-无条件，2-基于销售额，3-基于销售量',
  `condition_config` json DEFAULT NULL COMMENT '条件配置（JSON格式）',
  `min_order_amount` decimal(10,2) DEFAULT NULL COMMENT '最低订单金额（覆盖方案默认值）',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金（覆盖方案默认值）',
  `remark` varchar(256) DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_level` (`scheme_id`, `level_id`),
  KEY `idx_scheme_id` (`scheme_id`),
  KEY `idx_level_id` (`level_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='等级奖励配置表';
```

### 3.7 标签奖励配置表（yt_dist_reward_tag_config）【v2.0新增】
```sql
CREATE TABLE `yt_dist_reward_tag_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `scheme_id` bigint(20) NOT NULL COMMENT '奖励方案ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  `sales_commission_mode` tinyint(4) DEFAULT '1' COMMENT '销售佣金模式：1-不设置，2-百分比，3-固定金额',
  `sales_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '销售佣金比例（%）',
  `sales_commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '销售固定佣金金额',
  `parent_profit_config` json DEFAULT NULL COMMENT '上级分润配置（JSON格式）',
  `referrer_profit_config` json DEFAULT NULL COMMENT '介绍人分润配置（JSON格式）',
  `priority` int(11) DEFAULT '0' COMMENT '优先级（标签内部优先级）',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_tag` (`scheme_id`, `tag_id`),
  KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签奖励配置表';
```

### 3.8 个人专属奖励配置表（yt_dist_reward_personal_config）【v2.0新增】
```sql
CREATE TABLE `yt_dist_reward_personal_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `scheme_id` bigint(20) DEFAULT NULL COMMENT '关联的奖励方案ID（可选）',
  `config_name` varchar(128) NOT NULL COMMENT '配置名称',
  `config_desc` varchar(512) DEFAULT NULL COMMENT '配置描述',
  `apply_scope` tinyint(4) DEFAULT '1' COMMENT '适用范围：1-全部商品，2-指定类目，3-指定SPU，4-指定SKU',
  `scope_ids` json DEFAULT NULL COMMENT '适用范围ID列表（JSON数组）',
  `sales_commission_mode` tinyint(4) DEFAULT '2' COMMENT '销售佣金模式：2-百分比，3-固定金额',
  `sales_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '销售佣金比例（%）',
  `sales_commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '销售固定佣金金额',
  `enable_parent_profit` tinyint(1) DEFAULT '0' COMMENT '是否启用上级分润',
  `parent_profit_config` json DEFAULT NULL COMMENT '上级分润配置（JSON格式）',
  `enable_referrer_profit` tinyint(1) DEFAULT '0' COMMENT '是否启用介绍人分润',
  `referrer_profit_config` json DEFAULT NULL COMMENT '介绍人分润配置（JSON格式）',
  `min_order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最低订单金额',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金限制',
  `effective_type` tinyint(4) DEFAULT '1' COMMENT '生效类型：1-长期有效，2-限时有效',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `priority` int(11) DEFAULT '0' COMMENT '优先级',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `approve_status` tinyint(4) DEFAULT '0' COMMENT '审批状态：0-待审批，1-已通过，2-已拒绝',
  `approve_time` datetime DEFAULT NULL COMMENT '审批时间',
  `approver` varchar(64) DEFAULT NULL COMMENT '审批人',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_status` (`status`, `approve_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个人专属奖励配置表';
```

### 3.9 佣金账单记录表（yt_dist_agent_bill_record）
```sql
CREATE TABLE `yt_dist_agent_bill_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '账单ID',
  `bill_no` varchar(64) NOT NULL COMMENT '账单编号',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `agent_name` varchar(64) NOT NULL COMMENT '分销员名称',
  `agent_level_id` bigint(20) NOT NULL COMMENT '分销员等级ID（快照）',
  `bill_type` tinyint(4) NOT NULL COMMENT '账单类型：1-分销奖励，2-分润收益',
  `biz_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '业务类型：1-商品订单，2-优惠券核销',
  `biz_id` bigint(20) NOT NULL COMMENT '业务ID（订单ID或优惠券ID）',
  `biz_no` varchar(64) NOT NULL COMMENT '业务编号（订单号或券码）',
  `order_id` bigint(20) DEFAULT NULL COMMENT '订单ID',
  `order_amount` decimal(10,2) DEFAULT NULL COMMENT '订单金额',
  `channel_order_id` varchar(64) DEFAULT NULL COMMENT '渠道订单号',
  `coupon_id` bigint(20) DEFAULT NULL COMMENT '券码ID',
  `coupon_code` varchar(64) DEFAULT NULL COMMENT '券码编号',
  `spu_id` bigint(20) DEFAULT NULL COMMENT 'SPU ID',
  `spu_name` varchar(128) DEFAULT NULL COMMENT 'SPU名称',
  `sku_id` bigint(20) DEFAULT NULL COMMENT 'SKU ID',
  `sku_name` varchar(128) DEFAULT NULL COMMENT 'SKU名称',
  `source_agent_id` bigint(20) DEFAULT NULL COMMENT '来源分销员ID（产生业绩的分销员）',
  `source_agent_name` varchar(64) DEFAULT NULL COMMENT '来源分销员名称',
  `trace_level` int(11) DEFAULT '0' COMMENT '追溯层级：0-直接销售，1-一级，2-二级，3-三级',
  `amount` decimal(10,2) NOT NULL COMMENT '账单金额（正数为收入，负数为退款）',
  `base_amount` decimal(10,2) NOT NULL COMMENT '计算基数金额',
  `rate` decimal(5,2) DEFAULT NULL COMMENT '佣金比例（%）',
  `scheme_id` bigint(20) NOT NULL COMMENT '使用的方案ID',
  `config_id` bigint(20) DEFAULT NULL COMMENT '使用的配置ID',
  `config_type` varchar(32) DEFAULT NULL COMMENT '配置类型：PERSONAL/TAG/LEVEL/PRODUCT/GLOBAL',
  `scheme_snapshot` json DEFAULT NULL COMMENT '方案快照（JSON格式）',
  `trigger_stage` tinyint(4) DEFAULT '1' COMMENT '触发阶段：1-订单支付后，2-券码核销后，3-订单完成后',
  `freeze_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '冻结状态：0-未冻结，1-冻结中',
  `freeze_days` int(11) DEFAULT '0' COMMENT '冻结天数',
  `freeze_end_time` datetime DEFAULT NULL COMMENT '冻结结束时间',
  `unfreeze_time` datetime DEFAULT NULL COMMENT '实际解冻时间',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待结算，1-已结算，2-已冻结，3-已取消',
  `settle_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '结算状态：0-未结算，1-部分结算，2-已结算',
  `settled_amount` decimal(10,2) DEFAULT '0.00' COMMENT '已结算金额',
  `frozen_reason` varchar(256) DEFAULT NULL COMMENT '冻结原因',
  `remark` varchar(512) DEFAULT NULL COMMENT '备注',
  `bill_time` datetime NOT NULL COMMENT '账单时间',
  `settle_time` datetime DEFAULT NULL COMMENT '结算时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bill_no` (`bill_no`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_source_agent_id` (`source_agent_id`),
  KEY `idx_biz_id_type` (`biz_id`, `biz_type`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_coupon_id` (`coupon_id`),
  KEY `idx_spu_id` (`spu_id`),
  KEY `idx_sku_id` (`sku_id`),
  KEY `idx_status` (`status`),
  KEY `idx_bill_time` (`bill_time`),
  KEY `idx_freeze_status` (`freeze_status`),
  KEY `idx_freeze_end_time` (`freeze_end_time`),
  KEY `idx_config_type` (`config_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='佣金账单记录表';
```

### 3.10 配置生效追踪表（yt_dist_config_trace）【v2.0新增】
```sql
CREATE TABLE `yt_dist_config_trace` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trace_id` varchar(64) NOT NULL COMMENT '追踪ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `config_type` varchar(32) NOT NULL COMMENT '配置类型：PERSONAL/TAG/LEVEL/PRODUCT/GLOBAL',
  `config_id` bigint(20) NOT NULL COMMENT '配置ID',
  `config_snapshot` json NOT NULL COMMENT '配置快照',
  `match_path` varchar(512) DEFAULT NULL COMMENT '匹配路径',
  `priority_score` int(11) DEFAULT '0' COMMENT '优先级得分',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  KEY `idx_order_id` (`order_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_trace_id` (`trace_id`),
  KEY `idx_config_type` (`config_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置生效追踪表';
```

### 3.11 配置冲突日志表（yt_dist_config_conflict_log）【v2.0新增】
```sql
CREATE TABLE `yt_dist_config_conflict_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trace_id` varchar(64) NOT NULL COMMENT '追踪ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `conflict_configs` json NOT NULL COMMENT '冲突的配置列表（JSON）',
  `selected_config` json NOT NULL COMMENT '最终选择的配置（JSON）',
  `select_reason` varchar(512) DEFAULT NULL COMMENT '选择原因',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_trace_id` (`trace_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置冲突日志表';
```

## 4. 核心功能设计

### 4.1 多维度配置归因机制【v2.0核心功能】

#### 4.1.1 配置优先级体系

建立清晰的配置优先级体系，解决多维度配置的冲突问题：

```
优先级从高到低：
1. 个人专属配置（特定agent_id）- 优先级权重：1000
2. 标签配置（按标签优先级）- 优先级权重：800-900
3. 等级配置 - 优先级权重：600-700
4. 商品维度配置（SKU > SPU > 类目）- 优先级权重：300-500
5. 全局默认配置 - 优先级权重：100
```

#### 4.1.2 智能归因算法

```java
/**
 * 分销配置归因服务
 */
@Service
public class DistributionAttributionService {

    /**
     * 获取最终生效的奖励配置
     * @param agentId 分销员ID
     * @param productId 商品ID（SPU/SKU）
     * @param orderAmount 订单金额
     * @return 最终生效的配置
     */
    public EffectiveRewardConfig getEffectiveConfig(Long agentId, Long productId, BigDecimal orderAmount) {
        // 1. 查询分销员信息
        DistAgentDO agent = agentMapper.selectById(agentId);

        // 2. 查询所有可能的配置
        List<RewardConfigCandidate> candidates = new ArrayList<>();

        // 2.1 个人专属配置
        candidates.addAll(findPersonalConfigs(agentId, productId));

        // 2.2 标签配置
        if (StringUtils.isNotBlank(agent.getAgentTags())) {
            candidates.addAll(findTagConfigs(agent.getAgentTags(), productId));
        }

        // 2.3 等级配置
        candidates.addAll(findLevelConfigs(agent.getLevelId(), productId));

        // 2.4 商品维度配置
        candidates.addAll(findProductConfigs(productId));

        // 2.5 全局配置
        candidates.addAll(findGlobalConfigs());

        // 3. 按优先级排序并选择最优配置
        return selectBestConfig(candidates, orderAmount);
    }

    /**
     * 选择最优配置
     */
    private EffectiveRewardConfig selectBestConfig(List<RewardConfigCandidate> candidates,
                                                   BigDecimal orderAmount) {
        // 按照配置类型优先级和内部优先级排序
        candidates.sort((a, b) -> {
            // 先比较配置类型优先级
            int typeCompare = a.getConfigType().getPriority() - b.getConfigType().getPriority();
            if (typeCompare != 0) return typeCompare;

            // 再比较内部优先级
            return b.getPriority() - a.getPriority();
        });

        // 找到第一个满足条件的配置
        for (RewardConfigCandidate candidate : candidates) {
            if (isConfigApplicable(candidate, orderAmount)) {
                return buildEffectiveConfig(candidate);
            }
        }

        // 返回默认配置
        return getDefaultConfig();
    }
}

/**
 * 配置类型枚举
 */
public enum ConfigType {
    PERSONAL(1, "个人专属"),
    TAG(2, "标签配置"),
    LEVEL(3, "等级配置"),
    PRODUCT_SKU(4, "SKU配置"),
    PRODUCT_SPU(5, "SPU配置"),
    PRODUCT_CATEGORY(6, "类目配置"),
    GLOBAL(7, "全局配置");

    private final int priority; // 数字越小优先级越高
    private final String desc;
}
```

### 4.2 奖励计算流程【v2.0优化】

#### 4.2.1 触发阶段说明

系统支持三种奖励触发阶段：

1. **订单支付后（trigger_stage = 1）**
   - 订单支付成功立即触发奖励计算
   - 适用于需要快速激励的场景
   - 奖励可能进入冻结状态，等待订单最终完成

2. **券码核销后（trigger_stage = 2）**
   - 每次券码核销时触发对应金额的奖励计算
   - 适用于服务类商品，按实际消费计算奖励
   - 支持部分核销，按核销比例计算奖励

3. **订单完成后（trigger_stage = 3）**
   - 订单完成的定义：
     - 订单下所有券码已全部核销，或
     - 部分券码已核销，剩余部分已退款
   - 适用于需要等待服务完成后再发放奖励的场景
   - 奖励金额按实际核销金额计算

#### 4.2.2 计算流程图

```mermaid
graph TD
    A[业务触发] --> B[获取直接分销员]
    B --> C{是否有分销员}
    C -->|无| D[结束]
    C -->|有| E[智能归因获取最优配置]
    E --> F[记录配置选择过程]
    F --> G[计算销售奖励]
    G --> H{配置是否包含分润}
    H -->|否| I[仅记录销售奖励]
    H -->|是| J[追溯上级/介绍人]
    J --> K[计算分润奖励]
    K --> L[生成所有账单记录]
    I --> L
    L --> M[更新余额]
    M --> N[清除相关缓存]
```

### 4.3 人员标签管理【v2.0新增】

#### 4.3.1 标签类型定义

1. **系统标签**：系统自动生成的标签
   - 新人标签：注册30天内
   - 活跃标签：近30天有销售业绩
   - 沉睡标签：超过90天无业绩

2. **业务标签**：基于业务特征的标签
   - VIP客户：累计销售额超过阈值
   - 核心代理：团队规模达到要求
   - 专业分销：特定类目专家

3. **营销标签**：用于营销活动的标签
   - KOL达人：具有影响力的分销员
   - 推广大使：参与特定推广活动
   - 合作伙伴：战略合作关系

#### 4.3.2 标签自动化管理

```java
/**
 * 标签自动化管理服务
 */
@Service
public class AgentTagAutoService {

    /**
     * 自动更新系统标签
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void autoUpdateSystemTags() {
        // 1. 更新新人标签
        updateNewbieTag();

        // 2. 更新活跃标签
        updateActiveTag();

        // 3. 更新沉睡标签
        updateInactiveTag();

        // 4. 更新VIP标签
        updateVipTag();
    }

    /**
     * 基于业绩自动打标签
     */
    @EventListener
    public void handlePerformanceChange(AgentPerformanceChangeEvent event) {
        Long agentId = event.getAgentId();

        // 检查是否达到VIP标准
        if (event.getTotalSales().compareTo(VIP_THRESHOLD) >= 0) {
            attachTag(agentId, "VIP_001", "达到VIP销售额标准");
        }

        // 检查团队规模
        if (event.getTeamCount() >= CORE_AGENT_TEAM_SIZE) {
            attachTag(agentId, "CORE_001", "团队规模达到核心代理标准");
        }
    }
}
```

### 4.4 实时缓存优化【v2.0新增】

#### 4.4.1 缓存策略

```java
/**
 * 分销配置缓存服务
 */
@Service
public class DistributionConfigCacheService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String CACHE_PREFIX = "dist:config:";
    private static final long CACHE_TTL = 3600; // 1小时

    /**
     * 获取分销员的有效配置（带缓存）
     */
    public EffectiveRewardConfig getEffectiveConfigWithCache(Long agentId, Long productId) {
        String cacheKey = buildCacheKey(agentId, productId);

        // 尝试从缓存获取
        EffectiveRewardConfig config = (EffectiveRewardConfig) redisTemplate.opsForValue().get(cacheKey);
        if (config != null) {
            return config;
        }

        // 缓存未命中，查询数据库
        config = distributionAttributionService.getEffectiveConfig(agentId, productId, null);

        // 写入缓存
        redisTemplate.opsForValue().set(cacheKey, config, CACHE_TTL, TimeUnit.SECONDS);

        return config;
    }

    /**
     * 配置变更事件处理
     */
    @EventListener
    public void handleConfigChange(ConfigChangeEvent event) {
        // 清除受影响的缓存
        switch (event.getConfigType()) {
            case PERSONAL:
                clearCache(event.getAgentId());
                break;
            case TAG:
                clearCacheByTag(event.getTagId());
                break;
            case LEVEL:
                clearCacheByLevel(event.getLevelId());
                break;
            case GLOBAL:
                clearAllCache();
                break;
        }
    }
}
```

## 5. 管理界面设计

### 5.1 人员标签管理界面

```
┌─────────────────────────────────────────────────────────────────────┐
│ 分销员标签管理                                              [新增标签] │
├─────────────────────────────────────────────────────────────────────┤
│ 搜索：[标签名称/编码] [标签类型▼] [状态▼] [查询] [重置]           │
├─────────────────────────────────────────────────────────────────────┤
│ 标签编码 | 标签名称 | 类型 | 使用人数 | 状态 | 创建时间 | 操作      │
├─────────────────────────────────────────────────────────────────────┤
│ KOL_001 | KOL达人 | 营销标签 | 156 | 启用 | 2024-01-15 | [编辑][配置奖励] │
│ VIP_001 | VIP客户 | 业务标签 | 89 | 启用 | 2024-01-10 | [编辑][配置奖励] │
│ PARTNER | 战略合作伙伴 | 系统标签 | 12 | 启用 | 2024-01-01 | [编辑][配置奖励] │
└─────────────────────────────────────────────────────────────────────┘
```

### 5.2 个人专属配置管理界面

```
┌─────────────────────────────────────────────────────────────────────┐
│ 个人专属配置 - 张三（DS001）                                [返回] │
├─────────────────────────────────────────────────────────────────────┤
│ 当前等级：金牌分销员 | 标签：KOL达人, VIP客户                      │
├─────────────────────────────────────────────────────────────────────┤
│ [新增配置]                                                          │
├─────────────────────────────────────────────────────────────────────┤
│ 配置名称 | 适用范围 | 销售佣金 | 上级分润 | 状态 | 有效期 | 操作    │
├─────────────────────────────────────────────────────────────────────┤
│ 红酒专属高佣 | SPU:红酒系列 | 25% | 5% | 生效中 | 长期 | [编辑][禁用] │
│ 新品推广奖励 | 类目:数码 | 20% | 3% | 待审批 | 30天 | [查看][撤回] │
└─────────────────────────────────────────────────────────────────────┘
```

### 5.3 配置归因监控界面

```
┌─────────────────────────────────────────────────────────────────────┐
│ 配置归因监控                                    [导出] [刷新]      │
├─────────────────────────────────────────────────────────────────────┤
│ 筛选：[分销员] [订单号] [配置类型▼] [时间范围] [查询]             │
├─────────────────────────────────────────────────────────────────────┤
│ 订单号 | 分销员 | 商品 | 生效配置 | 优先级 | 佣金 | 时间 | 操作    │
├─────────────────────────────────────────────────────────────────────┤
│ O001 | 张三 | 红酒A | 个人专属 | 1000 | ¥50 | 10:30 | [详情] │
│ O002 | 李四 | 数码B | 标签配置 | 850 | ¥30 | 10:25 | [详情] │
│ O003 | 王五 | 服装C | 等级配置 | 600 | ¥20 | 10:20 | [详情] │
└─────────────────────────────────────────────────────────────────────┘
```

## 6. 系统监控与追踪

### 6.1 配置生效监控

系统提供完整的配置生效追踪能力：

1. **实时监控**：每次配置选择都会记录详细的决策过程
2. **冲突检测**：自动识别并记录配置冲突情况
3. **性能监控**：监控配置查询性能，优化缓存策略
4. **审计追踪**：提供完整的配置变更和生效历史

### 6.2 数据统计分析

```sql
-- 配置使用统计视图
CREATE VIEW v_config_usage_stats AS
SELECT
    config_type,
    COUNT(*) as usage_count,
    AVG(amount) as avg_commission,
    SUM(amount) as total_commission,
    DATE(create_time) as stat_date
FROM yt_dist_agent_bill_record
WHERE create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY config_type, DATE(create_time);
```

## 7. 总结

通过 v2.0 版本的全面升级，分销系统将具备：

### 7.1 核心能力提升

1. **多维度配置能力**：
   - 按人员（个人专属配置）
   - 按标签（特定人群配置）
   - 按等级（等级差异化）
   - 按商品（商品策略）

2. **智能归因机制**：
   - 清晰的优先级体系
   - 自动冲突处理
   - 决策过程可追溯

3. **标签化管理**：
   - 灵活的标签体系
   - 自动化标签管理
   - 基于标签的精准营销

4. **性能优化**：
   - 多层缓存策略
   - 实时配置生效
   - 高并发支持

### 7.2 业务价值

1. **精细化运营**：支持针对不同人群制定差异化分销策略
2. **灵活性提升**：多维度配置满足复杂业务场景需求
3. **决策透明**：完整的归因机制确保配置选择可解释
4. **运营效率**：自动化标签管理减少人工维护成本

### 7.3 技术架构优势

1. **扩展性强**：模块化设计，易于扩展新的配置维度
2. **性能优异**：多层缓存和智能刷新策略
3. **可维护性高**：清晰的代码结构和完善的监控体系
4. **稳定可靠**：完善的异常处理和数据一致性保障

这样的设计既保证了系统的灵活性和扩展性，又确保了规则的确定性和可追溯性，为业务的快速发展提供了强有力的技术支撑。
```
```

### 3.6 等级奖励配置表（yt_dist_reward_level_config）
```sql
CREATE TABLE `yt_dist_reward_level_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `scheme_id` bigint(20) NOT NULL COMMENT '方案ID',
  `level_id` bigint(20) NOT NULL COMMENT '分销等级ID',
  `level_name` varchar(64) DEFAULT NULL COMMENT '等级名称（冗余）',
  `sales_commission_mode` tinyint(4) DEFAULT '2' COMMENT '销售佣金模式：1-固定金额，2-百分比',
  `sales_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '销售佣金比例（%）',
  `sales_commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '销售固定佣金金额',
  `parent_profit_config` json DEFAULT NULL COMMENT '上级分润配置（JSON格式，包含各级分润比例）',
  `referrer_profit_config` json DEFAULT NULL COMMENT '介绍人分润配置（JSON格式，包含各级分润比例）',
  `condition_type` tinyint(4) DEFAULT '1' COMMENT '条件类型：1-无条件，2-基于销售额，3-基于销售量',
  `condition_config` json DEFAULT NULL COMMENT '条件配置（JSON格式）',
  `min_order_amount` decimal(10,2) DEFAULT NULL COMMENT '最低订单金额（覆盖方案默认值）',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金（覆盖方案默认值）',
  `remark` varchar(256) DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_level` (`scheme_id`, `level_id`),
  KEY `idx_scheme_id` (`scheme_id`),
  KEY `idx_level_id` (`level_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='等级奖励配置表';
```

### 3.7 标签奖励配置表（yt_dist_reward_tag_config）【v2.0新增】
```sql
CREATE TABLE `yt_dist_reward_tag_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `scheme_id` bigint(20) NOT NULL COMMENT '奖励方案ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  `sales_commission_mode` tinyint(4) DEFAULT '1' COMMENT '销售佣金模式：1-不设置，2-百分比，3-固定金额',
  `sales_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '销售佣金比例（%）',
  `sales_commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '销售固定佣金金额',
  `parent_profit_config` json DEFAULT NULL COMMENT '上级分润配置（JSON格式）',
  `referrer_profit_config` json DEFAULT NULL COMMENT '介绍人分润配置（JSON格式）',
  `priority` int(11) DEFAULT '0' COMMENT '优先级（标签内部优先级）',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_tag` (`scheme_id`, `tag_id`),
  KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签奖励配置表';
```

### 3.8 个人专属奖励配置表（yt_dist_reward_personal_config）【v2.0新增】
```sql
CREATE TABLE `yt_dist_reward_personal_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `scheme_id` bigint(20) DEFAULT NULL COMMENT '关联的奖励方案ID（可选）',
  `config_name` varchar(128) NOT NULL COMMENT '配置名称',
  `config_desc` varchar(512) DEFAULT NULL COMMENT '配置描述',
  `apply_scope` tinyint(4) DEFAULT '1' COMMENT '适用范围：1-全部商品，2-指定类目，3-指定SPU，4-指定SKU',
  `scope_ids` json DEFAULT NULL COMMENT '适用范围ID列表（JSON数组）',
  `sales_commission_mode` tinyint(4) DEFAULT '2' COMMENT '销售佣金模式：2-百分比，3-固定金额',
  `sales_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '销售佣金比例（%）',
  `sales_commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '销售固定佣金金额',
  `enable_parent_profit` tinyint(1) DEFAULT '0' COMMENT '是否启用上级分润',
  `parent_profit_config` json DEFAULT NULL COMMENT '上级分润配置（JSON格式）',
  `enable_referrer_profit` tinyint(1) DEFAULT '0' COMMENT '是否启用介绍人分润',
  `referrer_profit_config` json DEFAULT NULL COMMENT '介绍人分润配置（JSON格式）',
  `min_order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最低订单金额',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金限制',
  `effective_type` tinyint(4) DEFAULT '1' COMMENT '生效类型：1-长期有效，2-限时有效',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `priority` int(11) DEFAULT '0' COMMENT '优先级',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `approve_status` tinyint(4) DEFAULT '0' COMMENT '审批状态：0-待审批，1-已通过，2-已拒绝',
  `approve_time` datetime DEFAULT NULL COMMENT '审批时间',
  `approver` varchar(64) DEFAULT NULL COMMENT '审批人',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_status` (`status`, `approve_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个人专属奖励配置表';
```
```
