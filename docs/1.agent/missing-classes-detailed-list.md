# 分销模块缺失类详细清单

## 一、服务接口类（3个）

### 1. DistCommissionService
**位置**: `com.yitong.octopus.module.distribution.service.commission`
**用途**: 佣金管理核心服务
**依赖方**: 
- `DistCommissionController`
- `AppDistCommissionController`

### 2. DistTagRuleEngine  
**位置**: `com.yitong.octopus.module.distribution.service.tag`
**用途**: 标签规则引擎
**依赖方**:
- `DistTagRuleEngineImpl`
- 标签自动化服务

### 3. DistAgentTagRuleService
**位置**: `com.yitong.octopus.module.distribution.service.tag`
**用途**: 代理商标签规则服务
**依赖方**:
- 标签管理相关功能

## 二、VO类 - 商品分销相关（21个）

### 请求VO（11个）
1. **DistProductConfigBatchUpdateStatusReqVO**
   - 用途：批量更新商品配置状态
   - 字段：productIds, status, remark

2. **DistProductEnableReqVO**
   - 用途：启用商品分销
   - 字段：productId, enableTime, reason

3. **DistProductDisableReqVO**
   - 用途：禁用商品分销
   - 字段：productId, disableTime, reason

4. **DistProductBatchUpdateSchemeReqVO**
   - 用途：批量更新分销方案
   - 字段：productIds, schemeId, effectiveTime

5. **DistProductPageReqVO**
   - 用途：商品分页查询
   - 字段：productName, status, categoryId, createTime[]

6. **DistProductBatchOperationReqVO**
   - 用途：批量操作请求
   - 字段：operationType, productIds, params

7. **DistProductQuickSetupReqVO**
   - 用途：快速设置分销
   - 字段：productId, commissionRate, rules

8. **DistProductImportReqVO**
   - 用途：导入商品配置
   - 字段：fileUrl, importType, override

9. **DistProductExportReqVO**
   - 用途：导出商品配置
   - 字段：productIds, exportFields, format

10. **DistProductSyncReqVO**
    - 用途：同步商品信息
    - 字段：syncType, lastSyncTime

11. **DistGoodsPageReqVO**
    - 用途：商品分页查询
    - 继承：PageParam

### 响应VO（10个）
1. **DistProductDistributionInfoVO**
   - 用途：商品分销信息
   - 字段：productId, productName, commissionRate, status

2. **DistProductRespVO**
   - 用途：商品响应信息
   - 字段：基础商品信息 + 分销配置信息

3. **DistProductDistributionPermissionVO**
   - 用途：分销权限信息
   - 字段：canDistribute, restrictionReason, allowedLevels

4. **DistProductDistributionRuleVO**
   - 用途：分销规则信息
   - 字段：ruleId, ruleName, condition, action

5. **DistProductDistributionLimitVO**
   - 用途：分销限制信息
   - 字段：minAmount, maxAmount, dailyLimit

6. **DistProductDistributionConfigVO**
   - 用途：分销配置信息
   - 字段：所有配置相关字段

7. **DistProductDistributionDataVO**
   - 用途：分销数据统计
   - 字段：salesCount, commissionTotal, agentCount

8. **DistProductDistributionRankingVO**
   - 用途：商品分销排行
   - 字段：rank, productInfo, performance

9. **DistProductDistributionStatisticsVO**
   - 用途：分销统计信息
   - 字段：各维度统计数据

10. **DistProductBatchOperationRespVO**
    - 用途：批量操作响应
    - 字段：successCount, failCount, details

## 三、VO类 - 统计分析相关（15个）

### 趋势分析VO（11个）
1. **AgentTrendAnalysisReqVO**
   - 字段：agentId, dateRange, dimension

2. **AgentTrendOverviewVO**
   - 字段：总览数据

3. **AgentPerformanceTrendVO**
   - 字段：业绩趋势数据

4. **AgentTeamTrendVO**
   - 字段：团队发展趋势

5. **AgentCommissionTrendVO**
   - 字段：佣金趋势

6. **AgentActivityTrendVO**
   - 字段：活跃度趋势

7. **AgentGrowthTrendVO**
   - 字段：增长趋势

8. **AgentComparisonVO**
   - 字段：对比数据

9. **AgentForecastVO**
   - 字段：预测数据

10. **AgentAnomalyVO**
    - 字段：异常数据

11. **AgentTrendReportVO**
    - 字段：趋势报告

### 其他统计VO（4个）
1. **AgentTrendChartVO**
   - 字段：图表数据

2. **AgentTrendDetailVO**
   - 字段：趋势详情

3. **AgentTeamOverviewVO**
   - 字段：团队概览

4. **AgentRankingReqVO**
   - 字段：排名请求参数

## 四、DTO类 - 配置相关（5个）

1. **DistRewardConfigDTO**
   - 位置：`com.yitong.octopus.module.distribution.dto`
   - 字段：rewardType, conditions, amount, rules

2. **DistRewardConditionDTO**
   - 位置：`com.yitong.octopus.module.distribution.dto`
   - 字段：conditionType, operator, value

3. **DistConfigValidationResultDTO**
   - 位置：`com.yitong.octopus.module.distribution.dto`
   - 字段：isValid, errors, warnings

4. **DistConfigPriorityDTO**
   - 位置：`com.yitong.octopus.module.distribution.dto`
   - 字段：configType, priority, weight

5. **DistConfigConflictDTO**
   - 位置：`com.yitong.octopus.module.distribution.dto`
   - 字段：conflictType, config1, config2, resolution

## 五、Controller方法修正清单

### DistAgentTagController
- `createAgentTag` → `createTag`
- `updateAgentTag` → `updateTag`
- `deleteAgentTag` → `deleteTag`
- `getAgentTag` → `getTag`
- `getAgentTagPage` → `getTagPage`

## 六、包路径修正清单

### 错误引用修正
1. `com.yitong.octopus.module.distribution.service.tag.DistAgentTagService`
   → `com.yitong.octopus.module.distribution.service.agent.DistAgentTagService`

2. 所有 Controller 中的服务引用路径检查

## 七、优先级排序

### P0 - 立即修复（影响编译）
1. DistCommissionService 接口
2. DistRewardConfigDTO
3. Controller 包路径修正

### P1 - 紧急修复（影响功能）
1. 商品分销相关 VO
2. 统计分析相关 VO
3. 标签规则引擎

### P2 - 计划修复（优化相关）
1. 配置相关 DTO
2. 辅助工具类
3. 测试相关类

## 更新记录
- 2024-01-XX：完整梳理所有缺失类
- 总计：44个类需要创建或修复