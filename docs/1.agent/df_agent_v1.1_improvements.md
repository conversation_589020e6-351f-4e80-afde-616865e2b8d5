# 分销系统 v1.1 功能补充与完善

基于现有的分销系统设计，进行以下补充和完善：

## 1. 人员标签配置机制

### 1.1 需求背景
当前系统只支持按等级设置分销配置，但实际业务中经常需要对特定人群（如KOL、VIP客户、特约合作伙伴）设置专属的分销政策。

### 1.2 数据库设计补充

```sql
-- 分销员标签表
CREATE TABLE `yt_dist_agent_tag` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tag_code` varchar(32) NOT NULL COMMENT '标签编码（唯一）',
  `tag_name` varchar(64) NOT NULL COMMENT '标签名称',
  `tag_desc` varchar(256) DEFAULT NULL COMMENT '标签描述',
  `tag_type` tinyint(4) DEFAULT '1' COMMENT '标签类型：1-系统标签，2-业务标签，3-营销标签',
  `color` varchar(16) DEFAULT NULL COMMENT '标签颜色（用于前端展示）',
  `icon` varchar(64) DEFAULT NULL COMMENT '标签图标',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tag_code` (`tag_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员标签表';

-- 分销员标签关联表
CREATE TABLE `yt_dist_agent_tag_rel` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  `attach_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '标签附加时间',
  `expire_time` datetime DEFAULT NULL COMMENT '标签过期时间（NULL表示永久）',
  `attach_reason` varchar(256) DEFAULT NULL COMMENT '附加原因',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agent_tag` (`agent_id`, `tag_id`),
  KEY `idx_tag_id` (`tag_id`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员标签关联表';

-- 修改分销员信息表，添加标签字段
ALTER TABLE `yt_dist_agent` ADD COLUMN `agent_tags` varchar(512) DEFAULT NULL COMMENT '分销员标签（逗号分隔）' AFTER `referrer_id`;
ALTER TABLE `yt_dist_agent` ADD KEY `idx_agent_tags` (`agent_tags`);
```

### 1.3 奖励方案扩展

```sql
-- 标签奖励配置表（新增）
CREATE TABLE `yt_dist_reward_tag_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `scheme_id` bigint(20) NOT NULL COMMENT '奖励方案ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  `sales_commission_mode` tinyint(4) DEFAULT '1' COMMENT '销售佣金模式：1-不设置，2-百分比，3-固定金额',
  `sales_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '销售佣金比例（%）',
  `sales_commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '销售固定佣金金额',
  `parent_profit_config` json DEFAULT NULL COMMENT '上级分润配置（JSON格式）',
  `referrer_profit_config` json DEFAULT NULL COMMENT '介绍人分润配置（JSON格式）',
  `priority` int(11) DEFAULT '0' COMMENT '优先级（标签内部优先级）',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_tag` (`scheme_id`, `tag_id`),
  KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签奖励配置表';
```

## 2. 分销规则归因机制完善

### 2.1 配置优先级体系

建立清晰的配置优先级体系，解决多维度配置的冲突问题：

```
优先级从高到低：
1. 个人专属配置（特定agent_id）
2. 标签配置（按标签优先级）
3. 等级配置
4. 商品维度配置（SKU > SPU > 类目）
5. 全局默认配置
```

### 2.2 归因规则定义

```java
/**
 * 分销配置归因服务
 */
@Service
public class DistributionAttributionService {
    
    /**
     * 获取最终生效的奖励配置
     * @param agentId 分销员ID
     * @param productId 商品ID（SPU/SKU）
     * @param orderAmount 订单金额
     * @return 最终生效的配置
     */
    public EffectiveRewardConfig getEffectiveConfig(Long agentId, Long productId, BigDecimal orderAmount) {
        // 1. 查询分销员信息
        DistAgentDO agent = agentMapper.selectById(agentId);
        
        // 2. 查询所有可能的配置
        List<RewardConfigCandidate> candidates = new ArrayList<>();
        
        // 2.1 个人专属配置
        candidates.addAll(findPersonalConfigs(agentId, productId));
        
        // 2.2 标签配置
        if (StringUtils.isNotBlank(agent.getAgentTags())) {
            candidates.addAll(findTagConfigs(agent.getAgentTags(), productId));
        }
        
        // 2.3 等级配置
        candidates.addAll(findLevelConfigs(agent.getAgentLevelId(), productId));
        
        // 2.4 商品维度配置
        candidates.addAll(findProductConfigs(productId));
        
        // 2.5 全局配置
        candidates.addAll(findGlobalConfigs());
        
        // 3. 按优先级排序并选择最优配置
        return selectBestConfig(candidates, orderAmount);
    }
    
    /**
     * 选择最优配置
     */
    private EffectiveRewardConfig selectBestConfig(List<RewardConfigCandidate> candidates, 
                                                   BigDecimal orderAmount) {
        // 按照配置类型优先级和内部优先级排序
        candidates.sort((a, b) -> {
            // 先比较配置类型优先级
            int typeCompare = a.getConfigType().getPriority() - b.getConfigType().getPriority();
            if (typeCompare != 0) return typeCompare;
            
            // 再比较内部优先级
            return b.getPriority() - a.getPriority();
        });
        
        // 找到第一个满足条件的配置
        for (RewardConfigCandidate candidate : candidates) {
            if (isConfigApplicable(candidate, orderAmount)) {
                return buildEffectiveConfig(candidate);
            }
        }
        
        // 返回默认配置
        return getDefaultConfig();
    }
}

/**
 * 配置类型枚举
 */
public enum ConfigType {
    PERSONAL(1, "个人专属"),
    TAG(2, "标签配置"),
    LEVEL(3, "等级配置"),
    PRODUCT_SKU(4, "SKU配置"),
    PRODUCT_SPU(5, "SPU配置"),
    PRODUCT_CATEGORY(6, "类目配置"),
    GLOBAL(7, "全局配置");
    
    private final int priority; // 数字越小优先级越高
    private final String desc;
}
```

### 2.3 配置冲突处理机制

```sql
-- 配置冲突日志表
CREATE TABLE `yt_dist_config_conflict_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trace_id` varchar(64) NOT NULL COMMENT '追踪ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `conflict_configs` json NOT NULL COMMENT '冲突的配置列表（JSON）',
  `selected_config` json NOT NULL COMMENT '最终选择的配置（JSON）',
  `select_reason` varchar(512) DEFAULT NULL COMMENT '选择原因',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_trace_id` (`trace_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置冲突日志表';
```

## 3. 个人专属配置机制

### 3.1 数据库设计

```sql
-- 个人专属奖励配置表
CREATE TABLE `yt_dist_reward_personal_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `scheme_id` bigint(20) DEFAULT NULL COMMENT '关联的奖励方案ID（可选）',
  `config_name` varchar(128) NOT NULL COMMENT '配置名称',
  `config_desc` varchar(512) DEFAULT NULL COMMENT '配置描述',
  `apply_scope` tinyint(4) DEFAULT '1' COMMENT '适用范围：1-全部商品，2-指定类目，3-指定SPU，4-指定SKU',
  `scope_ids` json DEFAULT NULL COMMENT '适用范围ID列表（JSON数组）',
  `sales_commission_mode` tinyint(4) DEFAULT '2' COMMENT '销售佣金模式：2-百分比，3-固定金额',
  `sales_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '销售佣金比例（%）',
  `sales_commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '销售固定佣金金额',
  `enable_parent_profit` tinyint(1) DEFAULT '0' COMMENT '是否启用上级分润',
  `parent_profit_config` json DEFAULT NULL COMMENT '上级分润配置（JSON格式）',
  `enable_referrer_profit` tinyint(1) DEFAULT '0' COMMENT '是否启用介绍人分润',
  `referrer_profit_config` json DEFAULT NULL COMMENT '介绍人分润配置（JSON格式）',
  `min_order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最低订单金额',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金限制',
  `effective_type` tinyint(4) DEFAULT '1' COMMENT '生效类型：1-长期有效，2-限时有效',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `priority` int(11) DEFAULT '0' COMMENT '优先级',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `approve_status` tinyint(4) DEFAULT '0' COMMENT '审批状态：0-待审批，1-已通过，2-已拒绝',
  `approve_time` datetime DEFAULT NULL COMMENT '审批时间',
  `approver` varchar(64) DEFAULT NULL COMMENT '审批人',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_status` (`status`, `approve_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个人专属奖励配置表';
```

## 4. 配置生效实时性优化

### 4.1 缓存机制

```java
/**
 * 分销配置缓存服务
 */
@Service
public class DistributionConfigCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String CACHE_PREFIX = "dist:config:";
    private static final long CACHE_TTL = 3600; // 1小时
    
    /**
     * 获取分销员的有效配置（带缓存）
     */
    public EffectiveRewardConfig getEffectiveConfigWithCache(Long agentId, Long productId) {
        String cacheKey = buildCacheKey(agentId, productId);
        
        // 尝试从缓存获取
        EffectiveRewardConfig config = (EffectiveRewardConfig) redisTemplate.opsForValue().get(cacheKey);
        if (config != null) {
            return config;
        }
        
        // 缓存未命中，查询数据库
        config = distributionAttributionService.getEffectiveConfig(agentId, productId, null);
        
        // 写入缓存
        redisTemplate.opsForValue().set(cacheKey, config, CACHE_TTL, TimeUnit.SECONDS);
        
        return config;
    }
    
    /**
     * 清除相关缓存
     */
    public void clearCache(Long agentId) {
        String pattern = CACHE_PREFIX + agentId + ":*";
        Set<String> keys = redisTemplate.keys(pattern);
        if (CollectionUtils.isNotEmpty(keys)) {
            redisTemplate.delete(keys);
        }
    }
    
    /**
     * 配置变更事件处理
     */
    @EventListener
    public void handleConfigChange(ConfigChangeEvent event) {
        // 清除受影响的缓存
        switch (event.getConfigType()) {
            case PERSONAL:
                clearCache(event.getAgentId());
                break;
            case TAG:
                clearCacheByTag(event.getTagId());
                break;
            case LEVEL:
                clearCacheByLevel(event.getLevelId());
                break;
            case GLOBAL:
                clearAllCache();
                break;
        }
    }
}
```

## 5. 管理界面补充

### 5.1 人员标签管理

```
┌─────────────────────────────────────────────────────────────────────┐
│ 分销员标签管理                                              [新增标签] │
├─────────────────────────────────────────────────────────────────────┤
│ 搜索：[标签名称/编码] [标签类型▼] [状态▼] [查询] [重置]           │
├─────────────────────────────────────────────────────────────────────┤
│ 标签编码 | 标签名称 | 类型 | 使用人数 | 状态 | 创建时间 | 操作      │
├─────────────────────────────────────────────────────────────────────┤
│ KOL_001 | KOL达人 | 营销标签 | 156 | 启用 | 2024-01-15 | [编辑][配置奖励] │
│ VIP_001 | VIP客户 | 业务标签 | 89 | 启用 | 2024-01-10 | [编辑][配置奖励] │
│ PARTNER | 战略合作伙伴 | 系统标签 | 12 | 启用 | 2024-01-01 | [编辑][配置奖励] │
└─────────────────────────────────────────────────────────────────────┘
```

### 5.2 个人专属配置管理

```
┌─────────────────────────────────────────────────────────────────────┐
│ 个人专属配置 - 张三（DS001）                                [返回] │
├─────────────────────────────────────────────────────────────────────┤
│ 当前等级：金牌分销员 | 标签：KOL达人, VIP客户                      │
├─────────────────────────────────────────────────────────────────────┤
│ [新增配置]                                                          │
├─────────────────────────────────────────────────────────────────────┤
│ 配置名称 | 适用范围 | 销售佣金 | 上级分润 | 状态 | 有效期 | 操作    │
├─────────────────────────────────────────────────────────────────────┤
│ 红酒专属高佣 | SPU:红酒系列 | 25% | 5% | 生效中 | 长期 | [编辑][禁用] │
│ 新品推广奖励 | 类目:数码 | 20% | 3% | 待审批 | 30天 | [查看][撤回] │
└─────────────────────────────────────────────────────────────────────┘
```

## 6. 系统监控与追踪

### 6.1 配置生效监控

```sql
-- 配置生效追踪表
CREATE TABLE `yt_dist_config_trace` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trace_id` varchar(64) NOT NULL COMMENT '追踪ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `config_type` varchar(32) NOT NULL COMMENT '配置类型',
  `config_id` bigint(20) NOT NULL COMMENT '配置ID',
  `config_snapshot` json NOT NULL COMMENT '配置快照',
  `match_path` varchar(512) DEFAULT NULL COMMENT '匹配路径',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  KEY `idx_order_id` (`order_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_trace_id` (`trace_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置生效追踪表';
```

## 7. 总结

通过以上补充和完善，分销系统将具备：

1. **多维度配置能力**：
   - 按人员（个人专属配置）
   - 按标签（特定人群配置）
   - 按等级（等级差异化）
   - 按商品（商品策略）

2. **明确的归因规则**：
   - 清晰的优先级体系
   - 冲突自动处理
   - 决策过程可追溯

3. **灵活的管理能力**：
   - 标签化管理
   - 个性化配置
   - 实时生效

4. **完善的监控体系**：
   - 配置生效追踪
   - 冲突记录
   - 性能优化

这样的设计既保证了系统的灵活性，又确保了规则的确定性，避免了歧义和冲突。