# 分销模块快速修复检查清单

## 快速诊断流程

### 1. 编译错误类型判断
```bash
# 执行编译查看错误类型
mvn clean compile -DskipTests
```

### 2. 常见错误快速修复

#### ❌ 错误：找不到符号 DistAgentService
**快速修复**：
```java
// 将
import com.yitong.octopus.module.distribution.service.agent.DistAgentService;
@Resource
private DistAgentService distAgentService;

// 改为
import com.yitong.octopus.module.distribution.service.agent.AgentCoreService;
@Resource
private AgentCoreService agentCoreService;
```

#### ❌ 错误：方法 bindParent 找不到
**快速修复**：
```java
// 将
distAgentService.bindParent(agentId, parentId);

// 改为
import com.yitong.octopus.module.distribution.service.agent.AgentRelationService;
@Resource
private AgentRelationService agentRelationService;
agentRelationService.bindParentAgent(agentId, parentId);
```

#### ❌ 错误：方法 updateAgentLevel 找不到
**快速修复**：
```java
// 将
distAgentService.updateAgentLevel(agentId, levelId);

// 改为
import com.yitong.octopus.module.distribution.service.agent.AgentLevelService;
@Resource
private AgentLevelService agentLevelService;
agentLevelService.updateAgentLevel(agentId, levelId);
```

## 服务映射速查表

| 原服务方法 | 新服务 | 新方法 | 备注 |
|-----------|--------|--------|------|
| `distAgentService.getAgent()` | `AgentCoreService` | `getAgent()` | 查询代理商 |
| `distAgentService.getAgentByMemberId()` | `AgentCoreService` | `getAgentByMemberId()` | 按会员ID查询 |
| `distAgentService.getAgentByCode()` | `AgentCoreService` | `getAgentByCode()` | 按编码查询 |
| `distAgentService.bindParent()` | `AgentRelationService` | `bindParentAgent()` | 绑定上级 |
| `distAgentService.updateAgentLevel()` | `AgentLevelService` | `updateAgentLevel()` | 更新等级 |
| `distAgentService.applyAgent()` | `AgentApplicationService` | `applyAgent()` | 申请成为代理商 |
| `distAgentService.auditAgent()` | `AgentAuditService` | `auditAgent()` | 审核代理商 |

## 包结构速查

### 正确的包结构
```
service/
├── agent/          # 代理商相关
│   ├── AgentCoreService         # 核心查询
│   ├── AgentRelationService     # 关系管理
│   ├── AgentLevelService        # 等级管理
│   ├── AgentApplicationService  # 申请管理
│   ├── AgentAuditService        # 审核管理
│   └── AgentStatisticsService   # 统计分析
├── commission/     # 佣金相关
│   └── DistWithdrawService      # 提现服务（正确版本）
└── attribution/    # 归属相关
    └── DistAttributionService   # 归属服务
```

### 需要删除的重复定义
- ❌ `service/withdraw/DistWithdrawService.java` （使用 commission 包的版本）
- ❌ `service/tag/DistAgentTagService.java` （使用 agent 包的版本）

## 批量修复脚本

### 1. 查找所有需要修复的文件
```bash
# 查找引用 DistAgentService 的文件
find . -name "*.java" -type f -exec grep -l "DistAgentService" {} \;

# 查找重复定义的接口
find . -name "DistWithdrawService.java" -type f
find . -name "DistAgentTagService.java" -type f
```

### 2. IDE 批量替换建议
在 IntelliJ IDEA 中：
1. `Ctrl+Shift+R` 打开全局替换
2. 勾选 "File mask": `*.java`
3. 勾选 "Regular expressions"
4. 执行以下替换：

```
查找：import.*DistAgentService;
替换：import com.yitong.octopus.module.distribution.service.agent.AgentCoreService;

查找：private DistAgentService (\w+);
替换：private AgentCoreService $1;
```

## 验证修复

### 1. 编译验证
```bash
mvn clean compile -DskipTests
```

### 2. 检查点
- [ ] 所有 DistAgentService 引用已替换
- [ ] 删除了重复的接口定义
- [ ] 接口和实现类在同一包中
- [ ] 编译无错误
- [ ] 没有使用 Object 类型参数

## 紧急联系

如果遇到无法解决的问题：
1. 查看完整文档：`service-refactoring-lessons-learned.md`
2. 检查 git 历史：`git log --oneline -- **/DistAgentService.java`
3. 回滚到稳定版本：`git checkout <stable-commit>`

---
最后更新：2024-01-XX