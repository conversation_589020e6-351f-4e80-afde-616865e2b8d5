# 分销代理系统设计方案 v3.0

## 1. 概述

### 1.1 版本说明
本文档是分销代理系统的第三版设计方案，整合了v1.0、v1.1、v2.0的所有功能，并基于实际需求进行了全面优化升级。

### 1.2 核心特性
1. **三级分销体系**：支持多级分销关系管理，最多追溯三级
2. **多维度奖励配置**：个人、标签、等级、商品等多维度灵活配置
3. **智能归因机制**：自动选择最优配置，避免规则冲突
4. **邀请海报系统**：支持生成和管理分销邀请海报
5. **实时数据统计**：分销业绩、团队管理等数据实时更新
6. **移动端友好**：完整的H5端分销功能

### 1.3 系统架构
```
┌─────────────────────────────────────────────────────────────────┐
│                           前端应用层                              │
├─────────────────────┬───────────────────┬───────────────────────┤
│     管理后台        │    商家端H5      │      用户端H5         │
│  - 分销配置管理     │  - 邀请海报管理   │  - 申请成为分销员     │
│  - 分销员管理       │  - 团队管理       │  - 分享商品赚佣      │
│  - 佣金结算管理     │  - 业绩统计       │  - 查看收益明细      │
│  - 数据统计分析     │  - 佣金提现       │  - 邀请下级          │
└─────────────────────┴───────────────────┴───────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                           服务层                                 │
├─────────────────────┬───────────────────┬───────────────────────┤
│   分销员服务        │   奖励计算服务     │    海报生成服务       │
│ - 申请审核          │ - 智能归因         │  - 海报模板管理      │
│ - 关系绑定          │ - 佣金计算         │  - 二维码生成        │
│ - 等级管理          │ - 分润追溯         │  - 海报合成          │
├─────────────────────┼───────────────────┼───────────────────────┤
│   配置管理服务      │   结算服务         │    统计分析服务       │
│ - 方案配置          │ - 账单生成         │  - 实时统计          │
│ - 规则管理          │ - 冻结解冻         │  - 报表生成          │
│ - 缓存更新          │ - 提现管理         │  - 趋势分析          │
└─────────────────────┴───────────────────┴───────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                           数据层                                 │
├─────────────────────────────────────────────────────────────────┤
│  MySQL数据库 │ Redis缓存 │ OSS存储 │ MQ消息队列 │ ES搜索引擎   │
└─────────────────────────────────────────────────────────────────┘
```

## 2. 业务需求分析

### 2.1 分销员体系
#### 2.1.1 分销员申请
- 用户通过H5端申请成为分销员
- 支持通过邀请码快速申请
- 需要填写基本信息并同意分销协议
- 管理员可设置自动审核或人工审核

#### 2.1.2 分销关系
- **上下级关系**：通过分销码建立，用于团队管理和分润计算
- **介绍人关系**：记录推荐人信息，可用于额外奖励
- **等级体系**：不同等级享有不同权益和佣金比例

#### 2.1.3 分销等级
- 支持自定义等级（如：普通、银牌、金牌、钻石）
- 通过level_grade数值判断等级高低
- 支持自动升降级机制
- 等级变更触发通知和权益调整

### 2.2 商品分销
#### 2.2.1 分销商品管理
- 商品可配置是否参与分销
- 支持设置商品分销优先级和标签
- 可配置预计佣金提示信息
- 支持批量设置分销属性

#### 2.2.2 分销商品展示
- 分销商品列表按优先级排序
- 显示预计可赚佣金
- 支持分类筛选和搜索
- 一键分享功能

### 2.3 佣金体系
#### 2.3.1 佣金类型
- **销售佣金**：直接销售商品获得的佣金
- **分润佣金**：下级销售时上级获得的分润

#### 2.3.2 佣金计算
- 支持百分比和固定金额两种模式
- 可设置最低订单金额门槛
- 支持单笔最高佣金限制
- 实时计算预估佣金

#### 2.3.3 佣金发放
- 支持多种触发时机（支付后、核销后、订单完成后）
- 可配置冻结期，避免恶意刷单
- 支持手动/自动结算
- 提供完整的账单明细

### 2.4 邀请海报
#### 2.4.1 海报管理
- 商家可创建多个邀请海报模板
- 支持配置不同等级的专属海报
- 海报包含分销员专属二维码
- 支持自定义海报文案和样式

#### 2.4.2 海报分享
- 分销员可获取专属邀请海报
- 支持保存到相册和直接分享
- 扫码自动绑定分销关系
- 追踪海报带来的新用户

## 3. 数据库设计

### 3.1 分销等级表（yt_dist_level）
```sql
CREATE TABLE `yt_dist_level` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '等级ID',
  `level_code` varchar(32) NOT NULL COMMENT '等级编码',
  `level_name` varchar(64) NOT NULL COMMENT '等级名称',
  `level_grade` int(11) NOT NULL COMMENT '等级级别，数值越大等级越高',
  `invite_code` varchar(32) DEFAULT NULL COMMENT '等级邀请码',
  `icon_url` varchar(256) DEFAULT NULL COMMENT '等级图标',
  `color` varchar(32) DEFAULT NULL COMMENT '等级颜色',
  `description` text COMMENT '等级说明',
  `benefits` text COMMENT '等级权益描述',
  `upgrade_conditions` json DEFAULT NULL COMMENT '升级条件（JSON格式）',
  `downgrade_conditions` json DEFAULT NULL COMMENT '降级条件（JSON格式）',
  `auto_upgrade` tinyint(1) DEFAULT '1' COMMENT '是否自动升级',
  `auto_downgrade` tinyint(1) DEFAULT '0' COMMENT '是否自动降级',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序序号',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_level_code` (`level_code`),
  UNIQUE KEY `uk_invite_code` (`invite_code`),
  KEY `idx_level_grade` (`level_grade`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销等级表';
```

### 3.2 分销员信息表（yt_dist_agent）
```sql
CREATE TABLE `yt_dist_agent` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分销员ID',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `agent_code` varchar(32) NOT NULL COMMENT '分销员编码（分销码）',
  `agent_name` varchar(64) NOT NULL COMMENT '分销员名称',
  `agent_mobile` varchar(16) DEFAULT NULL COMMENT '分销员手机号',
  `level_id` bigint(20) NOT NULL COMMENT '等级ID',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '直接上级分销员ID',
  `parent_code` varchar(32) DEFAULT NULL COMMENT '上级分销码',
  `referrer_id` bigint(20) DEFAULT NULL COMMENT '介绍人ID',
  `referrer_code` varchar(32) DEFAULT NULL COMMENT '介绍人分销码',
  `agent_tags` varchar(512) DEFAULT NULL COMMENT '分销员标签（逗号分隔）',
  `path` varchar(512) DEFAULT NULL COMMENT '分销路径，如：/1/2/3/',
  `depth` int(11) DEFAULT '1' COMMENT '层级深度，顶级为1',
  `apply_time` datetime DEFAULT NULL COMMENT '申请时间',
  `approve_time` datetime DEFAULT NULL COMMENT '审批通过时间',
  `apply_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '申请状态：0-待审核，1-审核通过，2-审核拒绝',
  `approve_remark` varchar(256) DEFAULT NULL COMMENT '审批备注',
  `bind_time` datetime DEFAULT NULL COMMENT '绑定上级时间',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `team_count` int(11) DEFAULT '0' COMMENT '团队人数（包含所有下级）',
  `direct_count` int(11) DEFAULT '0' COMMENT '直属下级人数',
  `month_team_count` int(11) DEFAULT '0' COMMENT '本月新增团队人数',
  `total_sales` decimal(15,2) DEFAULT '0.00' COMMENT '累计销售额',
  `month_sales` decimal(15,2) DEFAULT '0.00' COMMENT '本月销售额',
  `total_commission` decimal(15,2) DEFAULT '0.00' COMMENT '累计佣金',
  `available_commission` decimal(15,2) DEFAULT '0.00' COMMENT '可提现佣金',
  `frozen_commission` decimal(15,2) DEFAULT '0.00' COMMENT '冻结佣金',
  `withdrawn_commission` decimal(15,2) DEFAULT '0.00' COMMENT '已提现佣金',
  `level_update_time` datetime DEFAULT NULL COMMENT '等级更新时间',
  `last_sales_time` datetime DEFAULT NULL COMMENT '最后销售时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_member_id` (`member_id`),
  UNIQUE KEY `uk_agent_code` (`agent_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_referrer_id` (`referrer_id`),
  KEY `idx_level_id` (`level_id`),
  KEY `idx_status` (`status`),
  KEY `idx_apply_status` (`apply_status`),
  KEY `idx_agent_tags` (`agent_tags`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员信息表';
```

### 3.3 分销员标签表（yt_dist_agent_tag）
```sql
CREATE TABLE `yt_dist_agent_tag` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tag_code` varchar(32) NOT NULL COMMENT '标签编码（唯一）',
  `tag_name` varchar(64) NOT NULL COMMENT '标签名称',
  `tag_desc` varchar(256) DEFAULT NULL COMMENT '标签描述',
  `tag_type` tinyint(4) DEFAULT '1' COMMENT '标签类型：1-系统标签，2-业务标签，3-营销标签',
  `color` varchar(16) DEFAULT NULL COMMENT '标签颜色',
  `icon` varchar(64) DEFAULT NULL COMMENT '标签图标',
  `auto_attach` tinyint(1) DEFAULT '0' COMMENT '是否自动附加',
  `attach_rule` json DEFAULT NULL COMMENT '自动附加规则（JSON）',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tag_code` (`tag_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员标签表';
```

### 3.4 分销员标签关联表（yt_dist_agent_tag_rel）
```sql
CREATE TABLE `yt_dist_agent_tag_rel` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  `attach_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '标签附加时间',
  `expire_time` datetime DEFAULT NULL COMMENT '标签过期时间',
  `attach_source` tinyint(4) DEFAULT '1' COMMENT '附加来源：1-手动，2-自动',
  `attach_reason` varchar(256) DEFAULT NULL COMMENT '附加原因',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agent_tag` (`agent_id`, `tag_id`),
  KEY `idx_tag_id` (`tag_id`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员标签关联表';
```

### 3.5 邀请海报表（yt_dist_invite_poster）
```sql
CREATE TABLE `yt_dist_invite_poster` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '海报ID',
  `poster_code` varchar(32) NOT NULL COMMENT '海报编码',
  `poster_name` varchar(128) NOT NULL COMMENT '海报名称',
  `poster_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '海报类型：1-通用海报，2-等级专属，3-活动海报',
  `target_level_id` bigint(20) DEFAULT NULL COMMENT '目标等级ID（等级专属时使用）',
  `template_url` varchar(512) NOT NULL COMMENT '海报模板URL',
  `qrcode_position` json NOT NULL COMMENT '二维码位置配置（JSON）',
  `text_config` json DEFAULT NULL COMMENT '文字配置（JSON，包含位置、字体、颜色等）',
  `share_title` varchar(256) DEFAULT NULL COMMENT '分享标题',
  `share_desc` varchar(512) DEFAULT NULL COMMENT '分享描述',
  `effective_days` int(11) DEFAULT '0' COMMENT '有效天数，0表示永久有效',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_poster_code` (`poster_code`),
  KEY `idx_poster_type` (`poster_type`),
  KEY `idx_target_level_id` (`target_level_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邀请海报表';
```

### 3.6 分销员海报记录表（yt_dist_agent_poster）
```sql
CREATE TABLE `yt_dist_agent_poster` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `poster_id` bigint(20) NOT NULL COMMENT '海报ID',
  `poster_url` varchar(512) NOT NULL COMMENT '生成的海报URL',
  `qrcode_url` varchar(512) NOT NULL COMMENT '二维码URL',
  `qrcode_content` varchar(512) NOT NULL COMMENT '二维码内容',
  `share_count` int(11) DEFAULT '0' COMMENT '分享次数',
  `scan_count` int(11) DEFAULT '0' COMMENT '扫码次数',
  `register_count` int(11) DEFAULT '0' COMMENT '注册人数',
  `bind_count` int(11) DEFAULT '0' COMMENT '绑定人数',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-已过期，1-有效',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_poster_id` (`poster_id`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员海报记录表';
```

### 3.7 商品分销配置表（yt_dist_product_config）
```sql
CREATE TABLE `yt_dist_product_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `spu_id` bigint(20) NOT NULL COMMENT 'SPU ID',
  `enable_distribution` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用分销',
  `dist_priority` int(11) DEFAULT '0' COMMENT '分销优先级',
  `dist_tags` varchar(256) DEFAULT NULL COMMENT '分销标签（逗号分隔）',
  `recommend_reason` varchar(256) DEFAULT NULL COMMENT '推荐理由',
  `commission_hint` varchar(64) DEFAULT NULL COMMENT '佣金提示',
  `max_commission_rate` decimal(5,2) DEFAULT NULL COMMENT '最高佣金比例（用于展示）',
  `restrict_level_ids` varchar(256) DEFAULT NULL COMMENT '限制等级ID（逗号分隔，空表示不限制）',
  `restrict_tag_ids` varchar(256) DEFAULT NULL COMMENT '限制标签ID（逗号分隔，空表示不限制）',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_spu_id` (`spu_id`),
  KEY `idx_enable_distribution` (`enable_distribution`),
  KEY `idx_dist_priority` (`dist_priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分销配置表';
```

### 3.8 奖励方案主表（yt_dist_reward_scheme）
```sql
CREATE TABLE `yt_dist_reward_scheme` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '方案ID',
  `scheme_code` varchar(32) NOT NULL COMMENT '方案编码',
  `scheme_name` varchar(64) NOT NULL COMMENT '方案名称',
  `scheme_desc` varchar(256) DEFAULT NULL COMMENT '方案描述',
  `apply_scope` tinyint(4) NOT NULL DEFAULT '1' COMMENT '适用范围：1-全局，2-指定商品类目，3-指定商品，4-指定SKU',
  `scope_ids` json DEFAULT NULL COMMENT '适用范围ID列表（JSON数组）',
  `enable_sales_reward` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用销售奖励',
  `enable_profit_sharing` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用分润奖励',
  `profit_target` tinyint(4) DEFAULT '1' COMMENT '分润对象：1-上级，2-介绍人，3-两者都有',
  `max_trace_level` int(11) DEFAULT '3' COMMENT '最大追溯层级',
  `level_config_mode` tinyint(4) NOT NULL DEFAULT '1' COMMENT '等级配置模式：1-统一配置，2-分等级配置',
  `default_sales_mode` tinyint(4) DEFAULT '2' COMMENT '默认销售佣金模式：1-固定金额，2-百分比',
  `default_sales_rate` decimal(5,2) DEFAULT '0.00' COMMENT '默认销售佣金比例（%）',
  `default_sales_amount` decimal(10,2) DEFAULT '0.00' COMMENT '默认销售固定佣金',
  `default_profit_mode` tinyint(4) DEFAULT '2' COMMENT '默认分润佣金模式：1-固定金额，2-百分比',
  `default_profit_config` json DEFAULT NULL COMMENT '默认分润配置（JSON，各级分润比例）',
  `min_order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最低订单金额',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金限制',
  `trigger_stage` tinyint(4) DEFAULT '1' COMMENT '触发阶段：1-订单支付后，2-券码核销后，3-订单完成后',
  `freeze_days` int(11) DEFAULT '0' COMMENT '佣金冻结天数',
  `effective_type` tinyint(4) DEFAULT '1' COMMENT '生效类型：1-长期有效，2-限时有效',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `priority` int(11) DEFAULT '0' COMMENT '优先级，数值越大优先级越高',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_code` (`scheme_code`),
  KEY `idx_apply_scope` (`apply_scope`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='奖励方案主表';
```

### 3.9 等级奖励配置表（yt_dist_reward_level_config）
```sql
CREATE TABLE `yt_dist_reward_level_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `scheme_id` bigint(20) NOT NULL COMMENT '方案ID',
  `level_id` bigint(20) NOT NULL COMMENT '分销等级ID',
  `level_name` varchar(64) DEFAULT NULL COMMENT '等级名称（冗余）',
  `sales_commission_mode` tinyint(4) DEFAULT '2' COMMENT '销售佣金模式：1-固定金额，2-百分比',
  `sales_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '销售佣金比例（%）',
  `sales_commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '销售固定佣金金额',
  `parent_profit_config` json DEFAULT NULL COMMENT '上级分润配置（JSON格式）',
  `referrer_profit_config` json DEFAULT NULL COMMENT '介绍人分润配置（JSON格式）',
  `min_order_amount` decimal(10,2) DEFAULT NULL COMMENT '最低订单金额',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_level` (`scheme_id`, `level_id`),
  KEY `idx_level_id` (`level_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='等级奖励配置表';
```

### 3.10 标签奖励配置表（yt_dist_reward_tag_config）
```sql
CREATE TABLE `yt_dist_reward_tag_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `scheme_id` bigint(20) NOT NULL COMMENT '奖励方案ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  `sales_commission_mode` tinyint(4) DEFAULT '1' COMMENT '销售佣金模式：1-不设置，2-百分比，3-固定金额',
  `sales_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '销售佣金比例（%）',
  `sales_commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '销售固定佣金金额',
  `parent_profit_config` json DEFAULT NULL COMMENT '上级分润配置',
  `referrer_profit_config` json DEFAULT NULL COMMENT '介绍人分润配置',
  `priority` int(11) DEFAULT '0' COMMENT '优先级（标签内部优先级）',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_tag` (`scheme_id`, `tag_id`),
  KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签奖励配置表';
```

### 3.11 个人专属奖励配置表（yt_dist_reward_personal_config）
```sql
CREATE TABLE `yt_dist_reward_personal_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `config_name` varchar(128) NOT NULL COMMENT '配置名称',
  `config_desc` varchar(512) DEFAULT NULL COMMENT '配置描述',
  `apply_scope` tinyint(4) DEFAULT '1' COMMENT '适用范围：1-全部商品，2-指定类目，3-指定SPU，4-指定SKU',
  `scope_ids` json DEFAULT NULL COMMENT '适用范围ID列表',
  `sales_commission_mode` tinyint(4) DEFAULT '2' COMMENT '销售佣金模式',
  `sales_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '销售佣金比例（%）',
  `sales_commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '销售固定佣金金额',
  `enable_parent_profit` tinyint(1) DEFAULT '0' COMMENT '是否启用上级分润',
  `parent_profit_config` json DEFAULT NULL COMMENT '上级分润配置',
  `enable_referrer_profit` tinyint(1) DEFAULT '0' COMMENT '是否启用介绍人分润',
  `referrer_profit_config` json DEFAULT NULL COMMENT '介绍人分润配置',
  `min_order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最低订单金额',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金限制',
  `effective_type` tinyint(4) DEFAULT '1' COMMENT '生效类型：1-长期有效，2-限时有效',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `priority` int(11) DEFAULT '0' COMMENT '优先级',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `approve_status` tinyint(4) DEFAULT '0' COMMENT '审批状态：0-待审批，1-已通过，2-已拒绝',
  `approve_time` datetime DEFAULT NULL COMMENT '审批时间',
  `approver` varchar(64) DEFAULT NULL COMMENT '审批人',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_status` (`status`, `approve_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个人专属奖励配置表';
```

### 3.12 佣金账单记录表（yt_dist_commission_bill）
```sql
CREATE TABLE `yt_dist_commission_bill` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '账单ID',
  `bill_no` varchar(64) NOT NULL COMMENT '账单编号',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `agent_name` varchar(64) NOT NULL COMMENT '分销员名称（快照）',
  `agent_level_id` bigint(20) NOT NULL COMMENT '分销员等级ID（快照）',
  `bill_type` tinyint(4) NOT NULL COMMENT '账单类型：1-销售佣金，2-分润佣金',
  `biz_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '业务类型：1-商品订单，2-优惠券核销',
  `biz_id` bigint(20) NOT NULL COMMENT '业务ID',
  `biz_no` varchar(64) NOT NULL COMMENT '业务编号',
  `order_id` bigint(20) DEFAULT NULL COMMENT '订单ID',
  `order_amount` decimal(10,2) DEFAULT NULL COMMENT '订单金额',
  `spu_id` bigint(20) DEFAULT NULL COMMENT 'SPU ID',
  `spu_name` varchar(128) DEFAULT NULL COMMENT 'SPU名称',
  `source_agent_id` bigint(20) DEFAULT NULL COMMENT '来源分销员ID',
  `source_agent_name` varchar(64) DEFAULT NULL COMMENT '来源分销员名称',
  `trace_level` int(11) DEFAULT '0' COMMENT '追溯层级：0-直接销售，1-一级，2-二级，3-三级',
  `amount` decimal(10,2) NOT NULL COMMENT '佣金金额',
  `base_amount` decimal(10,2) NOT NULL COMMENT '计算基数金额',
  `rate` decimal(5,2) DEFAULT NULL COMMENT '佣金比例（%）',
  `scheme_id` bigint(20) NOT NULL COMMENT '使用的方案ID',
  `config_type` varchar(32) DEFAULT NULL COMMENT '配置类型',
  `config_snapshot` json DEFAULT NULL COMMENT '配置快照',
  `freeze_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '冻结状态：0-未冻结，1-冻结中',
  `freeze_end_time` datetime DEFAULT NULL COMMENT '冻结结束时间',
  `unfreeze_time` datetime DEFAULT NULL COMMENT '实际解冻时间',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待结算，1-已结算，2-已取消',
  `settle_time` datetime DEFAULT NULL COMMENT '结算时间',
  `settle_batch_no` varchar(64) DEFAULT NULL COMMENT '结算批次号',
  `remark` varchar(512) DEFAULT NULL COMMENT '备注',
  `bill_time` datetime NOT NULL COMMENT '账单时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bill_no` (`bill_no`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_source_agent_id` (`source_agent_id`),
  KEY `idx_biz_id_type` (`biz_id`, `biz_type`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_status` (`status`),
  KEY `idx_freeze_status` (`freeze_status`),
  KEY `idx_bill_time` (`bill_time`),
  KEY `idx_settle_batch_no` (`settle_batch_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='佣金账单记录表';
```

### 3.13 佣金提现记录表（yt_dist_withdraw_record）
```sql
CREATE TABLE `yt_dist_withdraw_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '提现ID',
  `withdraw_no` varchar(64) NOT NULL COMMENT '提现单号',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `agent_name` varchar(64) NOT NULL COMMENT '分销员名称',
  `amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `fee` decimal(10,2) DEFAULT '0.00' COMMENT '手续费',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实际到账金额',
  `account_type` tinyint(4) NOT NULL COMMENT '账户类型：1-微信，2-支付宝，3-银行卡',
  `account_name` varchar(64) NOT NULL COMMENT '账户名称',
  `account_no` varchar(128) NOT NULL COMMENT '账户号',
  `bank_name` varchar(64) DEFAULT NULL COMMENT '银行名称',
  `apply_time` datetime NOT NULL COMMENT '申请时间',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_user` varchar(64) DEFAULT NULL COMMENT '审核人',
  `audit_remark` varchar(256) DEFAULT NULL COMMENT '审核备注',
  `payment_time` datetime DEFAULT NULL COMMENT '打款时间',
  `payment_no` varchar(128) DEFAULT NULL COMMENT '第三方支付单号',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待审核，1-审核通过，2-审核拒绝，3-打款中，4-打款成功，5-打款失败',
  `fail_reason` varchar(256) DEFAULT NULL COMMENT '失败原因',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_withdraw_no` (`withdraw_no`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_apply_time` (`apply_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='佣金提现记录表';
```

### 3.14 配置生效追踪表（yt_dist_config_trace）
```sql
CREATE TABLE `yt_dist_config_trace` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trace_id` varchar(64) NOT NULL COMMENT '追踪ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `config_type` varchar(32) NOT NULL COMMENT '配置类型：PERSONAL/TAG/LEVEL/PRODUCT/GLOBAL',
  `config_id` bigint(20) NOT NULL COMMENT '配置ID',
  `config_snapshot` json NOT NULL COMMENT '配置快照',
  `match_path` varchar(512) DEFAULT NULL COMMENT '匹配路径',
  `priority_score` int(11) DEFAULT '0' COMMENT '优先级得分',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  KEY `idx_order_id` (`order_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_trace_id` (`trace_id`),
  KEY `idx_config_type` (`config_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置生效追踪表';
```

## 4. 核心功能设计

### 4.1 智能归因机制

#### 4.1.1 配置优先级体系
```
优先级从高到低：
1. 个人专属配置（权重：1000）
2. 标签配置（权重：800-900）
3. 等级配置（权重：600-700）  
4. 商品维度配置（权重：300-500）
   - SKU配置（权重：500）
   - SPU配置（权重：400）
   - 类目配置（权重：300）
5. 全局默认配置（权重：100）
```

#### 4.1.2 归因算法实现
```java
@Service
public class DistributionAttributionService {
    
    /**
     * 获取最终生效的奖励配置
     */
    public EffectiveRewardConfig getEffectiveConfig(Long agentId, Long productId, BigDecimal orderAmount) {
        // 1. 查询分销员信息（包含等级、标签等）
        DistAgentDO agent = agentMapper.selectById(agentId);
        if (agent == null || agent.getStatus() != 1) {
            throw new BusinessException("分销员不存在或已禁用");
        }
        
        // 2. 查询所有可能的配置
        List<RewardConfigCandidate> candidates = new ArrayList<>();
        
        // 2.1 个人专属配置
        candidates.addAll(findPersonalConfigs(agentId, productId));
        
        // 2.2 标签配置
        if (StringUtils.isNotBlank(agent.getAgentTags())) {
            candidates.addAll(findTagConfigs(agent.getAgentTags(), productId));
        }
        
        // 2.3 等级配置
        candidates.addAll(findLevelConfigs(agent.getLevelId(), productId));
        
        // 2.4 商品维度配置
        candidates.addAll(findProductConfigs(productId));
        
        // 2.5 全局配置
        candidates.addAll(findGlobalConfigs());
        
        // 3. 记录所有候选配置（用于冲突分析）
        if (candidates.size() > 1) {
            logConfigConflict(agentId, productId, candidates);
        }
        
        // 4. 按优先级排序并选择最优配置
        EffectiveRewardConfig config = selectBestConfig(candidates, orderAmount);
        
        // 5. 记录配置选择结果
        traceConfigSelection(agentId, productId, config);
        
        return config;
    }
}
```

### 4.2 分销关系管理

#### 4.2.1 申请流程
```java
@Service
public class DistAgentApplyService {
    
    /**
     * 申请成为分销员
     */
    @Transactional
    public Long applyAgent(AgentApplyRequest request) {
        // 1. 检查是否已是分销员
        DistAgentDO existAgent = agentMapper.selectByMemberId(request.getMemberId());
        if (existAgent != null) {
            throw new BusinessException("您已经是分销员");
        }
        
        // 2. 生成分销码
        String agentCode = generateAgentCode();
        
        // 3. 解析邀请信息
        InviteInfo inviteInfo = null;
        if (StringUtils.isNotBlank(request.getInviteCode())) {
            inviteInfo = parseInviteCode(request.getInviteCode());
        }
        
        // 4. 创建分销员记录
        DistAgentDO agent = new DistAgentDO();
        agent.setMemberId(request.getMemberId());
        agent.setAgentCode(agentCode);
        agent.setAgentName(request.getAgentName());
        agent.setAgentMobile(request.getAgentMobile());
        agent.setLevelId(getDefaultLevelId());
        
        // 5. 设置关系
        if (inviteInfo != null) {
            // 设置上级
            if (inviteInfo.getAgentId() != null) {
                DistAgentDO parentAgent = agentMapper.selectById(inviteInfo.getAgentId());
                agent.setParentId(parentAgent.getId());
                agent.setParentCode(parentAgent.getAgentCode());
                agent.setPath(parentAgent.getPath() + parentAgent.getId() + "/");
                agent.setDepth(parentAgent.getDepth() + 1);
            }
            // 设置介绍人
            agent.setReferrerId(inviteInfo.getReferrerId());
            agent.setReferrerCode(inviteInfo.getReferrerCode());
        }
        
        // 6. 设置审核状态
        if (isAutoApprove()) {
            agent.setApplyStatus(1);
            agent.setApproveTime(new Date());
        } else {
            agent.setApplyStatus(0);
        }
        
        agent.setApplyTime(new Date());
        agentMapper.insert(agent);
        
        // 7. 更新上级团队人数
        if (agent.getParentId() != null) {
            updateTeamCount(agent.getParentId());
        }
        
        // 8. 发送通知
        sendApplyNotification(agent);
        
        return agent.getId();
    }
}
```

#### 4.2.2 关系绑定
```java
@Service  
public class DistAgentRelationService {
    
    /**
     * 绑定分销关系
     */
    @Transactional
    public void bindRelation(Long agentId, String inviteCode) {
        // 1. 获取当前分销员
        DistAgentDO agent = agentMapper.selectById(agentId);
        if (agent == null) {
            throw new BusinessException("分销员不存在");
        }
        
        // 2. 解析邀请码
        DistAgentDO inviter = agentMapper.selectByAgentCode(inviteCode);
        if (inviter == null) {
            throw new BusinessException("邀请码无效");
        }
        
        // 3. 检查是否可以绑定
        if (agent.getParentId() != null && agent.getParentId() > 0) {
            throw new BusinessException("已有上级，不能重复绑定");
        }
        
        // 4. 检查循环绑定
        if (isCircularBinding(agent.getId(), inviter.getId())) {
            throw new BusinessException("不能绑定下级为上级");
        }
        
        // 5. 更新关系
        agent.setParentId(inviter.getId());
        agent.setParentCode(inviter.getAgentCode());
        agent.setPath(inviter.getPath() + inviter.getId() + "/");
        agent.setDepth(inviter.getDepth() + 1);
        agent.setBindTime(new Date());
        agentMapper.updateById(agent);
        
        // 6. 更新团队人数
        updateTeamCount(inviter.getId());
        
        // 7. 发送通知
        sendBindNotification(agent, inviter);
    }
}
```

### 4.3 邀请海报功能

#### 4.3.1 海报生成
```java
@Service
public class InvitePosterService {
    
    /**
     * 生成分销员专属海报
     */
    public AgentPosterVO generatePoster(Long agentId, Long posterId) {
        // 1. 获取分销员信息
        DistAgentDO agent = agentMapper.selectById(agentId);
        
        // 2. 获取海报模板
        DistInvitePosterDO poster = posterMapper.selectById(posterId);
        
        // 3. 检查权限
        if (poster.getPosterType() == 2 && !poster.getTargetLevelId().equals(agent.getLevelId())) {
            throw new BusinessException("您无权使用该等级专属海报");
        }
        
        // 4. 生成邀请链接
        String inviteUrl = generateInviteUrl(agent.getAgentCode(), poster.getId());
        
        // 5. 生成二维码
        String qrcodeUrl = qrcodeService.generate(inviteUrl);
        
        // 6. 合成海报
        PosterConfig config = parsePosterConfig(poster);
        config.setQrcodeUrl(qrcodeUrl);
        config.setAgentName(agent.getAgentName());
        
        String posterUrl = imageService.composePoster(poster.getTemplateUrl(), config);
        
        // 7. 保存记录
        DistAgentPosterDO record = new DistAgentPosterDO();
        record.setAgentId(agentId);
        record.setPosterId(posterId);
        record.setPosterUrl(posterUrl);
        record.setQrcodeUrl(qrcodeUrl);
        record.setQrcodeContent(inviteUrl);
        
        if (poster.getEffectiveDays() > 0) {
            record.setExpireTime(DateUtils.addDays(new Date(), poster.getEffectiveDays()));
        }
        
        agentPosterMapper.insert(record);
        
        // 8. 返回结果
        return buildPosterVO(record, poster);
    }
}
```

### 4.4 佣金计算引擎

#### 4.4.1 销售佣金计算
```java
@Service
public class CommissionCalculateService {
    
    /**
     * 计算订单佣金
     */
    @Transactional
    public List<CommissionBillDO> calculateOrderCommission(OrderCommissionContext context) {
        List<CommissionBillDO> bills = new ArrayList<>();
        
        // 1. 获取直接分销员
        DistAgentDO directAgent = findDirectAgent(context.getBuyerId());
        if (directAgent == null) {
            return bills;
        }
        
        // 2. 获取有效配置
        EffectiveRewardConfig config = attributionService.getEffectiveConfig(
            directAgent.getId(), 
            context.getProductId(), 
            context.getOrderAmount()
        );
        
        // 3. 计算销售佣金
        if (config.isEnableSalesReward()) {
            CommissionBillDO salesBill = calculateSalesCommission(directAgent, context, config);
            if (salesBill != null) {
                bills.add(salesBill);
            }
        }
        
        // 4. 计算分润佣金
        if (config.isEnableProfitSharing()) {
            List<CommissionBillDO> profitBills = calculateProfitCommission(directAgent, context, config);
            bills.addAll(profitBills);
        }
        
        // 5. 保存账单
        for (CommissionBillDO bill : bills) {
            billMapper.insert(bill);
        }
        
        // 6. 更新分销员余额
        updateAgentBalance(bills);
        
        // 7. 发送通知
        sendCommissionNotification(bills);
        
        return bills;
    }
    
    /**
     * 计算分润佣金
     */
    private List<CommissionBillDO> calculateProfitCommission(DistAgentDO sourceAgent, 
                                                             OrderCommissionContext context,
                                                             EffectiveRewardConfig config) {
        List<CommissionBillDO> bills = new ArrayList<>();
        
        // 1. 上级分润
        if (config.getProfitTarget() == 1 || config.getProfitTarget() == 3) {
            bills.addAll(calculateParentProfit(sourceAgent, context, config));
        }
        
        // 2. 介绍人分润
        if (config.getProfitTarget() == 2 || config.getProfitTarget() == 3) {
            bills.addAll(calculateReferrerProfit(sourceAgent, context, config));
        }
        
        return bills;
    }
}
```

### 4.5 数据统计分析

#### 4.5.1 实时统计服务
```java
@Service
public class DistStatisticsService {
    
    /**
     * 更新分销员统计数据
     */
    @Async
    public void updateAgentStatistics(Long agentId, StatisticsEvent event) {
        // 1. 获取当前统计数据
        DistAgentDO agent = agentMapper.selectById(agentId);
        
        // 2. 根据事件类型更新
        switch (event.getType()) {
            case ORDER_PAID:
                // 更新销售额
                agent.setTotalSales(agent.getTotalSales().add(event.getAmount()));
                agent.setMonthSales(agent.getMonthSales().add(event.getAmount()));
                agent.setLastSalesTime(new Date());
                break;
                
            case COMMISSION_GENERATED:
                // 更新佣金
                agent.setTotalCommission(agent.getTotalCommission().add(event.getAmount()));
                if (event.getFreezeStatus() == 0) {
                    agent.setAvailableCommission(agent.getAvailableCommission().add(event.getAmount()));
                } else {
                    agent.setFrozenCommission(agent.getFrozenCommission().add(event.getAmount()));
                }
                break;
                
            case MEMBER_JOINED:
                // 更新团队人数
                agent.setDirectCount(agent.getDirectCount() + 1);
                agent.setMonthTeamCount(agent.getMonthTeamCount() + 1);
                break;
        }
        
        // 3. 保存更新
        agentMapper.updateById(agent);
        
        // 4. 检查等级升级
        checkLevelUpgrade(agent);
    }
    
    /**
     * 生成统计报表
     */
    public AgentStatisticsVO generateStatistics(Long agentId, StatisticsQuery query) {
        AgentStatisticsVO vo = new AgentStatisticsVO();
        
        // 1. 基础统计
        vo.setTotalSales(getTotalSales(agentId, query));
        vo.setTotalCommission(getTotalCommission(agentId, query));
        vo.setTeamSize(getTeamSize(agentId));
        
        // 2. 趋势分析
        vo.setSalesTrend(getSalesTrend(agentId, query));
        vo.setCommissionTrend(getCommissionTrend(agentId, query));
        
        // 3. 排行榜
        vo.setRanking(getAgentRanking(agentId, query));
        
        // 4. 团队贡献
        vo.setTeamContribution(getTeamContribution(agentId, query));
        
        return vo;
    }
}
```

## 5. API接口设计

### 5.1 管理端API

#### 5.1.1 分销配置管理

##### 1. 创建奖励方案
```
POST /api/v1/admin/distribution/scheme
```

请求参数：
```json
{
  "schemeCode": "SCHEME_001",
  "schemeName": "全场通用方案",
  "schemeDesc": "适用于所有商品的默认分销方案",
  "applyScope": 1,
  "scopeIds": [],
  "enableSalesReward": true,
  "enableProfitSharing": true,
  "profitTarget": 3,
  "maxTraceLevel": 3,
  "levelConfigMode": 2,
  "defaultSalesMode": 2,
  "defaultSalesRate": 10.0,
  "defaultProfitConfig": {
    "1": {"mode": 2, "rate": 5.0},
    "2": {"mode": 2, "rate": 3.0},
    "3": {"mode": 2, "rate": 1.0}
  },
  "minOrderAmount": 100.0,
  "maxCommission": 1000.0,
  "triggerStage": 1,
  "freezeDays": 7,
  "effectiveType": 1,
  "priority": 100
}
```

响应结果：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "schemeCode": "SCHEME_001"
  }
}
```

##### 2. 配置等级奖励
```
POST /api/v1/admin/distribution/scheme/{schemeId}/level-config
```

请求参数：
```json
{
  "levelId": 1,
  "salesCommissionMode": 2,
  "salesCommissionRate": 15.0,
  "parentProfitConfig": {
    "1": {"mode": 2, "rate": 8.0},
    "2": {"mode": 2, "rate": 5.0},
    "3": {"mode": 2, "rate": 2.0}
  },
  "referrerProfitConfig": {
    "1": {"mode": 2, "rate": 3.0}
  },
  "minOrderAmount": 50.0,
  "maxCommission": 2000.0
}
```

##### 3. 查询奖励方案列表
```
GET /api/v1/admin/distribution/scheme/list
```

查询参数：
- keyword: 关键词（方案名称/编码）
- applyScope: 适用范围
- status: 状态
- pageNo: 页码
- pageSize: 每页数量

响应结果：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 100,
    "list": [
      {
        "id": 1,
        "schemeCode": "SCHEME_001",
        "schemeName": "全场通用方案",
        "applyScope": 1,
        "enableSalesReward": true,
        "enableProfitSharing": true,
        "priority": 100,
        "status": 1,
        "createTime": "2024-01-01 10:00:00"
      }
    ]
  }
}
```

#### 5.1.2 分销员管理

##### 1. 查询分销员列表
```
GET /api/v1/admin/distribution/agent/list
```

查询参数：
- keyword: 关键词（姓名/手机号/分销码）
- levelId: 等级ID
- status: 状态
- applyStatus: 申请状态
- parentId: 上级ID
- dateRange: 时间范围
- pageNo: 页码
- pageSize: 每页数量

响应结果：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 500,
    "list": [
      {
        "id": 1,
        "agentCode": "DS000001",
        "agentName": "张三",
        "agentMobile": "13800138000",
        "levelId": 2,
        "levelName": "金牌分销员",
        "parentName": "李四",
        "teamCount": 50,
        "directCount": 10,
        "totalSales": 100000.00,
        "totalCommission": 10000.00,
        "status": 1,
        "applyTime": "2024-01-01 10:00:00"
      }
    ]
  }
}
```

##### 2. 审核分销员申请
```
POST /api/v1/admin/distribution/agent/{agentId}/approve
```

请求参数：
```json
{
  "action": "pass",
  "remark": "审核通过"
}
```

##### 3. 设置分销员标签
```
POST /api/v1/admin/distribution/agent/{agentId}/tags
```

请求参数：
```json
{
  "tagIds": [1, 2, 3],
  "reason": "设置KOL标签"
}
```

#### 5.1.3 邀请海报管理

##### 1. 创建邀请海报
```
POST /api/v1/admin/distribution/poster
```

请求参数：
```json
{
  "posterCode": "POSTER_001",
  "posterName": "新春招募海报",
  "posterType": 1,
  "targetLevelId": null,
  "templateUrl": "https://oss.example.com/poster/template1.jpg",
  "qrcodePosition": {
    "x": 200,
    "y": 500,
    "width": 150,
    "height": 150
  },
  "textConfig": [
    {
      "field": "agentName",
      "x": 200,
      "y": 450,
      "fontSize": 24,
      "color": "#333333"
    }
  ],
  "shareTitle": "邀请您成为分销合伙人",
  "shareDesc": "加入我们，轻松赚佣金",
  "effectiveDays": 30
}
```

##### 2. 查询海报列表
```
GET /api/v1/admin/distribution/poster/list
```

查询参数：
- keyword: 关键词
- posterType: 海报类型
- status: 状态
- pageNo: 页码
- pageSize: 每页数量

#### 5.1.4 商品分销配置

##### 1. 批量设置商品分销
```
POST /api/v1/admin/distribution/product/batch-config
```

请求参数：
```json
{
  "spuIds": [1001, 1002, 1003],
  "enableDistribution": true,
  "distPriority": 100,
  "distTags": ["热销", "高佣"],
  "recommendReason": "优质商品，佣金丰厚",
  "commissionHint": "最高可赚15%",
  "restrictLevelIds": [],
  "restrictTagIds": []
}
```

##### 2. 查询分销商品列表
```
GET /api/v1/admin/distribution/product/list
```

查询参数：
- keyword: 商品名称
- enableDistribution: 是否启用分销
- distTags: 分销标签
- categoryId: 类目ID
- pageNo: 页码
- pageSize: 每页数量

#### 5.1.5 佣金结算管理

##### 1. 查询佣金账单
```
GET /api/v1/admin/distribution/commission/bill/list
```

查询参数：
- agentId: 分销员ID
- billType: 账单类型
- status: 状态
- freezeStatus: 冻结状态
- dateRange: 时间范围
- pageNo: 页码
- pageSize: 每页数量

响应结果：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 1000,
    "summary": {
      "totalAmount": 50000.00,
      "frozenAmount": 10000.00,
      "settledAmount": 40000.00
    },
    "list": [
      {
        "id": 1,
        "billNo": "CB202401010001",
        "agentName": "张三",
        "billType": 1,
        "orderNo": "O202401010001",
        "spuName": "精品红酒",
        "amount": 100.00,
        "rate": 10.0,
        "freezeStatus": 1,
        "freezeEndTime": "2024-01-08 10:00:00",
        "status": 0,
        "billTime": "2024-01-01 10:00:00"
      }
    ]
  }
}
```

##### 2. 批量结算佣金
```
POST /api/v1/admin/distribution/commission/batch-settle
```

请求参数：
```json
{
  "billIds": [1, 2, 3, 4, 5],
  "settleBatchNo": "SETTLE202401010001",
  "remark": "月度结算"
}
```

##### 3. 审核提现申请
```
POST /api/v1/admin/distribution/withdraw/{withdrawId}/audit
```

请求参数：
```json
{
  "action": "pass",
  "remark": "审核通过，请等待打款"
}
```

#### 5.1.6 数据统计分析

##### 1. 分销概览统计
```
GET /api/v1/admin/distribution/statistics/overview
```

查询参数：
- dateRange: 时间范围

响应结果：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "agentCount": 5000,
    "activeAgentCount": 3000,
    "totalSales": 10000000.00,
    "totalCommission": 1000000.00,
    "todayData": {
      "newAgents": 50,
      "sales": 100000.00,
      "commission": 10000.00,
      "orders": 500
    },
    "trend": {
      "dates": ["2024-01-01", "2024-01-02"],
      "sales": [100000, 120000],
      "commission": [10000, 12000],
      "agents": [50, 55]
    }
  }
}
```

##### 2. 分销员排行榜
```
GET /api/v1/admin/distribution/statistics/ranking
```

查询参数：
- rankType: 排行类型（sales/commission/team）
- dateRange: 时间范围
- limit: 数量限制

### 5.2 用户端API

#### 5.2.1 分销员申请

##### 1. 申请成为分销员
```
POST /api/v1/distribution/apply
```

请求参数：
```json
{
  "agentName": "张三",
  "agentMobile": "13800138000",
  "inviteCode": "DS000001",
  "agreementAccepted": true
}
```

响应结果：
```json
{
  "code": 0,
  "msg": "申请提交成功，请等待审核",
  "data": {
    "agentId": 1,
    "applyStatus": 0
  }
}
```

##### 2. 查询申请状态
```
GET /api/v1/distribution/apply/status
```

响应结果：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "isAgent": false,
    "applyStatus": 0,
    "applyTime": "2024-01-01 10:00:00",
    "approveRemark": null
  }
}
```

#### 5.2.2 分销中心

##### 1. 获取分销员信息
```
GET /api/v1/distribution/agent/info
```

响应结果：
```json
{
  "code": 0,
  "msg": "success", 
  "data": {
    "agentId": 1,
    "agentCode": "DS000001",
    "agentName": "张三",
    "levelName": "金牌分销员",
    "levelIcon": "https://oss.example.com/level/gold.png",
    "tags": ["KOL达人", "优质代理"],
    "statistics": {
      "totalCommission": 10000.00,
      "availableCommission": 8000.00,
      "frozenCommission": 2000.00,
      "withdrawnCommission": 5000.00,
      "monthSales": 50000.00,
      "teamCount": 100,
      "directCount": 20
    }
  }
}
```

##### 2. 获取邀请海报
```
GET /api/v1/distribution/poster/list
```

响应结果：
```json
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "posterId": 1,
      "posterName": "新春招募海报",
      "posterUrl": "https://oss.example.com/poster/agent/1001.jpg",
      "shareTitle": "邀请您成为分销合伙人",
      "shareDesc": "加入我们，轻松赚佣金",
      "expireTime": "2024-02-01 00:00:00"
    }
  ]
}
```

##### 3. 生成专属海报
```
POST /api/v1/distribution/poster/{posterId}/generate
```

响应结果：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "posterUrl": "https://oss.example.com/poster/personal/xxx.jpg",
    "qrcodeUrl": "https://oss.example.com/qrcode/xxx.png",
    "shareUrl": "https://m.example.com/invite?code=DS000001&p=1"
  }
}
```

#### 5.2.3 商品分销

##### 1. 获取分销商品列表
```
GET /api/v1/distribution/product/list
```

查询参数：
- categoryId: 类目ID
- keyword: 关键词
- sortBy: 排序方式（commission/sales/priority）
- pageNo: 页码
- pageSize: 每页数量

响应结果：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 200,
    "list": [
      {
        "spuId": 1001,
        "spuName": "精品红酒",
        "spuPic": "https://oss.example.com/product/1001.jpg",
        "price": 299.00,
        "originalPrice": 399.00,
        "salesCount": 1000,
        "commissionHint": "最高可赚15%",
        "maxCommission": 44.85,
        "tags": ["热销", "高佣"],
        "recommendReason": "品质优选，佣金丰厚"
      }
    ]
  }
}
```

##### 2. 获取商品分销详情
```
GET /api/v1/distribution/product/{spuId}/detail
```

响应结果：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "spuId": 1001,
    "spuName": "精品红酒",
    "enableDistribution": true,
    "myCommission": {
      "mode": 2,
      "rate": 15.0,
      "estimateAmount": 44.85
    },
    "shareInfo": {
      "title": "精品红酒，品质之选",
      "desc": "原价399，现价仅需299",
      "pic": "https://oss.example.com/product/1001.jpg",
      "url": "https://m.example.com/product/1001?agent=DS000001"
    }
  }
}
```

#### 5.2.4 佣金管理

##### 1. 查询佣金明细
```
GET /api/v1/distribution/commission/list
```

查询参数：
- billType: 账单类型
- status: 状态
- dateRange: 时间范围
- pageNo: 页码
- pageSize: 每页数量

响应结果：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "summary": {
      "totalIncome": 10000.00,
      "monthIncome": 2000.00,
      "todayIncome": 100.00
    },
    "list": [
      {
        "billId": 1,
        "billNo": "CB202401010001",
        "billType": 1,
        "billTypeName": "销售佣金",
        "orderNo": "O202401010001",
        "spuName": "精品红酒",
        "amount": 44.85,
        "status": 0,
        "statusName": "待结算",
        "freezeEndTime": "2024-01-08 10:00:00",
        "billTime": "2024-01-01 10:00:00"
      }
    ]
  }
}
```

##### 2. 申请提现
```
POST /api/v1/distribution/withdraw/apply
```

请求参数：
```json
{
  "amount": 1000.00,
  "accountType": 1,
  "accountName": "张三",
  "accountNo": "wx_openid_xxx"
}
```

响应结果：
```json
{
  "code": 0,
  "msg": "提现申请已提交",
  "data": {
    "withdrawId": 1,
    "withdrawNo": "W202401010001"
  }
}
```

##### 3. 查询提现记录
```
GET /api/v1/distribution/withdraw/list
```

查询参数：
- status: 状态
- dateRange: 时间范围
- pageNo: 页码
- pageSize: 每页数量

#### 5.2.5 团队管理

##### 1. 查询团队成员
```
GET /api/v1/distribution/team/list
```

查询参数：
- level: 层级（1-直属，2-二级，3-三级，0-全部）
- keyword: 关键词
- sortBy: 排序方式
- pageNo: 页码
- pageSize: 每页数量

响应结果：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "summary": {
      "totalCount": 100,
      "directCount": 20,
      "monthNewCount": 5
    },
    "list": [
      {
        "agentId": 2,
        "agentName": "李四",
        "levelName": "银牌分销员",
        "joinTime": "2024-01-01 10:00:00",
        "teamCount": 10,
        "monthSales": 10000.00,
        "contribution": 500.00,
        "traceLevel": 1
      }
    ]
  }
}
```

##### 2. 查询团队业绩
```
GET /api/v1/distribution/team/performance
```

查询参数：
- dateRange: 时间范围
- groupBy: 分组方式（day/month）

响应结果：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "totalSales": 500000.00,
    "totalCommission": 50000.00,
    "teamContribution": 20000.00,
    "trend": {
      "dates": ["2024-01-01", "2024-01-02"],
      "sales": [20000, 25000],
      "commission": [2000, 2500]
    },
    "topMembers": [
      {
        "agentId": 2,
        "agentName": "李四",
        "sales": 50000.00,
        "contribution": 5000.00
      }
    ]
  }
}
```

## 6. 项目实施计划

### 6.0 实施进度汇总（2025-01-14更新）

#### 第一阶段：基础框架搭建 ✅ 完成度：95%

**已完成工作：**
1. ✅ Maven模块结构创建完成
   - yitong-module-distribution父模块
   - yitong-module-distribution-api子模块（包含错误码和业务枚举）
   - yitong-module-distribution-biz子模块
   - 模块已在父项目和server中注册

2. ✅ 数据库设计和SQL脚本完成
   - 创建了15个核心数据表的SQL定义
   - 添加了初始化数据（默认等级和标签）
   - 文件位置：`sql/mysql/distribution/distribution_tables.sql`

3. ✅ 实体类（DO）全部完成
   - 分销等级相关：DistLevelDO
   - 分销员相关：DistAgentDO、DistAgentTagDO、DistAgentTagRelDO
   - 奖励配置相关：DistRewardSchemeDO、DistRewardLevelConfigDO、DistRewardTagConfigDO、DistRewardPersonalConfigDO
   - 佣金账单相关：DistCommissionBillDO、DistWithdrawRecordDO、DistConfigTraceDO
   - 海报相关：DistInvitePosterDO、DistAgentPosterDO
   - 商品配置相关：DistProductConfigDO

4. ✅ Mapper接口全部完成
   - 所有实体对应的Mapper接口已创建
   - 定义了常用的查询方法和分页查询
   - XML文件待创建

5. ✅ Service接口定义完成
   - 分销等级服务：DistLevelService
   - 分销员服务：DistAgentService、DistAgentTagService
   - 奖励配置服务：DistRewardSchemeService、DistRewardPersonalConfigService
   - 佣金服务：DistCommissionService、DistWithdrawService、DistAttributionService
   - 海报服务：DistInvitePosterService
   - 商品配置服务：DistProductConfigService

6. ✅ Controller框架部分完成
   - 管理端Controller：DistLevelController、DistAgentController
   - 用户端Controller：AppDistAgentController
   - 其他Controller待创建

7. ✅ API模块基础设施完成
   - 错误码常量：DistributionErrorCodeConstants
   - 业务枚举：DistributionBusinessEnum

**待完成工作：**
- Mapper XML文件创建
- Service实现类
- VO/DTO类定义
- Convert转换器类
- 其余Controller类

### 6.1 项目阶段划分

#### 第一阶段：基础框架搭建（2周）
- 数据库设计与创建
- 基础实体类和Mapper开发
- 核心服务框架搭建
- 基础API接口开发

#### 第二阶段：核心功能开发（3周）✅ 完成度：100%
- ✅ 分销员申请与审核功能
- ✅ 分销关系管理功能
- ✅ 奖励方案配置功能
- ✅ 佣金计算引擎开发

#### 第三阶段：高级功能开发（3周）
- 智能归因机制实现
- 标签管理系统
- 邀请海报功能
- 个人专属配置

#### 第四阶段：前端开发（4周）
- 管理后台界面开发
- 商家端H5开发
- 用户端H5开发
- 接口联调

#### 第五阶段：测试与优化（2周）
- 功能测试
- 性能测试
- 安全测试
- Bug修复与优化

#### 第六阶段：上线部署（1周）
- 生产环境部署
- 数据迁移
- 监控配置
- 正式上线

### 6.2 详细实施TodoList

#### 6.2.1 数据库实施
- [ ] 创建数据库和用户权限
- [x] 执行建表SQL脚本
- [x] 创建索引优化查询性能
- [x] 初始化基础数据（默认等级、标签等）
- [ ] 配置数据库备份策略

#### 6.2.2 后端开发任务

##### 基础模块
- [x] 创建Maven模块：yitong-module-distribution
- [x] 配置模块依赖和Spring配置
- [x] 生成实体类（DO）
- [x] 生成Mapper接口和XML（Mapper接口已完成，XML待完成）
- [x] 创建Service接口定义
- [x] 创建Controller类框架
- [x] 创建API VO类和DTO类
- [x] 创建Convert转换器类
- [x] 创建Service实现类（全部完成）
- [x] 完成Controller实现细节（已完成主要Controller）
- [x] 创建Mapper XML文件（已完成主要XML文件）

##### 分销员管理
- [x] 分销员申请API开发
- [x] 申请审核功能开发
- [x] 分销关系绑定功能
- [x] 分销员信息查询API
- [x] 分销员标签管理功能（DistAgentTagService已实现）
- [x] 等级自动升降级功能（DistLevelUpgradeService已实现）
- [x] 团队统计功能（完整的统计数据查询）

##### 奖励配置管理
- [x] 奖励方案CRUD功能（DistRewardSchemeService已实现）
- [x] 等级配置管理功能（DistLevelService已实现）
- [x] 标签配置管理功能（DistAgentTagService已实现）
- [x] 个人专属配置功能（DistRewardPersonalConfigService已实现）
- [x] 配置缓存机制实现（DistConfigCacheService已实现）
- [x] 配置变更通知机制（DistConfigNotifyService已实现）

##### 佣金计算引擎
- [x] 智能归因算法实现（DistAttributionService已实现）
- [x] 销售佣金计算功能（DistCommissionService已实现）
- [x] 分润佣金计算功能（支持三级分润）
- [x] 佣金冻结解冻机制
- [x] 批量结算功能
- [x] 佣金账单查询API

##### 邀请海报功能
- [x] 海报模板管理功能（DistPosterService已实现）
- [x] 二维码生成服务（DistQrCodeService已实现）
- [x] 海报合成服务（已在DistPosterService中实现）
- [ ] 海报分享追踪
- [ ] 扫码绑定功能

##### 商品分销配置
- [x] 商品分销设置功能（DistProductConfigService已实现）
- [x] 批量配置功能
- [x] 分销商品查询API
- [x] 商品佣金预估功能

##### 数据统计分析
- [x] 实时统计服务开发（DistStatisticsService已实现）
- [x] 统计数据缓存优化（已在DistStatisticsService中实现缓存）
- [x] 报表生成功能（exportStatisticsReport方法已实现）
- [ ] 数据导出功能（具体实现待完善）

##### 提现管理
- [x] 提现申请功能（DistWithdrawService已实现）
- [x] 提现审核功能（包含批量审核）
- [x] 自动打款接口对接（processWithdrawPayment方法已实现）
- [x] 提现记录查询（包含统计功能）

#### 6.2.3 前端开发任务

##### 管理后台
- [x] 分销配置管理页面 ✅ 2025-07-15
  - [x] 奖励方案列表页
  - [x] 方案创建/编辑页
  - [x] 等级配置页
  - [x] 标签配置页
- [x] 分销员管理页面 ✅ 2025-07-15
  - [x] 分销员列表页
  - [x] 分销员详情页
  - [x] 申请审核页
  - [x] 标签管理页
- [x] 商品分销配置页面 ✅ 2025-07-15
  - [x] 商品列表页
  - [x] 批量设置页
- [ ] 佣金结算管理页面
  - [ ] 账单列表页
  - [ ] 批量结算页
  - [ ] 提现审核页
- [ ] 数据统计页面
  - [ ] 概览统计页
  - [ ] 排行榜页
  - [ ] 数据报表页

##### 商家端H5
- [ ] 邀请海报管理
  - [ ] 海报列表页
  - [ ] 创建海报页
  - [ ] 海报预览页
- [ ] 团队管理
  - [ ] 团队成员列表
  - [ ] 成员详情页
  - [ ] 业绩统计页
- [ ] 佣金管理
  - [ ] 佣金明细页
  - [ ] 提现申请页
  - [ ] 提现记录页

##### 用户端H5
- [ ] 分销申请
  - [ ] 申请页面
  - [ ] 协议页面
  - [ ] 状态查询页
- [ ] 分销中心
  - [ ] 个人中心页
  - [ ] 邀请海报页
  - [ ] 推广教程页
- [ ] 商品分销
  - [ ] 商品列表页
  - [ ] 商品详情页
  - [ ] 分享组件
- [ ] 佣金管理
  - [ ] 收益明细页
  - [ ] 提现页面
- [ ] 团队管理
  - [ ] 团队列表页
  - [ ] 业绩排行页

#### 6.2.4 测试任务
- [ ] 单元测试编写
- [ ] 接口测试用例设计
- [ ] 功能测试执行
- [ ] 性能压测
  - [ ] 佣金计算性能测试
  - [ ] 归因查询性能测试
  - [ ] 并发申请测试
- [ ] 安全测试
  - [ ] SQL注入测试
  - [ ] XSS攻击测试
  - [ ] 权限越权测试

#### 6.2.5 部署与运维
- [ ] 生产环境准备
  - [ ] 服务器配置
  - [ ] 数据库配置
  - [ ] Redis配置
  - [ ] OSS配置
- [ ] 应用部署
  - [ ] CI/CD配置
  - [ ] 应用发布
  - [ ] 配置更新
- [ ] 监控告警
  - [ ] 应用监控配置
  - [ ] 日志收集配置
  - [ ] 告警规则设置
- [ ] 运维文档
  - [ ] 部署文档
  - [ ] 运维手册
  - [ ] 故障处理流程

### 6.3 风险控制
1. **恶意刷单风险**：通过佣金冻结期和订单风控规则防范
2. **数据一致性**：使用事务和分布式锁保证数据一致性
3. **性能瓶颈**：通过缓存和异步处理优化性能
4. **配置冲突**：通过优先级机制和冲突日志解决

### 6.4 项目交付物
1. 源代码及技术文档
2. 数据库设计文档
3. API接口文档
4. 用户操作手册
5. 部署和运维文档
6. 测试报告

## 7. 实施进度追踪

### 7.1 第一阶段进度（基础框架搭建）
- **总体完成度**：100% ✅
- **已完成项目**：
  - ✅ 数据库表结构设计和创建（15个表）
  - ✅ Maven模块创建和配置
  - ✅ 实体类（DO）生成（15个实体类）
  - ✅ Mapper接口定义（15个Mapper接口）
  - ✅ Service接口定义（核心业务接口）
  - ✅ Controller框架搭建（管理端和用户端）
  - ✅ API VO/DTO类创建（核心VO类）
  - ✅ Convert转换器创建
  - ✅ 业务枚举定义（完整的业务状态枚举）
  - ✅ 错误码定义（模块错误码段）

### 7.2 第二阶段进度（核心功能开发）
- **总体完成度**：70%
- **已完成项目**：
  - ✅ DistLevelService实现 - 分销等级管理完整功能
  - ✅ DistAgentService实现 - 分销员管理核心功能
  - ✅ DistCommissionService实现 - 佣金计算引擎
  - ✅ DistAttributionService实现 - 智能归因算法
  - ✅ DistRewardSchemeService实现 - 奖励方案管理
  - ✅ DistAgentTagService实现 - 标签管理功能
  - ✅ DistLevelUpgradeService实现 - 自动升降级功能
  - ✅ DistProductConfigService实现 - 商品分销配置
  - ✅ 分销员申请审核流程
  - ✅ 分销关系绑定功能
  - ✅ 佣金计算引擎（支持多级分润）
  - ✅ 佣金冻结解冻机制
  - ✅ 团队统计功能（完整的统计数据查询）
  - ✅ 商品分销配置管理
- **待完成项目**：
  - ⏳ 海报模板管理功能
  - ⏳ 二维码生成服务
  - ⏳ 提现管理功能
  - ⏳ 数据统计分析服务
  - ⏳ 配置缓存机制
  - ⏳ Mapper XML文件创建
  - ⏳ Controller具体实现细节

### 7.3 当前工作重点
1. 实现海报模板管理和二维码生成服务
2. 完成提现管理功能
3. 实现配置缓存机制
4. 开始Controller具体实现

### 7.4 前端实现进度（2025-07-15）
#### 已完成功能
1. **分销配置管理模块**
   - ✅ 奖励方案管理（列表、新增、编辑、删除）
   - ✅ 等级配置功能（按方案配置不同等级的佣金和分润）
   - ✅ 标签配置功能（按方案配置不同标签的佣金和分润）
   - ✅ API接口定义（scheme、level、tag、agent相关接口）

#### 技术实现细节
1. **目录结构**
   ```
   src/
   ├── api/distribution/          # 分销API接口
   │   ├── config/               # 配置相关
   │   │   ├── scheme.ts        # 奖励方案接口
   │   │   ├── level.ts         # 等级配置接口
   │   │   └── tag.ts           # 标签配置接口
   │   └── agent/               # 分销员相关
   │       └── index.ts         # 分销员、等级、标签接口
   └── views/distribution/       # 分销页面
       └── config/              # 配置管理
           └── scheme/          # 奖励方案
               ├── index.vue           # 方案列表页
               ├── SchemeForm.vue      # 方案表单
               ├── LevelConfigDialog.vue    # 等级配置弹窗
               ├── LevelConfigForm.vue      # 等级配置表单
               ├── TagConfigDialog.vue      # 标签配置弹窗
               └── TagConfigForm.vue        # 标签配置表单
   ```

2. **核心功能特性**
   - 支持多种适用范围（全局、类目、商品、SKU）
   - 灵活的佣金模式（百分比、固定金额）
   - 三级分润体系（上级、上上级、介绍人）
   - 优先级管理机制
   - 生效时间控制

#### 待完成任务
1. 商品分销配置页面
2. 佣金结算管理页面
3. 数据统计页面
4. 路由配置和菜单集成

### 7.5 前端最新进度（2025-07-15 更新）
#### 分销员管理模块完成
1. **功能页面**
   - ✅ 分销员列表（支持搜索、筛选、分页）
   - ✅ 分销员详情（基本信息、关系、统计等多标签页展示）
   - ✅ 申请审核弹窗（支持通过/拒绝及备注）
   - ✅ 标签管理（支持添加/移除分销员标签）
   - ✅ 分销员表单（新增/编辑分销员信息）

2. **标签管理子模块**
   - ✅ 标签列表页（`agent/tag/index.vue`）
   - ✅ 标签表单（支持颜色、图标、自动附加规则配置）

3. **技术特性**
   - 完善的权限控制（v-hasPermi指令）
   - 实时统计数据展示
   - 优雅的UI交互设计
   - 批量操作支持
   - 数据导出功能

#### 商品分销配置模块完成
1. **功能页面**
   - ✅ 商品分销列表页（`product/index.vue`）
     - 支持多条件搜索（商品名称、分类、分销状态、标签）
     - 实时切换分销状态
     - 展示商品信息、分销配置、限制条件等
   - ✅ 批量设置弹窗（`product/BatchConfigDialog.vue`）
     - 支持批量启用/禁用分销
     - 统一设置优先级、标签、推荐理由等
     - 配置等级和标签限制
   - ✅ 单个配置编辑（`product/ConfigForm.vue`）
     - 编辑单个商品的分销配置
     - 设置最高佣金比例用于展示
     - 精细化的访问限制配置

2. **技术特性**
   - 商品分类选择器集成
   - 标签的动态创建和选择
   - 批量操作的友好提示
   - 数据导出功能
   - 与商品详情页的联动跳转

3. **目录结构**
   ```
   src/
   ├── api/distribution/
   │   ├── config/          # 配置相关API
   │   ├── agent/           # 分销员相关API
   │   └── product/         # 商品分销API
   │       └── index.ts     # 商品分销配置接口
   └── views/distribution/
       ├── config/          # 分销配置管理
       ├── agent/           # 分销员管理
       └── product/         # 商品分销配置
           ├── index.vue              # 商品列表页
           ├── BatchConfigDialog.vue  # 批量设置弹窗
           └── ConfigForm.vue         # 单个配置表单
   ```

#### 邀请海报管理模块完成
1. **功能页面**
   - ✅ 海报模板管理页（`poster/template/index.vue`）
     - 海报模板列表展示和搜索
     - 支持通用、等级专属、活动海报类型
     - 实时使用统计和效果分析
     - 状态管理和批量操作
   - ✅ 海报模板表单（`poster/template/PosterForm.vue`）
     - 完整的模板创建和编辑功能
     - 二维码位置配置
     - 动态文字配置（位置、样式、内容）
     - 图片上传和预览
   - ✅ 海报统计分析（`poster/template/PosterStatisticsDialog.vue`）
     - 使用量和转化率统计
     - 趋势分析图表
     - 近期数据表格展示
   - ✅ 分销员海报管理（`poster/agent/index.vue`）
     - 生成的海报列表管理
     - 分销员和模板筛选
     - 使用统计和转化跟踪
   - ✅ 海报生成弹窗（`poster/agent/GeneratePosterDialog.vue`）
     - 分销员选择和权限验证
     - 可用海报模板筛选
     - 模板预览和生成确认
   - ✅ 海报详情查看（`poster/agent/PosterDetailDialog.vue`）
     - 完整海报信息展示
     - 使用统计图表分析
     - 下载和分享功能
   - ✅ 海报预览功能（`poster/agent/PosterPreviewDialog.vue`）
     - 海报预览和社交分享
     - 链接复制和下载功能

2. **API接口完整性**
   - ✅ 海报模板管理API（`api/distribution/poster/index.ts`）
     - 模板CRUD操作
     - 状态管理和导出功能
   - ✅ 分销员海报生成API
     - 海报生成和刷新
     - 可用模板查询
     - 统计数据获取
   - ✅ 海报效果统计API
     - 使用量和转化数据
     - 趋势分析接口
     - 排行榜统计

3. **技术特性**
   - 智能权限控制（等级专属海报）
   - 实时统计数据展示
   - 交互式图表分析（ECharts集成）
   - 图片上传和预览组件
   - 社交分享功能集成
   - 响应式设计适配

4. **目录结构**
   ```
   src/
   ├── api/distribution/
   │   └── poster/
   │       └── index.ts           # 海报相关API接口
   └── views/distribution/
       └── poster/
           ├── template/          # 海报模板管理
           │   ├── index.vue             # 模板列表页
           │   ├── PosterForm.vue        # 模板表单
           │   └── PosterStatisticsDialog.vue # 统计分析
           └── agent/             # 分销员海报管理
               ├── index.vue             # 海报列表页
               ├── GeneratePosterDialog.vue    # 生成弹窗
               ├── PosterDetailDialog.vue      # 详情弹窗
               └── PosterPreviewDialog.vue     # 预览弹窗
   ```

### 7.4 核心功能实现亮点
1. **智能归因机制**：实现了5级优先级配置（个人>标签>等级>商品>全局）
2. **三级分润体系**：支持最多三级上下级关系的佣金分润
3. **灵活的佣金模式**：支持百分比和固定金额两种计算方式
4. **完整的审核流程**：分销员申请、审核、状态管理一体化
5. **防循环绑定**：实现了上下级关系的循环检测算法
6. **实时统计更新**：团队人数、销售额、佣金等数据实时同步

## 8. 总结

分销代理系统v3.0通过整合多版本的优秀设计，实现了一个功能完善、扩展性强、性能优异的分销管理平台。系统具备以下核心优势：

1. **灵活的配置体系**：支持多维度、多层级的奖励配置
2. **智能的归因机制**：自动选择最优配置，避免规则冲突
3. **完善的功能模块**：覆盖申请、审核、计算、结算全流程
4. **优秀的用户体验**：移动端友好，操作便捷
5. **可靠的技术架构**：高性能、高可用、易扩展

通过本系统的实施，将有效提升商家的分销管理效率，激发分销员的推广积极性，实现商家、分销员、消费者的多方共赢。

## 9. 开发进度跟踪

### 9.1 已完成功能
1. **分销配置管理** ✅
   - 奖励方案管理
   - 等级配置
   - 标签配置
   - 多维度优先级设置

2. **分销员管理** ✅
   - 分销员列表
   - 审核管理
   - 标签管理
   - 详情查看

3. **商品分销配置** ✅
   - 商品分销列表
   - 批量配置
   - 单个配置
   - 访问限制设置

4. **佣金结算管理** ✅
   - 佣金账单管理（含批量结算、账单详情、取消功能）
   - 提现审核（含批量审核、重新打款）
   - 统计分析（概览统计、趋势分析、排行榜）

5. **邀请海报管理** ✅
   - 海报模板管理（创建、编辑、删除、统计分析）
   - 分销员海报生成管理（生成、预览、下载、分享）
   - 海报效果统计（使用量、转化率、趋势图表）

### 9.2 待开发功能
1. **H5端功能开发**
   - 分销中心页面
   - 团队管理页面
   - 收益明细页面
   - 海报分享功能

### 9.3 菜单配置
分销管理模块需要在系统菜单中配置以下路由：

```
分销管理
├── 分销配置管理
│   └── 奖励方案 (/distribution/config/scheme)
├── 分销员管理
│   ├── 分销员列表 (/distribution/agent)
│   └── 分销员标签 (/distribution/agent/tag)
├── 商品分销配置
│   └── 商品配置 (/distribution/product)
├── 佣金结算管理
│   ├── 佣金账单 (/distribution/commission/bill)
│   ├── 提现审核 (/distribution/commission/withdraw)
│   └── 统计分析 (/distribution/commission/statistics)
└── 邀请海报管理
    ├── 海报模板 (/distribution/poster/template)
    └── 海报生成 (/distribution/poster/agent)
```

### 9.4 权限标识

#### 分销配置管理
- distribution:scheme:query - 查询奖励方案
- distribution:scheme:create - 创建奖励方案
- distribution:scheme:update - 更新奖励方案
- distribution:scheme:delete - 删除奖励方案
- distribution:scheme:export - 导出奖励方案

#### 分销员管理
- distribution:agent:query - 查询分销员
- distribution:agent:create - 创建分销员
- distribution:agent:update - 更新分销员
- distribution:agent:delete - 删除分销员
- distribution:agent:export - 导出分销员
- distribution:agent:audit - 审核分销员
- distribution:agent:tag - 管理分销员标签

#### 商品分销配置
- distribution:product:query - 查询商品配置
- distribution:product:update - 更新商品配置
- distribution:product:export - 导出商品配置

#### 佣金结算管理
- distribution:commission:query - 查询佣金账单
- distribution:commission:settle - 结算佣金
- distribution:commission:update - 更新佣金状态
- distribution:commission:export - 导出佣金账单
- distribution:withdraw:query - 查询提现记录
- distribution:withdraw:audit - 审核提现
- distribution:withdraw:payment - 处理打款
- distribution:withdraw:export - 导出提现记录
- distribution:statistics:query - 查询统计数据

#### 邀请海报管理
- distribution:poster:query - 查询海报模板
- distribution:poster:create - 创建海报模板
- distribution:poster:update - 更新海报模板
- distribution:poster:delete - 删除海报模板
- distribution:poster:export - 导出海报模板
- distribution:poster:generate - 生成分销员海报
- distribution:poster:statistics - 查询海报统计

## 10. 技术实现总结

### 10.1 前端技术栈
- **Vue 3.5.2** + Composition API - 现代化响应式框架
- **TypeScript 5.3.3** - 类型安全和代码质量保障
- **Element Plus 2.8.4** - 企业级UI组件库
- **ECharts** - 数据可视化图表库
- **Vite 5.1.4** - 快速构建工具
- **Pinia 2.1.7** - 状态管理
- **UnoCSS 0.58.5** - 原子化CSS框架

### 10.2 核心特性
1. **完整的类型定义**：所有API接口和数据结构都有完整的TypeScript类型定义
2. **组件化设计**：高度可复用的组件架构，便于维护和扩展
3. **权限控制**：基于角色的细粒度权限控制系统
4. **响应式设计**：适配不同屏幕尺寸的现代化界面
5. **数据可视化**：丰富的图表和统计分析功能
6. **用户体验优化**：流畅的交互和友好的操作反馈

### 10.3 代码质量
- **ESLint + Prettier**：代码规范和格式化
- **组件复用**：高度模块化的组件设计
- **错误处理**：完善的异常处理和用户提示
- **性能优化**：懒加载、虚拟滚动等性能优化措施
- **国际化支持**：完整的多语言支持框架

