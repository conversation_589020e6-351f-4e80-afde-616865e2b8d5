# 分销代理系统管理后台API接口文档 v1.1

## 1. 接口规范

### 1.1 基础规范
- 基础路径：`/admin-api/distribution/v1`
- 请求方式：RESTful风格
- 认证方式：Bearer Token
- 响应格式：JSON
- 字符编码：UTF-8

### 1.2 响应格式
```json
{
  "code": 0,      // 0-成功，其他-错误码
  "data": {},     // 响应数据
  "msg": "success" // 响应消息
}
```

### 1.3 分页参数
```json
{
  "pageNo": 1,    // 页码，从1开始
  "pageSize": 10  // 每页条数
}
```

### 1.4 分页响应
```json
{
  "list": [],     // 数据列表
  "total": 100    // 总条数
}
```

## 2. 分销员管理API

### 2.1 分销员列表查询
**接口地址**：`GET /agent/page`

**请求参数**：
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "keyword": "张三",          // 分销码/姓名/手机号
  "levelId": 1,              // 等级ID
  "status": 1,               // 状态：0-禁用，1-正常
  "createTime": ["2024-03-01", "2024-03-31"] // 时间范围
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "agentCode": "DS001",
        "agentName": "张三",
        "phone": "13812341234",
        "levelId": 4,
        "levelName": "钻石等级",
        "parentName": null,
        "teamCount": 128,
        "monthSales": "12580.00",
        "status": 1,
        "joinTime": "2024-01-15 10:30:25"
      }
    ],
    "total": 2659
  },
  "msg": "success"
}
```

### 2.2 分销员详情
**接口地址**：`GET /agent/{id}`

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "id": 1,
    "memberId": 1001,
    "agentCode": "DS001",
    "agentName": "张三",
    "phone": "13812341234",
    "levelId": 4,
    "levelName": "钻石等级",
    "parentId": null,
    "parentName": null,
    "referrerId": null,
    "referrerName": null,
    "status": 1,
    "teamCount": 128,
    "directCount": 15,
    "totalSales": "358900.00",
    "monthSales": "12580.00",
    "joinTime": "2024-01-15 10:30:25",
    "levelUpdateTime": "2024-03-01 00:00:00",
    "lastActiveTime": "2024-03-15 14:22:10",
    // 账户信息
    "balance": {
      "totalCommission": "125890.00",
      "frozenBalance": "1200.00",
      "availableBalance": "5280.00",
      "withdrawingBalance": "0.00",
      "withdrawnBalance": "119410.00"
    }
  },
  "msg": "success"
}
```

### 2.3 编辑分销员
**接口地址**：`PUT /agent/{id}`

**请求参数**：
```json
{
  "agentName": "张三",
  "phone": "13812341234",
  "levelId": 4,
  "levelChangeReason": "手动调整为钻石等级",
  "parentId": 2,
  "referrerId": 2,
  "status": 1,
  "statusReason": "恢复正常",
  "remark": "优秀分销员"
}
```

### 2.4 新增分销员
**接口地址**：`POST /agent`

**请求参数**：
```json
{
  "memberId": 1005,
  "levelId": 1,
  "parentId": null,
  "referrerId": null,
  "remark": "后台直接添加"
}
```

### 2.5 禁用/启用分销员
**接口地址**：`PUT /agent/{id}/status`

**请求参数**：
```json
{
  "status": 0,  // 0-禁用，1-启用
  "reason": "违规操作"
}
```

### 2.6 团队结构查询
**接口地址**：`GET /agent/{id}/team`

**请求参数**：
```json
{
  "keyword": "李四",  // 搜索关键词
  "maxDepth": 3      // 最大层级深度
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "agent": {
      "id": 1,
      "agentCode": "DS001",
      "agentName": "张三",
      "levelName": "钻石等级"
    },
    "children": [
      {
        "id": 2,
        "agentCode": "DS002",
        "agentName": "李四",
        "levelName": "黄金等级",
        "teamCount": 45,
        "monthSales": "8350.00",
        "children": [...]
      }
    ]
  },
  "msg": "success"
}
```

### 2.7 业绩统计
**接口地址**：`GET /agent/{id}/statistics`

**请求参数**：
```json
{
  "dateRange": ["2024-03-01", "2024-03-31"]
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "summary": {
      "personalSales": "12580.00",
      "teamSales": "68930.00",
      "orderCount": 156,
      "avgOrderAmount": "80.64",
      "salesCommission": "1887.00",
      "profitCommission": "263.00"
    },
    "trend": [
      {
        "date": "2024-03-01",
        "personalSales": "580.00",
        "teamSales": "3250.00",
        "commission": "95.00"
      }
    ],
    "topProducts": [
      {
        "rank": 1,
        "productName": "高端红酒套装",
        "salesCount": 23,
        "salesAmount": "5290.00",
        "commission": "793.50"
      }
    ]
  },
  "msg": "success"
}
```

### 2.8 佣金记录查询
**接口地址**：`GET /agent/{id}/commission`

**请求参数**：
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "billType": 1,    // 1-销售奖励，2-分润收益
  "status": 1,      // 0-待结算，1-已结算，2-已冻结，3-已取消
  "dateRange": ["2024-03-01", "2024-03-31"]
}
```

### 2.9 操作日志查询
**接口地址**：`GET /agent/{id}/logs`

**请求参数**：
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "dateRange": ["2024-03-08", "2024-03-15"]
}
```

## 3. 申请审核API

### 3.1 申请列表查询
**接口地址**：`GET /agent-apply/page`

**请求参数**：
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "keyword": "王五",
  "inviteCode": "DS001",
  "status": 0,  // 0-待审核，1-审核通过，2-审核拒绝
  "applyTime": ["2024-03-15", "2024-03-15"]
}
```

### 3.2 申请详情
**接口地址**：`GET /agent-apply/{id}`

### 3.3 审核通过
**接口地址**：`PUT /agent-apply/{id}/approve`

**请求参数**：
```json
{
  "auditRemark": "审核通过"
}
```

### 3.4 审核拒绝
**接口地址**：`PUT /agent-apply/{id}/reject`

**请求参数**：
```json
{
  "auditRemark": "资料不完整"
}
```

### 3.5 批量审核
**接口地址**：`PUT /agent-apply/batch-audit`

**请求参数**：
```json
{
  "ids": [1, 2, 3],
  "action": "approve",  // approve-通过，reject-拒绝
  "auditRemark": "批量审核通过"
}
```

## 4. 等级管理API

### 4.1 等级列表
**接口地址**：`GET /level/list`

**响应示例**：
```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "levelCode": "L001",
      "levelName": "青铜等级",
      "levelGrade": 10,
      "inviteCode": "BRONZE2024",
      "iconUrl": "http://xxx/bronze.png",
      "autoUpgrade": true,
      "autoDowngrade": false,
      "agentCount": 1250,
      "status": 1
    }
  ],
  "msg": "success"
}
```

### 4.2 创建等级
**接口地址**：`POST /level`

**请求参数**：
```json
{
  "levelCode": "L005",
  "levelName": "王者等级",
  "levelGrade": 50,
  "inviteCode": "KING2024",
  "iconUrl": "http://xxx/king.png",
  "color": "#FFD700",
  "description": "最高等级",
  "benefits": "享受最高权益",
  "upgradeConditions": {
    "totalSales": 500000,
    "directCount": 20,
    "teamCount": 100
  },
  "downgradeConditions": {
    "monthsCount": 3,
    "monthSales": 50000
  },
  "autoUpgrade": true,
  "autoDowngrade": false
}
```

### 4.3 编辑等级
**接口地址**：`PUT /level/{id}`

### 4.4 删除等级
**接口地址**：`DELETE /level/{id}`

### 4.5 等级权益配置
**接口地址**：`PUT /level/{id}/benefits`

**请求参数**：
```json
{
  "basicBenefits": [
    "专属客服支持",
    "优先发货权",
    "专属培训课程",
    "年度表彰资格"
  ],
  "marketingTools": {
    "monthlyPosters": 10,
    "yearlyCardDesign": 2,
    "exclusiveLink": true
  },
  "specialPermissions": [
    "查看下级业绩",
    "团队管理工具"
  ],
  "benefitDescription": "钻石等级分销员可享受最高级别的权益和支持"
}
```

## 5. 奖励方案API

### 5.1 方案列表查询
**接口地址**：`GET /reward-scheme/page`

**请求参数**：
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "keyword": "全局",
  "applyScope": 1,  // 1-全局，2-商品类目，3-指定商品
  "status": 1       // 0-禁用，1-启用
}
```

### 5.2 创建奖励方案
**接口地址**：`POST /reward-scheme`

**请求参数**：
```json
{
  "schemeCode": "GLOBAL_001",
  "schemeName": "全局统一奖励方案",
  "schemeDesc": "所有商品统一奖励",
  "applyScope": 1,
  "categoryId": null,
  "spuId": null,
  "skuId": null,
  "enableSalesReward": true,
  "enableProfitSharing": true,
  "profitTarget": 3,  // 1-上级，2-介绍人，3-两者都有
  "maxTraceLevel": 3,
  "levelConfigMode": 1,  // 1-统一配置，2-分等级配置
  "defaultSalesMode": 2,
  "defaultSalesRate": "10.00",
  "defaultProfitMode": 2,
  "defaultProfitRate": "5.00",
  "minOrderAmount": "100.00",
  "maxCommission": "1000.00",
  "triggerStage": 1,  // 1-订单支付后，2-券码核销后，3-订单完成后
  "effectiveType": 1,  // 1-长期有效，2-限时有效
  "startTime": null,
  "endTime": null,
  "priority": 0
}
```

### 5.3 编辑奖励方案
**接口地址**：`PUT /reward-scheme/{id}`

### 5.4 复制奖励方案
**接口地址**：`POST /reward-scheme/{id}/copy`

**请求参数**：
```json
{
  "newSchemeCode": "GLOBAL_002",
  "newSchemeName": "全局奖励方案-副本"
}
```

### 5.5 启用/禁用方案
**接口地址**：`PUT /reward-scheme/{id}/status`

**请求参数**：
```json
{
  "status": 1  // 0-禁用，1-启用
}
```

### 5.6 等级配置列表
**接口地址**：`GET /reward-scheme/{schemeId}/level-configs`

**响应示例**：
```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "schemeId": 1,
      "levelId": 1,
      "levelName": "青铜等级",
      "salesCommissionMode": 2,
      "salesCommissionRate": "8.00",
      "salesCommissionAmount": "0.00",
      "parentProfitConfig": {
        "1": {"mode": 2, "rate": "3.00"},
        "2": {"mode": 2, "rate": "2.00"},
        "3": {"mode": 2, "rate": "1.00"}
      },
      "referrerProfitConfig": {
        "1": {"mode": 2, "rate": "2.00"}
      }
    }
  ],
  "msg": "success"
}
```

### 5.7 保存等级配置
**接口地址**：`POST /reward-scheme/{schemeId}/level-configs`

**请求参数**：
```json
{
  "configs": [
    {
      "levelId": 1,
      "salesCommissionMode": 2,
      "salesCommissionRate": "8.00",
      "parentProfitConfig": {
        "1": {"mode": 2, "rate": "3.00"},
        "2": {"mode": 2, "rate": "2.00"}
      },
      "referrerProfitConfig": {
        "1": {"mode": 2, "rate": "2.00"}
      }
    }
  ]
}
```

### 5.8 批量设置等级配置
**接口地址**：`PUT /reward-scheme/{schemeId}/batch-config`

**请求参数**：
```json
{
  "levelIds": [1, 2, 3, 4],
  "salesCommission": {
    "mode": 2,
    "rate": "10.00"
  },
  "parentProfit": {
    "1": {"mode": 2, "rate": "5.00"},
    "2": {"mode": 2, "rate": "3.00"},
    "3": {"mode": 2, "rate": "1.00"}
  },
  "referrerProfit": {
    "1": {"mode": 2, "rate": "3.00"},
    "2": {"mode": 2, "rate": "2.00"}
  },
  "overrideExisting": true
}
```

### 5.9 方案导出
**接口地址**：`POST /reward-scheme/export`

**请求参数**：
```json
{
  "exportScope": 1,  // 1-当前筛选结果，2-选中的方案，3-全部方案
  "schemeIds": [1, 2],
  "includeDisabled": true,
  "exportFormat": "xlsx"  // xlsx, csv, json
}
```

**响应**：返回文件下载链接

### 5.10 方案导入
**接口地址**：`POST /reward-scheme/import`

**请求参数**：FormData
- file: 导入文件
- autoGenerateCode: true
- overrideExisting: false
- validateReferences: true

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "totalCount": 5,
    "successCount": 4,
    "failCount": 1,
    "details": [
      {
        "row": 1,
        "schemeCode": "GLOBAL_001_NEW",
        "schemeName": "全局统一奖励",
        "status": "success",
        "message": "导入成功"
      },
      {
        "row": 5,
        "schemeCode": "ERROR_001",
        "schemeName": "错误方案",
        "status": "fail",
        "message": "必填字段缺失：minOrderAmount"
      }
    ]
  },
  "msg": "success"
}
```

## 6. 佣金管理API

### 6.1 佣金账单列表
**接口地址**：`GET /commission/bill/page`

**请求参数**：
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "keyword": "张三",  // 分销员/订单号
  "billType": 1,      // 1-分销奖励，2-分润收益
  "status": 1,        // 0-待结算，1-已结算，2-已冻结，3-已取消
  "dateRange": ["2024-03-01", "2024-03-31"]
}
```

### 6.2 账单详情
**接口地址**：`GET /commission/bill/{id}`

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "id": 1,
    "billNo": "B2024031500001",
    "agentId": 1,
    "agentName": "张三",
    "agentCode": "DS001",
    "levelName": "钻石等级",
    "billType": 1,
    "bizType": 1,
    "orderId": 1001,
    "orderNo": "O123456",
    "orderAmount": "1255.00",
    "spuName": "高端红酒套装",
    "amount": "125.50",
    "baseAmount": "1255.00",
    "rate": "10.00",
    "schemeId": 1,
    "schemeName": "GLOBAL_001 - 全局统一奖励方案",
    "freezeStatus": 1,
    "freezeDays": 30,
    "freezeEndTime": "2024-04-14 10:30:25",
    "status": 0,
    "billTime": "2024-03-15 10:30:25"
  },
  "msg": "success"
}
```

### 6.3 提现申请列表
**接口地址**：`GET /withdraw/apply/page`

**请求参数**：
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "keyword": "张三",
  "withdrawType": 1,  // 1-银行卡，2-微信，3-支付宝
  "status": 0,        // 0-待审核，1-审核通过，2-审核拒绝，3-已打款，4-已完成
  "amountRange": ["1000", "5000"]
}
```

### 6.4 提现详情
**接口地址**：`GET /withdraw/apply/{id}`

### 6.5 提现审核
**接口地址**：`PUT /withdraw/apply/{id}/audit`

**请求参数**：
```json
{
  "action": "approve",  // approve-通过，reject-拒绝
  "invoiceType": 1,     // 1-代扣代缴，2-有发票
  "taxRate": "20.00",
  "auditRemark": "审核通过"
}
```

### 6.6 确认打款
**接口地址**：`PUT /withdraw/apply/{id}/payment`

**请求参数**：FormData
- paymentMethod: "支付宝转账"
- voucherFile: 凭证文件
- remark: "已通过支付宝企业付款完成"

### 6.7 提现账单明细
**接口地址**：`GET /withdraw/apply/{id}/bills`

### 6.8 结算记录列表
**接口地址**：`GET /settlement/record/page`

**请求参数**：
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "keyword": "ST202403150001",
  "dateRange": ["2024-03-01", "2024-03-31"]
}
```

### 6.9 结算记录详情
**接口地址**：`GET /settlement/record/{id}`

### 6.10 手动触发结算
**接口地址**：`POST /settlement/trigger`

**请求参数**：
```json
{
  "settlementDate": "2024-03-15"
}
```

## 7. 系统设置API

### 7.1 获取基础配置
**接口地址**：`GET /system/config`

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "applyAuditEnable": true,
    "applyAutoApprove": false,
    "allowNoInviteApply": false,
    "defaultLevelId": 1,
    "freezeDaysAfterPay": 30,
    "freezeDaysAfterRedeem": 7,
    "freezeDaysAfterComplete": 3,
    "withdrawMinAmount": "100.00",
    "withdrawMaxAmount": "50000.00",
    "withdrawTaxRate": "20.00",
    "withdrawDates": [1, 15],
    "maxTraceLevel": 10
  },
  "msg": "success"
}
```

### 7.2 保存基础配置
**接口地址**：`PUT /system/config`

### 7.3 操作日志列表
**接口地址**：`GET /system/operation-log/page`

**请求参数**：
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "operator": "admin",
  "operationType": "创建方案",
  "operationObject": "GLOBAL_001",
  "dateRange": ["2024-03-15", "2024-03-15"]
}
```

## 8. 数据概览API

### 8.1 获取统计概览
**接口地址**：`GET /dashboard/overview`

**请求参数**：
```json
{
  "timeRange": "month"  // today, week, month, year, custom
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "agentStats": {
      "totalCount": 2659,
      "totalCountChange": "+5.8%",
      "monthNewCount": 156,
      "monthNewCountChange": "+12.3%",
      "activeCount": 1823,
      "activeCountChange": "+3.2%",
      "pendingAuditCount": 12
    },
    "salesStats": {
      "monthSales": "1258900.00",
      "monthSalesChange": "+18.5%",
      "monthCommission": "125890.00",
      "monthCommissionChange": "+15.2%",
      "pendingCommission": "45280.00",
      "withdrawnAmount": "80610.00"
    },
    "salesTrend": [
      {
        "date": "2024-03-01",
        "sales": "45280.00",
        "commission": "4528.00"
      }
    ],
    "levelDistribution": [
      {
        "levelName": "青铜等级",
        "count": 1250,
        "percentage": "47.0%"
      }
    ],
    "topAgents": [
      {
        "rank": 1,
        "agentName": "张三",
        "agentCode": "DS001",
        "monthSales": "12580.00"
      }
    ]
  },
  "msg": "success"
}
```

### 8.2 导出统计报表
**接口地址**：`POST /dashboard/export`

**请求参数**：
```json
{
  "reportType": "monthly",  // daily, weekly, monthly
  "dateRange": ["2024-03-01", "2024-03-31"],
  "includeDetails": true
}
```

## 9. 公共接口

### 9.1 获取等级下拉列表
**接口地址**：`GET /common/level/select`

**响应示例**：
```json
{
  "code": 0,
  "data": [
    {
      "value": 1,
      "label": "青铜等级"
    },
    {
      "value": 2,
      "label": "白银等级"
    }
  ],
  "msg": "success"
}
```

### 9.2 获取分销员下拉列表
**接口地址**：`GET /common/agent/select`

**请求参数**：
```json
{
  "keyword": "张三",
  "excludeId": 1  // 排除指定ID
}
```

### 9.3 获取商品类目树
**接口地址**：`GET /common/category/tree`

### 9.4 搜索商品
**接口地址**：`GET /common/product/search`

**请求参数**：
```json
{
  "keyword": "红酒",
  "pageNo": 1,
  "pageSize": 10
}
```

### 9.5 文件上传
**接口地址**：`POST /common/upload`

**请求参数**：FormData
- file: 文件
- type: "image" / "document"

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "url": "https://cdn.example.com/upload/2024/03/xxx.png",
    "fileName": "xxx.png",
    "fileSize": 102400
  },
  "msg": "success"
}
```

### 9.6 下载模板
**接口地址**：`GET /common/template/{type}`

**参数说明**：
- type: reward-scheme（奖励方案导入模板）

## 10. 批量操作API

### 10.1 批量启用/禁用分销员
**接口地址**：`PUT /agent/batch-status`

**请求参数**：
```json
{
  "ids": [1, 2, 3],
  "status": 1,
  "reason": "批量启用"
}
```

### 10.2 批量删除奖励方案
**接口地址**：`DELETE /reward-scheme/batch`

**请求参数**：
```json
{
  "ids": [1, 2, 3]
}
```

### 10.3 批量审核提现申请
**接口地址**：`PUT /withdraw/apply/batch-audit`

**请求参数**：
```json
{
  "ids": [1, 2, 3],
  "action": "approve",
  "invoiceType": 1,
  "taxRate": "20.00",
  "auditRemark": "批量审核通过"
}
```

## 11. WebSocket推送

### 11.1 连接地址
`ws://localhost:48080/ws/distribution`

### 11.2 推送消息类型

**新申请通知**：
```json
{
  "type": "NEW_APPLY",
  "data": {
    "applyId": 1,
    "applyNo": "AP202403150001",
    "applicantName": "王五",
    "applyTime": "2024-03-15 10:30:25"
  }
}
```

**提现申请通知**：
```json
{
  "type": "NEW_WITHDRAW",
  "data": {
    "withdrawId": 1,
    "withdrawNo": "W2024031500001",
    "agentName": "张三",
    "amount": "5000.00",
    "applyTime": "2024-03-15 10:30:25"
  }
}
```

**数据更新通知**：
```json
{
  "type": "DATA_UPDATE",
  "data": {
    "module": "dashboard",
    "updateTime": "2024-03-15 10:30:25"
  }
}
```

## 12. 错误码说明

| 错误码 | 说明 |
|-------|------|
| 0 | 成功 |
| 400 | 参数错误 |
| 401 | 未授权 |
| 403 | 无权限 |
| 404 | 资源不存在 |
| 500 | 系统错误 |
| 10001 | 分销员不存在 |
| 10002 | 分销员已被禁用 |
| 10003 | 申请记录不存在 |
| 10004 | 申请已处理 |
| 10005 | 等级不存在 |
| 10006 | 等级编码已存在 |
| 10007 | 方案编码已存在 |
| 10008 | 余额不足 |
| 10009 | 提现金额超限 |
| 10010 | 文件格式错误 |

## 13. 接口权限说明

| 模块 | 权限标识 | 说明 |
|-----|---------|------|
| 分销员管理 | distribution:agent:query | 查询 |
| 分销员管理 | distribution:agent:create | 创建 |
| 分销员管理 | distribution:agent:update | 更新 |
| 分销员管理 | distribution:agent:delete | 删除 |
| 申请审核 | distribution:apply:query | 查询 |
| 申请审核 | distribution:apply:audit | 审核 |
| 等级管理 | distribution:level:query | 查询 |
| 等级管理 | distribution:level:create | 创建 |
| 等级管理 | distribution:level:update | 更新 |
| 等级管理 | distribution:level:delete | 删除 |
| 奖励配置 | distribution:reward:query | 查询 |
| 奖励配置 | distribution:reward:create | 创建 |
| 奖励配置 | distribution:reward:update | 更新 |
| 奖励配置 | distribution:reward:delete | 删除 |
| 佣金管理 | distribution:commission:query | 查询 |
| 佣金管理 | distribution:commission:export | 导出 |
| 提现管理 | distribution:withdraw:query | 查询 |
| 提现管理 | distribution:withdraw:audit | 审核 |
| 提现管理 | distribution:withdraw:payment | 打款 |
| 系统设置 | distribution:system:query | 查询 |
| 系统设置 | distribution:system:update | 更新 |
| 数据概览 | distribution:dashboard:query | 查询 |
| 数据概览 | distribution:dashboard:export | 导出 |

## 14. 分销商品管理API

### 14.1 分销商品列表查询
**接口地址**：`GET /dist-goods/page`

**请求参数**：
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "keyword": "红酒",     // 商品名称/SPU编码
  "categoryId": 1,      // 类目ID
  "status": 1,          // 状态：0-禁用，1-启用
  "createTime": ["2024-03-01", "2024-03-31"]
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "spuId": 10001,
        "spuCode": "SPU001",
        "spuName": "高端红酒套装",
        "categoryName": "酒水饮料",
        "picUrl": "https://cdn.example.com/products/wine.jpg",
        "price": "1288.00",
        "commissionRate": "10-15%",
        "estimateCommission": "128.80-193.20",
        "salesCount": 156,
        "status": 1,
        "appConfig": {
          "xiaohongshu": {
            "enabled": true,
            "appId": "xhs_123456",
            "appName": "小红书",
            "shareTemplate": "我发现了一款超棒的{product_name}，快来看看吧！"
          },
          "yitong": {
            "enabled": true,
            "appId": "yt_mini_001",
            "appName": "一筒不二小程序",
            "shareTemplate": "好物推荐：{product_name}"
          }
        },
        "createTime": "2024-03-01 10:00:00"
      }
    ],
    "total": 128
  },
  "msg": "success"
}
```

### 14.2 分销商品详情
**接口地址**：`GET /dist-goods/{id}`

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "id": 1,
    "spuId": 10001,
    "spuCode": "SPU001",
    "spuName": "高端红酒套装",
    "categoryId": 5,
    "categoryName": "酒水饮料",
    "picUrl": "https://cdn.example.com/products/wine.jpg",
    "picUrls": [
      "https://cdn.example.com/products/wine1.jpg",
      "https://cdn.example.com/products/wine2.jpg"
    ],
    "description": "精选法国波尔多产区红酒",
    "price": "1288.00",
    "recommendReason": "高佣金比例，销售转化率高",
    "appConfig": {
      "xiaohongshu": {
        "enabled": true,
        "appId": "xhs_123456",
        "appName": "小红书",
        "shareTemplate": "我发现了一款超棒的{product_name}，快来看看吧！",
        "miniProgramPath": "/pages/product/detail?id={spu_id}&dist_code={dist_code}",
        "shareConfig": {
          "title": "{product_name} - 限时特惠",
          "imageUrl": "{product_image}"
        }
      },
      "yitong": {
        "enabled": true,
        "appId": "yt_mini_001",
        "appName": "一筒不二小程序",
        "shareTemplate": "好物推荐：{product_name}",
        "miniProgramPath": "/pages/goods/detail?id={spu_id}&distCode={dist_code}",
        "shareConfig": {
          "title": "{product_name}",
          "imageUrl": "{product_image}"
        }
      },
      "merchant": {
        "enabled": false,
        "appId": "merchant_mini_001",
        "appName": "商家小程序",
        "shareTemplate": "推荐好物：{product_name}",
        "miniProgramPath": "/pages/product?id={spu_id}&ref={dist_code}"
      }
    },
    "status": 1,
    "createTime": "2024-03-01 10:00:00",
    "updateTime": "2024-03-15 14:30:00"
  },
  "msg": "success"
}
```

### 14.3 创建分销商品
**接口地址**：`POST /dist-goods`

**请求参数**：
```json
{
  "spuId": 10002,
  "recommendReason": "新品上市，佣金比例高",
  "appConfig": {
    "xiaohongshu": {
      "enabled": true,
      "appId": "xhs_123456",
      "appName": "小红书",
      "shareTemplate": "新品推荐：{product_name}，赶快来看看！",
      "miniProgramPath": "/pages/product/detail?id={spu_id}&dist_code={dist_code}",
      "shareConfig": {
        "title": "{product_name} - 新品上市",
        "imageUrl": "{product_image}"
      }
    },
    "yitong": {
      "enabled": true,
      "appId": "yt_mini_001",
      "appName": "一筒不二小程序",
      "shareTemplate": "新品推荐：{product_name}",
      "miniProgramPath": "/pages/goods/detail?id={spu_id}&distCode={dist_code}",
      "shareConfig": {
        "title": "{product_name}",
        "imageUrl": "{product_image}"
      }
    }
  },
  "status": 1
}
```

### 14.4 编辑分销商品
**接口地址**：`PUT /dist-goods/{id}`

### 14.5 删除分销商品
**接口地址**：`DELETE /dist-goods/{id}`

### 14.6 批量启用/禁用
**接口地址**：`PUT /dist-goods/batch-status`

**请求参数**：
```json
{
  "ids": [1, 2, 3],
  "status": 1  // 0-禁用，1-启用
}
```

### 14.7 批量配置应用
**接口地址**：`PUT /dist-goods/batch-app-config`

**请求参数**：
```json
{
  "ids": [1, 2, 3],
  "appType": "xiaohongshu",
  "config": {
    "enabled": true,
    "appId": "xhs_123456",
    "appName": "小红书",
    "shareTemplate": "发现好物：{product_name}",
    "miniProgramPath": "/pages/product/detail?id={spu_id}&dist_code={dist_code}"
  }
}
```

## 15. 用户端分销API

### 说明
- 所有用户端接口需要在请求头中包含 `app_id` 参数
- `app_id` 由客户端根据当前所属小程序自动添加
- 服务端会根据 `app_id` 自动筛选对应应用的分销商品和配置

### 15.1 分销商品列表（用户端）
**接口地址**：`GET /app-api/distribution/v1/goods/page`

**请求头**：
```
app_id: xhs_123456  // 客户端自动添加当前小程序的app_id
```

**请求参数**：
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "keyword": "红酒",     // 商品名称搜索
  "categoryId": 1,      // 类目ID
  "sortField": "commission",  // 排序字段：sales-销量，commission-佣金，create_time-上新时间
  "sortOrder": "desc"   // 排序方式：asc-升序，desc-降序
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "spuId": 10001,
        "spuName": "高端红酒套装",
        "picUrl": "https://cdn.example.com/products/wine.jpg",
        "price": "1288.00",
        "originalPrice": "1688.00",
        "salesCount": 156,
        "estimateCommission": "193.20",  // 基于当前用户等级计算
        "commissionRate": "15%",
        "tags": ["热销", "高佣"]
      }
    ],
    "total": 128
  },
  "msg": "success"
}
```

### 15.2 分销商品详情（用户端）
**接口地址**：`GET /app-api/distribution/v1/goods/{spuId}`

**请求头**：
```
app_id: xhs_123456  // 客户端自动添加当前小程序的app_id
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "spuId": 10001,
    "spuName": "高端红酒套装",
    "picUrl": "https://cdn.example.com/products/wine.jpg",
    "picUrls": [
      "https://cdn.example.com/products/wine1.jpg",
      "https://cdn.example.com/products/wine2.jpg"
    ],
    "description": "精选法国波尔多产区红酒，口感醇厚，适合商务宴请和送礼",
    "price": "1288.00",
    "originalPrice": "1688.00",
    "salesCount": 156,
    "stockCount": 999,
    "commission": {
      "estimateAmount": "193.20",
      "rate": "15%",
      "levelName": "钻石等级"
    },
    "shareInfo": {
      "title": "高端红酒套装 - 限时特惠",
      "desc": "我发现了一款超棒的高端红酒套装，快来看看吧！",
      "imageUrl": "https://cdn.example.com/products/wine_share.jpg",
      "shareTemplate": "我发现了一款超棒的{product_name}，快来看看吧！"  // 当前app_id对应的分享模板
    }
  },
  "msg": "success"
}
```

### 15.3 生成分享链接
**接口地址**：`POST /app-api/distribution/v1/goods/share`

**请求头**：
```
app_id: xhs_123456  // 客户端自动添加当前小程序的app_id
```

**请求参数**：
```json
{
  "spuId": 10001
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "shareUrl": "https://xhs.example.com/product/10001?dist_code=DS001",
    "miniProgramPath": "/pages/product/detail?id=10001&dist_code=DS001",
    "shareCode": "DS001",
    "shareContent": {
      "title": "高端红酒套装 - 限时特惠",
      "desc": "我发现了一款超棒的高端红酒套装，快来看看吧！",
      "imageUrl": "https://cdn.example.com/products/wine_share.jpg",
      "miniProgram": {
        "appId": "xhs_123456",  // 根据请求头的app_id返回对应配置
        "path": "/pages/product/detail?id=10001&dist_code=DS001",
        "type": 0  // 0-正式版，1-测试版，2-体验版
      }
    },
    "qrCodeUrl": "https://cdn.example.com/qrcode/share_DS001_10001.png"
  },
  "msg": "success"
}
```

### 15.4 记录分享行为
**接口地址**：`POST /app-api/distribution/v1/goods/share-record`

**请求头**：
```
app_id: xhs_123456  // 客户端自动添加当前小程序的app_id
```

**请求参数**：
```json
{
  "spuId": 10001,
  "shareChannel": 1  // 1-微信好友，2-朋友圈，3-复制链接，4-生成海报
}
```

### 15.5 获取商品佣金信息
**接口地址**：`GET /app-api/distribution/v1/goods/{spuId}/commission`

**请求头**：
```
app_id: xhs_123456  // 客户端自动添加当前小程序的app_id
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "currentLevel": {
      "levelId": 4,
      "levelName": "钻石等级",
      "commissionRate": "15%",
      "estimateCommission": "193.20"
    },
    "allLevels": [
      {
        "levelId": 1,
        "levelName": "青铜等级",
        "commissionRate": "8%",
        "estimateCommission": "103.04"
      },
      {
        "levelId": 2,
        "levelName": "白银等级",
        "commissionRate": "10%",
        "estimateCommission": "128.80"
      },
      {
        "levelId": 3,
        "levelName": "黄金等级",
        "commissionRate": "12%",
        "estimateCommission": "154.56"
      },
      {
        "levelId": 4,
        "levelName": "钻石等级",
        "commissionRate": "15%",
        "estimateCommission": "193.20",
        "isCurrent": true
      }
    ],
    "nextLevelTip": "升级到钻石等级可获得15%佣金"
  },
  "msg": "success"
}
```

### 15.6 获取热销分销商品
**接口地址**：`GET /app-api/distribution/v1/goods/hot`

**请求头**：
```
app_id: xhs_123456  // 客户端自动添加当前小程序的app_id
```

**请求参数**：
```json
{
  "limit": 10  // 获取数量，默认10
}
```

### 15.7 获取高佣商品推荐
**接口地址**：`GET /app-api/distribution/v1/goods/high-commission`

**请求头**：
```
app_id: xhs_123456  // 客户端自动添加当前小程序的app_id
```

**请求参数**：
```json
{
  "limit": 10  // 获取数量，默认10
}
```

### 15.8 分销商品分类列表
**接口地址**：`GET /app-api/distribution/v1/goods/categories`

**请求头**：
```
app_id: xhs_123456  // 客户端自动添加当前小程序的app_id
```

**响应示例**：
```json
{
  "code": 0,
  "data": [
    {
      "categoryId": 1,
      "categoryName": "美妆护肤",
      "iconUrl": "https://cdn.example.com/icons/beauty.png",
      "productCount": 45
    },
    {
      "categoryId": 2,
      "categoryName": "服饰鞋包",
      "iconUrl": "https://cdn.example.com/icons/fashion.png",
      "productCount": 128
    }
  ],
  "msg": "success"
}
```

### 15.9 生成商品分享海报
**接口地址**：`POST /app-api/distribution/v1/goods/poster`

**请求头**：
```
app_id: xhs_123456  // 客户端自动添加当前小程序的app_id
```

**请求参数**：
```json
{
  "spuId": 10001,
  "templateId": 1  // 海报模板ID，可选
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "posterUrl": "https://cdn.example.com/posters/DS001_10001_202403151030.jpg",
    "expireTime": "2024-03-16 10:30:25"  // 海报过期时间
  },
  "msg": "success"
}
```

## 16. 分销数据统计API（用户端）

### 16.1 我的分销数据概览
**接口地址**：`GET /app-api/distribution/v1/my/overview`

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "todayData": {
      "shareCount": 15,
      "viewCount": 238,
      "orderCount": 3,
      "commission": "285.60"
    },
    "monthData": {
      "shareCount": 156,
      "viewCount": 3580,
      "orderCount": 45,
      "commission": "5280.00"
    },
    "totalData": {
      "totalCommission": "125890.00",
      "withdrawnAmount": "119410.00",
      "frozenAmount": "1200.00",
      "availableAmount": "5280.00"
    },
    "rankInfo": {
      "monthRank": 12,
      "totalRank": 8,
      "rankTrend": "up"  // up-上升，down-下降，keep-持平
    }
  },
  "msg": "success"
}
```

### 16.2 分享数据统计
**接口地址**：`GET /app-api/distribution/v1/my/share-stats`

**请求参数**：
```json
{
  "dateRange": ["2024-03-01", "2024-03-31"],
  "spuId": 10001  // 可选，指定商品
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "summary": {
      "totalShares": 156,
      "totalViews": 3580,
      "conversionRate": "1.26%",
      "avgCommission": "117.33"
    },
    "shareDetails": [
      {
        "spuId": 10001,
        "spuName": "高端红酒套装",
        "shareCount": 45,
        "viewCount": 890,
        "orderCount": 12,
        "commission": "2318.40"
      }
    ],
    "dailyTrend": [
      {
        "date": "2024-03-01",
        "shareCount": 5,
        "viewCount": 120,
        "orderCount": 2,
        "commission": "386.40"
      }
    ]
  },
  "msg": "success"
}
```

### 16.3 我的分销客户
**接口地址**：`GET /app-api/distribution/v1/my/customers`

**请求参数**：
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "type": 1  // 1-直属客户，2-间接客户
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "customerId": 1001,
        "customerName": "李**",
        "avatar": "https://cdn.example.com/avatars/1001.jpg",
        "orderCount": 3,
        "totalAmount": "3864.00",
        "totalCommission": "579.60",
        "lastOrderTime": "2024-03-14 15:22:10",
        "registerTime": "2024-02-20 10:30:00"
      }
    ],
    "total": 45
  },
  "msg": "success"
}
```

### 16.4 我的佣金明细
**接口地址**：`GET /app-api/distribution/v1/my/commission`

**请求参数**：
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "type": 0,  // 0-全部，1-销售奖励，2-分润收益
  "status": 0,  // 0-全部，1-待结算，2-已结算，3-已提现
  "dateRange": ["2024-03-01", "2024-03-31"]
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "billNo": "B2024031500001",
        "type": 1,
        "typeName": "销售奖励",
        "orderNo": "O123456",
        "goodsName": "高端红酒套装",
        "amount": "193.20",
        "status": 1,
        "statusName": "待结算",
        "freezeEndTime": "2024-04-14 10:30:25",
        "createTime": "2024-03-15 10:30:25"
      }
    ],
    "total": 126,
    "summary": {
      "totalAmount": "5280.00",
      "pendingAmount": "1200.00",
      "settledAmount": "4080.00",
      "withdrawnAmount": "3500.00"
    }
  },
  "msg": "success"
}
```

### 16.5 佣金提现申请
**接口地址**：`POST /app-api/distribution/v1/my/withdraw`

**请求参数**：
```json
{
  "amount": "1000.00",
  "type": 1,  // 1-银行卡，2-微信，3-支付宝
  "accountInfo": {
    "bankName": "工商银行",
    "accountNo": "6222021234567890123",
    "accountName": "张三",
    "branchName": "北京市朝阳支行"
  },
  "invoiceInfo": {
    "hasInvoice": true,
    "invoiceType": 2,  // 1-普通发票，2-专票
    "invoiceNo": "INV202403150001",
    "invoiceFile": "https://cdn.example.com/invoices/001.pdf"
  },
  "remark": "佣金提现"
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "withdrawId": 1,
    "withdrawNo": "W2024031500001",
    "status": 0,
    "statusName": "待审核",
    "estimateArrivalTime": "2024-03-18 18:00:00"
  },
  "msg": "success"
}
```

### 16.6 提现记录列表
**接口地址**：`GET /app-api/distribution/v1/my/withdraw-list`

**请求参数**：
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "status": 0  // 0-全部，1-待审核，2-审核通过，3-审核拒绝，4-已打款，5-已完成
}
```

### 16.7 提现详情
**接口地址**：`GET /app-api/distribution/v1/my/withdraw/{id}`

### 16.8 我的团队
**接口地址**：`GET /app-api/distribution/v1/my/team`

**请求参数**：
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "level": 1  // 1-一级，2-二级，3-三级，0-全部
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "agentId": 2,
        "agentName": "李四",
        "agentCode": "DS002",
        "levelName": "黄金等级",
        "avatar": "https://cdn.example.com/avatars/2.jpg",
        "teamCount": 45,
        "monthSales": "8350.00",
        "monthCommission": "835.00",
        "joinTime": "2024-02-01 14:20:30",
        "depth": 1  // 层级深度
      }
    ],
    "total": 128,
    "summary": {
      "level1Count": 15,
      "level2Count": 68,
      "level3Count": 45,
      "totalTeamSales": "68930.00",
      "totalTeamCommission": "6893.00"
    }
  },
  "msg": "success"
}
```

### 16.9 分销员信息
**接口地址**：`GET /app-api/distribution/v1/my/info`

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "agentId": 1,
    "agentCode": "DS001",
    "agentName": "张三",
    "avatar": "https://cdn.example.com/avatars/1.jpg",
    "phone": "138****1234",
    "levelId": 4,
    "levelName": "钻石等级",
    "levelIcon": "https://cdn.example.com/icons/diamond.png",
    "joinTime": "2024-01-15 10:30:25",
    "parentName": null,
    "qrCodeUrl": "https://cdn.example.com/qrcode/DS001.png",
    "inviteUrl": "https://app.example.com/invite?code=DS001",
    "status": 1,
    "statusName": "正常"
  },
  "msg": "success"
}
```

### 16.10 更新分销员信息
**接口地址**：`PUT /app-api/distribution/v1/my/info`

**请求参数**：
```json
{
  "agentName": "张三",
  "avatar": "https://cdn.example.com/avatars/1_new.jpg"
}
```

## 17. 分销申请API（用户端）

### 17.1 申请成为分销员
**接口地址**：`POST /app-api/distribution/v1/apply`

**请求参数**：
```json
{
  "realName": "王五",
  "phone": "13912345678",
  "idCard": "110101199001011234",
  "inviteCode": "DS001",  // 邀请码（可选）
  "levelId": 1,  // 申请的等级ID（可选）
  "applyReason": "申请理由",
  "experience": "相关经验"
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "applyId": 1,
    "applyNo": "AP202403150001",
    "status": 0,
    "statusName": "待审核",
    "estimateTime": "1-3个工作日"
  },
  "msg": "success"
}
```

### 17.2 查询申请状态
**接口地址**：`GET /app-api/distribution/v1/apply/status`

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "hasApplied": true,
    "latestApply": {
      "applyId": 1,
      "applyNo": "AP202403150001",
      "status": 0,
      "statusName": "待审核",
      "applyTime": "2024-03-15 10:30:25",
      "auditTime": null,
      "auditRemark": null
    },
    "isAgent": false,
    "agentInfo": null
  },
  "msg": "success"
}
```

### 17.3 获取可申请等级列表
**接口地址**：`GET /app-api/distribution/v1/apply/levels`

**响应示例**：
```json
{
  "code": 0,
  "data": [
    {
      "levelId": 1,
      "levelName": "青铜等级",
      "levelIcon": "https://cdn.example.com/icons/bronze.png",
      "description": "初级分销员，享受8%佣金",
      "benefits": [
        "专属客服支持",
        "每月免费海报10张",
        "基础培训课程"
      ],
      "requirements": "无门槛要求",
      "canApply": true
    },
    {
      "levelId": 2,
      "levelName": "白银等级",
      "levelIcon": "https://cdn.example.com/icons/silver.png",
      "description": "中级分销员，享受10%佣金",
      "benefits": [
        "专属客服支持",
        "每月免费海报20张",
        "进阶培训课程",
        "优先发货权"
      ],
      "requirements": "需有特定邀请码",
      "canApply": false,
      "disableReason": "需要邀请码"
    }
  ],
  "msg": "success"
}
```

## 18. 错误码说明（用户端）

| 错误码 | 说明 |
|-------|——|
| 20001 | 未登录或登录已过期 |
| 20002 | 您还不是分销员 |
| 20003 | 分销员账号已被禁用 |
| 20004 | 商品不存在或已下架 |
| 20005 | 该商品暂不支持分销 |
| 20006 | 提现金额不足 |
| 20007 | 提现金额超出限制 |
| 20008 | 您已申请过分销员 |
| 20009 | 邀请码无效 |
| 20010 | 申请信息不完整 |