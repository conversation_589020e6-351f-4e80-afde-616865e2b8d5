# 分销代理系统详细设计文档 v1.1

## 1. 概述

本文档基于v1.0版本进行优化，主要改进点：
1. 统一奖励配置体系，合并销售奖和分润奖的配置
2. 支持按商品/全局、按等级/统一的灵活配置
3. 简化数据结构，提升系统可维护性

## 2. 核心需求分析

### 2.1 自定义分销
- **需求描述**：支持自定义分销等级，通过等级级别（level_grade）区分高低。
- **设计要点**：
  - 等级可灵活配置（数量、名称、条件）
  - 通过level_grade数值大小判断等级高低
  - 支持升降级条件配置
  - 等级变更自动触发相关业务流程

### 2.2 分销员申请与绑定
- **需求描述**：人员申请成为分销员后，有自己的分销码。其他人员根据分销码，申请或者注册就成功绑定其为分销员的子分销员。
- **设计要点**：
  - 每个分销员拥有唯一的分销码
  - 区分三种关系：
    - **上级**：管理关系，决定组织架构和团队归属
    - **等级**：决定分销员的分润比例和权益
    - **介绍人**：推荐关系，申请时谁介绍的（可有可无）
  - 支持通过分销码建立关系
  - **新注册用户**：通过分销码注册时，分销码拥有者同时成为其上级和介绍人
  - **已注册用户**：申请时可灵活选择绑定关系类型

### 2.3 统一奖励配置体系【v1.1优化】
- **需求描述**：统一管理销售奖励和分润奖励，支持灵活的配置方式
- **设计要点**：
  - 统一奖励方案表，支持配置销售奖和分润奖
  - 支持按商品或全局配置
  - 支持按分销等级分别设置或统一设置
  - 灵活的奖励规则组合
  - 实时计算与结算

## 3. 数据库设计

### 3.1 分销等级表（yt_dist_level）
```sql
CREATE TABLE `yt_dist_level` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '等级ID',
  `level_code` varchar(32) NOT NULL COMMENT '等级编码',
  `level_name` varchar(64) NOT NULL COMMENT '等级名称',
  `level_grade` int(11) NOT NULL COMMENT '等级级别，数值越大等级越高',
  `invite_code` varchar(32) DEFAULT NULL COMMENT '等级邀请码',
  `icon_url` varchar(256) DEFAULT NULL COMMENT '等级图标',
  `color` varchar(32) DEFAULT NULL COMMENT '等级颜色',
  `description` text COMMENT '等级说明',
  `benefits` text COMMENT '等级权益描述',
  `upgrade_conditions` json DEFAULT NULL COMMENT '升级条件（JSON格式）',
  `downgrade_conditions` json DEFAULT NULL COMMENT '降级条件（JSON格式）',
  `auto_upgrade` tinyint(1) DEFAULT '1' COMMENT '是否自动升级',
  `auto_downgrade` tinyint(1) DEFAULT '0' COMMENT '是否自动降级',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序序号',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_level_code` (`level_code`),
  UNIQUE KEY `uk_invite_code` (`invite_code`),
  KEY `idx_level_grade` (`level_grade`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销等级表';
```

### 3.2 分销员信息表（yt_dist_agent）
```sql
CREATE TABLE `yt_dist_agent` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分销员ID',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `agent_code` varchar(32) NOT NULL COMMENT '分销员编码（分销码）',
  `agent_name` varchar(64) NOT NULL COMMENT '分销员名称',
  `level_id` bigint(20) NOT NULL COMMENT '等级ID',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '直接上级分销员ID（管理关系）',
  `parent_code` varchar(32) DEFAULT NULL COMMENT '上级分销码',
  `referrer_id` bigint(20) DEFAULT NULL COMMENT '介绍人ID（推荐关系）',
  `referrer_code` varchar(32) DEFAULT NULL COMMENT '介绍人分销码',
  `referrer_time` datetime DEFAULT NULL COMMENT '介绍时间',
  `path` varchar(512) DEFAULT NULL COMMENT '分销路径，如：1,2,3',
  `depth` int(11) DEFAULT '1' COMMENT '层级深度，顶级为1',
  `bind_time` datetime DEFAULT NULL COMMENT '绑定上级时间',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `team_count` int(11) DEFAULT '0' COMMENT '团队人数（包含所有下级）',
  `direct_count` int(11) DEFAULT '0' COMMENT '直属下级人数',
  `referral_count` int(11) DEFAULT '0' COMMENT '介绍人数',
  `total_sales` decimal(10,2) DEFAULT '0.00' COMMENT '累计销售额',
  `month_sales` decimal(10,2) DEFAULT '0.00' COMMENT '本月销售额',
  `join_time` datetime NOT NULL COMMENT '成为分销员时间',
  `level_update_time` datetime DEFAULT NULL COMMENT '等级更新时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_member_id` (`member_id`),
  UNIQUE KEY `uk_agent_code` (`agent_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_parent_code` (`parent_code`),
  KEY `idx_referrer_id` (`referrer_id`),
  KEY `idx_referrer_code` (`referrer_code`),
  KEY `idx_level_id` (`level_id`),
  KEY `idx_status` (`status`),
  KEY `idx_depth` (`depth`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员信息表';
```

### 3.3 奖励方案主表（yt_dist_reward_scheme）【v1.1新增】
```sql
CREATE TABLE `yt_dist_reward_scheme` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '方案ID',
  `scheme_code` varchar(32) NOT NULL COMMENT '方案编码',
  `scheme_name` varchar(64) NOT NULL COMMENT '方案名称',
  `scheme_desc` varchar(256) DEFAULT NULL COMMENT '方案描述',
  `apply_scope` tinyint(4) NOT NULL DEFAULT '1' COMMENT '适用范围：1-全局，2-指定商品类目，3-指定商品',
  `category_id` bigint(20) DEFAULT NULL COMMENT '商品类目ID（apply_scope=2时使用）',
  `spu_id` bigint(20) DEFAULT NULL COMMENT 'SPU ID（apply_scope=3时使用）',
  `sku_id` bigint(20) DEFAULT NULL COMMENT 'SKU ID（apply_scope=3时使用，可选）',
  `enable_sales_reward` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用销售奖励',
  `enable_profit_sharing` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用分润奖励',
  `profit_target` tinyint(4) DEFAULT '1' COMMENT '分润对象：1-上级，2-介绍人，3-两者都有',
  `max_trace_level` int(11) DEFAULT '3' COMMENT '最大追溯层级',
  `level_config_mode` tinyint(4) NOT NULL DEFAULT '1' COMMENT '等级配置模式：1-统一配置，2-分等级配置',
  `default_sales_mode` tinyint(4) DEFAULT '2' COMMENT '默认销售佣金模式：1-固定金额，2-百分比',
  `default_sales_rate` decimal(5,2) DEFAULT '0.00' COMMENT '默认销售佣金比例（%）',
  `default_sales_amount` decimal(10,2) DEFAULT '0.00' COMMENT '默认销售固定佣金',
  `default_profit_mode` tinyint(4) DEFAULT '2' COMMENT '默认分润佣金模式：1-固定金额，2-百分比',
  `default_profit_rate` decimal(5,2) DEFAULT '0.00' COMMENT '默认分润佣金比例（%）',
  `default_profit_amount` decimal(10,2) DEFAULT '0.00' COMMENT '默认分润固定佣金',
  `min_order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最低订单金额',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金限制',
  `trigger_stage` tinyint(4) DEFAULT '1' COMMENT '触发阶段：1-订单支付后，2-券码核销后，3-订单完成后（订单下券码全部核销或部分核销部分退款）',
  `effective_type` tinyint(4) DEFAULT '1' COMMENT '生效类型：1-长期有效，2-限时有效',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `priority` int(11) DEFAULT '0' COMMENT '优先级，数值越大优先级越高',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_code` (`scheme_code`),
  KEY `idx_apply_scope` (`apply_scope`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_spu_id` (`spu_id`),
  KEY `idx_sku_id` (`sku_id`),
  KEY `idx_level_config_mode` (`level_config_mode`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='奖励方案主表（类似SPU）';
```

### 3.4 等级奖励配置表（yt_dist_reward_level_config）【v1.1新增】
```sql
CREATE TABLE `yt_dist_reward_level_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `scheme_id` bigint(20) NOT NULL COMMENT '方案ID',
  `level_id` bigint(20) NOT NULL COMMENT '分销等级ID',
  `level_name` varchar(64) DEFAULT NULL COMMENT '等级名称（冗余）',
  `sales_commission_mode` tinyint(4) DEFAULT '2' COMMENT '销售佣金模式：1-固定金额，2-百分比',
  `sales_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '销售佣金比例（%）',
  `sales_commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '销售固定佣金金额',
  `parent_profit_config` json DEFAULT NULL COMMENT '上级分润配置（JSON格式，包含各级分润比例）',
  `referrer_profit_config` json DEFAULT NULL COMMENT '介绍人分润配置（JSON格式，包含各级分润比例）',
  `condition_type` tinyint(4) DEFAULT '1' COMMENT '条件类型：1-无条件，2-基于销售额，3-基于销售量',
  `condition_config` json DEFAULT NULL COMMENT '条件配置（JSON格式）',
  `min_order_amount` decimal(10,2) DEFAULT NULL COMMENT '最低订单金额（覆盖方案默认值）',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金（覆盖方案默认值）',
  `remark` varchar(256) DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_level` (`scheme_id`, `level_id`),
  KEY `idx_scheme_id` (`scheme_id`),
  KEY `idx_level_id` (`level_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='等级奖励配置表（类似SKU）';
```

### 3.5 分销商品配置表（yt_dist_product_config）【v1.1新增】
```sql
CREATE TABLE `yt_dist_product_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `spu_id` bigint(20) NOT NULL COMMENT 'SPU ID',
  `spu_name` varchar(128) NOT NULL COMMENT 'SPU名称',
  `cover_image` varchar(256) NOT NULL COMMENT '商品首图',
  `price_range` varchar(64) NOT NULL COMMENT '价格范围，如：99.00-299.00',
  `min_price` decimal(10,2) NOT NULL COMMENT '最低价格',
  `max_price` decimal(10,2) NOT NULL COMMENT '最高价格',
  `commission_display_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '佣金展示类型：1-百分比，2-金额',
  `commission_display_value` varchar(32) NOT NULL COMMENT '佣金展示值，如：10%或¥10-50',
  `estimated_commission` varchar(64) NOT NULL COMMENT '预计佣金展示文案',
  `share_title` varchar(128) DEFAULT NULL COMMENT '分享标题',
  `share_desc` varchar(256) DEFAULT NULL COMMENT '分享描述',
  `share_image` varchar(256) DEFAULT NULL COMMENT '分享图片',
  `enable_distribution` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用分销',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序序号',
  `show_in_list` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否在列表显示',
  `tags` varchar(256) DEFAULT NULL COMMENT '商品标签，逗号分隔',
  `app_config` json DEFAULT NULL COMMENT '应用配置（JSON格式，包含不同小程序的配置）',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_spu_id` (`spu_id`),
  KEY `idx_enable_distribution` (`enable_distribution`),
  KEY `idx_show_in_list` (`show_in_list`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销商品配置表';
```

### 3.6 分销员申请记录表（yt_dist_agent_apply_record）
```sql
CREATE TABLE `yt_dist_agent_apply_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `apply_no` varchar(64) NOT NULL COMMENT '申请编号',
  `member_id` bigint(20) NOT NULL COMMENT '申请人会员ID',
  `member_name` varchar(64) DEFAULT NULL COMMENT '会员名称',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `invite_code` varchar(32) DEFAULT NULL COMMENT '使用的邀请码',
  `invite_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '邀请码类型：1-分销员邀请码，2-等级邀请码',
  `initial_level_id` bigint(20) DEFAULT NULL COMMENT '初始等级ID',
  `parent_agent_id` bigint(20) DEFAULT NULL COMMENT '上级分销员ID',
  `referrer_agent_id` bigint(20) DEFAULT NULL COMMENT '介绍人ID',
  `bind_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '绑定类型：1-同时绑定，2-仅上级，3-仅介绍人',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待审核，1-审核通过，2-审核拒绝',
  `agent_id` bigint(20) DEFAULT NULL COMMENT '分销员ID（审核通过后生成）',
  `agent_code` varchar(32) DEFAULT NULL COMMENT '分销码（审核通过后生成）',
  `apply_time` datetime NOT NULL COMMENT '申请时间',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_user` varchar(64) DEFAULT NULL COMMENT '审核人',
  `audit_remark` varchar(256) DEFAULT NULL COMMENT '审核备注',
  `auto_audit` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否自动审核',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_apply_no` (`apply_no`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_invite_code` (`invite_code`),
  KEY `idx_status` (`status`),
  KEY `idx_apply_time` (`apply_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员申请记录表';
```

### 3.6 佣金账单记录表（yt_dist_agent_bill_record）
```sql
CREATE TABLE `yt_dist_agent_bill_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '账单ID',
  `bill_no` varchar(64) NOT NULL COMMENT '账单编号',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `agent_name` varchar(64) NOT NULL COMMENT '分销员名称',
  `agent_level_id` bigint(20) NOT NULL COMMENT '分销员等级ID（快照）',
  `bill_type` tinyint(4) NOT NULL COMMENT '账单类型：1-分销奖励，2-分润收益',
  `biz_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '业务类型：1-商品订单，2-优惠券核销',
  `biz_id` bigint(20) NOT NULL COMMENT '业务ID（订单ID或优惠券ID）',
  `biz_no` varchar(64) NOT NULL COMMENT '业务编号（订单号或券码）',
  `order_id` bigint(20) DEFAULT NULL COMMENT '订单ID',
  `order_amount` decimal(10,2) DEFAULT NULL COMMENT '订单金额',
  `channel_order_id` varchar(64) DEFAULT NULL COMMENT '渠道订单号',
  `coupon_id` bigint(20) DEFAULT NULL COMMENT '券码ID',
  `coupon_code` varchar(64) DEFAULT NULL COMMENT '券码编号',
  `spu_id` bigint(20) DEFAULT NULL COMMENT 'SPU ID',
  `spu_name` varchar(128) DEFAULT NULL COMMENT 'SPU名称',
  `sku_id` bigint(20) DEFAULT NULL COMMENT 'SKU ID',
  `sku_name` varchar(128) DEFAULT NULL COMMENT 'SKU名称',
  `source_agent_id` bigint(20) DEFAULT NULL COMMENT '来源分销员ID（产生业绩的分销员）',
  `source_agent_name` varchar(64) DEFAULT NULL COMMENT '来源分销员名称',
  `trace_level` int(11) DEFAULT '0' COMMENT '追溯层级：0-直接销售，1-一级，2-二级，3-三级',
  `amount` decimal(10,2) NOT NULL COMMENT '账单金额（正数为收入，负数为退款）',
  `base_amount` decimal(10,2) NOT NULL COMMENT '计算基数金额',
  `rate` decimal(5,2) DEFAULT NULL COMMENT '佣金比例（%）',
  `scheme_id` bigint(20) NOT NULL COMMENT '使用的方案ID',
  `config_id` bigint(20) DEFAULT NULL COMMENT '使用的配置ID',
  `scheme_snapshot` json DEFAULT NULL COMMENT '方案快照（JSON格式）',
  `trigger_stage` tinyint(4) DEFAULT '1' COMMENT '触发阶段：1-订单支付后，2-券码核销后，3-订单完成后（订单下券码全部核销或部分核销部分退款）',
  `freeze_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '冻结状态：0-未冻结，1-冻结中',
  `freeze_days` int(11) DEFAULT '0' COMMENT '冻结天数',
  `freeze_end_time` datetime DEFAULT NULL COMMENT '冻结结束时间',
  `unfreeze_time` datetime DEFAULT NULL COMMENT '实际解冻时间',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待结算，1-已结算，2-已冻结，3-已取消',
  `settle_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '结算状态：0-未结算，1-部分结算，2-已结算',
  `settled_amount` decimal(10,2) DEFAULT '0.00' COMMENT '已结算金额',
  `frozen_reason` varchar(256) DEFAULT NULL COMMENT '冻结原因',
  `remark` varchar(512) DEFAULT NULL COMMENT '备注',
  `bill_time` datetime NOT NULL COMMENT '账单时间',
  `settle_time` datetime DEFAULT NULL COMMENT '结算时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bill_no` (`bill_no`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_source_agent_id` (`source_agent_id`),
  KEY `idx_biz_id_type` (`biz_id`, `biz_type`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_coupon_id` (`coupon_id`),
  KEY `idx_spu_id` (`spu_id`),
  KEY `idx_sku_id` (`sku_id`),
  KEY `idx_status` (`status`),
  KEY `idx_bill_time` (`bill_time`),
  KEY `idx_freeze_status` (`freeze_status`),
  KEY `idx_freeze_end_time` (`freeze_end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='佣金账单记录表';
```

### 3.7 分销员账户余额表（yt_dist_agent_balance）
```sql
CREATE TABLE `yt_dist_agent_balance` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '余额ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `total_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '累计佣金',
  `frozen_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '冻结金额',
  `available_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '可提现余额',
  `withdrawing_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '提现中金额',
  `withdrawn_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '已提现金额',
  `balance_version` bigint(20) NOT NULL DEFAULT '0' COMMENT '余额版本号（乐观锁）',
  `last_bill_id` bigint(20) DEFAULT NULL COMMENT '最后处理的账单ID',
  `last_calculate_time` datetime DEFAULT NULL COMMENT '最后计算时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agent_id` (`agent_id`),
  KEY `idx_balance_version` (`balance_version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员账户余额表';
```

### 3.8 分销员提现申请表（yt_dist_agent_withdraw_apply）
```sql
CREATE TABLE `yt_dist_agent_withdraw_apply` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '提现申请ID',
  `withdraw_no` varchar(64) NOT NULL COMMENT '提现申请编号',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `agent_name` varchar(64) NOT NULL COMMENT '分销员名称',
  `agent_level_id` bigint(20) NOT NULL COMMENT '分销员等级ID',
  `apply_amount` decimal(10,2) NOT NULL COMMENT '申请提现金额',
  `withdraw_type` tinyint(4) NOT NULL COMMENT '提现方式：1-银行卡，2-微信，3-支付宝',
  `account_info` json NOT NULL COMMENT '收款账户信息（JSON格式）',
  `invoice_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '发票类型：1-无发票（代扣申报），2-有发票',
  `invoice_amount` decimal(10,2) DEFAULT NULL COMMENT '发票金额',
  `invoice_urls` json DEFAULT NULL COMMENT '发票图片URL列表',
  `tax_rate` decimal(5,2) DEFAULT '0.00' COMMENT '税率（%）',
  `tax_amount` decimal(10,2) DEFAULT '0.00' COMMENT '税费金额',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实际打款金额',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待财务审核，1-审核通过，2-审核拒绝，3-已打款，4-已完成',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_user` varchar(64) DEFAULT NULL COMMENT '审核人',
  `audit_remark` varchar(256) DEFAULT NULL COMMENT '审核备注',
  `payment_time` datetime DEFAULT NULL COMMENT '打款时间',
  `payment_user` varchar(64) DEFAULT NULL COMMENT '打款人',
  `payment_voucher` varchar(256) DEFAULT NULL COMMENT '打款凭证',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `remark` varchar(512) DEFAULT NULL COMMENT '备注',
  `apply_time` datetime NOT NULL COMMENT '申请时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_withdraw_no` (`withdraw_no`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_apply_time` (`apply_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员提现申请表';
```

### 3.10 分销商品配置优化方案【v1.2更新】

#### 问题分析
当前v1.1版本的分销商品配置存在以下问题：
1. 需要单独维护分销商品表，与商品中心脱节
2. 每个应用的配置存在JSON字段中，不利于查询和维护  
3. 配置操作路径长，需要在多个地方维护

#### 优化方案设计

##### 3.10.1 商品分销扩展表（yt_product_dist_ext）
```sql
-- 直接挂靠在商品主表上的分销扩展信息
CREATE TABLE `yt_product_dist_ext` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `spu_id` bigint(20) NOT NULL COMMENT 'SPU ID',
  `enable_distribution` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用分销',
  `dist_priority` int(11) DEFAULT '0' COMMENT '分销优先级（数值越大优先级越高）',
  `dist_tags` varchar(256) DEFAULT NULL COMMENT '分销标签，逗号分隔（热销、高佣、新品等）',
  `recommend_reason` varchar(256) DEFAULT NULL COMMENT '推荐理由',
  `commission_hint` varchar(64) DEFAULT NULL COMMENT '佣金提示（如：最高可赚15%）',
  `share_template_id` bigint(20) DEFAULT NULL COMMENT '分享模板ID',
  `custom_share_data` json DEFAULT NULL COMMENT '自定义分享数据（覆盖模板）',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_spu_id` (`spu_id`),
  KEY `idx_enable_distribution` (`enable_distribution`),
  KEY `idx_dist_priority` (`dist_priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分销扩展表';
```

##### 3.10.2 分享模板表（yt_dist_share_template）
```sql
-- 统一管理分享模板，支持复用
CREATE TABLE `yt_dist_share_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_code` varchar(32) NOT NULL COMMENT '模板编码',
  `template_name` varchar(64) NOT NULL COMMENT '模板名称',
  `template_type` varchar(32) NOT NULL COMMENT '模板类型：product/category/global',
  `apply_scope` varchar(64) DEFAULT NULL COMMENT '适用范围（商品类目等）',
  `share_content` json NOT NULL COMMENT '分享内容配置',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认模板',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_code` (`template_code`),
  KEY `idx_template_type` (`template_type`),
  KEY `idx_is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分享模板表';
```

##### 3.10.3 应用分享配置表（yt_app_share_config）
```sql
-- 每个应用的分享配置
CREATE TABLE `yt_app_share_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` varchar(64) NOT NULL COMMENT '应用ID',
  `app_name` varchar(64) NOT NULL COMMENT '应用名称',
  `app_type` varchar(32) NOT NULL COMMENT '应用类型：wechat/xiaohongshu/merchant',
  `mini_program_id` varchar(64) DEFAULT NULL COMMENT '小程序AppID',
  `share_path_template` varchar(256) NOT NULL COMMENT '分享路径模板',
  `default_template_id` bigint(20) DEFAULT NULL COMMENT '默认分享模板ID',
  `share_config` json DEFAULT NULL COMMENT '其他分享配置',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序序号',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_id` (`app_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用分享配置表';
```

#### 数据结构说明

##### 分享模板share_content字段结构：
```json
{
  "default": {
    "title": "发现好物：{product_name}",
    "desc": "我发现了一款超棒的{product_name}，{recommend_reason}，快来看看吧！",
    "imageType": "main",  // main-主图, custom-自定义, template-模板图
    "imageUrl": "{product_image}"
  },
  "wechat": {
    "title": "{product_name} | {commission_hint}",
    "desc": "限时特惠，速来抢购！"
  },
  "xiaohongshu": {
    "title": "笔记推荐 | {product_name}",
    "desc": "姐妹们快来看！{product_name}真的太香了！{recommend_reason}",
    "tags": ["好物推荐", "必买清单"]
  }
}
```

##### 应用配置share_config字段结构：
```json
{
  "variables": {
    "product_name": "商品名称",
    "product_image": "商品主图",
    "product_price": "商品价格",
    "commission_hint": "佣金提示",
    "recommend_reason": "推荐理由",
    "dist_code": "分销码",
    "spu_id": "商品ID"
  },
  "miniProgram": {
    "type": 0,  // 0-正式版，1-测试版，2-体验版
    "requireAuth": true  // 是否需要授权
  }
}
```

## 4. 核心功能设计

### 4.1 统一奖励配置体系设计【v1.1核心优化】

#### 4.1.1 设计理念
采用类似SPU/SKU的设计模式，将奖励方案分为两层：

1. **奖励方案主表（类似SPU）**
   - 定义奖励方案的整体策略
   - 设置销售奖励和分润奖励的开关
   - 定义适用范围和默认配置
   - 统一管理触发条件和有效期

2. **等级奖励配置表（类似SKU）**
   - 基于奖励方案，为每个等级定义具体配置
   - 覆盖或继承方案的默认设置
   - 支持差异化的销售和分润配置

#### 4.1.2 配置结构优势

1. **灵活的开关控制**
   - 独立控制销售奖励和分润奖励
   - 支持只开启销售奖、只开启分润或两者都开启

2. **等级配置模式**
   - 统一配置：使用方案主表的默认配置
   - 分等级配置：每个等级有自己的配置记录

3. **配置继承机制**
   - 等级配置可以覆盖方案的默认值
   - 未配置的字段自动继承方案默认值

#### 4.1.3 UI设计示意

**1. 奖励方案列表页**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 奖励方案管理                                [导出] [导入] [新增方案] │
├─────────────────────────────────────────────────────────────────────┤
│ 搜索：[方案名称/编码] [适用范围▼] [状态▼] [查询] [重置]           │
├─────────────────────────────────────────────────────────────────────┤
│ [✓] | 方案编码 | 方案名称 | 适用范围 | 销售奖 | 分润奖 | 优先级 | 操作 │
├─────────────────────────────────────────────────────────────────────┤
│ [✓] | GLOBAL_001 | 全局统一奖励 | 全局 | ✓ | ✓ | 0 | [编辑][复制][禁用] │
│ [ ] | SPU_001 | 高端商品奖励 | 商品:红酒 | ✓ | ✓ | 100 | [编辑][复制][禁用] │
│ [✓] | LEVEL_001 | 分等级销售奖 | 全局 | ✓ | ✗ | 50 | [编辑][复制][启用] │
└─────────────────────────────────────────────────────────────────────┘
已选择 2 项 | [批量启用] [批量禁用] [批量删除] [批量导出]
```

**2. 创建/编辑奖励方案页面（标签页设计）**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 新增奖励方案                                    [保存] [保存并继续] │
├─────────────────────────────────────────────────────────────────────┤
│ [基础信息] [奖励配置] [等级设置] [高级设置]                        │
├─────────────────────────────────────────────────────────────────────┤
│ 基础信息                                                            │
│ ┌─────────────────────────────────────────────────────────────┐   │
│ │ 方案编码*：[_______________] （唯一标识，保存后不可修改）    │   │
│ │ 方案名称*：[_______________________________]                │   │
│ │ 方案描述：[_______________________________]                 │   │
│ │                                                              │   │
│ │ 适用范围*：(●) 全局  ( ) 商品类目  ( ) 指定商品            │   │
│ │           类目：[选择类目▼]  商品：[选择商品▼]            │   │
│ │                                                              │   │
│ │ 触发阶段*：[订单支付后 ▼]                                  │   │
│ │ 生效时间：[长期有效 ▼] 开始：[____] 结束：[____]          │   │
│ │ 优先级：  [0_____] （数值越大优先级越高）                   │   │
│ └─────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────┘
```

**3. 奖励配置标签页（核心配置）**
```
┌─────────────────────────────────────────────────────────────────────┐
│ [基础信息] [奖励配置] [等级设置] [高级设置]                        │
├─────────────────────────────────────────────────────────────────────┤
│ 奖励配置                                                            │
│ ┌─────────────────────────────────────────────────────────────┐   │
│ │ 销售奖励 [✓] 启用                                           │   │
│ │ ├─ 配置模式：(●) 统一配置  ( ) 分等级配置                 │   │
│ │ └─ 默认配置：[百分比 ▼] [10.00]% 最低订单金额：[100]元   │   │
│ │                                                              │   │
│ │ 分润奖励 [✓] 启用                                           │   │
│ │ ├─ 分润对象：[✓] 上级  [✓] 介绍人                         │   │
│ │ ├─ 最大追溯层级：[3___] 级                                 │   │
│ │ └─ 默认配置：[百分比 ▼] [5.00]%                           │   │
│ │                                                              │   │
│ │ 单笔最高佣金限制：[1000____] 元（选填）                     │   │
│ └─────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────┘
```

**4. 等级设置标签页（分等级配置时显示）**
```
┌─────────────────────────────────────────────────────────────────────┐
│ [基础信息] [奖励配置] [等级设置] [高级设置]                        │
├─────────────────────────────────────────────────────────────────────┤
│ 等级差异化配置                                    [批量设置]        │
│ ┌─────────────────────────────────────────────────────────────┐   │
│ │ 等级名称 | 销售佣金 | 上级分润(1级|2级|3级) | 介绍人分润    │   │
│ ├─────────────────────────────────────────────────────────────┤   │
│ │ 青铜等级 | [8.00]% | [3]%|[2]%|[1]% | [2]%|[-]%|[-]%       │   │
│ │ 白银等级 | [10.0]% | [4]%|[2]%|[1]% | [3]%|[1]%|[-]%       │   │
│ │ 黄金等级 | [12.0]% | [5]%|[3]%|[2]% | [3]%|[2]%|[-]%       │   │
│ │ 钻石等级 | [15.0]% | [6]%|[4]%|[2]% | [4]%|[2]%|[1]%       │   │
│ └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
│ 说明：留空表示继承方案默认值，"-"表示该层级不分润               │
└─────────────────────────────────────────────────────────────────────┘
```

**5. 快速配置弹窗（批量设置）**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 批量设置                                              [确定] [取消] │
├─────────────────────────────────────────────────────────────────────┤
│ 选择等级：[✓] 全选  [✓] 青铜  [✓] 白银  [✓] 黄金  [✓] 钻石      │
│                                                                     │
│ 销售佣金：[百分比 ▼] [____]%                                      │
│                                                                     │
│ 上级分润：                                                          │
│   一级：[____]%  二级：[____]%  三级：[____]%                     │
│                                                                     │
│ 介绍人分润：                                                        │
│   一级：[____]%  二级：[____]%  三级：[____]%                     │
│                                                                     │
│ [✓] 覆盖已有配置                                                   │
└─────────────────────────────────────────────────────────────────────┘
```

**6. 配置预览（只读模式）**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 奖励方案预览                                          [编辑] [关闭] │
├─────────────────────────────────────────────────────────────────────┤
│ 基础信息                                                            │
│ • 方案编码：GLOBAL_001                                              │
│ • 方案名称：全局统一奖励方案                                        │
│ • 适用范围：全局                                                    │
│ • 触发阶段：订单支付后                                              │
│                                                                     │
│ 奖励配置                                                            │
│ • 销售奖励：✓ 启用（统一配置 10%）                                │
│ • 分润奖励：✓ 启用（上级+介绍人，最大3级）                       │
│                                                                     │
│ 等级配置明细                                                        │
│ ┌─────────────────────────────────────────────────────────────┐   │
│ │ 钻石等级：销售15% | 上级6%-4%-2% | 介绍人4%-2%-1%          │   │
│ │ 黄金等级：销售12% | 上级5%-3%-2% | 介绍人3%-2%-0%          │   │
│ │ 白银等级：销售10% | 上级4%-2%-1% | 介绍人3%-1%-0%          │   │
│ │ 青铜等级：销售8%  | 上级3%-2%-1% | 介绍人2%-0%-0%          │   │
│ └─────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────┘
```

**7. 批量导出功能**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 导出奖励方案                                          [导出] [取消] │
├─────────────────────────────────────────────────────────────────────┤
│ 导出范围：                                                          │
│ (●) 当前筛选结果（12个方案）                                       │
│ ( ) 选中的方案（2个方案）                                          │
│ ( ) 全部方案                                                       │
│                                                                     │
│ 导出内容：                                                          │
│ [✓] 方案基础信息                                                   │
│ [✓] 等级配置明细                                                   │
│ [✓] 包含禁用方案                                                   │
│                                                                     │
│ 导出格式：                                                          │
│ (●) Excel（.xlsx）- 推荐，支持再导入                              │
│ ( ) CSV（.csv）- 通用格式                                          │
│ ( ) JSON（.json）- 开发使用                                        │
│                                                                     │
│ 说明：导出文件包含所有配置信息，可用于数据备份或跨环境迁移        │
└─────────────────────────────────────────────────────────────────────┘
```

**8. 批量导入功能**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 导入奖励方案                                          [导入] [取消] │
├─────────────────────────────────────────────────────────────────────┤
│ 选择文件：[选择文件] reward_scheme_20240315.xlsx                   │
│                                                                     │
│ 导入选项：                                                          │
│ [✓] 自动生成新的方案编码（避免重复）                              │
│ [ ] 覆盖已存在的方案（按方案编码匹配）                            │
│ [✓] 校验商品和类目ID是否存在                                      │
│                                                                     │
│ 预览（检测到5个方案）：                                            │
│ ┌─────────────────────────────────────────────────────────────┐   │
│ │ ✓ GLOBAL_001_NEW  | 全局统一奖励 | 校验通过                 │   │
│ │ ✓ SPU_001_NEW     | 高端商品奖励 | 校验通过                 │   │
│ │ ⚠ LEVEL_001_NEW   | 分等级销售奖 | 警告：等级ID 5 不存在    │   │
│ │ ✓ CAT_001_NEW     | 类目奖励方案 | 校验通过                 │   │
│ │ ✗ ERROR_001       | 错误方案     | 错误：必填字段缺失       │   │
│ └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
│ [下载模板] [查看导入日志]                                          │
└─────────────────────────────────────────────────────────────────────┘
```

**9. 导出文件格式示例（Excel）**
```
Sheet 1: 方案主表
┌──────────────┬──────────────┬──────────┬────────────┬───────────┐
│ 方案编码     │ 方案名称     │ 适用范围 │ 销售奖励   │ 分润奖励  │
├──────────────┼──────────────┼──────────┼────────────┼───────────┤
│ GLOBAL_001   │ 全局统一奖励 │ 全局     │ 启用(10%)  │ 启用(5%)  │
│ SPU_001      │ 高端商品奖励 │ 商品:1001│ 启用(分级) │ 启用(分级)│
└──────────────┴──────────────┴──────────┴────────────┴───────────┘

Sheet 2: 等级配置明细
┌──────────────┬──────────┬──────────┬────────────────┬──────────┐
│ 方案编码     │ 等级名称 │ 销售佣金 │ 上级分润       │ 介绍人分润│
├──────────────┼──────────┼──────────┼────────────────┼──────────┤
│ SPU_001      │ 钻石等级 │ 15%      │ 1级:6%,2级:4% │ 1级:4%   │
│ SPU_001      │ 黄金等级 │ 12%      │ 1级:5%,2级:3% │ 1级:3%   │
└──────────────┴──────────┴──────────┴────────────────┴──────────┘
```

#### 4.1.4 UI设计原则

1. **简明高效**
   - 采用标签页设计，将复杂配置分组管理
   - 常用配置项突出显示，高级选项折叠
   - 提供批量设置功能，减少重复操作

2. **智能联动**
   - 选择"统一配置"时隐藏等级设置标签
   - 关闭销售/分润开关时，相关配置项自动禁用
   - 适用范围改变时，动态显示相关选择器

3. **可视化展示**
   - 列表页直观显示奖励开关状态
   - 配置预览功能，一目了然查看所有设置
   - 等级配置采用表格形式，便于对比

4. **操作便捷**
   - 支持方案复制，快速创建相似配置
   - 提供配置模板，一键应用常用方案
   - 保存并继续功能，连续创建多个方案
   - 批量导出导入，支持数据迁移和备份

#### 4.1.3 配置示例

**示例1：全局统一配置方案（所有等级相同）**
```sql
-- 方案主表记录
INSERT INTO yt_dist_reward_scheme VALUES (
  1, 'GLOBAL_UNIFIED_001', '全局统一奖励方案', '所有商品、所有等级统一配置',
  1, NULL, NULL, NULL,  -- 全局适用
  1, 1, 1, 3, 1,  -- 启用销售和分润，统一配置
  2, 10.0, 0.00,  -- 销售奖10%
  2, 5.0, 0.00,   -- 分润奖5%
  100.00, 1000.00, 1, 1, NULL, NULL,
  0, 1, 'admin', NOW(), 'admin', NOW(), 0, 1
);

-- 由于是统一配置模式，不需要在等级配置表中创建记录
-- 系统会直接使用方案主表的默认配置
```

**示例2：分等级销售奖励方案**
```sql
-- 方案主表记录
INSERT INTO yt_dist_reward_scheme VALUES (
  2, 'LEVEL_SALES_001', '分等级销售奖励', '不同等级不同销售佣金',
  1, NULL, NULL, NULL,  -- 全局适用
  1, 0, NULL, 0, 2,  -- 只启用销售奖励，分等级配置
  2, 0.0, 0.00,  -- 默认值（会被等级配置覆盖）
  2, 0.0, 0.00,
  100.00, NULL, 1, 1, NULL, NULL,
  0, 1, 'admin', NOW(), 'admin', NOW(), 0, 1
);

-- 等级配置表记录
INSERT INTO yt_dist_reward_level_config VALUES
  (1, 2, 1, '青铜等级', 2, 8.0, 0.00, NULL, NULL, 1, NULL, NULL, NULL, NULL, 'admin', NOW(), 'admin', NOW(), 0, 1),
  (2, 2, 2, '白银等级', 2, 10.0, 0.00, NULL, NULL, 1, NULL, NULL, NULL, NULL, 'admin', NOW(), 'admin', NOW(), 0, 1),
  (3, 2, 3, '黄金等级', 2, 12.0, 0.00, NULL, NULL, 1, NULL, NULL, NULL, NULL, 'admin', NOW(), 'admin', NOW(), 0, 1),
  (4, 2, 4, '钻石等级', 2, 15.0, 0.00, NULL, NULL, 1, NULL, NULL, NULL, NULL, 'admin', NOW(), 'admin', NOW(), 0, 1);
```

**示例3：特定商品的综合奖励方案（销售+分润）**
```sql
-- 方案主表记录
INSERT INTO yt_dist_reward_scheme VALUES (
  3, 'SPU_COMBINED_001', '高端商品综合奖励', '特定商品的销售和分润奖励',
  3, NULL, 10001, NULL,  -- 指定SPU
  1, 1, 3, 2, 2,  -- 启用销售和分润，上级和介绍人都分润，分等级配置
  2, 0.0, 0.00,  -- 默认值（会被等级配置覆盖）
  2, 0.0, 0.00,
  500.00, 5000.00, 1, 1, NULL, NULL,
  100, 1, 'admin', NOW(), 'admin', NOW(), 0, 1
);

-- 等级配置表记录
INSERT INTO yt_dist_reward_level_config VALUES
  -- 钻石等级配置
  (5, 3, 4, '钻石等级', 
   2, 20.0, 0.00,  -- 销售佣金20%
   '{"1": {"mode": 2, "rate": 5.0}, "2": {"mode": 2, "rate": 3.0}}',  -- 上级分润：一级5%，二级3%
   '{"1": {"mode": 2, "rate": 3.0}}',  -- 介绍人分润：一级3%
   1, NULL, NULL, NULL, NULL, 'admin', NOW(), 'admin', NOW(), 0, 1),
  
  -- 黄金等级配置
  (6, 3, 3, '黄金等级',
   2, 15.0, 0.00,  -- 销售佣金15%
   '{"1": {"mode": 2, "rate": 3.0}}',  -- 上级分润：一级3%
   '{"1": {"mode": 2, "rate": 2.0}}',  -- 介绍人分润：一级2%
   1, NULL, NULL, NULL, NULL, 'admin', NOW(), 'admin', NOW(), 0, 1);
```

**分润配置JSON结构说明**：
```json
{
  "1": {  // 追溯层级
    "mode": 2,  // 佣金模式：1-固定金额，2-百分比
    "rate": 5.0,  // 佣金比例
    "amount": 0  // 固定金额
  },
  "2": {  // 二级
    "mode": 2,
    "rate": 3.0,
    "amount": 0
  }
}
```

### 4.2 奖励计算流程【v1.1优化】

#### 4.2.1 触发阶段说明

系统支持三种奖励触发阶段：

1. **订单支付后（trigger_stage = 1）**
   - 订单支付成功立即触发奖励计算
   - 适用于需要快速激励的场景
   - 奖励可能进入冻结状态，等待订单最终完成

2. **券码核销后（trigger_stage = 2）**
   - 每次券码核销时触发对应金额的奖励计算
   - 适用于服务类商品，按实际消费计算奖励
   - 支持部分核销，按核销比例计算奖励

3. **订单完成后（trigger_stage = 3）**
   - 订单完成的定义：
     - 订单下所有券码已全部核销，或
     - 部分券码已核销，剩余部分已退款
   - 适用于需要等待服务完成后再发放奖励的场景
   - 奖励金额按实际核销金额计算

#### 4.2.2 计算流程图
```mermaid
graph TD
    A[业务触发] --> B[获取直接分销员]
    B --> C{是否有分销员}
    C -->|无| D[结束]
    C -->|有| E[查找适用的奖励方案]
    E --> F[按优先级排序方案]
    F --> G[计算销售奖励]
    G --> H{方案是否包含分润}
    H -->|否| I[仅记录销售奖励]
    H -->|是| J[追溯上级/介绍人]
    J --> K[计算分润奖励]
    K --> L[生成所有账单记录]
    I --> L
    L --> M[更新余额]
```

#### 4.2.2 方案匹配规则

1. **匹配优先级**（从高到低）：
   - 指定SKU的方案
   - 指定SPU的方案  
   - 指定类目的方案
   - 全局方案

2. **等级匹配**：
   - 分等级配置：匹配分销员当前等级
   - 统一配置：适用所有等级

3. **有效性验证**：
   - 方案状态必须为启用
   - 当前时间在方案有效期内
   - 订单金额满足最低要求

#### 4.2.4 核心计算服务实现

```java
@Service
public class UnifiedRewardCalculationService {
    
    @Resource
    private DistRewardSchemeMapper rewardSchemeMapper;
    
    @Resource
    private DistRewardLevelConfigMapper levelConfigMapper;
    
    @Resource
    private DistAgentMapper agentMapper;
    
    @Resource
    private DistAgentBillRecordMapper billRecordMapper;
    
    /**
     * 计算订单奖励
     */
    @Transactional(rollbackFor = Exception.class)
    public void calculateOrderRewards(OrderRewardContext context) {
        // 1. 获取直接分销员
        DistAgentDO directAgent = getDirectAgent(context.getDistributionCode());
        if (directAgent == null) {
            return;
        }
        
        // 2. 查找适用的奖励方案
        List<DistRewardSchemeDO> schemes = findApplicableSchemes(context);
        if (CollectionUtils.isEmpty(schemes)) {
            return;
        }
        
        // 3. 按优先级排序并获取最优方案
        DistRewardSchemeDO bestScheme = schemes.stream()
            .sorted(Comparator.comparing(DistRewardSchemeDO::getPriority).reversed())
            .findFirst()
            .orElse(null);
            
        if (bestScheme == null) {
            return;
        }
        
        // 4. 获取等级配置（如果是分等级配置模式）
        DistRewardLevelConfigDO levelConfig = null;
        if (bestScheme.getLevelConfigMode() == 2) {
            levelConfig = levelConfigMapper.selectOne(
                new LambdaQueryWrapper<DistRewardLevelConfigDO>()
                    .eq(DistRewardLevelConfigDO::getSchemeId, bestScheme.getId())
                    .eq(DistRewardLevelConfigDO::getLevelId, directAgent.getLevelId())
                    .eq(DistRewardLevelConfigDO::getDeleted, false)
            );
        }
        
        // 5. 计算奖励
        List<AgentBillRecordDO> bills = new ArrayList<>();
        
        // 5.1 计算销售奖励
        if (bestScheme.getEnableSalesReward()) {
            calculateSalesReward(directAgent, bestScheme, levelConfig, context, bills);
        }
        
        // 5.2 计算分润奖励
        if (bestScheme.getEnableProfitSharing()) {
            calculateProfitRewards(directAgent, bestScheme, levelConfig, context, bills);
        }
        
        // 6. 批量保存账单
        if (!bills.isEmpty()) {
            billRecordMapper.insertBatch(bills);
        }
    }
    
    /**
     * 计算销售奖励
     */
    private void calculateSalesReward(DistAgentDO agent, DistRewardSchemeDO scheme, 
                                    DistRewardLevelConfigDO levelConfig, OrderRewardContext context,
                                    List<AgentBillRecordDO> bills) {
        // 获取销售佣金配置
        BigDecimal commissionRate;
        BigDecimal commissionAmount;
        Integer commissionMode;
        
        if (levelConfig != null) {
            // 使用等级配置
            commissionMode = levelConfig.getSalesCommissionMode();
            commissionRate = levelConfig.getSalesCommissionRate();
            commissionAmount = levelConfig.getSalesCommissionAmount();
        } else {
            // 使用方案默认配置
            commissionMode = scheme.getDefaultSalesMode();
            commissionRate = scheme.getDefaultSalesRate();
            commissionAmount = scheme.getDefaultSalesAmount();
        }
        
        // 计算佣金
        BigDecimal commission = calculateCommission(
            context.getOrderAmount(), 
            commissionMode,
            commissionRate,
            commissionAmount
        );
        
        // 应用最高限制
        BigDecimal maxCommission = levelConfig != null && levelConfig.getMaxCommission() != null 
            ? levelConfig.getMaxCommission() : scheme.getMaxCommission();
        if (maxCommission != null && commission.compareTo(maxCommission) > 0) {
            commission = maxCommission;
        }
        
        // 创建账单
        AgentBillRecordDO bill = createBillRecord(
            agent, context, commission, scheme, null, 1, 0, commissionRate
        );
        bills.add(bill);
    }
    
    /**
     * 计算分润奖励
     */
    private void calculateProfitRewards(DistAgentDO sourceAgent, DistRewardSchemeDO scheme,
                                      DistRewardLevelConfigDO levelConfig, OrderRewardContext context,
                                      List<AgentBillRecordDO> bills) {
        // 获取分润目标
        Integer profitTarget = scheme.getProfitTarget();
        Integer maxTraceLevel = scheme.getMaxTraceLevel();
        
        // 计算上级分润
        if (profitTarget == 1 || profitTarget == 3) {
            calculateParentProfit(sourceAgent, scheme, levelConfig, context, bills, maxTraceLevel);
        }
        
        // 计算介绍人分润
        if (profitTarget == 2 || profitTarget == 3) {
            calculateReferrerProfit(sourceAgent, scheme, levelConfig, context, bills, maxTraceLevel);
        }
    }
    
    /**
     * 计算上级分润
     */
    private void calculateParentProfit(DistAgentDO sourceAgent, DistRewardSchemeDO scheme,
                                     DistRewardLevelConfigDO levelConfig, OrderRewardContext context,
                                     List<AgentBillRecordDO> bills, Integer maxTraceLevel) {
        // 获取上级分润配置
        Map<Integer, ProfitConfig> parentProfitMap = levelConfig != null 
            ? parseParentProfitConfig(levelConfig.getParentProfitConfig())
            : getDefaultProfitConfig(scheme);
            
        // 逐级向上追溯
        DistAgentDO currentAgent = sourceAgent;
        for (int level = 1; level <= maxTraceLevel; level++) {
            if (currentAgent.getParentId() == null) {
                break;
            }
            
            DistAgentDO parentAgent = agentMapper.selectById(currentAgent.getParentId());
            if (parentAgent == null || parentAgent.getStatus() != 1) {
                break;
            }
            
            // 获取该层级的分润配置
            ProfitConfig profitConfig = parentProfitMap.get(level);
            if (profitConfig == null) {
                break;
            }
            
            // 计算分润
            BigDecimal profit = calculateCommission(
                context.getOrderAmount(),
                profitConfig.getMode(),
                profitConfig.getRate(),
                profitConfig.getAmount()
            );
            
            if (profit.compareTo(BigDecimal.ZERO) > 0) {
                AgentBillRecordDO bill = createBillRecord(
                    parentAgent, context, profit, scheme, null, 2, level, profitConfig.getRate()
                );
                bills.add(bill);
            }
            
            currentAgent = parentAgent;
        }
    }
    
    /**
     * 计算介绍人分润
     */
    private void calculateReferrerProfit(DistAgentDO sourceAgent, DistRewardSchemeDO scheme,
                                       DistRewardLevelConfigDO levelConfig, OrderRewardContext context,
                                       List<AgentBillRecordDO> bills, Integer maxTraceLevel) {
        // 获取介绍人分润配置
        Map<Integer, ProfitConfig> referrerProfitMap = levelConfig != null 
            ? parseReferrerProfitConfig(levelConfig.getReferrerProfitConfig())
            : getDefaultProfitConfig(scheme);
            
        // 逐级追溯介绍人
        DistAgentDO currentAgent = sourceAgent;
        for (int level = 1; level <= maxTraceLevel; level++) {
            if (currentAgent.getReferrerId() == null) {
                break;
            }
            
            DistAgentDO referrerAgent = agentMapper.selectById(currentAgent.getReferrerId());
            if (referrerAgent == null || referrerAgent.getStatus() != 1) {
                break;
            }
            
            // 获取该层级的分润配置
            ProfitConfig profitConfig = referrerProfitMap.get(level);
            if (profitConfig == null) {
                break;
            }
            
            // 计算分润
            BigDecimal profit = calculateCommission(
                context.getOrderAmount(),
                profitConfig.getMode(),
                profitConfig.getRate(),
                profitConfig.getAmount()
            );
            
            if (profit.compareTo(BigDecimal.ZERO) > 0) {
                AgentBillRecordDO bill = createBillRecord(
                    referrerAgent, context, profit, scheme, null, 2, level, profitConfig.getRate()
                );
                bills.add(bill);
            }
            
            currentAgent = referrerAgent;
        }
    }
    
    /**
     * 计算佣金
     */
    private BigDecimal calculateCommission(BigDecimal baseAmount, Integer mode,
                                         BigDecimal rate, BigDecimal fixedAmount) {
        if (mode == 1) {
            // 固定金额
            return fixedAmount;
        } else {
            // 百分比
            return baseAmount.multiply(rate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        }
    }
    
    /**
     * 解析分润配置
     */
    private Map<Integer, ProfitConfig> parseParentProfitConfig(String configJson) {
        if (StringUtils.isBlank(configJson)) {
            return new HashMap<>();
        }
        // 解析JSON配置为Map<层级, 配置>
        return JSON.parseObject(configJson, new TypeReference<Map<Integer, ProfitConfig>>(){});
    }
    
    /**
     * 分润配置内部类
     */
    @Data
    static class ProfitConfig {
        private Integer mode;  // 1-固定金额，2-百分比
        private BigDecimal rate;
        private BigDecimal amount;
    }
}
```

### 4.3 分销商品配置设计【v1.1新增】

#### 4.3.1 设计说明

分销商品配置用于管理哪些商品可以被分销员分销，以及如何展示预计收益。主要特点：

1. **多应用支持**：支持配置不同小程序（一筒不二、商家小程序等）的分享设置
2. **灵活的佣金展示**：支持百分比或金额范围展示
3. **个性化分享**：每个商品可配置独立的分享文案和图片
4. **标签管理**：便于商品分类和筛选

#### 4.3.2 app_config字段结构
```json
{
  "apps": [
    {
      "appId": "wx123456789",
      "appName": "一筒不二小程序",
      "platform": "weixin",
      "shareConfig": {
        "path": "/pages/goods/detail",
        "pageParam": "id",
        "distributionParam": "distCode"
      }
    },
    {
      "appId": "xhs987654321",
      "appName": "商家小红书小程序",
      "platform": "xiaohongshu",
      "shareConfig": {
        "path": "/pages/product/detail",
        "pageParam": "spuId",
        "distributionParam": "agentCode"
      }
    }
  ],
  "defaultAppId": "wx123456789"
}
```

#### 4.3.3 管理后台分销商品配置UI

**分销商品列表页**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 分销商品管理                          [批量配置] [导入] [新增商品]  │
├─────────────────────────────────────────────────────────────────────┤
│ [商品名称/SPU] [启用状态 ▼] [标签 ▼] [查询] [重置]               │
│                 全部         全部                                   │
├─────────────────────────────────────────────────────────────────────┤
│ [✓] | 商品信息 | 价格范围 | 预计佣金 | 状态 | 排序 | 操作         │
├─────────────────────────────────────────────────────────────────────┤
│ [✓] | [图] 高端红酒套装 | ¥299-599 | 10-15% | 启用 | 1 |         │
│     | SPU: 10001       |          | ¥29.9-89.85 |     |   |[编辑] │
│     | 标签: 热销,高佣   |          |             |     |   |[禁用] │
└─────────────────────────────────────────────────────────────────────┘
已选择 1 项 | [批量启用] [批量禁用] [批量删除]
```

**分销商品配置编辑页**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 编辑分销商品配置                                 [保存] [取消]      │
├─────────────────────────────────────────────────────────────────────┤
│ 商品信息                                                            │
│ ├─ SPU选择*：[选择商品 ▼] 高端红酒套装 (SPU: 10001)             │
│ ├─ 商品首图：[自动获取] 或 [上传图片]                             │
│ └─ 价格范围：¥299.00 - ¥599.00 (自动获取)                        │
│                                                                     │
│ 佣金展示配置                                                        │
│ ├─ 展示类型：(●) 百分比  ( ) 金额                                │
│ ├─ 展示值*：[10-15_______] %                                      │
│ └─ 预计佣金文案：[赚 ¥29.9-89.85_________________________]       │
│                                                                     │
│ 分享设置                                                            │
│ ├─ 分享标题：[【限时特惠】高端红酒套装，品质生活必备_______]      │
│ ├─ 分享描述：[原装进口，口感醇厚，送礼自饮皆宜___________]        │
│ └─ 分享图片：[使用商品首图] 或 [上传分享图]                       │
│                                                                     │
│ 应用配置                                                            │
│ ├─ [✓] 一筒不二小程序 (wx123456789)                              │
│ │   └─ 详情页路径：/pages/goods/detail?id={spuId}&distCode={code} │
│ ├─ [✓] 商家小红书小程序 (xhs987654321)                           │
│ │   └─ 详情页路径：/pages/product/detail?spuId={spuId}&agentCode={code}│
│ └─ 默认应用：[一筒不二小程序 ▼]                                  │
│                                                                     │
│ 其他设置                                                            │
│ ├─ 商品标签：[热销] [高佣] [新品] [+添加]                        │
│ ├─ 排序序号：[1_____]                                             │
│ ├─ [✓] 在分销商品列表显示                                         │
│ └─ [✓] 启用分销                                                   │
└─────────────────────────────────────────────────────────────────────┘
```

### 4.4 管理后台完整UI设计【v1.1新增】

#### 4.3.1 分销系统导航菜单
```
┌─────────────────────────────────────────────────────────────────────┐
│ 分销管理                                                            │
├─────────────────────────────────────────────────────────────────────┤
│ ├─ 📊 数据概览                                                      │
│ ├─ 👥 分销员管理                                                    │
│ │   ├─ 分销员列表                                                  │
│ │   ├─ 申请审核                                                    │
│ │   └─ 等级管理                                                    │
│ ├─ 💰 奖励配置                                                      │
│ │   ├─ 奖励方案 ←当前页面                                         │
│ │   └─ 等级权益                                                    │
│ ├─ 📈 佣金管理                                                      │
│ │   ├─ 佣金账单                                                    │
│ │   ├─ 提现申请                                                    │
│ │   └─ 结算记录                                                    │
│ └─ ⚙️ 系统设置                                                      │
│     ├─ 基础配置                                                    │
│     └─ 操作日志                                                    │
└─────────────────────────────────────────────────────────────────────┘
```

#### 4.3.2 分销员管理

**1. 分销员列表页**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 分销员管理                                     [导出] [新增分销员]  │
├─────────────────────────────────────────────────────────────────────┤
│ [分销码/姓名/手机] [等级 ▼] [状态 ▼] [时间范围 ▼] [查询] [重置]  │
│                    全部等级   全部状态   最近7天                    │
├─────────────────────────────────────────────────────────────────────┤
│ 分销码 | 姓名 | 手机号 | 等级 | 上级 | 团队 | 本月业绩 | 状态 | 操作│
├─────────────────────────────────────────────────────────────────────┤
│ DS001 | 张三 | 138****1234 | 钻石 | - | 128人 | ¥12,580 | 正常 |   │
│       |      |            |      |   |       |         |      |[详情]│
│ DS002 | 李四 | 139****5678 | 黄金 | 张三 | 45人 | ¥8,350 | 正常 |   │
│       |      |            |      |      |      |        |      |[详情]│
└─────────────────────────────────────────────────────────────────────┘
```

**2. 分销员详情页（Tab页完整设计）**

**基本信息Tab**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 分销员详情 - 张三（DS001）                    [编辑] [禁用] [返回] │
├─────────────────────────────────────────────────────────────────────┤
│ [基本信息] [团队结构] [业绩统计] [佣金记录] [操作日志]            │
├─────────────────────────────────────────────────────────────────────┤
│ 基本信息                                                            │
│ ┌─────────────────────────────────────────────────────────────┐   │
│ │ 分销码：DS001          加入时间：2024-01-15 10:30:25        │   │
│ │ 姓名：张三             手机号：138****1234                   │   │
│ │ 当前等级：钻石等级     等级更新：2024-03-01                 │   │
│ │ 上级分销员：无         介绍人：系统                         │   │
│ │ 账户状态：正常         最后活跃：2024-03-15 14:22:10        │   │
│ └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
│ 统计数据                                                            │
│ ┌─────────────────────────────────────────────────────────────┐   │
│ │ 团队总人数：128人     直属下级：15人    累计销售：¥358,900  │   │
│ │ 本月销售：¥12,580    本月佣金：¥2,150  可提现：¥5,280     │   │
│ └─────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────┘
```

**团队结构Tab**
```
┌─────────────────────────────────────────────────────────────────────┐
│ [基本信息] [团队结构] [业绩统计] [佣金记录] [操作日志]            │
├─────────────────────────────────────────────────────────────────────┤
│ 团队结构                                              [导出]        │
│ ┌─────────────────────────────────────────────────────────────┐   │
│ │ 🔍 [搜索成员姓名/分销码]                                    │   │
│ │                                                             │   │
│ │ 📊 团队层级树                                               │   │
│ │ └─ 张三 (DS001) - 钻石 - 本人                              │   │
│ │    ├─ 李四 (DS002) - 黄金 - 45人团队 - ¥8,350             │   │
│ │    │  ├─ 王五 (DS005) - 白银 - 12人团队 - ¥3,280          │   │
│ │    │  └─ 赵六 (DS006) - 白银 - 8人团队 - ¥2,150           │   │
│ │    ├─ 陈七 (DS003) - 黄金 - 38人团队 - ¥7,890             │   │
│ │    └─ 刘八 (DS004) - 白银 - 25人团队 - ¥5,420             │   │
│ └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
│ 直属下级列表                                                        │
│ ┌─────────────────────────────────────────────────────────────┐   │
│ │ 分销码 | 姓名 | 等级 | 加入时间 | 团队 | 本月业绩 | 操作    │   │
│ ├─────────────────────────────────────────────────────────────┤   │
│ │ DS002 | 李四 | 黄金 | 02-01 | 45人 | ¥8,350 | [查看]      │   │
│ │ DS003 | 陈七 | 黄金 | 02-05 | 38人 | ¥7,890 | [查看]      │   │
│ └─────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────┘
```

**业绩统计Tab**
```
┌─────────────────────────────────────────────────────────────────────┐
│ [基本信息] [团队结构] [业绩统计] [佣金记录] [操作日志]            │
├─────────────────────────────────────────────────────────────────────┤
│ 业绩统计                              时间范围：[本月 ▼] [查询]    │
├─────────────────────────────────────────────────────────────────────┤
│ 关键指标                                                            │
│ ┌─────────────────────────────────────────────────────────────┐   │
│ │ 个人销售额：¥12,580    团队销售额：¥68,930                 │   │
│ │ 订单数量：156单        客单价：¥80.64                      │   │
│ │ 销售佣金：¥1,887       分润收益：¥263                      │   │
│ └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
│ 销售趋势图                                                          │
│ ┌─────────────────────────────────────────────────────────────┐   │
│ │ 📈 [个人] [团队] [佣金]                                     │   │
│ │    显示最近30天的趋势变化                                   │   │
│ └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
│ 商品销售排行                                                        │
│ ┌─────────────────────────────────────────────────────────────┐   │
│ │ 排名 | 商品名称 | 销量 | 销售额 | 佣金                      │   │
│ ├─────────────────────────────────────────────────────────────┤   │
│ │ 1 | 高端红酒套装 | 23 | ¥5,290 | ¥793.50                   │   │
│ │ 2 | 进口橄榄油 | 45 | ¥3,150 | ¥315.00                     │   │
│ └─────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────┘
```

**佣金记录Tab**
```
┌─────────────────────────────────────────────────────────────────────┐
│ [基本信息] [团队结构] [业绩统计] [佣金记录] [操作日志]            │
├─────────────────────────────────────────────────────────────────────┤
│ 佣金记录              [类型 ▼] [状态 ▼] [时间 ▼] [查询] [导出]   │
│                       全部类型   全部状态   本月                    │
├─────────────────────────────────────────────────────────────────────┤
│ 账单号 | 类型 | 关联订单 | 金额 | 状态 | 解冻时间 | 创建时间      │
├─────────────────────────────────────────────────────────────────────┤
│ B202403150001 | 销售奖励 | O123456 | +¥125.50 | 冻结中 | 04-14 | │
│               |         |         |          |        | 03-15  │
│ B202403150002 | 上级分润 | O123457 | +¥62.75 | 已结算 | - |     │
│               |         |         |         |        | 03-14   │
│ B202403140003 | 退款扣除 | O123458 | -¥35.20 | 已结算 | - |     │
│               |         |         |         |        | 03-14   │
└─────────────────────────────────────────────────────────────────────┘
统计：收入 ¥2,150.25 | 支出 ¥35.20 | 净收入 ¥2,115.05
```

**操作日志Tab**
```
┌─────────────────────────────────────────────────────────────────────┐
│ [基本信息] [团队结构] [业绩统计] [佣金记录] [操作日志]            │
├─────────────────────────────────────────────────────────────────────┤
│ 操作日志                                     [时间范围 ▼] [查询]   │
│                                              最近7天                │
├─────────────────────────────────────────────────────────────────────┤
│ 时间 | 操作类型 | 操作人 | 操作详情 | IP地址                      │
├─────────────────────────────────────────────────────────────────────┤
│ 03-15 14:22:10 | 登录系统 | 本人 | 手机端登录 | 223.104.*.* │
│ 03-15 10:30:25 | 等级变更 | 系统 | 白银→黄金 自动升级 | - │
│ 03-14 16:45:30 | 提现申请 | 本人 | 申请提现¥3,000 | 223.104.*.* │
│ 03-13 09:12:15 | 信息修改 | admin | 修改手机号 | 192.168.*.* │
└─────────────────────────────────────────────────────────────────────┘
```

**3. 分销员编辑页**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 编辑分销员信息                                   [保存] [取消]      │
├─────────────────────────────────────────────────────────────────────┤
│ 基本信息                                                            │
│ ├─ 分销码：DS001（不可修改）                                       │
│ ├─ 姓名*：[张三____________]                                       │
│ ├─ 手机号*：[13812341234____]                                     │
│ └─ 备注：[_______________________________________________]        │
│                                                                     │
│ 等级设置                                                            │
│ ├─ 当前等级：[钻石等级 ▼]（手动调整需要原因）                    │
│ └─ 调整原因：[_______________________________________________]     │
│                                                                     │
│ 关系绑定                                                            │
│ ├─ 上级分销员：[选择分销员 ▼] DS002-李四                         │
│ └─ 介绍人：    [选择分销员 ▼] 无                                 │
│                                                                     │
│ 账户状态                                                            │
│ └─ 状态：(●) 正常  ( ) 禁用                                       │
│     禁用原因：[_______________________________________________]     │
└─────────────────────────────────────────────────────────────────────┘
```

**3. 申请审核页**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 分销员申请审核                               [批量通过] [批量拒绝] │
├─────────────────────────────────────────────────────────────────────┤
│ [申请人] [邀请码] [申请时间 ▼] [状态 ▼] [查询]                   │
│                    最近申请      待审核                             │
├─────────────────────────────────────────────────────────────────────┤
│ [✓] | 申请人 | 手机号 | 邀请码 | 邀请人 | 申请时间 | 状态 | 操作  │
├─────────────────────────────────────────────────────────────────────┤
│ [✓] | 王五 | 135****1111 | DS001 | 张三 | 03-15 10:30 | 待审核 |  │
│     |      |            |       |      |            |        |[通过]│
│     |      |            |       |      |            |        |[拒绝]│
│ [ ] | 赵六 | 136****2222 | GOLD2024 | - | 03-15 09:15 | 待审核 | │
│     |      |            |          |   |            |        |[查看]│
└─────────────────────────────────────────────────────────────────────┘
已选择 1 项
```

**4. 申请详情/审核弹窗**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 分销员申请详情                          [通过] [拒绝] [关闭]       │
├─────────────────────────────────────────────────────────────────────┤
│ 申请信息                                                            │
│ ├─ 申请编号：AP202403150001                                        │
│ ├─ 申请时间：2024-03-15 10:30:25                                  │
│ ├─ 申请人：王五                                                    │
│ ├─ 手机号：13512341111                                            │
│ └─ 邮箱：<EMAIL>                                       │
│                                                                     │
│ 邀请信息                                                            │
│ ├─ 邀请码：DS001                                                   │
│ ├─ 邀请类型：分销员邀请                                           │
│ ├─ 邀请人：张三（DS001）                                           │
│ └─ 初始等级：青铜等级                                              │
│                                                                     │
│ 绑定关系                                                            │
│ ├─ 绑定类型：同时绑定                                             │
│ ├─ 上级分销员：张三（DS001）                                      │
│ └─ 介绍人：张三（DS001）                                          │
│                                                                     │
│ 审核意见                                                            │
│ └─ [请输入审核意见（拒绝时必填）_________________________]        │
└─────────────────────────────────────────────────────────────────────┘
```

**5. 新增分销员页**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 新增分销员                                       [保存] [取消]      │
├─────────────────────────────────────────────────────────────────────┤
│ 会员选择                                                            │
│ └─ 选择会员*：[搜索会员姓名/手机号 ▼]                            │
│                王五 - 13512341111                                   │
│                                                                     │
│ 分销信息                                                            │
│ ├─ 分销码：系统自动生成                                           │
│ ├─ 初始等级*：[青铜等级 ▼]                                        │
│ └─ 备注：[_______________________________________________]        │
│                                                                     │
│ 关系绑定                                                            │
│ ├─ 上级分销员：[选择分销员 ▼] 无                                 │
│ └─ 介绍人：    [选择分销员 ▼] 无                                 │
│                                                                     │
│ 说明：                                                              │
│ • 分销码由系统自动生成，确保唯一性                                │
│ • 选择的会员必须未成为分销员                                      │
│ • 可以不设置上级和介绍人，成为独立分销员                          │
└─────────────────────────────────────────────────────────────────────┘
```

#### 4.3.3 等级管理

**1. 等级列表页**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 分销等级管理                                          [新增等级]    │
├─────────────────────────────────────────────────────────────────────┤
│ 等级编码 | 等级名称 | 级别值 | 邀请码 | 自动升级 | 人数 | 操作    │
├─────────────────────────────────────────────────────────────────────┤
│ L001 | 青铜等级 | 10 | BRONZE2024 | ✓ | 1,250人 | [编辑][删除]   │
│ L002 | 白银等级 | 20 | SILVER2024 | ✓ | 856人 | [编辑][删除]     │
│ L003 | 黄金等级 | 30 | GOLD2024 | ✓ | 428人 | [编辑][删除]       │
│ L004 | 钻石等级 | 40 | DIA2024 | ✓ | 125人 | [编辑][删除]        │
└─────────────────────────────────────────────────────────────────────┘
```

**2. 等级编辑页**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 编辑等级 - 钻石等级                              [保存] [取消]     │
├─────────────────────────────────────────────────────────────────────┤
│ 基础信息                                                            │
│ ├─ 等级编码*：L004（不可修改）                                     │
│ ├─ 等级名称*：[钻石等级_________]                                  │
│ ├─ 等级级别*：[40___] （数值越大等级越高）                        │
│ ├─ 邀请码：  [DIA2024__________]                                   │
│ └─ 等级图标：[选择文件] diamond.png                                │
│                                                                     │
│ 升降级设置                                                          │
│ ├─ [✓] 启用自动升级                                                │
│ │   └─ 升级条件：                                                  │
│ │       • 累计销售额 ≥ [100000___] 元                             │
│ │       • 直属下级数 ≥ [10_______] 人                             │
│ │       • 团队人数 ≥ [50________] 人                              │
│ ├─ [ ] 启用自动降级                                                │
│ │   └─ 降级条件：                                                  │
│ │       • 连续 [3__] 个月销售额 < [10000____] 元                  │
│                                                                     │
│ 权益说明                                                            │
│ └─ [可享受最高等级奖励方案，专属客服支持...___________________]    │
└─────────────────────────────────────────────────────────────────────┘
```

#### 4.3.4 佣金管理

**1. 佣金账单列表**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 佣金账单                                              [导出]        │
├─────────────────────────────────────────────────────────────────────┤
│ [分销员/订单号] [账单类型 ▼] [状态 ▼] [日期范围 ▼] [查询]       │
│                  全部类型      全部状态   本月                      │
├─────────────────────────────────────────────────────────────────────┤
│ 账单号 | 分销员 | 类型 | 订单号 | 金额 | 状态 | 时间 | 操作       │
├─────────────────────────────────────────────────────────────────────┤
│ B2024031500001 | 张三 | 销售奖励 | O123456 | +¥125.50 | 已结算 | │
│                |      |         |         |          | 03-15  |[详情]│
│ B2024031500002 | 李四 | 上级分润 | O123456 | +¥62.75 | 冻结中 |  │
│                |      |         |         |         | 03-15  |[详情]│
└─────────────────────────────────────────────────────────────────────┘
```

**2. 佣金账单详情弹窗**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 佣金账单详情 - B2024031500001                         [关闭]       │
├─────────────────────────────────────────────────────────────────────┤
│ 账单信息                                                            │
│ ├─ 账单编号：B2024031500001                                        │
│ ├─ 账单类型：销售奖励                                             │
│ ├─ 账单金额：¥125.50                                              │
│ ├─ 账单状态：冻结中                                               │
│ ├─ 创建时间：2024-03-15 10:30:25                                  │
│ └─ 解冻时间：2024-04-14 10:30:25                                  │
│                                                                     │
│ 分销员信息                                                          │
│ ├─ 分销员：张三（DS001）                                          │
│ ├─ 等级：钻石等级                                                 │
│ └─ 联系方式：138****1234                                          │
│                                                                     │
│ 订单信息                                                            │
│ ├─ 订单编号：O123456                                              │
│ ├─ 订单金额：¥1,255.00                                            │
│ ├─ 商品名称：高端红酒套装                                         │
│ └─ 下单时间：2024-03-15 10:25:30                                  │
│                                                                     │
│ 计算明细                                                            │
│ ├─ 使用方案：GLOBAL_001 - 全局统一奖励方案                        │
│ ├─ 计算基数：¥1,255.00                                            │
│ ├─ 佣金比例：10%                                                  │
│ └─ 佣金金额：¥125.50                                              │
└─────────────────────────────────────────────────────────────────────┘
```

**3. 提现申请管理**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 提现申请管理                                 [批量审核] [导出]      │
├─────────────────────────────────────────────────────────────────────┤
│ [分销员/申请号] [提现方式 ▼] [状态 ▼] [金额范围] [查询]          │
│                  全部         待审核                                │
├─────────────────────────────────────────────────────────────────────┤
│ 申请号 | 分销员 | 金额 | 方式 | 税后 | 状态 | 申请时间 | 操作     │
├─────────────────────────────────────────────────────────────────────┤
│ W2024031500001 | 张三 | ¥5,000 | 银行卡 | ¥4,000 | 待审核 |      │
│                |      |        |        |        | 03-15 |[审核]  │
│ W2024031500002 | 李四 | ¥3,000 | 支付宝 | ¥2,400 | 已打款 |      │
│                |      |        |        |        | 03-14 |[详情]  │
└─────────────────────────────────────────────────────────────────────┘
```

**4. 提现审核弹窗**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 提现审核 - W2024031500001                      [通过] [拒绝] [取消]│
├─────────────────────────────────────────────────────────────────────┤
│ 申请信息                                                            │
│ ├─ 申请人：张三（DS001）                                           │
│ ├─ 申请金额：¥5,000.00                                            │
│ ├─ 提现方式：银行卡                                               │
│ ├─ 收款账户：工商银行 尾号8888                                    │
│ ├─ 发票类型：[代扣代缴 ▼] （无发票/有发票）                      │
│ └─ 税率：20%  税费：¥1,000  实付：¥4,000                        │
│                                                                     │
│ 账户余额                                                            │
│ ├─ 可提现余额：¥5,280.00 ✓                                       │
│ └─ 冻结金额：¥1,200.00                                           │
│                                                                     │
│ 审核意见                                                            │
│ └─ [___________________________________________]                  │
└─────────────────────────────────────────────────────────────────────┘
```

**5. 提现详情页（包含打款功能）**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 提现详情 - W2024031500002                    [打款] [完成] [返回] │
├─────────────────────────────────────────────────────────────────────┤
│ [申请信息] [账单明细] [打款记录] [操作日志]                        │
├─────────────────────────────────────────────────────────────────────┤
│ 申请信息                                                            │
│ ┌─────────────────────────────────────────────────────────────┐   │
│ │ 申请编号：W2024031500002      状态：已打款                  │   │
│ │ 申请人：李四（DS002）         等级：黄金等级                │   │
│ │ 申请时间：2024-03-14 15:30    审核时间：2024-03-14 16:00    │   │
│ │ 申请金额：¥3,000.00          实付金额：¥2,400.00           │   │
│ │ 税费信息：20% / ¥600.00      发票类型：代扣代缴            │   │
│ └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
│ 收款账户                                                            │
│ ┌─────────────────────────────────────────────────────────────┐   │
│ │ 提现方式：支付宝                                            │   │
│ │ 账户名称：李四                                              │   │
│ │ 账户号码：<EMAIL>                                   │   │
│ └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
│ 打款信息                                                            │
│ ┌─────────────────────────────────────────────────────────────┐   │
│ │ 打款时间：2024-03-14 17:30                                  │   │
│ │ 打款人：财务-王芳                                           │   │
│ │ 打款凭证：[查看凭证]                                        │   │
│ │ 备注：已通过支付宝企业付款完成                             │   │
│ └─────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────┘
```

**账单明细Tab**
```
┌─────────────────────────────────────────────────────────────────────┐
│ [申请信息] [账单明细] [打款记录] [操作日志]                        │
├─────────────────────────────────────────────────────────────────────┤
│ 提现账单明细                                         共15笔         │
├─────────────────────────────────────────────────────────────────────┤
│ 账单号 | 类型 | 金额 | 订单号 | 创建时间                           │
├─────────────────────────────────────────────────────────────────────┤
│ B202403100001 | 销售奖励 | ¥235.00 | O123401 | 03-10 10:30       │
│ B202403100002 | 上级分润 | ¥125.00 | O123402 | 03-10 11:15       │
│ B202403110003 | 销售奖励 | ¥188.50 | O123405 | 03-11 09:20       │
└─────────────────────────────────────────────────────────────────────┘
合计：¥3,000.00
```

**6. 打款操作弹窗**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 确认打款                                        [确认] [取消]       │
├─────────────────────────────────────────────────────────────────────┤
│ 打款信息确认                                                        │
│ ├─ 收款人：李四                                                    │
│ ├─ 收款账户：支付宝 - <EMAIL>                            │
│ ├─ 打款金额：¥2,400.00                                           │
│ └─ 打款方式：[支付宝转账 ▼]                                      │
│               银行转账                                              │
│               微信转账                                              │
│                                                                     │
│ 上传凭证*                                                           │
│ └─ [选择文件] 支持jpg、png、pdf格式，最大5MB                      │
│                                                                     │
│ 备注                                                                │
│ └─ [_______________________________________________]              │
│                                                                     │
│ ⚠️ 请确认已完成线下打款操作后再进行确认                            │
└─────────────────────────────────────────────────────────────────────┘
```

**7. 结算记录列表**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 结算记录                                              [导出]        │
├─────────────────────────────────────────────────────────────────────┤
│ [分销员] [结算批次] [时间范围 ▼] [查询]                           │
│                      本月                                           │
├─────────────────────────────────────────────────────────────────────┤
│ 批次号 | 结算时间 | 结算人数 | 结算金额 | 状态 | 操作人 | 操作    │
├─────────────────────────────────────────────────────────────────────┤
│ ST202403150001 | 03-15 10:00 | 156人 | ¥125,890 | 已完成 | 系统 |│
│                |             |       |          |        |      |[详情]│
│ ST202403010001 | 03-01 10:00 | 142人 | ¥108,560 | 已完成 | 系统 |│
│                |             |       |          |        |      |[详情]│
└─────────────────────────────────────────────────────────────────────┘
```

#### 4.3.5 系统设置

**1. 基础配置页**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 分销系统配置                                          [保存]        │
├─────────────────────────────────────────────────────────────────────┤
│ 申请设置                                                            │
│ ├─ [✓] 开启申请审核（关闭后自动通过）                             │
│ ├─ [ ] 允许无邀请码申请                                           │
│ └─ 默认等级：[青铜等级 ▼]                                         │
│                                                                     │
│ 佣金设置                                                            │
│ ├─ 订单支付后冻结天数：[30____] 天                                │
│ ├─ 券码核销后冻结天数：[7_____] 天                                │
│ └─ 订单完成后冻结天数：[3_____] 天                                │
│                                                                     │
│ 提现设置                                                            │
│ ├─ 最低提现金额：[100_____] 元                                    │
│ ├─ 单次最高金额：[50000___] 元                                    │
│ ├─ 代扣税率：    [20______] %                                     │
│ └─ 提现时间：每月 [1,15 ▼] 日（多选）                            │
│                                                                     │
│ 其他设置                                                            │
│ └─ 最大追溯层级：[10_____] 级                                     │
└─────────────────────────────────────────────────────────────────────┘
```

**2. 操作日志页**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 操作日志                                              [导出]        │
├─────────────────────────────────────────────────────────────────────┤
│ [操作人] [操作类型 ▼] [操作对象] [时间范围 ▼] [查询]             │
│           全部类型                 今天                             │
├─────────────────────────────────────────────────────────────────────┤
│ 时间 | 操作人 | 操作类型 | 操作对象 | 详情 | IP地址              │
├─────────────────────────────────────────────────────────────────────┤
│ 15:30:25 | admin | 创建方案 | GLOBAL_001 | 创建奖励方案 | 192... │
│ 14:22:10 | admin | 审核通过 | 张三 | 分销员申请审核通过 | 192... │
│ 10:15:33 | admin | 修改配置 | 系统设置 | 修改提现设置 | 192...   │
└─────────────────────────────────────────────────────────────────────┘
```

#### 4.3.6 数据概览（仪表盘）

```
┌─────────────────────────────────────────────────────────────────────┐
│ 分销数据概览                          时间范围：[本月 ▼] [刷新]    │
├─────────────────────────────────────────────────────────────────────┤
│ ┌──────────────┬──────────────┬──────────────┬──────────────┐     │
│ │ 分销员总数   │ 本月新增     │ 活跃分销员   │ 待审核申请   │     │
│ │ 2,659 人     │ +156 人      │ 1,823 人     │ 12 个        │     │
│ │ ↑ 5.8%       │ ↑ 12.3%      │ ↑ 3.2%       │              │     │
│ └──────────────┴──────────────┴──────────────┴──────────────┘     │
│                                                                     │
│ ┌──────────────┬──────────────┬──────────────┬──────────────┐     │
│ │ 本月销售额   │ 本月佣金     │ 待结算佣金   │ 已提现金额   │     │
│ │ ¥1,258,900   │ ¥125,890     │ ¥45,280      │ ¥80,610      │     │
│ │ ↑ 18.5%      │ ↑ 15.2%      │              │              │     │
│ └──────────────┴──────────────┴──────────────┴──────────────┘     │
│                                                                     │
│ 销售趋势图                        等级分布                          │
│ ┌─────────────────────────┐     ┌─────────────────────────┐     │
│ │     📊 折线图           │     │     🍩 环形图           │     │
│ │  显示最近30天销售趋势   │     │   钻石 125 (4.7%)       │     │
│ └─────────────────────────┘     │   黄金 428 (16.1%)      │     │
│                                  │   白银 856 (32.2%)      │     │
│ TOP 10 分销员                    │   青铜 1250 (47.0%)     │     │
│ ┌─────────────────────────┐     └─────────────────────────┘     │
│ │ 1. 张三  ¥12,580        │                                       │
│ │ 2. 李四  ¥8,350         │                                       │
│ │ 3. 王五  ¥7,890         │                                       │
│ └─────────────────────────┘                                       │
└─────────────────────────────────────────────────────────────────────┘
```

#### 4.3.7 奖励配置补充设计

**1. 等级权益管理页**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 等级权益配置                                          [保存]        │
├─────────────────────────────────────────────────────────────────────┤
│ [选择等级 ▼] 钻石等级                                             │
├─────────────────────────────────────────────────────────────────────┤
│ 基础权益                                                            │
│ ├─ [✓] 专属客服支持                                               │
│ ├─ [✓] 优先发货权                                                 │
│ ├─ [✓] 专属培训课程                                               │
│ └─ [✓] 年度表彰资格                                               │
│                                                                     │
│ 营销工具                                                            │
│ ├─ [✓] 专属推广海报（每月 [10___] 张）                           │
│ ├─ [✓] 定制名片设计（每年 [2____] 次）                           │
│ └─ [✓] 专属推广链接                                               │
│                                                                     │
│ 特殊权限                                                            │
│ ├─ [✓] 查看下级业绩                                               │
│ ├─ [✓] 团队管理工具                                               │
│ └─ [ ] 跨区域招募权限                                             │
│                                                                     │
│ 权益说明                                                            │
│ └─ [钻石等级分销员可享受最高级别的权益和支持...____________]      │
└─────────────────────────────────────────────────────────────────────┘
```

**2. 收款账户管理（分销员端功能，后台可查看）**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 收款账户管理 - 张三（DS001）                          [返回]       │
├─────────────────────────────────────────────────────────────────────┤
│ 账户类型 | 账户名称 | 账户号码 | 开户行 | 默认 | 状态 | 操作       │
├─────────────────────────────────────────────────────────────────────┤
│ 银行卡 | 张三 | ****8888 | 工商银行北京分行 | ✓ | 正常 | [查看]    │
│ 支付宝 | 张三 | zhang***@alipay.com | - | | 正常 | [查看]        │
│ 微信 | 张三 | ****1234 | - | | 正常 | [查看]                     │
└─────────────────────────────────────────────────────────────────────┘
```

#### 4.3.8 UI组件说明

**下拉列表标注**：
- `▼` 表示下拉选择框
- 下拉内容在控件下方显示当前选中值

**常用下拉选项**：
1. **状态类**：全部、启用、禁用、待审核、已审核、已拒绝
2. **时间类**：今天、昨天、最近7天、最近30天、本月、上月、自定义
3. **类型类**：全部类型、销售奖励、上级分润、介绍人分润
4. **等级类**：全部等级、青铜等级、白银等级、黄金等级、钻石等级
5. **支付方式**：银行卡、支付宝、微信
6. **触发阶段**：订单支付后、券码核销后、订单完成后
7. **适用范围**：全局、商品类目、指定商品
8. **佣金模式**：固定金额、百分比
9. **发票类型**：代扣代缴、有发票

**操作按钮规范**：
- 主要操作：蓝色按钮 `[保存]` `[确定]`
- 次要操作：白色按钮 `[取消]` `[返回]`
- 危险操作：红色按钮 `[删除]` `[拒绝]`
- 批量操作：显示在列表底部
- 导出操作：绿色按钮 `[导出]`

**表单验证提示**：
- 必填项：字段名后加 `*`
- 输入限制：在输入框内显示提示文字
- 错误提示：输入框下方红色文字
- 成功提示：顶部绿色通知栏

**数据展示规范**：
- 金额：¥ 符号 + 千分位
- 百分比：保留1-2位小数 + % 符号
- 时间：月-日 时:分 格式
- 手机号：中间4位打码（138****1234）
- 状态标签：使用不同颜色区分
  - 正常/启用：绿色
  - 禁用/拒绝：红色
  - 待审核/冻结：橙色
  - 已完成：蓝色

**Tab页切换规范**：
- 当前Tab高亮显示
- Tab切换不刷新页面
- 每个Tab独立的查询条件
- Tab右侧可显示统计数字

**列表操作规范**：
- 支持多选框批量操作
- 鼠标悬停显示完整信息
- 点击行可查看详情
- 操作按钮固定在右侧

**弹窗规范**：
- 遮罩层防止误操作
- ESC键可关闭弹窗
- 弹窗大小自适应内容
- 重要操作需二次确认

### 4.4 商品中心分销配置优化【v1.2新增】

#### 4.4.1 商品编辑页集成分销配置

**1. 商品编辑页新增分销Tab**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 编辑商品                                             [保存] [取消] │
├─────────────────────────────────────────────────────────────────────┤
│ [基本信息] [销售信息] [商品详情] [分销设置] [其他设置]           │
├─────────────────────────────────────────────────────────────────────┤
│ 分销设置                                                            │
│ ┌─────────────────────────────────────────────────────────────┐   │
│ │ 启用分销：[ON ⚬] OFF                 预计佣金：10%-15%       │   │
│ │                                                              │   │
│ │ 分销优先级：[100____] （数值越大在分销列表中越靠前）       │   │
│ │                                                              │   │
│ │ 分销标签：[✓] 热销  [✓] 高佣  [ ] 新品  [ ] 限时              │   │
│ │         [自定义标签____]                                     │   │
│ │                                                              │   │
│ │ 推荐理由：[_____________________________________________]   │   │
│ │         （如：高品质、好评多、转化率高）                    │   │
│ └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
│ 分享设置                                                            │
│ ┌─────────────────────────────────────────────────────────────┐   │
│ │ 分享模板：[默认模板 ▼]            [预览] [管理模板]         │   │
│ │                                                              │   │
│ │ 自定义分享内容：[ ] 启用（覆盖模板设置）                      │   │
│ │ 分享标题：[___________________________________________]      │   │
│ │ 分享描述：[___________________________________________]      │   │
│ │          [___________________________________________]      │   │
│ │ 分享图片：(●) 使用商品主图  ( ) 自定义  [上传图片]           │   │
│ └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
│ 应用配置                                           [批量设置]     │
│ ┌─────────────────────────────────────────────────────────────┐   │
│ │ 应用名称 | 状态 | 使用模板 | 操作                              │   │
│ ├─────────────────────────────────────────────────────────────┤   │
│ │ 小红书 | [⚬ ON] | 默认 | [自定义]                             │   │
│ │ 一筒不二 | [⚬ ON] | 默认 | [自定义]                           │   │
│ │ 商家小程序 | [OFF] | - | [自定义]                             │   │
│ └─────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────┘
```

**2. 分享模板管理弹窗**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 分享模板管理                                      [新建模板] [关闭] │
├─────────────────────────────────────────────────────────────────────┤
│ 模板类型：[全部 ▼] [商品模板] [类目模板] [全局模板]             │
├─────────────────────────────────────────────────────────────────────┤
│ 模板名称 | 类型 | 适用范围 | 是否默认 | 状态 | 操作                  │
├─────────────────────────────────────────────────────────────────────┤
│ 通用模板 | 全局 | 所有商品 | ✓ | 启用 | [编辑][复制]           │
│ 美妆模板 | 类目 | 美妆护肤 | × | 启用 | [编辑][复制][删除]     │
│ 高佣模板 | 商品 | 特定商品 | × | 启用 | [编辑][复制][删除]     │
└─────────────────────────────────────────────────────────────────────┘
```

**3. 应用自定义配置弹窗**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 小红书 - 自定义分享配置                              [保存] [取消] │
├─────────────────────────────────────────────────────────────────────┤
│ [✓] 使用自定义配置（不勾选则使用模板配置）                      │
│                                                                     │
│ 分享标题：[_______________________________________________]        │
│          变量：{product_name} {commission_hint}                    │
│                                                                     │
│ 分享描述：[_______________________________________________]        │
│          [_______________________________________________]        │
│          变量：{product_name} {product_price} {recommend_reason}  │
│                                                                     │
│ 笔记标签：[好物推荐] [必买清单] [________] [+添加]               │
│                                                                     │
│ 预览：                                                               │
│ ┌───────────────────────────────────────────────────────────┐   │
│ │ 笔记推荐 | 高端红酒套装                                    │   │
│ │ 姐妹们快来看！高端红酒套装真的太香了！高品质、好评多...  │   │
│ │ #好物推荐 #必买清单                                       │   │
│ └───────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────┘
```

#### 4.4.2 分销商品列表页优化

**1. 商品列表新增分销筛选**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 商品列表                                             [新增] [导出] │
├─────────────────────────────────────────────────────────────────────┤
│ 搜索：[商品名称/编码] [类目▼] [分销状态▼] [查询] [重置]         │
│ 分销筛选：(●) 全部 ( ) 已启用分销 ( ) 未启用分销                    │
├─────────────────────────────────────────────────────────────────────┤
│ [✓] | 商品信息 | 价格 | 库存 | 销量 | 分销状态 | 佣金 | 操作       │
├─────────────────────────────────────────────────────────────────────┤
│ [✓] | [图] 高端红酒套装 | 1288 | 999 | 156 | ⚬ 已启用 | 10-15% |    │
│     | SPU001          |      |     |     | 热销·高佣 |        |    │
│     |                 |      |     |     |           |        | 更多▼│
└─────────────────────────────────────────────────────────────────────┘
已选择 1 项 | [批量启用分销] [批量禁用分销] [批量设置分销]
```

**2. 批量设置分销弹窗**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 批量设置分销                                        [保存] [取消] │
├─────────────────────────────────────────────────────────────────────┤
│ 已选择 5 个商品                                                     │
│                                                                     │
│ 分销状态：[⚬ 启用] 禁用                                            │
│                                                                     │
│ 分销优先级：                                                         │
│ ( ) 不修改                                                         │
│ (●) 设置为：[100____]                                               │
│                                                                     │
│ 分销标签：                                                           │
│ [ ] 不修改                                                         │
│ [✓] 添加标签：[✓] 热销  [ ] 高佣  [✓] 新品  [ ] 限时              │
│                                                                     │
│ 分享模板：                                                           │
│ ( ) 不修改                                                         │
│ (●) 设置为：[通用模板 ▼]                                           │
│                                                                     │
│ 应用设置：                                                           │
│ [✓] 小红书  [✓] 一筒不二  [ ] 商家小程序                           │
└─────────────────────────────────────────────────────────────────────┘
```

#### 4.4.3 应用配置中心

**1. 应用管理页面**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 应用配置中心                                        [新增应用]     │
├─────────────────────────────────────────────────────────────────────┤
│ ┌──────────────────┐ ┌──────────────────┐ ┌──────────────────┐│
│ │      [小红书]      │ │    [一筒不二]    │ │   [商家小程序]   ││
│ │                  │ │                  │ │                  ││
│ │ 状态：⚬ 启用      │ │ 状态：⚬ 启用      │ │ 状态：未启用     ││
│ │ 商品：128        │ │ 商品：256        │ │ 商品：0          ││
│ │ 模板：小红书模板 │ │ 模板：通用模板   │ │ 模板：-          ││
│ │                  │ │                  │ │                  ││
│ │ [配置] [管理]    │ │ [配置] [管理]    │ │ [配置] [启用]    ││
│ └──────────────────┘ └──────────────────┘ └──────────────────┘│
└─────────────────────────────────────────────────────────────────────┘
```

**2. 应用配置页面**
```
┌─────────────────────────────────────────────────────────────────────┐
│ 配置小红书应用                                       [保存] [取消] │
├─────────────────────────────────────────────────────────────────────┤
│ 基本信息                                                            │
│ ├─ 应用ID：xhs_123456                                              │
│ ├─ 应用名称：[小红书________________]                              │
│ ├─ 小程序AppID：[___________________]                             │
│ └─ 状态：(●) 启用  ( ) 禁用                                       │
│                                                                     │
│ 分享配置                                                            │
│ ├─ 默认模板：[小红书模板 ▼]                                       │
│ ├─ 分享路径：[/pages/product/detail?id={spu_id}&dist_code={dist_code}]│
│ └─ 小程序版本：(●) 正式版  ( ) 测试版  ( ) 体验版                │
│                                                                     │
│ 变量说明                                                            │
│ ├─ {spu_id} - 商品ID                                              │
│ ├─ {dist_code} - 分销员分销码                                     │
│ ├─ {product_name} - 商品名称                                      │
│ ├─ {product_price} - 商品价格                                     │
│ └─ {commission_hint} - 佣金提示                                   │
└─────────────────────────────────────────────────────────────────────┘
```

#### 4.4.4 操作流程优化

**优化前流程**：
1. 在商品中心创建商品
2. 进入分销管理-分销商品
3. 选择商品添加到分销商品
4. 为每个应用单独配置分享信息
5. 保存配置

**优化后流程**：
1. 在商品中心创建商品时，直接在分销Tab页配置
2. 一次性完成所有分销相关设置
3. 保存商品

**优势**：
- 操作路径从5步缩短到2步
- 所有配置集中在一处完成
- 分享模板可复用，减少重复配置
- 应用配置独立管理，一次配置多处生效

### 4.5 数据迁移方案【v1.1新增】

如果从v1.0升级到v1.1，需要进行数据迁移：

```sql
-- 1. 迁移销售方案到奖励方案主表
INSERT INTO yt_dist_reward_scheme (
    scheme_code, scheme_name, scheme_desc, apply_scope, category_id, spu_id, sku_id,
    enable_sales_reward, enable_profit_sharing, profit_target, max_trace_level,
    level_config_mode, default_sales_mode, default_sales_rate, default_sales_amount,
    default_profit_mode, default_profit_rate, default_profit_amount,
    min_order_amount, max_commission, trigger_stage, effective_type,
    start_time, end_time, priority, status, creator, create_time,
    updater, update_time, deleted, tenant_id
)
SELECT 
    scheme_code, scheme_name, scheme_desc, apply_scope, NULL, spu_id, sku_id,
    1, 0, NULL, 0,  -- 只启用销售奖励
    2, commission_mode, commission_rate, commission_amount,  -- 分等级配置
    NULL, NULL, NULL,  -- 不启用分润
    min_order_amount, max_commission, trigger_stage, effective_type,
    start_time, end_time, priority, status, creator, create_time,
    updater, update_time, deleted, tenant_id
FROM yt_dist_sales_scheme;

-- 2. 迁移销售等级配置到等级配置表
INSERT INTO yt_dist_reward_level_config (
    scheme_id, level_id, level_name, sales_commission_mode, sales_commission_rate,
    sales_commission_amount, parent_profit_config, referrer_profit_config,
    condition_type, condition_config, min_order_amount, max_commission,
    remark, creator, create_time, updater, update_time, deleted, tenant_id
)
SELECT 
    r.id, s.level_id, l.level_name, s.commission_mode, s.commission_rate,
    s.commission_amount, NULL, NULL,
    1, NULL, s.min_order_amount, s.max_commission,
    '从v1.0销售方案迁移', s.creator, s.create_time, s.updater, s.update_time, s.deleted, s.tenant_id
FROM yt_dist_sales_scheme s
JOIN yt_dist_reward_scheme r ON s.scheme_code = r.scheme_code
LEFT JOIN yt_dist_level l ON s.level_id = l.id;

-- 3. 迁移分润方案
INSERT INTO yt_dist_reward_scheme (
    scheme_code, scheme_name, scheme_desc, apply_scope, category_id, spu_id, sku_id,
    enable_sales_reward, enable_profit_sharing, profit_target, max_trace_level,
    level_config_mode, default_sales_mode, default_sales_rate, default_sales_amount,
    default_profit_mode, default_profit_rate, default_profit_amount,
    min_order_amount, max_commission, trigger_stage, effective_type,
    start_time, end_time, priority, status, creator, create_time,
    updater, update_time, deleted, tenant_id
)
SELECT 
    scheme_code, scheme_name, scheme_desc, apply_scope, NULL, spu_id, sku_id,
    0, 1, profit_target, trace_level,  -- 只启用分润奖励
    2, NULL, NULL, NULL,  -- 分等级配置
    commission_mode, commission_rate, commission_amount,  -- 分润默认配置
    min_order_amount, max_commission, trigger_stage, effective_type,
    start_time, end_time, priority, status, creator, create_time,
    updater, update_time, deleted, tenant_id
FROM yt_dist_profit_scheme;

-- 4. 迁移分润等级配置
-- 需要根据profit_target和profit_config进行解析迁移
-- 具体逻辑根据v1.0的数据结构定制
```

## 5. 系统优势总结

### 5.1 v1.1版本优势

1. **SPU/SKU设计模式**
   - 方案主表类似SPU，定义整体策略
   - 等级配置表类似SKU，定义具体规格
   - 清晰的层次结构，易于理解和管理

2. **独立开关控制**
   - 销售奖励和分润奖励独立开关
   - 灵活组合，支持多种业务场景
   - 避免不必要的计算，提升性能

3. **配置继承机制**
   - 统一配置模式下直接使用方案默认值
   - 分等级配置模式下可覆盖默认值
   - 减少重复配置，提高维护效率

4. **统一管理界面**
   - 一个方案同时管理销售和分润
   - 减少操作复杂度
   - 提升用户体验

### 5.2 配置灵活性体现

1. **范围灵活**：全局 → 类目 → 商品 → SKU
2. **等级灵活**：统一配置 or 分等级差异化配置
3. **对象灵活**：销售奖、上级分润、介绍人分润独立配置
4. **追溯灵活**：支持N级追溯，不限层级

### 5.3 业务场景覆盖

1. **简单场景**：全局统一佣金比例
2. **等级激励**：不同等级不同佣金
3. **商品策略**：特定商品特殊佣金
4. **综合激励**：销售+分润组合配置
5. **推广活动**：限时高佣金活动
6. **数据迁移**：跨环境方案迁移和备份

## 6. 实施建议

1. **分阶段实施**：
   - 第一阶段：实现基础的统一奖励配置
   - 第二阶段：完善分等级配置功能
   - 第三阶段：优化管理界面和报表

2. **配置建议**：
   - 先配置全局默认方案
   - 根据需要添加特殊商品方案
   - 定期审查和优化配置

3. **性能优化**：
   - 方案配置缓存
   - 批量计算优化
   - 异步处理大批量订单

## 7. 附录

### 7.1 配置JSON结构示例

**parent_profit_config/referrer_profit_config字段结构**：
```json
{
  "1": {
    "mode": 2,
    "rate": 5.0,
    "amount": 0
  },
  "2": {
    "mode": 2,
    "rate": 3.0,
    "amount": 0
  },
  "3": {
    "mode": 2,
    "rate": 1.0,
    "amount": 0
  }
}
```

**condition_config字段结构（用于条件分润）**：
```json
{
  "type": 2,
  "rules": [
    {
      "min": 1000,
      "max": 5000,
      "rate": 5.0
    },
    {
      "min": 5000,
      "max": null,
      "rate": 8.0
    }
  ]
}
```

### 7.2 其他表结构说明

其他未修改的表结构（如分销员申请记录表、佣金账单记录表、账户余额表、提现申请表等）保持v1.0版本设计不变，确保系统的平滑升级。