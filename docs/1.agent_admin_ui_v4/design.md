# 分销管理后台 V4 设计文档 (最终版)

## 概述

基于API v1文档和现有管理后台样式，采用最简洁的设计方案：

1. 使用原生 el-drawer 和 el-dialog，不封装 BaseDialog.vue 或 BaseDrawer.vue
2. 使用官方组件，不自定义封装 card 等样式组件
3. 使用 el-collapse 折叠面板组织表单内容  
4. 直接使用 API 字段结构，无需字段映射
5. 使用现有系统的统一API封装
6. 参考 src/views/member/level 的实现模式
7. 使用 dict-tag 显示字典字段
8. 封装分销模块专用的选择下拉组件，供关联模块查询和编辑使用

## 技术架构

### 前端技术栈

- **框架**: Vue 3 + TypeScript (现有版本)
- **UI组件库**: Element Plus (现有版本)
- **状态管理**: Pinia (现有架构)
- **路由管理**: Vue Router (现有架构)
- **构建工具**: Vite (现有配置)
- **HTTP客户端**: Axios (现有配置)
- **样式方案**: SCSS (现有样式系统)

### API接口规范

```typescript
// 基础配置
const API_BASE = '/api/v1'
const AUTH_TYPE = 'Bearer Token'

// 统一响应格式
interface CommonResult<T = any> {
  code: number
  data: T
  msg: string
}

// 分页响应格式
interface PageResult<T> {
  list: T[]
  total: number
}

// API响应数据处理说明
// 后端返回格式: CommonResult<PageResult<T>>
// 前端接收格式: 经过axios拦截器处理后直接返回 PageResult<T>
// 
// 示例:
// 后端返回: { code: 200, data: { list: [...], total: 100 }, msg: "success" }
// 前端接收: { list: [...], total: 100 }
//
// 因此在组件中使用:
// const data = await AgentApi.getAgentPage(queryParams)
// list.value = data.list
// total.value = data.total

// 数据处理模式示例
export const dataHandlingPatterns = {
  // 1. 分页列表数据处理
  async getPageData(apiMethod: Function, params: any) {
    const data = await apiMethod(params)
    return {
      list: data.list,
      total: data.total
    }
  },
  
  // 2. 详情数据处理
  async getDetailData(apiMethod: Function, id: number) {
    const data = await apiMethod(id)
    return data // 直接返回详情对象
  },
  
  // 3. 列表数据处理（非分页）
  async getListData(apiMethod: Function, params?: any) {
    const data = await apiMethod(params)
    return data // 直接返回数组
  },
  
  // 4. 统计数据处理
  async getStatisticsData(apiMethod: Function, params?: any) {
    const data = await apiMethod(params)
    return data // 直接返回统计对象
  },
  
  // 5. 操作结果处理（增删改）
  async handleOperation(apiMethod: Function, params: any) {
    await apiMethod(params) // 操作成功无返回数据
    // 操作成功后通常需要刷新列表
  }
}

// 各模块数据处理示例
export const moduleDataHandling = {
  // 分销员管理
  agent: {
    async getList(params: any) {
      const data = await agentApi.getAgentPage(params)
      return { list: data.list, total: data.total }
    },
    async getDetail(id: number) {
      return await agentApi.getAgentDetail(id)
    },
    async getLevelList() {
      return await levelApi.getLevelList({})
    }
  },
  
  // 分销员标签管理
  agentTag: {
    async getList(params: any) {
      const data = await agentTagApi.getTagPage(params)
      return { list: data.list, total: data.total }
    },
    async getDetail(id: number) {
      return await agentTagApi.getTagDetail(id)
    }
  },
  
  // 佣金管理
  commission: {
    async getList(params: any) {
      const data = await commissionApi.getCommissionPage(params)
      return { list: data.list, total: data.total }
    },
    async getStatistics(params: any) {
      return await commissionApi.getCommissionStatistics(params)
    },
    async getRanking(params: any) {
      return await commissionApi.getCommissionRanking(params)
    }
  },
  
  // 商品配置管理
  goodsConfig: {
    async getList(params: any) {
      const data = await goodsConfigApi.getGoodsConfigPage(params)
      return { list: data.list, total: data.total }
    },
    async getDetail(id: number) {
      return await goodsConfigApi.getGoodsConfigDetail(id)
    }
  },
  
  // 等级管理
  level: {
    async getList(params: any) {
      const data = await levelApi.getLevelPage(params)
      return { list: data.list, total: data.total }
    },
    async getSimpleList() {
      return await levelApi.getLevelList({})
    }
  },
  
  // 海报管理
  poster: {
    async getTemplateList(params: any) {
      const data = await posterApi.getPosterTemplatePage(params)
      return { list: data.list, total: data.total }
    },
    async getAgentPosterList(params: any) {
      const data = await posterApi.getAgentPosterPage(params)
      return { list: data.list, total: data.total }
    }
  },
  
  // 奖励方案管理
  reward: {
    async getList(params: any) {
      const data = await rewardApi.getRewardPage(params)
      return { list: data.list, total: data.total }
    },
    async getLevelConfig(rewardId: number) {
      return await rewardApi.getRewardLevelByReward(rewardId)
    },
    async getTagConfig(rewardId: number) {
      return await rewardApi.getRewardTagByReward(rewardId)
    }
  },
  
  // 统计分析
  statistics: {
    async getOverview(params: any) {
      return await statisticsApi.getOverviewStatistics(params)
    },
    async getAgentRanking(params: any) {
      return await statisticsApi.getAgentRanking(params)
    },
    async getTrend(params: any) {
      return await statisticsApi.getCommissionTrend(params)
    }
  },
  
  // 提现管理
  withdraw: {
    async getList(params: any) {
      const data = await withdrawApi.getWithdrawPage(params)
      return { list: data.list, total: data.total }
    },
    async getStatistics(params: any) {
      return await withdrawApi.getWithdrawStatistics(params)
    },
    async getDetail(id: number) {
      return await withdrawApi.getWithdrawDetail(id)
    }
  }
}
```

## 完整业务模块 UI 设计

基于需求文档的9个核心模块，设计完整的业务功能界面：

### 模块概览

| 序号 | 模块名称 | 主要功能 | UI组件 |
|------|---------|---------|--------|
| 1 | 分销员管理 | 申请审核、信息管理、等级调整、团队关系 | 列表+抽屉表单+统计卡片 |
| 2 | 分销员标签管理 | 标签创建、分配、管理 | 列表+抽屉表单+标签选择器 |
| 3 | 佣金管理 | 账单管理、结算处理、统计分析 | 列表+详情抽屉+统计图表 |
| 4 | 商品配置管理 | 分销商品配置、佣金设置 | 列表+批量配置+商品选择器 |
| 5 | 等级管理 | 等级体系设置、权益配置 | 列表+抽屉表单+等级展示 |
| 6 | 海报管理 | 模板管理、海报生成、统计 | 列表+预览+生成器 |
| 7 | 奖励方案管理 | 方案配置、规则设置 | 列表+复杂表单+规则配置器 |
| 8 | 统计分析 | 数据统计、趋势分析、排行榜 | 仪表板+图表+数据表格 |
| 9 | 提现管理 | 提现审核、状态管理、统计 | 列表+审核流程+状态跟踪 |

## 核心设计模式

### 1. 分销员管理模块设计

```vue
<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="搜索" prop="keyword">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入姓名/手机号/分销码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="分销等级" prop="levelId">
        <LevelSelect v-model="queryParams.levelId" placeholder="请选择分销等级" clearable class="!w-240px" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> 重置
        </el-button>
        <el-button type="primary" @click="openForm('create')" v-hasPermi="['distribution:agent:create']">
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button type="success" @click="handleBatchAudit" :disabled="!selectedRows.length" v-hasPermi="['distribution:agent:audit']">
          <Icon icon="ep:check" class="mr-5px" /> 批量审核
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column label="分销员信息" align="left" min-width="200">
        <template #default="scope">
          <div class="flex items-center">
            <el-avatar :size="40" class="mr-10px">
              {{ scope.row.agentName.substring(0, 1) }}
            </el-avatar>
            <div>
              <div class="font-bold">{{ scope.row.agentName }}</div>
              <div class="text-xs text-gray-500">{{ scope.row.agentCode }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="手机号" align="center" prop="agentMobile" min-width="120" />
      <el-table-column label="分销等级" align="center" prop="levelName" min-width="100">
        <template #default="scope">
          <el-tag type="primary">{{ scope.row.levelName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="申请状态" align="center" prop="applyStatus" min-width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DIST_AGENT_APPLY_STATUS" :value="scope.row.applyStatus" />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" min-width="80">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center" prop="applyTime" :formatter="dateFormatter" min-width="170" />
      <el-table-column label="操作" align="center" min-width="200px" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="handleDetail(scope.row)" v-hasPermi="['distribution:agent:query']">
            详情
          </el-button>
          <el-button link type="primary" @click="openForm('update', scope.row.id)" v-hasPermi="['distribution:agent:update']">
            编辑
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)" v-hasPermi="['distribution:agent:delete']">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单抽屉 -->
  <AgentForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as AgentApi from '@/api/distribution-v4/agent'
import AgentForm from './AgentForm.vue'
import { LevelSelect } from '@/components/distribution'

defineOptions({ name: 'DistributionAgent' })

const message = useMessage()
const { t } = useI18n()

const loading = ref(true)
const list = ref([])
const total = ref(0)
const selectedRows = ref([])
const levelList = ref([])

// 查询参数 - 直接使用API字段结构
const queryParams = reactive({
  pageNo: 1,
  pageSize: 20,
  keyword: undefined,
  levelId: undefined,
  status: undefined,
  applyStatus: undefined
})

const queryFormRef = ref()

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await AgentApi.getAgentPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 选择变化 */
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 详情操作 */
const handleDetail = (row: any) => {
  formRef.value.open('detail', row.id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    await message.delConfirm()
    await AgentApi.deleteAgent(id)
    message.success(t('common.delSuccess'))
    await getList()
  } catch {}
}

/** 批量审核 */
const handleBatchAudit = () => {
  // 实现批量审核逻辑
}

/** 初始化 */
onMounted(async () => {
  await getList()
  // 加载等级列表
  levelList.value = await AgentApi.getLevelList()
})
</script>
```

### 2. 分销员标签管理模块设计

```vue
<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item label="标签名称" prop="tagName">
        <el-input v-model="queryParams.tagName" placeholder="请输入标签名称" clearable class="!w-240px" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="primary" @click="openForm('create')" v-hasPermi="['distribution:agent-tag:create']">
          <Icon icon="ep:plus" class="mr-5px" /> 新增标签
        </el-button>
        <el-button type="success" @click="handleExport" v-hasPermi="['distribution:agent-tag:export']">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 标签列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column label="标签名称" prop="tagName" min-width="120" />
      <el-table-column label="标签颜色" prop="tagColor" width="100">
        <template #default="scope">
          <el-tag :color="scope.row.tagColor" style="color: white;">{{ scope.row.tagName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="使用人数" prop="agentCount" width="100" />
      <el-table-column label="状态" prop="status" width="80">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" :formatter="dateFormatter" width="170" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)">编辑</el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" @pagination="getList" />
  </ContentWrap>

  <!-- 标签表单抽屉 -->
  <TagForm ref="formRef" @success="getList" />
</template>
```

### 3. 佣金管理模块设计

```vue
<template>
  <ContentWrap>
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb-20px">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="flex items-center">
            <Icon icon="ep:money" class="text-3xl text-green-500 mr-10px" />
            <div>
              <div class="text-2xl font-bold">¥{{ statistics.totalCommission }}</div>
              <div class="text-gray-500">总佣金</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="flex items-center">
            <Icon icon="ep:clock" class="text-3xl text-orange-500 mr-10px" />
            <div>
              <div class="text-2xl font-bold">¥{{ statistics.pendingCommission }}</div>
              <div class="text-gray-500">待结算佣金</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="flex items-center">
            <Icon icon="ep:check" class="text-3xl text-blue-500 mr-10px" />
            <div>
              <div class="text-2xl font-bold">¥{{ statistics.settledCommission }}</div>
              <div class="text-gray-500">已结算佣金</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="flex items-center">
            <Icon icon="ep:user" class="text-3xl text-purple-500 mr-10px" />
            <div>
              <div class="text-2xl font-bold">{{ statistics.agentCount }}</div>
              <div class="text-gray-500">分销员数量</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item label="分销员" prop="agentId">
        <AgentSelect v-model="queryParams.agentId" placeholder="请选择分销员" clearable class="!w-240px" />
      </el-form-item>
      <el-form-item label="佣金状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.DIST_COMMISSION_STATUS)" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="时间范围" prop="timeRange">
        <el-date-picker v-model="queryParams.timeRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" class="!w-240px" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="success" @click="handleBatchSettle" :disabled="!selectedRows.length" v-hasPermi="['distribution:commission:settle']">
          <Icon icon="ep:check" class="mr-5px" /> 批量结算
        </el-button>
        <el-button type="warning" @click="handleExport" v-hasPermi="['distribution:commission:export']">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 佣金列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column label="分销员信息" min-width="200">
        <template #default="scope">
          <div class="flex items-center">
            <el-avatar :size="40" class="mr-10px">{{ scope.row.agentName.substring(0, 1) }}</el-avatar>
            <div>
              <div class="font-bold">{{ scope.row.agentName }}</div>
              <div class="text-xs text-gray-500">{{ scope.row.agentCode }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="订单信息" prop="orderNo" min-width="150" />
      <el-table-column label="佣金金额" prop="commissionAmount" width="120">
        <template #default="scope">
          <span class="text-green-600 font-bold">¥{{ scope.row.commissionAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="佣金状态" prop="status" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DIST_COMMISSION_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="结算时间" prop="settleTime" :formatter="dateFormatter" width="170" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="handleDetail(scope.row)">详情</el-button>
          <el-button link type="success" @click="handleSettle(scope.row)" v-if="scope.row.status === 0" v-hasPermi="['distribution:commission:settle']">结算</el-button>
          <el-button link type="warning" @click="handleFreeze(scope.row)" v-if="scope.row.status === 1" v-hasPermi="['distribution:commission:freeze']">冻结</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" @pagination="getList" />
  </ContentWrap>

  <!-- 佣金详情抽屉 -->
  <CommissionDetail ref="detailRef" />
</template>
```

### 4. 商品配置管理模块设计

```vue
<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item label="商品名称" prop="productName">
        <el-input v-model="queryParams.productName" placeholder="请输入商品名称" clearable class="!w-240px" />
      </el-form-item>
      <el-form-item label="分销状态" prop="distributionStatus">
        <el-select v-model="queryParams.distributionStatus" placeholder="请选择状态" clearable class="!w-240px">
          <el-option label="启用分销" :value="1" />
          <el-option label="禁用分销" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="primary" @click="handleBatchConfig" :disabled="!selectedRows.length" v-hasPermi="['distribution:goods-config:update']">
          <Icon icon="ep:setting" class="mr-5px" /> 批量配置
        </el-button>
        <el-button type="success" @click="handleExport" v-hasPermi="['distribution:goods-config:export']">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 商品配置列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column label="商品信息" min-width="300">
        <template #default="scope">
          <div class="flex items-center">
            <el-image :src="scope.row.productImage" class="w-50px h-50px mr-10px" fit="cover" />
            <div>
              <div class="font-bold">{{ scope.row.productName }}</div>
              <div class="text-xs text-gray-500">SKU: {{ scope.row.productSku }}</div>
              <div class="text-sm text-red-500">¥{{ scope.row.productPrice }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="分销状态" prop="distributionStatus" width="100">
        <template #default="scope">
          <el-switch v-model="scope.row.distributionStatus" :active-value="1" :inactive-value="0" @change="handleStatusChange(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="佣金比例" prop="commissionRate" width="120">
        <template #default="scope">
          <span class="text-orange-600 font-bold">{{ scope.row.commissionRate }}%</span>
        </template>
      </el-table-column>
      <el-table-column label="固定佣金" prop="fixedCommission" width="120">
        <template #default="scope">
          <span class="text-green-600 font-bold" v-if="scope.row.fixedCommission">¥{{ scope.row.fixedCommission }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" prop="updateTime" :formatter="dateFormatter" width="170" />
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="openConfigForm(scope.row)" v-hasPermi="['distribution:goods-config:update']">配置</el-button>
          <el-button link type="primary" @click="handleDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" @pagination="getList" />
  </ContentWrap>

  <!-- 商品配置抽屉 -->
  <GoodsConfigForm ref="configFormRef" @success="getList" />
</template>
```

### 5. 等级管理模块设计

```vue
<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item label="等级名称" prop="levelName">
        <el-input v-model="queryParams.levelName" placeholder="请输入等级名称" clearable class="!w-240px" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="primary" @click="openForm('create')" v-hasPermi="['distribution:level:create']">
          <Icon icon="ep:plus" class="mr-5px" /> 新增等级
        </el-button>
        <el-button type="success" @click="handleExport" v-hasPermi="['distribution:level:export']">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 等级列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list">
      <el-table-column label="等级信息" min-width="200">
        <template #default="scope">
          <div class="flex items-center">
            <el-tag :type="getLevelTagType(scope.row.levelOrder)" size="large" class="mr-10px">
              {{ scope.row.levelName }}
            </el-tag>
            <div>
              <div class="font-bold">等级 {{ scope.row.levelOrder }}</div>
              <div class="text-xs text-gray-500">{{ scope.row.levelDesc }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="佣金比例" prop="commissionRate" width="120">
        <template #default="scope">
          <span class="text-orange-600 font-bold">{{ scope.row.commissionRate }}%</span>
        </template>
      </el-table-column>
      <el-table-column label="升级条件" min-width="200">
        <template #default="scope">
          <div class="text-sm">
            <div v-if="scope.row.upgradeOrderCount">订单数量: {{ scope.row.upgradeOrderCount }}</div>
            <div v-if="scope.row.upgradeOrderAmount">订单金额: ¥{{ scope.row.upgradeOrderAmount }}</div>
            <div v-if="scope.row.upgradeTeamCount">团队人数: {{ scope.row.upgradeTeamCount }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="分销员数量" prop="agentCount" width="100" />
      <el-table-column label="状态" prop="status" width="80">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)">编辑</el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" @pagination="getList" />
  </ContentWrap>

  <!-- 等级表单抽屉 -->
  <LevelForm ref="formRef" @success="getList" />
</template>
```

### 6. 抽屉表单设计 (使用原生 el-drawer + el-collapse)

```vue
<template>
  <el-drawer
    v-model="dialogVisible"
    :title="dialogTitle"
    :size="600"
    :before-close="handleBeforeClose"
  >
    <!-- 使用折叠面板组织表单内容 -->
    <el-collapse v-model="activeCollapse" class="drawer-collapse">
      <!-- 基本信息面板 -->
      <el-collapse-item title="基本信息" name="basic">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
          v-loading="formLoading"
        >
          <el-form-item label="分销员姓名" prop="agentName">
            <el-input v-model="formData.agentName" placeholder="请输入分销员姓名" />
          </el-form-item>
          <el-form-item label="手机号" prop="agentMobile">
            <el-input v-model="formData.agentMobile" placeholder="请输入手机号" />
          </el-form-item>
          <el-form-item label="分销员编码" prop="agentCode">
            <el-input v-model="formData.agentCode" placeholder="系统自动生成" disabled />
          </el-form-item>
        </el-form>
      </el-collapse-item>
      
      <!-- 等级标签面板 -->
      <el-collapse-item title="等级标签" name="level">
        <el-form :model="formData" label-width="120px">
          <el-form-item label="分销等级" prop="levelId">
            <LevelSelect 
              v-model="formData.levelId" 
              placeholder="请选择分销等级"
              :status="1"
            />
          </el-form-item>
          <el-form-item label="上级分销员" prop="parentId">
            <ReferrerSelect 
              v-model="formData.parentId" 
              :exclude-agent-id="formData.id"
              placeholder="请选择上级分销员" 
              clearable
            />
          </el-form-item>
        </el-form>
      </el-collapse-item>
      
      <!-- 状态信息面板 -->
      <el-collapse-item title="状态信息" name="status" v-if="formType !== 'create'">
        <el-form :model="formData" label-width="120px">
          <el-form-item label="申请状态" prop="applyStatus">
            <el-radio-group v-model="formData.applyStatus">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.DIST_AGENT_APPLY_STATUS)"
                :key="dict.value"
                :label="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="分销员状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                :key="dict.value"
                :label="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </el-collapse-item>
      
      <!-- 标签信息面板 -->
      <el-collapse-item title="标签信息" name="tags">
        <el-form :model="formData" label-width="120px">
          <el-form-item label="分销员标签" prop="tagIds">
            <AgentTagSelect 
              v-model="formData.tagIds" 
              multiple 
              placeholder="请选择分销员标签"
              :status="1"
            />
          </el-form-item>
        </el-form>
      </el-collapse-item>
      
      <!-- 其他信息面板 -->
      <el-collapse-item title="其他信息" name="other">
        <el-form :model="formData" label-width="120px">
          <el-form-item label="备注" prop="remark">
            <el-input 
              v-model="formData.remark" 
              type="textarea" 
              :rows="3" 
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-form>
      </el-collapse-item>
    </el-collapse>
    
    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="submitForm"
          :loading="formLoading"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as AgentApi from '@/api/distribution-v4/agent'
import { CommonStatusEnum } from '@/utils/constants'
import { ReferrerSelect, LevelSelect, AgentTagSelect } from '@/components/distribution'

defineOptions({ name: 'AgentForm' })

const { t } = useI18n()
const message = useMessage()

const dialogVisible = ref(false)
const dialogTitle = ref('')
const formLoading = ref(false)
const formType = ref('')
const activeCollapse = ref(['basic', 'level', 'tags']) // 默认展开的面板

// 表单数据 - 直接使用API字段结构
const formData = ref({
  id: undefined,
  agentName: undefined,
  agentMobile: undefined,
  agentCode: undefined,
  levelId: undefined,
  parentId: undefined,
  tagIds: [],
  status: CommonStatusEnum.ENABLE,
  applyStatus: 0,
  remark: undefined
})

const formRules = reactive({
  agentName: [{ required: true, message: '分销员姓名不能为空', trigger: 'blur' }],
  agentMobile: [
    { required: true, message: '手机号不能为空', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
})

const formRef = ref()
const levelOptions = ref([])
const agentOptions = ref([])

/** 打开抽屉 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  
  // 加载选项数据
  await loadOptions()
  
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await AgentApi.getAgentDetail(id)
      formData.value = data.data
    } finally {
      formLoading.value = false
    }
  }
}

/** 加载选项数据 */
const loadOptions = async () => {
  try {
    const [levels, agents] = await Promise.all([
      AgentApi.getLevelList(),
      AgentApi.getAgentList({ pageNo: 1, pageSize: 1000 })
    ])
    levelOptions.value = levels.data
    agentOptions.value = agents.data.list
  } catch (error) {
    console.error('加载选项数据失败:', error)
  }
}

/** 提交表单 */
const emit = defineEmits(['success'])
const submitForm = async () => {
  if (!formRef.value) return
  const valid = await formRef.value.validate()
  if (!valid) return
  
  formLoading.value = true
  try {
    // 直接使用API字段结构提交
    if (formType.value === 'create') {
      await AgentApi.createAgent(formData.value)
      message.success(t('common.createSuccess'))
    } else {
      await AgentApi.updateAgent(formData.value)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    agentName: undefined,
    agentMobile: undefined,
    agentCode: undefined,
    levelId: undefined,
    parentId: undefined,
    tagIds: [],
    status: CommonStatusEnum.ENABLE,
    applyStatus: 0,
    remark: undefined
  }
  formRef.value?.resetFields()
}

/** 关闭前确认 */
const handleBeforeClose = (done: () => void) => {
  done()
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
.drawer-collapse {
  .el-collapse-item__header {
    font-weight: 600;
    font-size: 16px;
    background-color: #f5f7fa;
  }
  
  .el-collapse-item__content {
    padding: 20px;
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid var(--el-border-color-light);
}
</style>
```

### 7. 海报管理模块设计

```vue
<template>
  <ContentWrap>
    <!-- 海报模板管理 -->
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="海报模板" name="template">
        <el-form class="-mb-15px" :model="templateQuery" :inline="true">
          <el-form-item label="模板名称" prop="templateName">
            <el-input v-model="templateQuery.templateName" placeholder="请输入模板名称" clearable class="!w-240px" />
          </el-form-item>
          <el-form-item label="海报类型" prop="posterType">
            <el-select v-model="templateQuery.posterType" placeholder="请选择类型" clearable class="!w-240px">
              <el-option v-for="dict in getIntDictOptions(DICT_TYPE.DIST_POSTER_TYPE)" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="handleTemplateQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
            <el-button type="primary" @click="openTemplateForm('create')">
              <Icon icon="ep:plus" class="mr-5px" /> 新增模板
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 模板列表 -->
        <el-row :gutter="20">
          <el-col :span="6" v-for="template in templateList" :key="template.id">
            <el-card class="template-card mb-20px">
              <div class="template-preview">
                <el-image :src="template.templateImage" fit="cover" class="w-full h-200px" />
              </div>
              <div class="template-info mt-10px">
                <div class="font-bold">{{ template.templateName }}</div>
                <div class="text-sm text-gray-500">{{ template.templateDesc }}</div>
                <div class="flex justify-between items-center mt-10px">
                  <el-tag :type="template.status === 1 ? 'success' : 'danger'">
                    {{ template.status === 1 ? '启用' : '禁用' }}
                  </el-tag>
                  <div>
                    <el-button link type="primary" @click="openTemplateForm('update', template.id)">编辑</el-button>
                    <el-button link type="danger" @click="handleTemplateDelete(template.id)">删除</el-button>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>

      <el-tab-pane label="分销员海报" name="agent-poster">
        <!-- 分销员海报记录 -->
        <el-form class="-mb-15px" :model="posterQuery" :inline="true">
          <el-form-item label="分销员" prop="agentId">
            <AgentSelect v-model="posterQuery.agentId" placeholder="请选择分销员" clearable class="!w-240px" />
          </el-form-item>
          <el-form-item label="海报类型" prop="posterType">
            <el-select v-model="posterQuery.posterType" placeholder="请选择类型" clearable class="!w-240px">
              <el-option v-for="dict in getIntDictOptions(DICT_TYPE.DIST_POSTER_TYPE)" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="handlePosterQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="posterLoading" :data="posterList">
          <el-table-column label="分销员信息" min-width="200">
            <template #default="scope">
              <div class="flex items-center">
                <el-avatar :size="40" class="mr-10px">{{ scope.row.agentName.substring(0, 1) }}</el-avatar>
                <div>
                  <div class="font-bold">{{ scope.row.agentName }}</div>
                  <div class="text-xs text-gray-500">{{ scope.row.agentCode }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="海报预览" width="120">
            <template #default="scope">
              <el-image :src="scope.row.posterUrl" class="w-60px h-80px" fit="cover" @click="handlePreview(scope.row.posterUrl)" />
            </template>
          </el-table-column>
          <el-table-column label="海报类型" prop="posterType" width="100">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.DIST_POSTER_TYPE" :value="scope.row.posterType" />
            </template>
          </el-table-column>
          <el-table-column label="分享次数" prop="shareCount" width="100" />
          <el-table-column label="浏览次数" prop="viewCount" width="100" />
          <el-table-column label="生成时间" prop="createTime" :formatter="dateFormatter" width="170" />
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <el-button link type="primary" @click="handlePreview(scope.row.posterUrl)">预览</el-button>
              <el-button link type="danger" @click="handlePosterDelete(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>

  <!-- 海报模板表单抽屉 -->
  <PosterTemplateForm ref="templateFormRef" @success="getTemplateList" />
  
  <!-- 海报预览对话框 -->
  <el-dialog v-model="previewVisible" title="海报预览" width="400px">
    <el-image :src="previewUrl" class="w-full" fit="contain" />
  </el-dialog>
</template>
```

### 8. 奖励方案管理模块设计

```vue
<template>
  <ContentWrap>
    <!-- 奖励方案列表 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item label="方案名称" prop="rewardName">
        <el-input v-model="queryParams.rewardName" placeholder="请输入方案名称" clearable class="!w-240px" />
      </el-form-item>
      <el-form-item label="奖励类型" prop="rewardType">
        <el-select v-model="queryParams.rewardType" placeholder="请选择类型" clearable class="!w-240px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.DIST_REWARD_TYPE)" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="primary" @click="openForm('create')" v-hasPermi="['distribution:reward:create']">
          <Icon icon="ep:plus" class="mr-5px" /> 新增方案
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 方案列表 -->
    <el-table v-loading="loading" :data="list">
      <el-table-column label="方案信息" min-width="250">
        <template #default="scope">
          <div>
            <div class="font-bold">{{ scope.row.rewardName }}</div>
            <div class="text-sm text-gray-500">{{ scope.row.rewardDesc }}</div>
            <div class="text-xs text-gray-400 mt-5px">
              有效期: {{ formatDate(scope.row.startTime) }} ~ {{ formatDate(scope.row.endTime) }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="奖励类型" prop="rewardType" width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DIST_REWARD_TYPE" :value="scope.row.rewardType" />
        </template>
      </el-table-column>
      <el-table-column label="奖励金额" prop="rewardAmount" width="120">
        <template #default="scope">
          <span class="text-red-600 font-bold">¥{{ scope.row.rewardAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="参与人数" prop="participantCount" width="100" />
      <el-table-column label="状态" prop="status" width="80">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)">编辑</el-button>
          <el-button link type="success" @click="handleConfigLevel(scope.row)" v-hasPermi="['distribution:reward-level:manage']">等级配置</el-button>
          <el-button link type="warning" @click="handleConfigTag(scope.row)" v-hasPermi="['distribution:reward-tag:manage']">标签配置</el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" @pagination="getList" />
  </ContentWrap>

  <!-- 奖励方案表单抽屉 -->
  <RewardForm ref="formRef" @success="getList" />
  
  <!-- 等级配置抽屉 -->
  <RewardLevelConfig ref="levelConfigRef" @success="getList" />
  
  <!-- 标签配置抽屉 -->
  <RewardTagConfig ref="tagConfigRef" @success="getList" />
</template>
```

### 9. 统计分析模块设计

```vue
<template>
  <ContentWrap>
    <!-- 概览统计 -->
    <el-row :gutter="20" class="mb-20px">
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic title="总分销员数" :value="overview.totalAgents" />
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic title="总佣金金额" :value="overview.totalCommission" prefix="¥" />
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic title="总订单数" :value="overview.totalOrders" />
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic title="总销售额" :value="overview.totalSales" prefix="¥" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 统计图表 -->
    <el-row :gutter="20" class="mb-20px">
      <el-col :span="12">
        <el-card title="分销趋势">
          <div ref="trendChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card title="佣金分析">
          <div ref="commissionChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 排行榜 -->
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card title="分销员排行榜">
          <el-table :data="agentRanking" size="small">
            <el-table-column label="排名" type="index" width="60" />
            <el-table-column label="分销员" prop="agentName" />
            <el-table-column label="佣金" prop="commissionAmount">
              <template #default="scope">
                <span class="text-green-600">¥{{ scope.row.commissionAmount }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card title="商品销量排行">
          <el-table :data="productRanking" size="small">
            <el-table-column label="排名" type="index" width="60" />
            <el-table-column label="商品名称" prop="productName" />
            <el-table-column label="销量" prop="salesCount" />
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card title="等级分布">
          <div ref="levelChartRef" style="height: 200px;"></div>
        </el-card>
      </el-col>
    </el-row>
  </ContentWrap>
</template>
```

### 10. 提现管理模块设计

```vue
<template>
  <ContentWrap>
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb-20px">
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic title="待审核提现" :value="statistics.pendingCount" />
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic title="今日提现金额" :value="statistics.todayAmount" prefix="¥" />
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic title="本月提现金额" :value="statistics.monthAmount" prefix="¥" />
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <el-statistic title="提现成功率" :value="statistics.successRate" suffix="%" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item label="分销员" prop="agentId">
        <AgentSelect v-model="queryParams.agentId" placeholder="请选择分销员" clearable class="!w-240px" />
      </el-form-item>
      <el-form-item label="提现状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable class="!w-240px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.DIST_WITHDRAW_STATUS)" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="时间范围" prop="timeRange">
        <el-date-picker v-model="queryParams.timeRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" class="!w-240px" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button type="success" @click="handleBatchAudit" :disabled="!selectedRows.length" v-hasPermi="['distribution:withdraw:audit']">
          <Icon icon="ep:check" class="mr-5px" /> 批量审核
        </el-button>
        <el-button type="warning" @click="handleExport" v-hasPermi="['distribution:withdraw:export']">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 提现列表 -->
    <el-table v-loading="loading" :data="list" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column label="分销员信息" min-width="200">
        <template #default="scope">
          <div class="flex items-center">
            <el-avatar :size="40" class="mr-10px">{{ scope.row.agentName.substring(0, 1) }}</el-avatar>
            <div>
              <div class="font-bold">{{ scope.row.agentName }}</div>
              <div class="text-xs text-gray-500">{{ scope.row.agentMobile }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="提现金额" prop="withdrawAmount" width="120">
        <template #default="scope">
          <span class="text-red-600 font-bold">¥{{ scope.row.withdrawAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="手续费" prop="feeAmount" width="100">
        <template #default="scope">
          <span class="text-orange-600">¥{{ scope.row.feeAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实际到账" prop="actualAmount" width="120">
        <template #default="scope">
          <span class="text-green-600 font-bold">¥{{ scope.row.actualAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="提现方式" prop="withdrawType" width="100">
        <template #default="scope">
          <el-tag>{{ scope.row.withdrawType === 1 ? '微信' : '支付宝' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DIST_WITHDRAW_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="申请时间" prop="applyTime" :formatter="dateFormatter" width="170" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="handleDetail(scope.row)">详情</el-button>
          <el-button link type="success" @click="handleAudit(scope.row, 1)" v-if="scope.row.status === 0" v-hasPermi="['distribution:withdraw:audit']">通过</el-button>
          <el-button link type="danger" @click="handleAudit(scope.row, 2)" v-if="scope.row.status === 0" v-hasPermi="['distribution:withdraw:audit']">拒绝</el-button>
          <el-button link type="warning" @click="handleMarkTransferred(scope.row)" v-if="scope.row.status === 1" v-hasPermi="['distribution:withdraw:transfer']">标记已转账</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" @pagination="getList" />
  </ContentWrap>

  <!-- 提现详情抽屉 -->
  <WithdrawDetail ref="detailRef" />
  
  <!-- 审核对话框 -->
  <WithdrawAudit ref="auditRef" @success="getList" />
</template>
```

### 11. API接口设计 (使用现有request封装)

```typescript
// api/distribution-v4/index.ts - 完整的9个模块API接口
import request from '@/config/axios'

// 1. 分销员管理API
export const agentApi = {
  getAgentPage: (params: any) => request.get({ url: '/distribution/agent/page', params }),
  getAgentDetail: (id: number) => request.get({ url: '/distribution/agent/get', params: { id } }),
  createAgent: (data: any) => request.post({ url: '/distribution/agent/create', data }),
  updateAgent: (data: any) => request.put({ url: '/distribution/agent/update', data }),
  deleteAgent: (id: number) => request.delete({ url: '/distribution/agent/delete', params: { id } }),
  auditAgent: (data: any) => request.post({ url: '/distribution/agent/audit', data }),
  batchAuditAgent: (data: any) => request.post({ url: '/distribution/agent/batch-audit', data }),
  updateAgentStatus: (data: any) => request.put({ url: '/distribution/agent/update-status', data }),
  batchUpdateAgentStatus: (data: any) => request.post({ url: '/distribution/agent/batch-update-status', data }),
  updateAgentTags: (data: any) => request.put({ url: '/distribution/agent/update-tags', data }),
  updateAgentLevel: (data: any) => request.put({ url: '/distribution/agent/update-level', data }),
  adjustAgentLevel: (data: any) => request.put({ url: '/distribution/agent/adjust-level', data }),
  adjustAgentParent: (data: any) => request.put({ url: '/distribution/agent/adjust-parent', data }),
  getAgentRelationship: (params: any) => request.get({ url: '/distribution/agent/relationship', params }),
  getTeamMembers: (params: any) => request.get({ url: '/distribution/agent/team-members', params }),
  getTeamList: (params: any) => request.get({ url: '/distribution/agent/team-list', params }),
  getChildrenTree: (params: any) => request.get({ url: '/distribution/agent/children-tree', params }),
  getAgentStatistics: () => request.get({ url: '/distribution/agent/statistics' }),
  getTeamOrders: (params: any) => request.get({ url: '/distribution/agent/team-orders', params }),
  getPersonalOrders: (params: any) => request.get({ url: '/distribution/agent/personal-orders', params }),
  exportAgent: (params: any) => request.get({ url: '/distribution/agent/export', params, responseType: 'blob' })
}

// 2. 分销员标签管理API
export const agentTagApi = {
  getTagPage: (params: any) => request.get({ url: '/distribution/agent-tag/page', params }),
  getTagList: (params: any) => request.get({ url: '/distribution/agent-tag/list', params }),
  getTagDetail: (id: number) => request.get({ url: '/distribution/agent-tag/get', params: { id } }),
  createTag: (data: any) => request.post({ url: '/distribution/agent-tag/create', data }),
  updateTag: (data: any) => request.put({ url: '/distribution/agent-tag/update', data }),
  deleteTag: (id: number) => request.delete({ url: '/distribution/agent-tag/delete', params: { id } }),
  getAgentTags: (agentId: number) => request.get({ url: '/distribution/agent-tag/get-by-agent', params: { agentId } }),
  updateAgentTags: (data: any) => request.put({ url: '/distribution/agent-tag/update-agent-tags', data }),
  exportTag: (params: any) => request.get({ url: '/distribution/agent-tag/export', params, responseType: 'blob' })
}

// 3. 佣金管理API
export const commissionApi = {
  getCommissionPage: (params: any) => request.get({ url: '/distribution/commission/page', params }),
  getCommissionDetail: (id: number) => request.get({ url: '/distribution/commission/get', params: { id } }),
  getPendingSettlement: (params: any) => request.get({ url: '/distribution/commission/pending-settlement', params }),
  settleCommission: (data: any) => request.post({ url: '/distribution/commission/settle', data }),
  batchSettleCommission: (data: any) => request.post({ url: '/distribution/commission/batch-settle', data }),
  freezeCommission: (data: any) => request.post({ url: '/distribution/commission/freeze', data }),
  cancelCommission: (data: any) => request.post({ url: '/distribution/commission/cancel', data }),
  refundCommission: (data: any) => request.post({ url: '/distribution/commission/refund', data }),
  getCommissionStatistics: (params: any) => request.get({ url: '/distribution/commission/statistics', params }),
  getCommissionRanking: (params: any) => request.get({ url: '/distribution/commission/ranking', params }),
  exportCommission: (params: any) => request.get({ url: '/distribution/commission/export', params, responseType: 'blob' })
}

// 4. 商品配置管理API
export const goodsConfigApi = {
  getGoodsConfigPage: (params: any) => request.get({ url: '/distribution/goods-config/page', params }),
  getGoodsConfigDetail: (id: number) => request.get({ url: '/distribution/goods-config/get', params: { id } }),
  batchConfigGoods: (data: any) => request.post({ url: '/distribution/goods-config/batch-config', data }),
  updateGoodsConfig: (data: any) => request.put({ url: '/distribution/goods-config/update', data }),
  updateDistributionStatus: (data: any) => request.put({ url: '/distribution/goods-config/update-status', data }),
  exportGoodsConfig: (params: any) => request.get({ url: '/distribution/goods-config/export', params, responseType: 'blob' })
}

// 5. 等级管理API
export const levelApi = {
  getLevelPage: (params: any) => request.get({ url: '/distribution/level/page', params }),
  getLevelList: (params: any) => request.get({ url: '/distribution/level/list', params }),
  getLevelDetail: (id: number) => request.get({ url: '/distribution/level/get', params: { id } }),
  createLevel: (data: any) => request.post({ url: '/distribution/level/create', data }),
  updateLevel: (data: any) => request.put({ url: '/distribution/level/update', data }),
  deleteLevel: (id: number) => request.delete({ url: '/distribution/level/delete', params: { id } }),
  exportLevel: (params: any) => request.get({ url: '/distribution/level/export', params, responseType: 'blob' })
}

// 6. 海报管理API
export const posterApi = {
  // 海报模板管理
  getPosterTemplatePage: (params: any) => request.get({ url: '/distribution/poster/template/page', params }),
  getPosterTemplateDetail: (id: number) => request.get({ url: '/distribution/poster/template/get', params: { id } }),
  createPosterTemplate: (data: any) => request.post({ url: '/distribution/poster/template/create', data }),
  updatePosterTemplate: (data: any) => request.put({ url: '/distribution/poster/template/update', data }),
  deletePosterTemplate: (id: number) => request.delete({ url: '/distribution/poster/template/delete', params: { id } }),
  updateTemplateStatus: (data: any) => request.put({ url: '/distribution/poster/template/update-status', data }),
  
  // 分销员海报管理
  getAgentPosterPage: (params: any) => request.get({ url: '/distribution/agent-poster/page', params }),
  getAgentPosterDetail: (id: number) => request.get({ url: '/distribution/agent-poster/get', params: { id } }),
  generateAgentPoster: (data: any) => request.post({ url: '/distribution/agent-poster/generate', data }),
  deleteAgentPoster: (id: number) => request.delete({ url: '/distribution/agent-poster/delete', params: { id } }),
  getAgentPosterStatistics: (params: any) => request.get({ url: '/distribution/agent-poster/statistics', params })
}

// 7. 奖励方案管理API
export const rewardApi = {
  // 奖励方案管理
  getRewardPage: (params: any) => request.get({ url: '/distribution/reward/page', params }),
  getRewardDetail: (id: number) => request.get({ url: '/distribution/reward/get', params: { id } }),
  createReward: (data: any) => request.post({ url: '/distribution/reward/create', data }),
  updateReward: (data: any) => request.put({ url: '/distribution/reward/update', data }),
  deleteReward: (id: number) => request.delete({ url: '/distribution/reward/delete', params: { id } }),
  updateRewardStatus: (data: any) => request.put({ url: '/distribution/reward/update-status', data }),
  
  // 等级奖励配置
  getRewardLevelPage: (params: any) => request.get({ url: '/distribution/reward-level/page', params }),
  getRewardLevelDetail: (id: number) => request.get({ url: '/distribution/reward-level/get', params: { id } }),
  createRewardLevel: (data: any) => request.post({ url: '/distribution/reward-level/create', data }),
  updateRewardLevel: (data: any) => request.put({ url: '/distribution/reward-level/update', data }),
  deleteRewardLevel: (id: number) => request.delete({ url: '/distribution/reward-level/delete', params: { id } }),
  batchSaveRewardLevel: (data: any) => request.post({ url: '/distribution/reward-level/batch-save', data }),
  getRewardLevelByReward: (rewardId: number) => request.get({ url: '/distribution/reward-level/get-by-reward', params: { rewardId } }),
  
  // 标签奖励配置
  getRewardTagPage: (params: any) => request.get({ url: '/distribution/reward-tag/page', params }),
  getRewardTagDetail: (id: number) => request.get({ url: '/distribution/reward-tag/get', params: { id } }),
  createRewardTag: (data: any) => request.post({ url: '/distribution/reward-tag/create', data }),
  updateRewardTag: (data: any) => request.put({ url: '/distribution/reward-tag/update', data }),
  deleteRewardTag: (id: number) => request.delete({ url: '/distribution/reward-tag/delete', params: { id } }),
  batchSaveRewardTag: (data: any) => request.post({ url: '/distribution/reward-tag/batch-save', data }),
  getRewardTagByReward: (rewardId: number) => request.get({ url: '/distribution/reward-tag/get-by-reward', params: { rewardId } })
}

// 8. 统计分析API
export const statisticsApi = {
  getOverviewStatistics: (params: any) => request.get({ url: '/distribution/statistics/overview', params }),
  getAgentRanking: (params: any) => request.get({ url: '/distribution/statistics/agent-ranking', params }),
  getCommissionTrend: (params: any) => request.get({ url: '/distribution/statistics/commission-trend', params }),
  getDistributionAnalysis: (params: any) => request.get({ url: '/distribution/statistics/distribution-analysis', params }),
  getAgentAnalysis: (params: any) => request.get({ url: '/distribution/statistics/agent-analysis', params }),
  getProductAnalysis: (params: any) => request.get({ url: '/distribution/statistics/product-analysis', params })
}

// 9. 提现管理API
export const withdrawApi = {
  getWithdrawPage: (params: any) => request.get({ url: '/distribution/withdraw/page', params }),
  getWithdrawDetail: (id: number) => request.get({ url: '/distribution/withdraw/get', params: { id } }),
  auditWithdraw: (data: any) => request.post({ url: '/distribution/withdraw/audit', data }),
  batchAuditWithdraw: (data: any) => request.post({ url: '/distribution/withdraw/batch-audit', data }),
  markTransferred: (data: any) => request.put({ url: '/distribution/withdraw/mark-transferred', data }),
  cancelWithdraw: (data: any) => request.put({ url: '/distribution/withdraw/cancel', data }),
  getWithdrawStatistics: (params: any) => request.get({ url: '/distribution/withdraw/statistics', params }),
  checkTimeout: () => request.post({ url: '/distribution/withdraw/check-timeout' }),
  retryPayment: (data: any) => request.post({ url: '/distribution/withdraw/retry-payment', data }),
  exportWithdraw: (params: any) => request.get({ url: '/distribution/withdraw/export', params, responseType: 'blob' })
}
```

## 关键设计决策

### 1. 为什么参考 member/level 模式

- 这是现有系统的标准实现模式
- 代码结构清晰，易于理解和维护
- 与现有系统保持高度一致性
- 减少学习成本和开发复杂度

### 2. 为什么使用 dict-tag 显示字典字段

- 现有系统的标准做法
- 自动处理字典值的显示和样式
- 支持多种字典类型和主题
- 无需手动编写状态映射逻辑

### 3. 为什么使用 ContentWrap 布局

- 现有系统的标准布局组件
- 提供统一的页面间距和样式
- 自动处理响应式布局
- 保持视觉一致性

### 4. 为什么直接使用 Element Plus 官方组件而不封装 BaseDialog/BaseDrawer

- 减少自定义组件的维护成本
- 保持与 Element Plus 官方组件的一致性
- 避免过度封装导致的复杂性
- 提高开发效率和代码可读性
- 各个模块实现保持简洁
- 遵循 Element Plus 官方设计规范 (<https://element-plus.org/zh-CN/component/overview.html>)

### 5. 为什么使用 el-collapse 组织表单

- 提高复杂表单的可读性
- 用户可以按需展开/折叠面板
- 适合信息分组和层次化展示
- 节省屏幕空间

## 字典类型定义

需要在系统中定义以下字典类型：

```typescript
// utils/dict.ts 中添加分销相关字典
export const DICT_TYPE = {
  // 现有字典...
  COMMON_STATUS: 'common_status',
  
  // 分销相关字典
  DIST_AGENT_APPLY_STATUS: 'dist_agent_apply_status', // 分销员申请状态
  DIST_AGENT_STATUS: 'dist_agent_status',             // 分销员状态
  DIST_COMMISSION_STATUS: 'dist_commission_status',   // 佣金状态
  DIST_WITHDRAW_STATUS: 'dist_withdraw_status',       // 提现状态
  DIST_POSTER_TYPE: 'dist_poster_type',               // 海报类型
  DIST_REWARD_TYPE: 'dist_reward_type'                // 奖励类型
}
```

## 样式规范

```scss
// 分销模块样式
.distribution-page {
  // 统计卡片样式
  .stats-card {
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
}

// 抽屉样式
.drawer-collapse {
  .el-collapse-item__header {
    font-weight: 600;
    font-size: 16px;
    background-color: #f5f7fa;
  }
  
  .el-collapse-item__content {
    padding: 20px;
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid var(--el-border-color-light);
}
```

## 分销模块专用选择组件

为了方便关联模块进行查询和编辑关联选择，封装以下专用选择组件。这些组件是分销系统的核心实体，会在多个模块中被频繁引用：

### 核心实体组件设计思路

1. **分销员选择组件** - 在佣金管理、提现管理、海报管理、奖励配置等模块中选择分销员
2. **等级选择组件** - 在分销员管理、奖励配置、商品配置等模块中选择等级
3. **标签选择组件** - 在分销员管理、奖励配置等模块中选择标签
4. **商品选择组件** - 在商品配置、海报管理等模块中选择商品
5. **推荐人选择组件** - 在分销员管理中选择推荐人（上级分销员）

### 1. 分销员选择组件 (AgentSelect.vue)

```vue
<template>
  <el-select
    v-model="selectedValue"
    :placeholder="placeholder"
    :clearable="clearable"
    :multiple="multiple"
    :disabled="disabled"
    :loading="loading"
    filterable
    remote
    :remote-method="handleRemoteSearch"
    @change="handleChange"
    @clear="handleClear"
  >
    <el-option
      v-for="agent in agentOptions"
      :key="agent.id"
      :label="`${agent.agentName} (${agent.agentCode})`"
      :value="agent.id"
    >
      <div class="flex items-center justify-between">
        <span>{{ agent.agentName }}</span>
        <span class="text-gray-400 text-sm">{{ agent.agentCode }}</span>
      </div>
    </el-option>
  </el-select>
</template>

<script setup lang="ts">
import * as AgentApi from '@/api/distribution-v4/agent'

interface Props {
  modelValue?: number | number[]
  placeholder?: string
  clearable?: boolean
  multiple?: boolean
  disabled?: boolean
  levelId?: number // 按等级筛选
  status?: number // 按状态筛选
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择分销员',
  clearable: true,
  multiple: false,
  disabled: false
})

const emit = defineEmits(['update:modelValue', 'change'])

const selectedValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const agentOptions = ref([])

// 远程搜索
const handleRemoteSearch = async (query: string) => {
  if (!query) {
    agentOptions.value = []
    return
  }
  
  loading.value = true
  try {
    const params = {
      keyword: query,
      pageNo: 1,
      pageSize: 20,
      ...(props.levelId && { levelId: props.levelId }),
      ...(props.status && { status: props.status })
    }
    const data = await AgentApi.getAgentPage(params)
    agentOptions.value = data.data.list
  } finally {
    loading.value = false
  }
}

// 初始化加载
const initLoad = async () => {
  if (props.modelValue) {
    loading.value = true
    try {
      const ids = Array.isArray(props.modelValue) ? props.modelValue : [props.modelValue]
      const promises = ids.map(id => AgentApi.getAgentDetail(id))
      const results = await Promise.all(promises)
      agentOptions.value = results.map(res => res.data)
    } finally {
      loading.value = false
    }
  }
}

const handleChange = (value: any) => {
  emit('change', value)
}

const handleClear = () => {
  agentOptions.value = []
}

onMounted(() => {
  initLoad()
})

watch(() => props.modelValue, () => {
  initLoad()
})
</script>
```

### 2. 推荐人选择组件 (ReferrerSelect.vue)

```vue
<template>
  <el-select
    v-model="selectedValue"
    :placeholder="placeholder"
    :clearable="clearable"
    :disabled="disabled"
    :loading="loading"
    filterable
    remote
    :remote-method="handleRemoteSearch"
    @change="handleChange"
    @clear="handleClear"
  >
    <el-option
      v-for="agent in agentOptions"
      :key="agent.id"
      :label="`${agent.agentName} (${agent.agentCode})`"
      :value="agent.id"
      :disabled="agent.id === excludeAgentId"
    >
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <el-avatar :size="24" class="mr-8px">{{ agent.agentName.substring(0, 1) }}</el-avatar>
          <div>
            <div class="font-medium">{{ agent.agentName }}</div>
            <div class="text-xs text-gray-400">{{ agent.agentCode }}</div>
          </div>
        </div>
        <div class="text-right">
          <el-tag size="small" type="primary">{{ agent.levelName }}</el-tag>
          <div class="text-xs text-gray-500 mt-2px">团队{{ agent.teamCount }}人</div>
        </div>
      </div>
    </el-option>
  </el-select>
</template>

<script setup lang="ts">
import * as AgentApi from '@/api/distribution-v4/agent'

interface Props {
  modelValue?: number
  placeholder?: string
  clearable?: boolean
  disabled?: boolean
  excludeAgentId?: number // 排除的分销员ID（避免选择自己作为推荐人）
  minLevel?: number // 最小等级要求
  maxLevel?: number // 最大等级要求
  onlyActiveAgent?: boolean // 只显示活跃分销员
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择推荐人',
  clearable: true,
  disabled: false,
  onlyActiveAgent: true
})

const emit = defineEmits(['update:modelValue', 'change'])

const selectedValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const agentOptions = ref([])

// 远程搜索推荐人
const handleRemoteSearch = async (query: string) => {
  if (!query) {
    agentOptions.value = []
    return
  }
  
  loading.value = true
  try {
    const params = {
      keyword: query,
      pageNo: 1,
      pageSize: 20,
      ...(props.onlyActiveAgent && { status: 1 }),
      ...(props.minLevel && { minLevel: props.minLevel }),
      ...(props.maxLevel && { maxLevel: props.maxLevel })
    }
    const data = await AgentApi.getAgentPage(params)
    // 排除指定的分销员ID
    agentOptions.value = data.list.filter(agent => agent.id !== props.excludeAgentId)
  } finally {
    loading.value = false
  }
}

// 初始化加载已选择的推荐人
const initLoad = async () => {
  if (props.modelValue) {
    loading.value = true
    try {
      const data = await AgentApi.getAgentDetail(props.modelValue)
      agentOptions.value = [data]
    } finally {
      loading.value = false
    }
  }
}

const handleChange = (value: any) => {
  emit('change', value)
}

const handleClear = () => {
  agentOptions.value = []
}

onMounted(() => {
  initLoad()
})

watch(() => props.modelValue, () => {
  initLoad()
})
</script>
```

### 3. 分销等级选择组件 (LevelSelect.vue)

```vue
<template>
  <el-select
    v-model="selectedValue"
    :placeholder="placeholder"
    :clearable="clearable"
    :multiple="multiple"
    :disabled="disabled"
    @change="handleChange"
  >
    <el-option
      v-for="level in levelOptions"
      :key="level.id"
      :label="level.levelName"
      :value="level.id"
    >
      <div class="flex items-center justify-between">
        <span>{{ level.levelName }}</span>
        <el-tag size="small" type="info">{{ level.commissionRate }}%</el-tag>
      </div>
    </el-option>
  </el-select>
</template>

<script setup lang="ts">
import * as LevelApi from '@/api/distribution-v4/level'

interface Props {
  modelValue?: number | number[]
  placeholder?: string
  clearable?: boolean
  multiple?: boolean
  disabled?: boolean
  status?: number // 按状态筛选
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择分销等级',
  clearable: true,
  multiple: false,
  disabled: false
})

const emit = defineEmits(['update:modelValue', 'change'])

const selectedValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const levelOptions = ref([])

// 加载等级列表
const loadLevels = async () => {
  try {
    const params = {
      ...(props.status && { status: props.status })
    }
    const data = await LevelApi.getLevelList(params)
    levelOptions.value = data.data
  } catch (error) {
    console.error('加载分销等级失败:', error)
  }
}

const handleChange = (value: any) => {
  emit('change', value)
}

onMounted(() => {
  loadLevels()
})
</script>
```

### 4. 分销员标签选择组件 (AgentTagSelect.vue)

```vue
<template>
  <el-select
    v-model="selectedValue"
    :placeholder="placeholder"
    :clearable="clearable"
    :multiple="multiple"
    :disabled="disabled"
    :loading="loading"
    filterable
    @change="handleChange"
    @visible-change="handleVisibleChange"
  >
    <el-option
      v-for="tag in tagOptions"
      :key="tag.id"
      :label="tag.tagName"
      :value="tag.id"
    >
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <el-tag :color="tag.tagColor" size="small" class="mr-8px" style="color: white;">
            {{ tag.tagName }}
          </el-tag>
        </div>
        <span class="text-gray-400 text-sm">{{ tag.agentCount }}人</span>
      </div>
    </el-option>
  </el-select>
</template>

<script setup lang="ts">
import * as AgentTagApi from '@/api/distribution-v4/agent-tag'

interface Props {
  modelValue?: number | number[]
  placeholder?: string
  clearable?: boolean
  multiple?: boolean
  disabled?: boolean
  status?: number // 按状态筛选标签
  showAgentCount?: boolean // 是否显示使用人数
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择标签',
  clearable: true,
  multiple: false,
  disabled: false,
  showAgentCount: true
})

const emit = defineEmits(['update:modelValue', 'change'])

const selectedValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const tagOptions = ref([])

// 加载标签列表
const loadTags = async () => {
  loading.value = true
  try {
    const params = {
      pageNo: 1,
      pageSize: 1000,
      ...(props.status && { status: props.status })
    }
    const data = await AgentTagApi.getTagPage(params)
    tagOptions.value = data.list
  } catch (error) {
    console.error('加载标签列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handleChange = (value: any) => {
  emit('change', value)
}

const handleVisibleChange = (visible: boolean) => {
  if (visible && tagOptions.value.length === 0) {
    loadTags()
  }
}

onMounted(() => {
  loadTags()
})
</script>
```

### 5. 分销商品选择组件 (ProductSelect.vue)

```vue
<template>
  <el-select
    v-model="selectedValue"
    :placeholder="placeholder"
    :clearable="clearable"
    :multiple="multiple"
    :disabled="disabled"
    :loading="loading"
    filterable
    remote
    :remote-method="handleRemoteSearch"
    @change="handleChange"
    @clear="handleClear"
  >
    <el-option
      v-for="product in productOptions"
      :key="product.id"
      :label="product.productName"
      :value="product.id"
    >
      <div class="flex items-center justify-between">
        <span>{{ product.productName }}</span>
        <span class="text-gray-400 text-sm">¥{{ product.price }}</span>
      </div>
    </el-option>
  </el-select>
</template>

<script setup lang="ts">
import * as ProductApi from '@/api/distribution-v4/product'

interface Props {
  modelValue?: number | number[]
  placeholder?: string
  clearable?: boolean
  multiple?: boolean
  disabled?: boolean
  categoryId?: number // 按分类筛选
  status?: number // 按状态筛选
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择分销商品',
  clearable: true,
  multiple: false,
  disabled: false
})

const emit = defineEmits(['update:modelValue', 'change'])

const selectedValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const productOptions = ref([])

// 远程搜索
const handleRemoteSearch = async (query: string) => {
  if (!query) {
    productOptions.value = []
    return
  }
  
  loading.value = true
  try {
    const params = {
      keyword: query,
      pageNo: 1,
      pageSize: 20,
      ...(props.categoryId && { categoryId: props.categoryId }),
      ...(props.status && { status: props.status })
    }
    const data = await ProductApi.getProductPage(params)
    productOptions.value = data.data.list
  } finally {
    loading.value = false
  }
}

// 初始化加载
const initLoad = async () => {
  if (props.modelValue) {
    loading.value = true
    try {
      const ids = Array.isArray(props.modelValue) ? props.modelValue : [props.modelValue]
      const promises = ids.map(id => ProductApi.getProductDetail(id))
      const results = await Promise.all(promises)
      productOptions.value = results.map(res => res.data)
    } finally {
      loading.value = false
    }
  }
}

const handleChange = (value: any) => {
  emit('change', value)
}

const handleClear = () => {
  productOptions.value = []
}

onMounted(() => {
  initLoad()
})

watch(() => props.modelValue, () => {
  initLoad()
})
</script>
```

### 6. 组件使用示例

```vue
<template>
  <!-- 分销员管理表单中的使用 -->
  <el-form :model="agentFormData" label-width="120px">
    <!-- 选择推荐人 -->
    <el-form-item label="推荐人" prop="parentId">
      <ReferrerSelect 
        v-model="agentFormData.parentId" 
        :exclude-agent-id="agentFormData.id"
        :min-level="1"
        @change="handleReferrerChange" 
      />
    </el-form-item>
    
    <!-- 选择分销等级 -->
    <el-form-item label="分销等级" prop="levelId">
      <LevelSelect 
        v-model="agentFormData.levelId" 
        :status="1" 
        @change="handleLevelChange" 
      />
    </el-form-item>
    
    <!-- 选择分销员标签 -->
    <el-form-item label="分销员标签" prop="tagIds">
      <AgentTagSelect 
        v-model="agentFormData.tagIds" 
        multiple 
        :status="1"
        @change="handleTagChange" 
      />
    </el-form-item>
  </el-form>

  <!-- 佣金管理中的使用 -->
  <el-form :model="commissionQuery" :inline="true">
    <el-form-item label="分销员" prop="agentId">
      <AgentSelect 
        v-model="commissionQuery.agentId" 
        placeholder="请选择分销员"
        clearable
        @change="handleAgentFilter" 
      />
    </el-form-item>
    
    <el-form-item label="分销等级" prop="levelId">
      <LevelSelect 
        v-model="commissionQuery.levelId" 
        placeholder="请选择等级"
        clearable
        @change="handleLevelFilter" 
      />
    </el-form-item>
  </el-form>

  <!-- 奖励方案配置中的使用 -->
  <el-form :model="rewardConfigData" label-width="120px">
    <!-- 等级奖励配置 -->
    <el-form-item label="适用等级" prop="levelIds">
      <LevelSelect 
        v-model="rewardConfigData.levelIds" 
        multiple
        placeholder="请选择适用等级"
        @change="handleRewardLevelChange" 
      />
    </el-form-item>
    
    <!-- 标签奖励配置 -->
    <el-form-item label="适用标签" prop="tagIds">
      <AgentTagSelect 
        v-model="rewardConfigData.tagIds" 
        multiple
        placeholder="请选择适用标签"
        @change="handleRewardTagChange" 
      />
    </el-form-item>
    
    <!-- 推广商品选择 -->
    <el-form-item label="推广商品" prop="productIds">
      <ProductSelect 
        v-model="rewardConfigData.productIds" 
        multiple 
        :category-id="rewardConfigData.categoryId"
        @change="handleProductChange" 
      />
    </el-form-item>
  </el-form>

  <!-- 海报管理中的使用 -->
  <el-form :model="posterQuery" :inline="true">
    <el-form-item label="分销员" prop="agentId">
      <AgentSelect 
        v-model="posterQuery.agentId" 
        placeholder="请选择分销员"
        clearable
      />
    </el-form-item>
    
    <el-form-item label="分销等级" prop="levelId">
      <LevelSelect 
        v-model="posterQuery.levelId" 
        placeholder="请选择等级"
        clearable
      />
    </el-form-item>
  </el-form>

  <!-- 提现管理中的使用 -->
  <el-form :model="withdrawQuery" :inline="true">
    <el-form-item label="分销员" prop="agentId">
      <AgentSelect 
        v-model="withdrawQuery.agentId" 
        placeholder="请选择分销员"
        clearable
      />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import AgentSelect from '@/components/distribution/AgentSelect.vue'
import ReferrerSelect from '@/components/distribution/ReferrerSelect.vue'
import LevelSelect from '@/components/distribution/LevelSelect.vue'
import AgentTagSelect from '@/components/distribution/AgentTagSelect.vue'
import ProductSelect from '@/components/distribution/ProductSelect.vue'

// 分销员管理表单数据
const agentFormData = ref({
  id: undefined,
  parentId: undefined,
  levelId: undefined,
  tagIds: []
})

// 佣金管理查询数据
const commissionQuery = ref({
  agentId: undefined,
  levelId: undefined
})

// 奖励方案配置数据
const rewardConfigData = ref({
  levelIds: [],
  tagIds: [],
  productIds: [],
  categoryId: undefined
})

// 海报管理查询数据
const posterQuery = ref({
  agentId: undefined,
  levelId: undefined
})

// 提现管理查询数据
const withdrawQuery = ref({
  agentId: undefined
})

// 事件处理函数
const handleReferrerChange = (value: number) => {
  console.log('选择的推荐人ID:', value)
  // 可以根据推荐人信息自动设置一些默认值
}

const handleLevelChange = (value: number) => {
  console.log('选择的等级ID:', value)
  // 可以根据等级信息更新相关配置
}

const handleTagChange = (value: number[]) => {
  console.log('选择的标签IDs:', value)
}

const handleAgentFilter = (value: number) => {
  console.log('筛选分销员ID:', value)
  // 刷新列表数据
}

const handleLevelFilter = (value: number) => {
  console.log('筛选等级ID:', value)
  // 刷新列表数据
}

const handleRewardLevelChange = (value: number[]) => {
  console.log('奖励适用等级IDs:', value)
}

const handleRewardTagChange = (value: number[]) => {
  console.log('奖励适用标签IDs:', value)
}

const handleProductChange = (value: number[]) => {
  console.log('选择的商品IDs:', value)
}
</script>
```

### 7. 组件注册和导出

```typescript
// components/distribution/index.ts
import AgentSelect from './AgentSelect.vue'
import ReferrerSelect from './ReferrerSelect.vue'
import LevelSelect from './LevelSelect.vue'
import AgentTagSelect from './AgentTagSelect.vue'
import ProductSelect from './ProductSelect.vue'

export {
  AgentSelect,
  ReferrerSelect,
  LevelSelect,
  AgentTagSelect,
  ProductSelect
}

// 在 main.ts 中全局注册
import { 
  AgentSelect, 
  ReferrerSelect, 
  LevelSelect, 
  AgentTagSelect, 
  ProductSelect 
} from '@/components/distribution'

app.component('AgentSelect', AgentSelect)
app.component('ReferrerSelect', ReferrerSelect)
app.component('LevelSelect', LevelSelect)
app.component('AgentTagSelect', AgentTagSelect)
app.component('ProductSelect', ProductSelect)
```

### 8. 组件使用场景总结

| 组件名称 | 主要使用场景 | 关键特性 |
|---------|-------------|---------|
| **AgentSelect** | 佣金管理、提现管理、海报管理等模块中选择分销员 | 远程搜索、等级筛选、状态筛选 |
| **ReferrerSelect** | 分销员管理中选择推荐人（上级分销员） | 排除自己、等级限制、团队信息展示 |
| **LevelSelect** | 分销员管理、奖励配置、筛选查询等 | 等级信息展示、佣金比例显示 |
| **AgentTagSelect** | 分销员管理、奖励配置等 | 多选支持、标签颜色显示、使用人数 |
| **ProductSelect** | 商品配置、海报管理、奖励配置等 | 远程搜索、分类筛选、价格显示 |

### 9. 组件设计原则

1. **统一的API调用方式** - 所有组件都使用统一的API接口规范
2. **灵活的筛选条件** - 支持按状态、等级、分类等多维度筛选
3. **丰富的显示信息** - 不仅显示名称，还显示相关的业务信息
4. **良好的用户体验** - 支持远程搜索、加载状态、清空操作
5. **高度可复用** - 可在任何需要选择相关实体的地方使用
6. **类型安全** - 使用TypeScript提供完整的类型定义
7. **事件回调** - 提供完整的事件回调，方便父组件处理业务逻辑

### 10. 组件扩展建议

如果后续需要扩展功能，可以考虑添加：

1. **AgentTreeSelect** - 分销员树形选择组件（显示上下级关系）
2. **LevelCascader** - 等级级联选择组件
3. **TagGroup** - 标签组管理组件
4. **ProductCascader** - 商品分类级联选择组件
5. **CommissionRangeSelect** - 佣金范围选择组件

这些专用选择组件将大大提高开发效率，确保整个分销系统的一致性和可维护性。

```

这些专用选择组件的特点：

1. **统一的API调用**: 所有组件都使用统一的API接口
2. **远程搜索**: 支持关键词搜索，提高用户体验
3. **灵活的筛选**: 支持按状态、等级、分类等条件筛选
4. **多选支持**: 支持单选和多选模式
5. **初始化加载**: 自动加载已选中项的详细信息
6. **事件回调**: 提供change事件，方便父组件处理选择变化
7. **可复用性**: 可在任何需要选择分销相关数据的地方使用

## 路由和菜单配置

```typescript
// router/modules/distribution.ts
export default {
  path: '/distribution',
  component: Layout,
  name: 'Distribution',
  meta: {
    title: '分销管理',
    icon: 'ep:share',
    alwaysShow: true
  },
  children: [
    {
      path: 'agent',
      component: () => import('@/views/distribution/agent/index.vue'),
      name: 'DistributionAgent',
      meta: { title: '分销员管理', icon: 'ep:user' }
    },
    {
      path: 'agent-tag',
      component: () => import('@/views/distribution/agent-tag/index.vue'),
      name: 'DistributionAgentTag',
      meta: { title: '分销员标签', icon: 'ep:collection-tag' }
    },
    {
      path: 'commission',
      component: () => import('@/views/distribution/commission/index.vue'),
      name: 'DistributionCommission',
      meta: { title: '佣金管理', icon: 'ep:money' }
    },
    {
      path: 'goods-config',
      component: () => import('@/views/distribution/goods-config/index.vue'),
      name: 'DistributionGoodsConfig',
      meta: { title: '商品配置', icon: 'ep:goods' }
    },
    {
      path: 'level',
      component: () => import('@/views/distribution/level/index.vue'),
      name: 'DistributionLevel',
      meta: { title: '等级管理', icon: 'ep:trophy' }
    },
    {
      path: 'poster',
      component: () => import('@/views/distribution/poster/index.vue'),
      name: 'DistributionPoster',
      meta: { title: '海报管理', icon: 'ep:picture' }
    },
    {
      path: 'reward',
      component: () => import('@/views/distribution/reward/index.vue'),
      name: 'DistributionReward',
      meta: { title: '奖励方案', icon: 'ep:present' }
    },
    {
      path: 'statistics',
      component: () => import('@/views/distribution/statistics/index.vue'),
      name: 'DistributionStatistics',
      meta: { title: '统计分析', icon: 'ep:data-analysis' }
    },
    {
      path: 'withdraw',
      component: () => import('@/views/distribution/withdraw/index.vue'),
      name: 'DistributionWithdraw',
      meta: { title: '提现管理', icon: 'ep:wallet' }
    }
  ]
}
```

## 权限配置

```typescript
// 权限常量定义
export const DISTRIBUTION_PERMISSIONS = {
  // 分销员管理权限
  AGENT_QUERY: 'distribution:agent:query',
  AGENT_CREATE: 'distribution:agent:create',
  AGENT_UPDATE: 'distribution:agent:update',
  AGENT_DELETE: 'distribution:agent:delete',
  AGENT_AUDIT: 'distribution:agent:audit',
  AGENT_EXPORT: 'distribution:agent:export',
  
  // 分销员标签权限
  AGENT_TAG_QUERY: 'distribution:agent-tag:query',
  AGENT_TAG_CREATE: 'distribution:agent-tag:create',
  AGENT_TAG_UPDATE: 'distribution:agent-tag:update',
  AGENT_TAG_DELETE: 'distribution:agent-tag:delete',
  AGENT_TAG_EXPORT: 'distribution:agent-tag:export',
  
  // 佣金管理权限
  COMMISSION_QUERY: 'distribution:commission:query',
  COMMISSION_SETTLE: 'distribution:commission:settle',
  COMMISSION_FREEZE: 'distribution:commission:freeze',
  COMMISSION_CANCEL: 'distribution:commission:cancel',
  COMMISSION_REFUND: 'distribution:commission:refund',
  COMMISSION_EXPORT: 'distribution:commission:export',
  
  // 商品配置权限
  GOODS_CONFIG_QUERY: 'distribution:goods-config:query',
  GOODS_CONFIG_UPDATE: 'distribution:goods-config:update',
  GOODS_CONFIG_EXPORT: 'distribution:goods-config:export',
  
  // 等级管理权限
  LEVEL_QUERY: 'distribution:level:query',
  LEVEL_CREATE: 'distribution:level:create',
  LEVEL_UPDATE: 'distribution:level:update',
  LEVEL_DELETE: 'distribution:level:delete',
  LEVEL_EXPORT: 'distribution:level:export',
  
  // 海报管理权限
  POSTER_QUERY: 'distribution:poster:query',
  POSTER_CREATE: 'distribution:poster:create',
  POSTER_UPDATE: 'distribution:poster:update',
  POSTER_DELETE: 'distribution:poster:delete',
  
  // 奖励方案权限
  REWARD_QUERY: 'distribution:reward:query',
  REWARD_CREATE: 'distribution:reward:create',
  REWARD_UPDATE: 'distribution:reward:update',
  REWARD_DELETE: 'distribution:reward:delete',
  REWARD_LEVEL_MANAGE: 'distribution:reward-level:manage',
  REWARD_TAG_MANAGE: 'distribution:reward-tag:manage',
  
  // 统计分析权限
  STATISTICS_QUERY: 'distribution:statistics:query',
  
  // 提现管理权限
  WITHDRAW_QUERY: 'distribution:withdraw:query',
  WITHDRAW_AUDIT: 'distribution:withdraw:audit',
  WITHDRAW_TRANSFER: 'distribution:withdraw:transfer',
  WITHDRAW_CANCEL: 'distribution:withdraw:cancel',
  WITHDRAW_EXPORT: 'distribution:withdraw:export'
}
```

## 字典类型扩展

```typescript
// utils/dict.ts 中添加完整的分销相关字典
export const DICT_TYPE = {
  // 现有字典...
  COMMON_STATUS: 'common_status',
  
  // 分销相关字典
  DIST_AGENT_APPLY_STATUS: 'dist_agent_apply_status',     // 分销员申请状态
  DIST_AGENT_STATUS: 'dist_agent_status',                 // 分销员状态
  DIST_COMMISSION_STATUS: 'dist_commission_status',       // 佣金状态
  DIST_WITHDRAW_STATUS: 'dist_withdraw_status',           // 提现状态
  DIST_POSTER_TYPE: 'dist_poster_type',                   // 海报类型
  DIST_REWARD_TYPE: 'dist_reward_type',                   // 奖励类型
  DIST_GOODS_DISTRIBUTION_STATUS: 'dist_goods_distribution_status', // 商品分销状态
  DIST_LEVEL_STATUS: 'dist_level_status',                 // 等级状态
  DIST_TAG_STATUS: 'dist_tag_status'                      // 标签状态
}
```

## 完整业务功能总结

本设计文档涵盖了分销管理后台的完整业务功能：

### 核心业务模块 (9个)

1. **分销员管理** - 申请审核、信息管理、等级调整、团队关系
2. **分销员标签管理** - 标签创建、分配、管理
3. **佣金管理** - 账单管理、结算处理、统计分析
4. **商品配置管理** - 分销商品配置、佣金设置
5. **等级管理** - 等级体系设置、权益配置
6. **海报管理** - 模板管理、海报生成、统计
7. **奖励方案管理** - 方案配置、规则设置
8. **统计分析** - 数据统计、趋势分析、排行榜
9. **提现管理** - 提现审核、状态管理、统计

### 技术特点

- 使用 Element Plus 官方组件，避免过度封装
- 统一的抽屉式交互设计
- 完整的权限控制体系
- 丰富的统计分析功能
- 专用的选择组件供其他模块复用
- 完整的API接口对接
- 统一的样式和交互规范

### 用户体验

- 直观的数据展示和操作流程
- 丰富的筛选和搜索功能
- 批量操作和导出功能
- 实时的统计数据和图表展示
- 友好的错误提示和加载状态

这个设计方案完全基于需求文档的9个核心模块，提供了完整的分销业务管理功能，确保了系统的完整性和实用性。
