# 商品分销配置模板和历史记录 API 需求文档 v1.0

## 1. 概述

### 1.1 背景
当前商品分销配置功能使用 localStorage 存储配置模板和历史记录数据，需要改为后端 API 实现，以支持：
- 数据持久化和安全性
- 多用户数据隔离
- 数据同步和共享
- 更好的性能和扩展性

### 1.2 功能范围
本文档定义了商品分销配置相关的两个核心功能的 API 接口：
1. **配置模板管理**：创建、编辑、删除、查询配置模板
2. **配置历史记录**：记录配置变更历史，支持查询和恢复

## 2. 数据模型设计

### 2.1 配置模板表 (dist_goods_config_template)

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | bigint | 是 | 主键ID |
| name | varchar(100) | 是 | 模板名称 |
| description | varchar(500) | 否 | 模板说明 |
| commission_type | tinyint | 是 | 佣金类型：0-固定金额，1-百分比 |
| commission_value | decimal(10,2) | 是 | 默认佣金值（分或百分比） |
| level_configs | json | 否 | 等级配置JSON数组 |
| tag_configs | json | 否 | 标签配置JSON数组 |
| status | tinyint | 是 | 状态：0-禁用，1-启用 |
| creator | varchar(64) | 是 | 创建人 |
| create_time | datetime | 是 | 创建时间 |
| updater | varchar(64) | 否 | 更新人 |
| update_time | datetime | 否 | 更新时间 |
| deleted | bit | 是 | 是否删除 |
| tenant_id | bigint | 是 | 租户ID |

**level_configs 和 tag_configs 的 JSON 结构：**
```json
{
  "levelConfigs": [
    {
      "levelId": 1,
      "levelName": "等级名称",
      "commissionValue": 1500  // 固定金额时为分，百分比时为百分比值
    }
  ],
  "tagConfigs": [
    {
      "tagId": 1,
      "tagName": "标签名称",
      "commissionValue": 20
    }
  ]
}
```

### 2.2 配置历史记录表 (dist_goods_config_history)

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | bigint | 是 | 主键ID |
| product_id | bigint | 是 | 商品ID |
| product_name | varchar(200) | 是 | 商品名称（冗余） |
| product_code | varchar(100) | 是 | 商品编码（冗余） |
| operation_type | varchar(20) | 是 | 操作类型：create-创建，update-更新，enable-启用，disable-禁用 |
| enabled | bit | 是 | 分销状态 |
| commission_type | tinyint | 是 | 佣金类型：0-固定金额，1-百分比 |
| commission_value | decimal(10,2) | 是 | 默认佣金值 |
| level_configs | json | 否 | 等级配置JSON数组 |
| tag_configs | json | 否 | 标签配置JSON数组 |
| remark | varchar(500) | 否 | 备注说明 |
| operator_id | bigint | 是 | 操作人ID |
| operator_name | varchar(64) | 是 | 操作人姓名 |
| create_time | datetime | 是 | 操作时间 |
| tenant_id | bigint | 是 | 租户ID |

## 3. API 接口定义

### 3.1 配置模板管理接口

#### 3.1.1 创建配置模板

**接口地址**：`POST /distribution/goods-config-template/create`

**权限标识**：`distribution:goods-config-template:create`

**请求参数**：
```json
{
  "name": "标准佣金模板",
  "description": "适用于大部分商品的标准佣金配置",
  "commissionType": 1,
  "commissionValue": 10,
  "levelConfigs": [
    {
      "levelId": 1,
      "commissionValue": 12
    }
  ],
  "tagConfigs": [
    {
      "tagId": 1,
      "commissionValue": 15
    }
  ]
}
```

**响应结果**：
```json
{
  "code": 0,
  "data": 1001,  // 返回创建的模板ID
  "msg": "success"
}
```

#### 3.1.2 更新配置模板

**接口地址**：`PUT /distribution/goods-config-template/update`

**权限标识**：`distribution:goods-config-template:update`

**请求参数**：
```json
{
  "id": 1001,
  "name": "标准佣金模板",
  "description": "适用于大部分商品的标准佣金配置",
  "commissionType": 1,
  "commissionValue": 10,
  "levelConfigs": [],
  "tagConfigs": []
}
```

#### 3.1.3 删除配置模板

**接口地址**：`DELETE /distribution/goods-config-template/delete`

**权限标识**：`distribution:goods-config-template:delete`

**请求参数**：
```
DELETE /distribution/goods-config-template/delete?id=1001
```

#### 3.1.4 获取配置模板详情

**接口地址**：`GET /distribution/goods-config-template/get`

**权限标识**：`distribution:goods-config-template:query`

**请求参数**：
```
GET /distribution/goods-config-template/get?id=1001
```

**响应结果**：
```json
{
  "code": 0,
  "data": {
    "id": 1001,
    "name": "标准佣金模板",
    "description": "适用于大部分商品的标准佣金配置",
    "commissionType": 1,
    "commissionValue": 10,
    "levelConfigs": [],
    "tagConfigs": [],
    "status": 1,
    "creator": "admin",
    "createTime": "2024-01-01 10:00:00"
  }
}
```

#### 3.1.5 获取配置模板分页列表

**接口地址**：`GET /distribution/goods-config-template/page`

**权限标识**：`distribution:goods-config-template:query`

**请求参数**：
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "name": "模板名称关键词",
  "status": 1,
  "commissionType": 1
}
```

**响应结果**：
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1001,
        "name": "标准佣金模板",
        "description": "适用于大部分商品的标准佣金配置",
        "commissionType": 1,
        "commissionValue": 10,
        "levelConfigCount": 2,  // 等级配置数量
        "tagConfigCount": 3,    // 标签配置数量
        "status": 1,
        "creator": "admin",
        "createTime": "2024-01-01 10:00:00"
      }
    ],
    "total": 20
  }
}
```

#### 3.1.6 启用/禁用配置模板

**接口地址**：`PUT /distribution/goods-config-template/update-status`

**权限标识**：`distribution:goods-config-template:update`

**请求参数**：
```json
{
  "id": 1001,
  "status": 1  // 0-禁用，1-启用
}
```

### 3.2 配置历史记录接口

#### 3.2.1 保存配置历史（内部调用）

**说明**：此接口由商品分销配置的创建/更新接口内部调用，自动记录历史

**接口地址**：`POST /distribution/goods-config-history/save`

**请求参数**：
```json
{
  "productId": 1001,
  "productName": "测试商品",
  "productCode": "TEST001",
  "operationType": "update",
  "enabled": true,
  "commissionType": 1,
  "commissionValue": 10,
  "levelConfigs": [],
  "tagConfigs": [],
  "remark": "更新佣金比例"
}
```

#### 3.2.2 获取配置历史分页列表

**接口地址**：`GET /distribution/goods-config-history/page`

**权限标识**：`distribution:goods-config-history:query`

**请求参数**：
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "productId": 1001,
  "operationType": "update",
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-12-31 23:59:59"
}
```

**响应结果**：
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "productId": 1001,
        "productName": "测试商品",
        "productCode": "TEST001",
        "operationType": "update",
        "operationTypeText": "更新",
        "enabled": true,
        "commissionType": 1,
        "commissionValue": 10,
        "levelConfigs": [],
        "tagConfigs": [],
        "remark": "更新佣金比例",
        "operatorId": 1,
        "operatorName": "管理员",
        "createTime": "2024-01-01 10:00:00"
      }
    ],
    "total": 50
  }
}
```

#### 3.2.3 获取配置历史详情

**接口地址**：`GET /distribution/goods-config-history/get`

**权限标识**：`distribution:goods-config-history:query`

**请求参数**：
```
GET /distribution/goods-config-history/get?id=1
```

#### 3.2.4 获取商品最新配置历史

**接口地址**：`GET /distribution/goods-config-history/get-latest`

**权限标识**：`distribution:goods-config-history:query`

**请求参数**：
```
GET /distribution/goods-config-history/get-latest?productId=1001
```

**说明**：返回指定商品的最新一条配置历史记录

## 4. 业务规则

### 4.1 配置模板规则
1. 模板名称在同一租户下必须唯一
2. 模板的佣金配置规则与商品配置保持一致：
   - 固定金额类型：佣金值单位为分，必须大于0
   - 百分比类型：佣金值为百分比（0-100），支持小数点后1位
3. 等级配置和标签配置中的ID必须是有效的等级/标签ID
4. 删除模板时采用逻辑删除

### 4.2 历史记录规则
1. 历史记录只能查询，不能修改或删除
2. 每次商品分销配置的创建、更新、启用、禁用操作都要记录历史
3. 历史记录要包含完整的配置快照，便于恢复
4. 历史记录按时间倒序排列，最新的在前

## 5. 性能要求

1. 模板列表查询响应时间 < 500ms
2. 历史记录查询支持分页，单页最大100条
3. 历史记录保留策略：
   - 默认保留最近6个月的记录
   - 每个商品最多保留最近100条记录
   - 超出限制的旧记录定期归档

## 6. 安全要求

1. 所有接口需要登录认证
2. 接口权限通过权限标识控制
3. 数据隔离：不同租户的数据完全隔离
4. 敏感操作（如删除模板）需要二次确认
5. 历史记录要记录完整的操作人信息

## 7. 前端改造点

### 7.1 配置模板
- 将 localStorage 操作改为调用相应的 API
- 模板列表支持分页加载
- 添加模板状态管理（启用/禁用）
- 模板名称唯一性前端校验

### 7.2 历史记录
- 历史记录改为 API 分页查询
- 不再需要前端保存历史记录
- 支持按时间范围筛选历史记录

## 8. 接口清单汇总

| 模块 | 接口地址 | 说明 | 权限标识 |
|------|----------|------|----------|
| 配置模板 | POST /distribution/goods-config-template/create | 创建模板 | distribution:goods-config-template:create |
| 配置模板 | PUT /distribution/goods-config-template/update | 更新模板 | distribution:goods-config-template:update |
| 配置模板 | DELETE /distribution/goods-config-template/delete | 删除模板 | distribution:goods-config-template:delete |
| 配置模板 | GET /distribution/goods-config-template/get | 获取模板详情 | distribution:goods-config-template:query |
| 配置模板 | GET /distribution/goods-config-template/page | 获取模板分页列表 | distribution:goods-config-template:query |
| 配置模板 | PUT /distribution/goods-config-template/update-status | 更新模板状态 | distribution:goods-config-template:update |
| 历史记录 | POST /distribution/goods-config-history/save | 保存历史记录 | 内部接口 |
| 历史记录 | GET /distribution/goods-config-history/page | 获取历史分页列表 | distribution:goods-config-history:query |
| 历史记录 | GET /distribution/goods-config-history/get | 获取历史详情 | distribution:goods-config-history:query |
| 历史记录 | GET /distribution/goods-config-history/get-latest | 获取最新历史 | distribution:goods-config-history:query |