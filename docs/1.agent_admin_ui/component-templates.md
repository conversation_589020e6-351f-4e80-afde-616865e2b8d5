# 分销管理后台组件模板库 - Claude Code 专用

## 🎯 使用说明

**重要**: 这些组件模板专门为Claude Code优化，可以直接复制粘贴使用。

### 📋 使用步骤

1. **按顺序复制**: 严格按照组件依赖关系复制
2. **完整复制**: 复制完整的代码块，包括所有部分
3. **路径准确**: 确保文件路径与模板中的路径一致
4. **立即测试**: 每复制一个组件立即测试功能

### 🔗 组件依赖关系

```
BaseDrawer (基础) 
    ↓
StatisticsCard + LevelBadge (基础组件)
    ↓
AgentDetailDrawer (复合组件)
    ↓
TeamOrdersSection (业务组件)
    ↓
AgentList (页面组件)
```

## 📦 基础组件模板

### 1. 分销员详情抽屉组件

**文件路径**: `src/components/distribution/AgentDetailDrawer.vue`

```vue
<template>
  <el-drawer
    v-model="visible"
    :title="drawerTitle"
    :size="drawerSize"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="agent-detail-drawer" v-loading="loading">
      <!-- 分销员基本信息头部 -->
      <div class="agent-header-section">
        <div class="agent-basic-info">
          <el-avatar :src="agentInfo.avatar" :size="60" />
          <div class="basic-details">
            <h3 class="agent-name">{{ agentInfo.nickname }}</h3>
            <div class="agent-meta">
              <LevelBadge :level="agentInfo.level" />
              <el-tag :type="getStatusTagType(agentInfo.status)" size="small">
                {{ getStatusText(agentInfo.status) }}
              </el-tag>
            </div>
            <div class="contact-info">
              <span class="mobile">{{ agentInfo.mobile }}</span>
              <span class="code">{{ agentInfo.agentCode }}</span>
            </div>
          </div>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="editAgent" v-if="mode !== 'create'">
              编辑
            </el-button>
            <el-button size="small" @click="adjustLevel" v-if="mode !== 'create'">
              调整等级
            </el-button>
          </div>
        </div>

        <!-- 关系链展示 -->
        <div class="relationship-chain" v-if="agentInfo.parentChain?.length">
          <div class="chain-title">
            <el-icon><Share /></el-icon>
            <span>分销关系链</span>
          </div>
          <el-breadcrumb separator="→" class="chain-breadcrumb">
            <el-breadcrumb-item 
              v-for="parent in agentInfo.parentChain" 
              :key="parent.id"
              @click="viewParentDetail(parent.id)"
              class="clickable-breadcrumb"
            >
              <el-avatar :src="parent.avatar" :size="20" />
              <span>{{ parent.nickname }}</span>
            </el-breadcrumb-item>
            <el-breadcrumb-item class="current-agent">
              <el-avatar :src="agentInfo.avatar" :size="20" />
              <span>{{ agentInfo.nickname }}</span>
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </div>

      <!-- 快速统计卡片 -->
      <div class="quick-stats-section">
        <el-row :gutter="12">
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-value">{{ agentInfo.directChildren }}</div>
              <div class="stat-label">直属下级</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-value">{{ agentInfo.totalTeam }}</div>
              <div class="stat-label">团队总数</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-value">¥{{ formatNumber(agentInfo.totalCommission) }}</div>
              <div class="stat-label">累计佣金</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 详细信息区块 -->
      <div class="detail-sections">
        <el-collapse v-model="activeCollapse" accordion>
          <el-collapse-item title="业绩统计" name="performance">
            <template #title>
              <div class="section-title">
                <el-icon><TrendCharts /></el-icon>
                <span>业绩统计</span>
                <el-badge :value="monthlyStats.orders" class="section-badge" />
              </div>
            </template>
            <AgentPerformanceSection :agent-id="agentId" />
          </el-collapse-item>

          <el-collapse-item title="分销关系" name="relationship">
            <template #title>
              <div class="section-title">
                <el-icon><Connection /></el-icon>
                <span>分销关系</span>
                <el-badge :value="agentInfo.directChildren" class="section-badge" />
              </div>
            </template>
            <AgentRelationshipSection :agent-id="agentId" @view-agent="viewRelatedAgent" />
          </el-collapse-item>

          <el-collapse-item title="团队订单" name="team-orders">
            <template #title>
              <div class="section-title">
                <el-icon><ShoppingCart /></el-icon>
                <span>团队订单</span>
                <el-badge :value="teamOrderCount" class="section-badge" />
              </div>
            </template>
            <TeamOrdersSection :agent-id="agentId" />
          </el-collapse-item>

          <el-collapse-item title="个人订单" name="personal-orders">
            <template #title>
              <div class="section-title">
                <el-icon><User /></el-icon>
                <span>个人订单</span>
                <el-badge :value="agentInfo.personalOrders" class="section-badge" />
              </div>
            </template>
            <PersonalOrdersSection :agent-id="agentId" />
          </el-collapse-item>

          <el-collapse-item title="佣金明细" name="commission">
            <template #title>
              <div class="section-title">
                <el-icon><Money /></el-icon>
                <span>佣金明细</span>
                <el-badge :value="commissionCount" class="section-badge" />
              </div>
            </template>
            <CommissionDetailsSection :agent-id="agentId" />
          </el-collapse-item>

          <el-collapse-item title="操作日志" name="logs">
            <template #title>
              <div class="section-title">
                <el-icon><Document /></el-icon>
                <span>操作日志</span>
              </div>
            </template>
            <OperationLogsSection :agent-id="agentId" />
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>

    <!-- 抽屉底部操作栏 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSave" v-if="mode === 'edit'">
          保存修改
        </el-button>
        <el-button type="success" @click="handleCreate" v-if="mode === 'create'">
          创建分销员
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { DistributionAgentAPI } from '@/api/distribution/agent'
import type { AgentInfo } from '@/types/distribution/agent'
import LevelBadge from './LevelBadge.vue'
import AgentPerformanceSection from './AgentPerformanceSection.vue'
import AgentRelationshipSection from './AgentRelationshipSection.vue'
import TeamOrdersSection from './TeamOrdersSection.vue'
import PersonalOrdersSection from './PersonalOrdersSection.vue'
import CommissionDetailsSection from './CommissionDetailsSection.vue'
import OperationLogsSection from './OperationLogsSection.vue'

interface Props {
  modelValue: boolean
  agentId?: number
  mode: 'detail' | 'edit' | 'create'
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'refresh'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const drawerTitle = computed(() => {
  switch (props.mode) {
    case 'create': return '新增分销员'
    case 'edit': return '编辑分销员'
    default: return '分销员详情'
  }
})

const drawerSize = computed(() => {
  return props.mode === 'detail' ? '60%' : '40%'
})

const loading = ref(false)
const agentInfo = ref<AgentInfo>({} as AgentInfo)
const activeCollapse = ref('performance')
const monthlyStats = ref({ orders: 0 })
const teamOrderCount = ref(0)
const commissionCount = ref(0)

const fetchAgentDetail = async () => {
  if (!props.agentId) return
  
  loading.value = true
  try {
    const response = await DistributionAgentAPI.getAgentDetail(props.agentId)
    agentInfo.value = response.data
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  emit('update:modelValue', false)
}

const handleSave = () => {
  // 保存逻辑
}

const handleCreate = () => {
  // 创建逻辑
}

const editAgent = () => {
  // 编辑逻辑
}

const adjustLevel = () => {
  // 调整等级逻辑
}

const viewParentDetail = (id: number) => {
  // 查看上级详情
}

const viewRelatedAgent = (id: number) => {
  // 查看相关分销员
}

const formatNumber = (num: number) => {
  return num?.toLocaleString() || '0'
}

const getStatusTagType = (status: number) => {
  const types = ['info', 'success', 'warning', 'danger']
  return types[status] || 'info'
}

const getStatusText = (status: number) => {
  const texts = ['未激活', '正常', '暂停', '禁用']
  return texts[status] || '未知'
}

watch(() => props.agentId, (newId) => {
  if (newId && props.mode !== 'create') {
    fetchAgentDetail()
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.agent-detail-drawer {
  .agent-header-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;

    .agent-basic-info {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      margin-bottom: 16px;

      .basic-details {
        flex: 1;

        .agent-name {
          margin: 0 0 8px 0;
          font-size: 18px;
          font-weight: 600;
        }

        .agent-meta {
          display: flex;
          gap: 8px;
          margin-bottom: 8px;
        }

        .contact-info {
          display: flex;
          gap: 16px;
          color: #666;
          font-size: 14px;
        }
      }

      .header-actions {
        display: flex;
        gap: 8px;
      }
    }

    .relationship-chain {
      .chain-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
      }

      .chain-breadcrumb {
        :deep(.el-breadcrumb__item) {
          .el-breadcrumb__inner {
            display: flex;
            align-items: center;
            gap: 4px;
          }
        }

        .clickable-breadcrumb {
          cursor: pointer;
          &:hover {
            color: var(--el-color-primary);
          }
        }

        .current-agent {
          font-weight: 600;
        }
      }
    }
  }

  .quick-stats-section {
    margin-bottom: 20px;

    .stat-card {
      text-align: center;
      padding: 16px;
      background: white;
      border: 1px solid #e4e7ed;
      border-radius: 6px;

      .stat-value {
        font-size: 20px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .detail-sections {
    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;

      .section-badge {
        margin-left: auto;
      }
    }

    :deep(.el-collapse-item__header) {
      padding-left: 0;
      padding-right: 0;
    }

    :deep(.el-collapse-item__content) {
      padding-bottom: 16px;
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 0;
  border-top: 1px solid #e4e7ed;
}
</style>
```

### 2. 等级徽章组件

**文件路径**: `src/components/distribution/LevelBadge.vue`

```vue
<template>
  <el-tag
    :type="tagType"
    :color="level.levelColor"
    :style="{ color: textColor }"
    :size="size"
    class="level-badge"
  >
    <el-icon v-if="level.levelIcon" class="level-icon">
      <component :is="level.levelIcon" />
    </el-icon>
    {{ level.levelName }}
  </el-tag>
</template>

<script setup lang="ts">
interface LevelInfo {
  levelName: string
  levelColor: string
  levelIcon?: string
  levelGrade: number
}

interface Props {
  level: LevelInfo
  size?: 'small' | 'default' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default'
})

const tagType = computed(() => {
  const grade = props.level.levelGrade
  if (grade >= 4) return 'success'
  if (grade >= 2) return 'warning'
  return 'info'
})

const textColor = computed(() => {
  // 根据背景色计算合适的文字颜色
  const color = props.level.levelColor
  if (!color) return '#333'
  
  // 简单的对比度计算
  const hex = color.replace('#', '')
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)
  const brightness = (r * 299 + g * 587 + b * 114) / 1000
  
  return brightness > 128 ? '#333' : '#fff'
})
</script>

<style lang="scss" scoped>
.level-badge {
  .level-icon {
    margin-right: 4px;
  }
}
</style>
```

### 3. 团队订单区块组件

**文件路径**: `src/components/distribution/TeamOrdersSection.vue`

```vue
<template>
  <div class="team-orders-section">
    <!-- 快速筛选 -->
    <div class="quick-filters">
      <el-radio-group v-model="quickFilter" @change="handleQuickFilter">
        <el-radio-button label="all">全部订单</el-radio-button>
        <el-radio-button label="today">今日订单</el-radio-button>
        <el-radio-button label="week">本周订单</el-radio-button>
        <el-radio-button label="month">本月订单</el-radio-button>
      </el-radio-group>
      
      <div class="filter-actions">
        <el-button size="small" @click="showAdvancedFilter = !showAdvancedFilter">
          <el-icon><Filter /></el-icon>
          高级筛选
        </el-button>
        <el-button size="small" type="success" @click="handleExport">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- 高级筛选面板 -->
    <el-collapse-transition>
      <div v-show="showAdvancedFilter" class="advanced-filters">
        <el-form :model="searchForm" inline size="small">
          <el-form-item label="订单号">
            <el-input v-model="searchForm.orderNo" placeholder="订单号" clearable />
          </el-form-item>
          <el-form-item label="分销员">
            <el-select v-model="searchForm.agentId" placeholder="选择分销员" filterable clearable>
              <el-option 
                v-for="agent in teamMembers" 
                :key="agent.id" 
                :label="agent.nickname" 
                :value="agent.id" 
              />
            </el-select>
          </el-form-item>
          <el-form-item label="分销层级">
            <el-select v-model="searchForm.distributionLevel" placeholder="选择层级" clearable>
              <el-option label="直属下级" :value="1" />
              <el-option label="二级下级" :value="2" />
              <el-option label="三级下级" :value="3" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-collapse-transition>

    <!-- 统计概览 -->
    <div class="orders-overview">
      <el-row :gutter="16">
        <el-col :span="6">
          <div class="overview-item">
            <div class="item-value">{{ orderStats.totalCount }}</div>
            <div class="item-label">总订单数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="overview-item">
            <div class="item-value">¥{{ formatNumber(orderStats.totalAmount) }}</div>
            <div class="item-label">总订单金额</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="overview-item">
            <div class="item-value">¥{{ formatNumber(orderStats.totalCommission) }}</div>
            <div class="item-label">总佣金金额</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="overview-item">
            <div class="item-value">{{ orderStats.activeAgents }}</div>
            <div class="item-label">活跃分销员</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 订单列表 -->
    <div class="orders-list">
      <div v-if="orderList.length" class="order-cards">
        <div 
          v-for="order in orderList" 
          :key="order.id"
          class="order-card"
          @click="viewOrderDetail(order)"
        >
          <!-- 订单头部 -->
          <div class="order-header">
            <div class="order-info">
              <span class="order-no">{{ order.orderNo }}</span>
              <el-tag :type="getOrderStatusType(order.orderStatus)" size="small">
                {{ getOrderStatusText(order.orderStatus) }}
              </el-tag>
            </div>
            <div class="order-time">{{ formatTime(order.orderTime) }}</div>
          </div>

          <!-- 分销员信息 -->
          <div class="agent-section">
            <el-avatar :src="order.agent.avatar" :size="32" />
            <div class="agent-details">
              <div class="agent-name">{{ order.agent.nickname }}</div>
              <div class="agent-meta">
                <LevelBadge :level="order.agent.level" size="small" />
                <el-tag size="small" :type="getDistributionLevelTagType(order.distributionLevel)">
                  {{ getDistributionLevelText(order.distributionLevel) }}
                </el-tag>
              </div>
            </div>
          </div>

          <!-- 商品信息 -->
          <div class="product-section">
            <el-image :src="order.product.image" :size="40" class="product-image" />
            <div class="product-details">
              <div class="product-name">{{ order.product.name }}</div>
              <div class="product-price">¥{{ order.product.price }} × {{ order.quantity }}</div>
            </div>
          </div>

          <!-- 金额信息 -->
          <div class="amount-section">
            <div class="amount-row">
              <span class="label">订单金额:</span>
              <span class="value">¥{{ order.orderAmount }}</span>
            </div>
            <div class="amount-row">
              <span class="label">佣金金额:</span>
              <span class="value commission">¥{{ order.commissionAmount }}</span>
              <el-tag :type="getCommissionStatusType(order.commissionStatus)" size="small">
                {{ getCommissionStatusText(order.commissionStatus) }}
              </el-tag>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="order-actions" @click.stop>
            <el-button size="small" @click="viewOrderDetail(order)">详情</el-button>
            <el-button size="small" @click="viewCommissionDetail(order)">佣金</el-button>
            <el-dropdown @command="(cmd) => handleOrderCommand(cmd, order)">
              <el-button size="small">
                更多<el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="view-agent">查看分销员</el-dropdown-item>
                  <el-dropdown-item command="view-buyer">查看买家</el-dropdown-item>
                  <el-dropdown-item command="track" divided>订单跟踪</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>

      <el-empty v-else description="暂无团队订单" :image-size="100" />

      <!-- 加载更多 -->
      <div v-if="hasMore" class="load-more">
        <el-button @click="loadMore" :loading="loadingMore">
          加载更多
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DistributionAgentAPI } from '@/api/distribution/agent'
import type { TeamOrderInfo } from '@/types/distribution/agent'
import LevelBadge from './LevelBadge.vue'

interface Props {
  agentId: number
}

const props = defineProps<Props>()

const quickFilter = ref('all')
const showAdvancedFilter = ref(false)
const orderList = ref<TeamOrderInfo[]>([])
const teamMembers = ref([])
const hasMore = ref(false)
const loadingMore = ref(false)

const orderStats = ref({
  totalCount: 0,
  totalAmount: 0,
  totalCommission: 0,
  activeAgents: 0
})

const searchForm = reactive({
  orderNo: '',
  agentId: null,
  distributionLevel: null
})

const fetchTeamOrders = async () => {
  try {
    const response = await DistributionAgentAPI.getTeamOrders(props.agentId, {
      ...searchForm,
      quickFilter: quickFilter.value
    })
    orderList.value = response.data.list
    // 更新统计数据
  } catch (error) {
    console.error('获取团队订单失败:', error)
  }
}

const handleQuickFilter = () => {
  fetchTeamOrders()
}

const handleSearch = () => {
  fetchTeamOrders()
}

const handleReset = () => {
  Object.assign(searchForm, {
    orderNo: '',
    agentId: null,
    distributionLevel: null
  })
  fetchTeamOrders()
}

const handleExport = () => {
  // 导出逻辑
}

const viewOrderDetail = (order: TeamOrderInfo) => {
  // 查看订单详情
}

const viewCommissionDetail = (order: TeamOrderInfo) => {
  // 查看佣金详情
}

const handleOrderCommand = (command: string, order: TeamOrderInfo) => {
  // 处理订单操作命令
}

const loadMore = () => {
  // 加载更多订单
}

const formatNumber = (num: number) => {
  return num?.toLocaleString() || '0'
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

const getOrderStatusType = (status: string) => {
  const typeMap = {
    pending: 'warning',
    paid: 'success',
    completed: 'success',
    cancelled: 'danger',
    refunded: 'info'
  }
  return typeMap[status] || 'info'
}

const getOrderStatusText = (status: string) => {
  const textMap = {
    pending: '待付款',
    paid: '已付款',
    completed: '已完成',
    cancelled: '已取消',
    refunded: '已退款'
  }
  return textMap[status] || '未知'
}

const getDistributionLevelTagType = (level: number) => {
  return level === 1 ? 'success' : level === 2 ? 'warning' : 'info'
}

const getDistributionLevelText = (level: number) => {
  const textMap = {
    1: '直属下级',
    2: '二级下级',
    3: '三级下级'
  }
  return textMap[level] || `${level}级下级`
}

const getCommissionStatusType = (status: number) => {
  const types = ['warning', 'success', 'success', 'success', 'danger', 'info']
  return types[status] || 'info'
}

const getCommissionStatusText = (status: number) => {
  const texts = ['待确认', '已确认', '已结算', '已提现', '已冻结', '已取消']
  return texts[status] || '未知'
}

onMounted(() => {
  fetchTeamOrders()
})
</script>

<style lang="scss" scoped>
.team-orders-section {
  .quick-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .filter-actions {
      display: flex;
      gap: 8px;
    }
  }

  .advanced-filters {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 16px;
  }

  .orders-overview {
    margin-bottom: 20px;

    .overview-item {
      text-align: center;
      padding: 16px;
      background: white;
      border: 1px solid #e4e7ed;
      border-radius: 6px;

      .item-value {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
      }

      .item-label {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .order-cards {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .order-card {
      padding: 16px;
      background: white;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        border-color: var(--el-color-primary);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;

        .order-info {
          display: flex;
          align-items: center;
          gap: 8px;

          .order-no {
            font-weight: 500;
            color: #333;
          }
        }

        .order-time {
          font-size: 12px;
          color: #999;
        }
      }

      .agent-section,
      .product-section {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;

        .agent-details,
        .product-details {
          flex: 1;

          .agent-name,
          .product-name {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
          }

          .agent-meta {
            display: flex;
            gap: 8px;
          }

          .product-price {
            font-size: 12px;
            color: #666;
          }
        }
      }

      .amount-section {
        margin-bottom: 12px;

        .amount-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;

          .label {
            font-size: 12px;
            color: #666;
          }

          .value {
            font-size: 14px;
            font-weight: 500;

            &.commission {
              color: var(--el-color-success);
            }
          }
        }
      }

      .order-actions {
        display: flex;
        justify-content: flex-end;
        gap: 8px;
        opacity: 0;
        transition: opacity 0.2s;
      }

      &:hover .order-actions {
        opacity: 1;
      }
    }
  }

  .load-more {
    text-align: center;
    margin-top: 20px;
  }
}
</style>
```

## 📋 Claude Code 使用指南

### 1. 复制使用步骤

**Step 1**: 复制基础组件

```bash
# 先复制这些基础组件
src/components/common/BaseDrawer.vue
src/components/distribution/StatisticsCard.vue
src/components/distribution/LevelBadge.vue
```

**Step 2**: 复制复合组件

```bash
# 再复制这些复合组件
src/components/distribution/AgentDetailDrawer.vue
src/components/distribution/TeamOrdersSection.vue
```

**Step 3**: 复制页面组件

```bash
# 最后复制页面组件
src/views/distribution/agent/list/index.vue
src/views/distribution/dashboard/index.vue
```

### 2. 导入依赖检查

每个组件复制后，检查以下导入：

```typescript
// 确保这些类型定义已创建
import type { AgentInfo } from '@/types/distribution/agent'
import type { PageResponse } from '@/types/distribution/common'

// 确保这些API已创建
import { DistributionAgentAPI } from '@/api/distribution/agent'

// 确保这些组件已创建
import LevelBadge from '@/components/distribution/LevelBadge.vue'
import BaseDrawer from '@/components/common/BaseDrawer.vue'
```

### 3. 快速验证方法

**组件渲染测试**:

```vue
<template>
  <div>
    <!-- 测试统计卡片 -->
    <StatisticsCard title="测试" :value="100" icon="User" />
    
    <!-- 测试等级徽章 -->
    <LevelBadge :level="{ levelName: '测试', levelColor: '#409EFF', levelGrade: 1 }" />
  </div>
</template>
```

**API调用测试**:

```typescript
// 在组件中测试API调用
const testAPI = async () => {
  try {
    const result = await DistributionAgentAPI.getAgentList({ pageNo: 1, pageSize: 10 })
    console.log('API调用成功:', result)
  } catch (error) {
    console.error('API调用失败:', error)
  }
}
```

### 4. 常见错误解决

**导入路径错误**:

```typescript
// ❌ 错误
import LevelBadge from './LevelBadge.vue'

// ✅ 正确
import LevelBadge from '@/components/distribution/LevelBadge.vue'
```

**类型定义缺失**:

```typescript
// 确保先创建类型定义文件
// src/types/distribution/agent.ts
// src/types/distribution/common.ts
```

**组件注册问题**:

```vue
<script setup lang="ts">
// 确保组件已正确导入
import LevelBadge from '@/components/distribution/LevelBadge.vue'
// 在 <script setup> 中导入的组件会自动注册
</script>
```

## 🔧 自定义扩展

### 1. 添加新的区块组件

```vue
<!-- 新的区块组件模板 -->
<template>
  <div class="custom-section">
    <div class="section-header">
      <h4>{{ title }}</h4>
      <div class="section-actions">
        <slot name="actions" />
      </div>
    </div>
    <div class="section-content">
      <slot />
    </div>
  </div>
</template>
```

### 2. 扩展抽屉功能

```typescript
// 在AgentDetailDrawer中添加新的折叠面板
const newSection = {
  key: 'custom',
  title: '自定义区块',
  icon: 'Custom',
  component: CustomSection,
  props: { agentId: props.agentId }
}
```

---

**注意**: 这些组件模板已经包含了完整的功能实现，可以直接复制使用。根据实际需求调整样式和功能即可。
