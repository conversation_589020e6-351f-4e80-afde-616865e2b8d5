# 分销管理后台设计文档

## 概述

全新设计的分销管理后台系统，基于Vue 3 + TypeScript + Element Plus + Vite架构，提供完整的分销业务管理功能。系统采用模块化设计，支持多级分销、灵活佣金配置和全面的数据分析。

## 系统架构

### 技术栈

- **前端框架**: Vue 3.5+ (Composition API)
- **开发语言**: TypeScript 5.3+
- **UI组件库**: Element Plus 2.8+
- **构建工具**: Vite 5.1+
- **状态管理**: Pinia 2.1+
- **路由管理**: Vue Router 4.2+
- **HTTP客户端**: Axios 1.6+
- **图表库**: ECharts 5.5+
- **样式预处理**: SCSS
- **工具库**: Lodash-es, Day.js

### 项目结构

```
src/
├── views/distribution/              # 分销管理页面
│   ├── dashboard/                   # 分销概览
│   │   └── index.vue
│   ├── agent/                       # 分销员管理
│   │   ├── list/                    # 分销员列表
│   │   ├── detail/                  # 分销员详情
│   │   ├── audit/                   # 分销员审核
│   │   └── relationship/            # 分销关系管理
│   ├── level/                       # 等级管理
│   │   ├── config/                  # 等级配置
│   │   └── upgrade/                 # 等级升级管理
│   ├── product/                     # 商品分销
│   │   ├── config/                  # 商品配置
│   │   ├── material/                # 推广素材
│   │   └── performance/             # 商品表现
│   ├── commission/                  # 佣金管理
│   │   ├── records/                 # 佣金记录
│   │   ├── settlement/              # 佣金结算
│   │   ├── withdraw/                # 提现管理
│   │   └── statistics/              # 佣金统计
│   ├── order/                       # 订单管理
│   │   ├── list/                    # 分销订单
│   │   └── tracking/                # 订单跟踪
│   ├── statistics/                  # 数据统计
│   │   ├── overview/                # 数据概览
│   │   ├── agent-performance/       # 分销员表现
│   │   ├── product-analysis/        # 商品分析
│   │   └── trend-analysis/          # 趋势分析
│   └── settings/                    # 系统配置
│       ├── basic/                   # 基础配置
│       ├── audit/                   # 审核配置
│       ├── notification/            # 通知配置
│       └── material-template/       # 素材模板
├── api/distribution/                # 分销API
│   ├── agent.ts                     # 分销员API
│   ├── level.ts                     # 等级API
│   ├── product.ts                   # 商品API
│   ├── commission.ts                # 佣金API
│   ├── order.ts                     # 订单API
│   ├── statistics.ts                # 统计API
│   └── settings.ts                  # 配置API
├── components/distribution/         # 分销专用组件
│   ├── AgentCard/                   # 分销员卡片
│   ├── LevelBadge/                  # 等级徽章
│   ├── CommissionChart/             # 佣金图表
│   ├── RelationshipTree/            # 关系树
│   ├── ProductConfigForm/           # 商品配置表单
│   └── StatisticsCard/              # 统计卡片
├── stores/distribution/             # 分销状态管理
│   ├── agent.ts                     # 分销员状态
│   ├── commission.ts                # 佣金状态
│   └── settings.ts                  # 配置状态
└── types/distribution/              # 分销类型定义
    ├── agent.ts                     # 分销员类型
    ├── commission.ts                # 佣金类型
    ├── product.ts                   # 商品类型
    └── statistics.ts                # 统计类型
```

## 核心模块设计

### 1. 分销概览模块 (Dashboard)

#### 功能特性

- 关键业务指标实时展示
- 数据趋势图表
- 待处理事项提醒
- 快捷操作入口

#### 组件结构

```vue
<!-- src/views/distribution/dashboard/index.vue -->
<template>
  <div class="distribution-dashboard">
    <!-- 统计卡片区域 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <StatisticsCard
          title="总分销员数"
          :value="dashboardData.totalAgents"
          icon="user"
          trend="+12%"
        />
      </el-col>
      <el-col :span="6">
        <StatisticsCard
          title="本月佣金"
          :value="dashboardData.monthlyCommission"
          icon="money"
          trend="+8.5%"
          format="currency"
        />
      </el-col>
      <el-col :span="6">
        <StatisticsCard
          title="分销订单"
          :value="dashboardData.distributionOrders"
          icon="shopping-cart"
          trend="+15.2%"
        />
      </el-col>
      <el-col :span="6">
        <StatisticsCard
          title="待审核"
          :value="dashboardData.pendingAudits"
          icon="warning"
          :clickable="true"
          @click="goToAudit"
        />
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="16">
        <el-card title="佣金趋势">
          <CommissionTrendChart :data="trendData" />
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card title="分销员等级分布">
          <AgentLevelChart :data="levelData" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 待处理事项 -->
    <el-card title="待处理事项" class="pending-tasks">
      <PendingTasksList :tasks="pendingTasks" />
    </el-card>
  </div>
</template>
```

### 2. 分销员管理模块 (Agent)

#### 分销员列表页面

```typescript
// 分销员搜索参数
interface AgentSearchParams {
  keyword?: string           // 姓名/手机号
  levelId?: number          // 等级ID
  status?: AgentStatus      // 状态
  auditStatus?: AuditStatus // 审核状态
  parentId?: number         // 上级ID
  registerTime?: [string, string] // 注册时间范围
  lastActiveTime?: [string, string] // 最后活跃时间
  pageNo: number
  pageSize: number
  sortField?: string        // 排序字段
  sortOrder?: 'asc' | 'desc' // 排序方向
}

// 分销员信息
interface AgentInfo {
  id: number
  agentCode: string         // 分销员编码
  nickname: string          // 昵称
  realName?: string         // 真实姓名
  mobile: string            // 手机号
  avatar?: string           // 头像
  levelId: number           // 等级ID
  levelName: string         // 等级名称
  levelColor: string        // 等级颜色
  
  // 分销关系
  parentId?: number         // 直属上级ID
  parentName?: string       // 直属上级姓名
  parentChain: AgentBasicInfo[] // 上级关系链（从顶级到直属上级）
  directChildren: number    // 直属下级数量
  totalTeam: number         // 团队总数（包括所有下级）
  maxDepth: number          // 最大分销层级深度
  
  // 业绩数据
  totalCommission: number   // 累计佣金
  availableCommission: number // 可用佣金
  frozenCommission: number  // 冻结佣金
  totalOrders: number       // 累计订单数
  monthlyOrders: number     // 本月订单数
  teamOrders: number        // 团队订单数
  personalOrders: number    // 个人推广订单数
  conversionRate: number    // 转化率
  
  // 状态信息
  status: AgentStatus       // 状态
  auditStatus: AuditStatus  // 审核状态
  auditRemark?: string      // 审核备注
  registerTime: string      // 注册时间
  lastActiveTime?: string   // 最后活跃时间
  tags?: string[]           // 标签
}

// 分销员基本信息（用于关系链展示）
interface AgentBasicInfo {
  id: number
  nickname: string
  avatar?: string
  level: {
    levelName: string
    levelColor: string
    levelGrade: number
  }
}

// 团队订单信息
interface TeamOrderInfo {
  id: number
  orderNo: string           // 订单号
  orderAmount: number       // 订单金额
  orderStatus: OrderStatus  // 订单状态
  orderTime: string         // 下单时间
  
  // 分销员信息
  agent: AgentBasicInfo     // 推广分销员
  distributionLevel: number // 分销层级（1-直属，2-二级，3-三级等）
  
  // 商品信息
  product: {
    id: number
    name: string
    image: string
    price: number
  }
  quantity: number          // 数量
  
  // 佣金信息
  commissionAmount: number  // 佣金金额
  commissionRate: number    // 佣金比例
  commissionStatus: CommissionStatus // 佣金状态
  
  // 买家信息
  buyer: {
    id: number
    nickname: string
    mobile?: string
  }
}

enum OrderStatus {
  PENDING = 'pending',      // 待付款
  PAID = 'paid',           // 已付款
  SHIPPED = 'shipped',     // 已发货
  COMPLETED = 'completed', // 已完成
  CANCELLED = 'cancelled', // 已取消
  REFUNDED = 'refunded'    // 已退款
}

enum AgentStatus {
  INACTIVE = 0,    // 未激活
  ACTIVE = 1,      // 正常
  SUSPENDED = 2,   // 暂停
  DISABLED = 3     // 禁用
}

enum AuditStatus {
  PENDING = 0,     // 待审核
  APPROVED = 1,    // 已通过
  REJECTED = 2     // 已拒绝
}
```

#### 分销员列表页面（主页面）

```vue
<!-- src/views/distribution/agent/list/index.vue -->
<template>
  <div class="agent-list-page">
    <!-- 搜索筛选区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input 
            v-model="searchForm.keyword" 
            placeholder="姓名/手机号/编码" 
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="等级">
          <el-select v-model="searchForm.levelId" placeholder="选择等级" clearable>
            <el-option 
              v-for="level in agentLevels" 
              :key="level.id" 
              :label="level.levelName" 
              :value="level.id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="正常" :value="1" />
            <el-option label="暂停" :value="2" />
            <el-option label="禁用" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="注册时间">
          <el-date-picker
            v-model="searchForm.registerTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>分销员列表</span>
          <div class="header-actions">
            <el-button 
              type="primary" 
              @click="openAgentDrawer('create')"
              v-if="hasPermission('agent:create')"
            >
              <el-icon><Plus /></el-icon>
              新增分销员
            </el-button>
            <el-button 
              type="warning" 
              @click="handleBatchAudit"
              :disabled="!selectedAgents.length"
            >
              <el-icon><Check /></el-icon>
              批量审核
            </el-button>
          </div>
        </div>
      </template>

      <el-table 
        :data="agentList" 
        v-loading="loading"
        @selection-change="handleSelectionChange"
        row-key="id"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="分销员信息" width="280" fixed="left">
          <template #default="scope">
            <div class="agent-info-cell">
              <el-avatar :src="scope.row.avatar" :size="40" />
              <div class="info">
                <div class="name-row">
                  <span class="nickname">{{ scope.row.nickname }}</span>
                  <LevelBadge :level="scope.row.level" size="small" />
                </div>
                <div class="details">
                  <span class="mobile">{{ scope.row.mobile }}</span>
                  <span class="code">{{ scope.row.agentCode }}</span>
                </div>
                <!-- 关系链预览 -->
                <div class="relationship-preview" v-if="scope.row.parentChain.length">
                  <el-tag size="small" type="info">
                    {{ scope.row.parentChain.map(p => p.nickname).join(' → ') }}
                  </el-tag>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="团队数据" width="150">
          <template #default="scope">
            <div class="team-stats">
              <div class="stat-item">
                <span class="label">直属:</span>
                <span class="value">{{ scope.row.directChildren }}</span>
              </div>
              <div class="stat-item">
                <span class="label">团队:</span>
                <span class="value">{{ scope.row.totalTeam }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="业绩数据" width="180">
          <template #default="scope">
            <div class="performance-stats">
              <div class="stat-item">
                <span class="label">累计佣金:</span>
                <span class="value">¥{{ formatNumber(scope.row.totalCommission) }}</span>
              </div>
              <div class="stat-item">
                <span class="label">本月订单:</span>
                <span class="value">{{ scope.row.monthlyOrders }}</span>
              </div>
              <div class="stat-item">
                <span class="label">转化率:</span>
                <span class="value">{{ scope.row.conversionRate }}%</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="registerTime" label="注册时间" width="160" />

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button 
              size="small" 
              type="primary" 
              @click="openAgentDrawer('detail', scope.row)"
            >
              详情
            </el-button>
            <el-button 
              size="small" 
              @click="openAgentDrawer('edit', scope.row)"
              v-if="hasPermission('agent:edit')"
            >
              编辑
            </el-button>
            <el-dropdown @command="(command) => handleCommand(command, scope.row)">
              <el-button size="small">
                更多<el-icon><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="adjust-level">调整等级</el-dropdown-item>
                  <el-dropdown-item command="view-team">查看团队</el-dropdown-item>
                  <el-dropdown-item command="view-orders">查看订单</el-dropdown-item>
                  <el-dropdown-item command="suspend" divided>暂停账户</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.pageNo"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 分销员详情抽屉 -->
    <AgentDetailDrawer
      v-model="drawerVisible"
      :agent-id="currentAgentId"
      :mode="drawerMode"
      @refresh="fetchAgentList"
    />
  </div>
</template>
```

#### 分销员详情抽屉组件

```vue
<!-- src/components/distribution/AgentDetailDrawer.vue -->
<template>
  <el-drawer
    v-model="visible"
    :title="drawerTitle"
    :size="drawerSize"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="agent-detail-drawer" v-loading="loading">
      <!-- 分销员基本信息头部 -->
      <div class="agent-header-section">
        <div class="agent-basic-info">
          <el-avatar :src="agentInfo.avatar" :size="60" />
          <div class="basic-details">
            <h3 class="agent-name">{{ agentInfo.nickname }}</h3>
            <div class="agent-meta">
              <LevelBadge :level="agentInfo.level" />
              <el-tag :type="getStatusTagType(agentInfo.status)" size="small">
                {{ getStatusText(agentInfo.status) }}
              </el-tag>
            </div>
            <div class="contact-info">
              <span class="mobile">{{ agentInfo.mobile }}</span>
              <span class="code">{{ agentInfo.agentCode }}</span>
            </div>
          </div>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="editAgent" v-if="mode !== 'create'">
              编辑
            </el-button>
            <el-button size="small" @click="adjustLevel" v-if="mode !== 'create'">
              调整等级
            </el-button>
          </div>
        </div>

        <!-- 关系链展示 -->
        <div class="relationship-chain" v-if="agentInfo.parentChain?.length">
          <div class="chain-title">
            <el-icon><Share /></el-icon>
            <span>分销关系链</span>
          </div>
          <el-breadcrumb separator="→" class="chain-breadcrumb">
            <el-breadcrumb-item 
              v-for="parent in agentInfo.parentChain" 
              :key="parent.id"
              @click="viewParentDetail(parent.id)"
              class="clickable-breadcrumb"
            >
              <el-avatar :src="parent.avatar" :size="20" />
              <span>{{ parent.nickname }}</span>
            </el-breadcrumb-item>
            <el-breadcrumb-item class="current-agent">
              <el-avatar :src="agentInfo.avatar" :size="20" />
              <span>{{ agentInfo.nickname }}</span>
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </div>

      <!-- 快速统计卡片 -->
      <div class="quick-stats-section">
        <el-row :gutter="12">
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-value">{{ agentInfo.directChildren }}</div>
              <div class="stat-label">直属下级</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-value">{{ agentInfo.totalTeam }}</div>
              <div class="stat-label">团队总数</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-value">¥{{ formatNumber(agentInfo.totalCommission) }}</div>
              <div class="stat-label">累计佣金</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 详细信息区块 -->
      <div class="detail-sections">
        <!-- 业绩统计区块 -->
        <el-collapse v-model="activeCollapse" accordion>
          <el-collapse-item title="业绩统计" name="performance">
            <template #title>
              <div class="section-title">
                <el-icon><TrendCharts /></el-icon>
                <span>业绩统计</span>
                <el-badge :value="monthlyStats.orders" class="section-badge" />
              </div>
            </template>
            <AgentPerformanceSection :agent-id="agentId" />
          </el-collapse-item>

          <!-- 分销关系区块 -->
          <el-collapse-item title="分销关系" name="relationship">
            <template #title>
              <div class="section-title">
                <el-icon><Connection /></el-icon>
                <span>分销关系</span>
                <el-badge :value="agentInfo.directChildren" class="section-badge" />
              </div>
            </template>
            <AgentRelationshipSection :agent-id="agentId" @view-agent="viewRelatedAgent" />
          </el-collapse-item>

          <!-- 团队订单区块 -->
          <el-collapse-item title="团队订单" name="team-orders">
            <template #title>
              <div class="section-title">
                <el-icon><ShoppingCart /></el-icon>
                <span>团队订单</span>
                <el-badge :value="teamOrderCount" class="section-badge" />
              </div>
            </template>
            <TeamOrdersSection :agent-id="agentId" />
          </el-collapse-item>

          <!-- 个人订单区块 -->
          <el-collapse-item title="个人订单" name="personal-orders">
            <template #title>
              <div class="section-title">
                <el-icon><User /></el-icon>
                <span>个人订单</span>
                <el-badge :value="agentInfo.personalOrders" class="section-badge" />
              </div>
            </template>
            <PersonalOrdersSection :agent-id="agentId" />
          </el-collapse-item>

          <!-- 佣金明细区块 -->
          <el-collapse-item title="佣金明细" name="commission">
            <template #title>
              <div class="section-title">
                <el-icon><Money /></el-icon>
                <span>佣金明细</span>
                <el-badge :value="commissionCount" class="section-badge" />
              </div>
            </template>
            <CommissionDetailsSection :agent-id="agentId" />
          </el-collapse-item>

          <!-- 操作日志区块 -->
          <el-collapse-item title="操作日志" name="logs">
            <template #title>
              <div class="section-title">
                <el-icon><Document /></el-icon>
                <span>操作日志</span>
              </div>
            </template>
            <OperationLogsSection :agent-id="agentId" />
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>

    <!-- 抽屉底部操作栏 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSave" v-if="mode === 'edit'">
          保存修改
        </el-button>
        <el-button type="success" @click="handleCreate" v-if="mode === 'create'">
          创建分销员
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
interface Props {
  modelValue: boolean
  agentId?: number
  mode: 'detail' | 'edit' | 'create'
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'refresh'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const drawerTitle = computed(() => {
  switch (props.mode) {
    case 'create': return '新增分销员'
    case 'edit': return '编辑分销员'
    default: return '分销员详情'
  }
})

const drawerSize = computed(() => {
  return props.mode === 'detail' ? '60%' : '40%'
})

// 默认展开第一个区块
const activeCollapse = ref('performance')
</script>

<style lang="scss" scoped>
.agent-detail-drawer {
  .agent-header-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;

    .agent-basic-info {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      margin-bottom: 16px;

      .basic-details {
        flex: 1;

        .agent-name {
          margin: 0 0 8px 0;
          font-size: 18px;
          font-weight: 600;
        }

        .agent-meta {
          display: flex;
          gap: 8px;
          margin-bottom: 8px;
        }

        .contact-info {
          display: flex;
          gap: 16px;
          color: #666;
          font-size: 14px;
        }
      }

      .header-actions {
        display: flex;
        gap: 8px;
      }
    }

    .relationship-chain {
      .chain-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
      }

      .chain-breadcrumb {
        :deep(.el-breadcrumb__item) {
          .el-breadcrumb__inner {
            display: flex;
            align-items: center;
            gap: 4px;
          }
        }

        .clickable-breadcrumb {
          cursor: pointer;
          &:hover {
            color: var(--el-color-primary);
          }
        }

        .current-agent {
          font-weight: 600;
        }
      }
    }
  }

  .quick-stats-section {
    margin-bottom: 20px;

    .stat-card {
      text-align: center;
      padding: 16px;
      background: white;
      border: 1px solid #e4e7ed;
      border-radius: 6px;

      .stat-value {
        font-size: 20px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .detail-sections {
    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;

      .section-badge {
        margin-left: auto;
      }
    }

    :deep(.el-collapse-item__header) {
      padding-left: 0;
      padding-right: 0;
    }

    :deep(.el-collapse-item__content) {
      padding-bottom: 16px;
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 0;
  border-top: 1px solid #e4e7ed;
}
</style>
```

#### 分销关系区块组件

```vue
<!-- src/components/distribution/AgentRelationshipSection.vue -->
<template>
  <div class="agent-relationship-section">
    <!-- 上级关系链 -->
    <div class="parent-chain-block" v-if="parentChain.length">
      <div class="block-header">
        <h4>上级关系链</h4>
        <el-button size="small" @click="adjustRelationship">调整关系</el-button>
      </div>
      <div class="relationship-path">
        <div 
          v-for="(agent, index) in parentChain" 
          :key="agent.id"
          class="relationship-node"
          @click="$emit('view-agent', agent.id)"
        >
          <el-avatar :src="agent.avatar" :size="36" />
          <div class="node-info">
            <div class="node-name">{{ agent.nickname }}</div>
            <LevelBadge :level="agent.level" size="small" />
          </div>
          <el-icon v-if="index < parentChain.length - 1" class="arrow">
            <ArrowRight />
          </el-icon>
        </div>
      </div>
    </div>

    <!-- 直属下级 -->
    <div class="direct-children-block">
      <div class="block-header">
        <h4>直属下级 ({{ directChildren.length }})</h4>
        <div class="header-actions">
          <el-button size="small" @click="addChild">添加下级</el-button>
          <el-button size="small" @click="viewAllTeam">查看全部</el-button>
        </div>
      </div>
      
      <div class="children-grid" v-if="directChildren.length">
        <div 
          v-for="child in directChildren" 
          :key="child.id"
          class="child-card"
          @click="$emit('view-agent', child.id)"
        >
          <el-avatar :src="child.avatar" :size="40" />
          <div class="child-info">
            <div class="child-name">{{ child.nickname }}</div>
            <LevelBadge :level="child.level" size="small" />
            <div class="child-stats">
              <span class="stat">团队: {{ child.teamCount }}</span>
              <span class="stat">佣金: ¥{{ formatNumber(child.totalCommission) }}</span>
            </div>
          </div>
          <div class="child-actions" @click.stop>
            <el-dropdown @command="(cmd) => handleChildCommand(cmd, child)">
              <el-button size="small" circle>
                <el-icon><More /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="view">查看详情</el-dropdown-item>
                  <el-dropdown-item command="orders">查看订单</el-dropdown-item>
                  <el-dropdown-item command="adjust" divided>调整关系</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>

      <el-empty v-else description="暂无直属下级" :image-size="80" />
    </div>

    <!-- 团队层级结构 -->
    <div class="team-structure-block" v-if="teamStructure.length">
      <div class="block-header">
        <h4>团队层级结构</h4>
        <el-button size="small" @click="expandAll">
          {{ allExpanded ? '收起全部' : '展开全部' }}
        </el-button>
      </div>
      
      <el-tree
        ref="teamTreeRef"
        :data="teamStructure"
        :props="treeProps"
        :default-expand-all="false"
        node-key="id"
        :expand-on-click-node="false"
        class="team-tree"
      >
        <template #default="{ node, data }">
          <div class="tree-node-content">
            <div class="node-main" @click="$emit('view-agent', data.id)">
              <el-avatar :src="data.avatar" :size="28" />
              <div class="node-info">
                <span class="node-name">{{ data.nickname }}</span>
                <LevelBadge :level="data.level" size="small" />
              </div>
              <div class="node-stats">
                <el-tag size="small" type="info">L{{ node.level }}</el-tag>
                <span class="team-count">{{ data.teamCount }}人</span>
              </div>
            </div>
            <div class="node-actions">
              <el-button size="small" @click.stop="$emit('view-agent', data.id)">
                详情
              </el-button>
            </div>
          </div>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  agentId: number
}

const props = defineProps<Props>()
const emit = defineEmits(['view-agent'])

const parentChain = ref<AgentBasicInfo[]>([])
const directChildren = ref<AgentBasicInfo[]>([])
const teamStructure = ref<AgentTreeNode[]>([])
const allExpanded = ref(false)
const teamTreeRef = ref()

const treeProps = {
  children: 'children',
  label: 'nickname'
}

const expandAll = () => {
  if (allExpanded.value) {
    teamTreeRef.value?.collapseAll()
  } else {
    teamTreeRef.value?.expandAll()
  }
  allExpanded.value = !allExpanded.value
}
</script>

<style lang="scss" scoped>
.agent-relationship-section {
  .parent-chain-block,
  .direct-children-block,
  .team-structure-block {
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }

    .block-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }

      .header-actions {
        display: flex;
        gap: 8px;
      }
    }
  }

  .relationship-path {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;

    .relationship-node {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background: white;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
      }

      .node-info {
        .node-name {
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 2px;
        }
      }
    }

    .arrow {
      color: #999;
      font-size: 16px;
    }
  }

  .children-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;

    .child-card {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background: white;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        border-color: var(--el-color-primary);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .child-info {
        flex: 1;

        .child-name {
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 4px;
        }

        .child-stats {
          display: flex;
          gap: 12px;
          margin-top: 4px;

          .stat {
            font-size: 12px;
            color: #666;
          }
        }
      }

      .child-actions {
        opacity: 0;
        transition: opacity 0.2s;
      }

      &:hover .child-actions {
        opacity: 1;
      }
    }
  }

  .team-tree {
    .tree-node-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 4px 0;

      .node-main {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;
        cursor: pointer;

        .node-info {
          display: flex;
          align-items: center;
          gap: 8px;

          .node-name {
            font-size: 14px;
            font-weight: 500;
          }
        }

        .node-stats {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-left: auto;

          .team-count {
            font-size: 12px;
            color: #666;
          }
        }
      }

      .node-actions {
        opacity: 0;
        transition: opacity 0.2s;
      }

      &:hover .node-actions {
        opacity: 1;
      }
    }
  }
}
</style>
```

#### 团队订单区块组件

```vue
<!-- src/components/distribution/TeamOrdersSection.vue -->
<template>
  <div class="team-orders-section">
    <!-- 快速筛选 -->
    <div class="quick-filters">
      <el-radio-group v-model="quickFilter" @change="handleQuickFilter">
        <el-radio-button label="all">全部订单</el-radio-button>
        <el-radio-button label="today">今日订单</el-radio-button>
        <el-radio-button label="week">本周订单</el-radio-button>
        <el-radio-button label="month">本月订单</el-radio-button>
      </el-radio-group>
      
      <div class="filter-actions">
        <el-button size="small" @click="showAdvancedFilter = !showAdvancedFilter">
          <el-icon><Filter /></el-icon>
          高级筛选
        </el-button>
        <el-button size="small" type="success" @click="handleExport">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- 高级筛选面板 -->
    <el-collapse-transition>
      <div v-show="showAdvancedFilter" class="advanced-filters">
        <el-form :model="searchForm" inline size="small">
          <el-form-item label="订单号">
            <el-input v-model="searchForm.orderNo" placeholder="订单号" clearable />
          </el-form-item>
          <el-form-item label="分销员">
            <el-select v-model="searchForm.agentId" placeholder="选择分销员" filterable clearable>
              <el-option 
                v-for="agent in teamMembers" 
                :key="agent.id" 
                :label="agent.nickname" 
                :value="agent.id" 
              />
            </el-select>
          </el-form-item>
          <el-form-item label="分销层级">
            <el-select v-model="searchForm.distributionLevel" placeholder="选择层级" clearable>
              <el-option label="直属下级" :value="1" />
              <el-option label="二级下级" :value="2" />
              <el-option label="三级下级" :value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="订单状态">
            <el-select v-model="searchForm.orderStatus" placeholder="选择状态" clearable>
              <el-option label="待付款" value="pending" />
              <el-option label="已付款" value="paid" />
              <el-option label="已完成" value="completed" />
              <el-option label="已取消" value="cancelled" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-collapse-transition>

    <!-- 统计概览 -->
    <div class="orders-overview">
      <el-row :gutter="16">
        <el-col :span="6">
          <div class="overview-item">
            <div class="item-value">{{ orderStats.totalCount }}</div>
            <div class="item-label">总订单数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="overview-item">
            <div class="item-value">¥{{ formatNumber(orderStats.totalAmount) }}</div>
            <div class="item-label">总订单金额</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="overview-item">
            <div class="item-value">¥{{ formatNumber(orderStats.totalCommission) }}</div>
            <div class="item-label">总佣金金额</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="overview-item">
            <div class="item-value">{{ orderStats.activeAgents }}</div>
            <div class="item-label">活跃分销员</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 订单列表 -->
    <div class="orders-list">
      <div v-if="orderList.length" class="order-cards">
        <div 
          v-for="order in orderList" 
          :key="order.id"
          class="order-card"
          @click="viewOrderDetail(order)"
        >
          <!-- 订单头部 -->
          <div class="order-header">
            <div class="order-info">
              <span class="order-no">{{ order.orderNo }}</span>
              <el-tag :type="getOrderStatusType(order.orderStatus)" size="small">
                {{ getOrderStatusText(order.orderStatus) }}
              </el-tag>
            </div>
            <div class="order-time">{{ formatTime(order.orderTime) }}</div>
          </div>

          <!-- 分销员信息 -->
          <div class="agent-section">
            <el-avatar :src="order.agent.avatar" :size="32" />
            <div class="agent-details">
              <div class="agent-name">{{ order.agent.nickname }}</div>
              <div class="agent-meta">
                <LevelBadge :level="order.agent.level" size="small" />
                <el-tag size="small" :type="getDistributionLevelTagType(order.distributionLevel)">
                  {{ getDistributionLevelText(order.distributionLevel) }}
                </el-tag>
              </div>
            </div>
          </div>

          <!-- 商品信息 -->
          <div class="product-section">
            <el-image :src="order.product.image" :size="40" class="product-image" />
            <div class="product-details">
              <div class="product-name">{{ order.product.name }}</div>
              <div class="product-price">¥{{ order.product.price }} × {{ order.quantity }}</div>
            </div>
          </div>

          <!-- 金额信息 -->
          <div class="amount-section">
            <div class="amount-row">
              <span class="label">订单金额:</span>
              <span class="value">¥{{ order.orderAmount }}</span>
            </div>
            <div class="amount-row">
              <span class="label">佣金金额:</span>
              <span class="value commission">¥{{ order.commissionAmount }}</span>
              <el-tag :type="getCommissionStatusType(order.commissionStatus)" size="small">
                {{ getCommissionStatusText(order.commissionStatus) }}
              </el-tag>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="order-actions" @click.stop>
            <el-button size="small" @click="viewOrderDetail(order)">详情</el-button>
            <el-button size="small" @click="viewCommissionDetail(order)">佣金</el-button>
            <el-dropdown @command="(cmd) => handleOrderCommand(cmd, order)">
              <el-button size="small">
                更多<el-icon><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="view-agent">查看分销员</el-dropdown-item>
                  <el-dropdown-item command="view-buyer">查看买家</el-dropdown-item>
                  <el-dropdown-item command="track" divided>订单跟踪</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>

      <el-empty v-else description="暂无团队订单" :image-size="100" />

      <!-- 加载更多 -->
      <div v-if="hasMore" class="load-more">
        <el-button @click="loadMore" :loading="loadingMore">
          加载更多
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  agentId: number
}

const props = defineProps<Props>()

const quickFilter = ref('all')
const showAdvancedFilter = ref(false)
const orderList = ref<TeamOrderInfo[]>([])
const orderStats = ref({
  totalCount: 0,
  totalAmount: 0,
  totalCommission: 0,
  activeAgents: 0
})
const hasMore = ref(false)
const loadingMore = ref(false)

const searchForm = reactive({
  orderNo: '',
  agentId: null,
  distributionLevel: null,
  orderStatus: null
})
</script>

<style lang="scss" scoped>
.team-orders-section {
  .quick-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .filter-actions {
      display: flex;
      gap: 8px;
    }
  }

  .advanced-filters {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 16px;
  }

  .orders-overview {
    margin-bottom: 20px;

    .overview-item {
      text-align: center;
      padding: 16px;
      background: white;
      border: 1px solid #e4e7ed;
      border-radius: 6px;

      .item-value {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
      }

      .item-label {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .order-cards {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .order-card {
      padding: 16px;
      background: white;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        border-color: var(--el-color-primary);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;

        .order-info {
          display: flex;
          align-items: center;
          gap: 8px;

          .order-no {
            font-weight: 500;
            color: #333;
          }
        }

        .order-time {
          font-size: 12px;
          color: #999;
        }
      }

      .agent-section,
      .product-section {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;

        .agent-details,
        .product-details {
          flex: 1;

          .agent-name,
          .product-name {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
          }

          .agent-meta {
            display: flex;
            gap: 8px;
          }

          .product-price {
            font-size: 12px;
            color: #666;
          }
        }
      }

      .amount-section {
        margin-bottom: 12px;

        .amount-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;

          .label {
            font-size: 12px;
            color: #666;
          }

          .value {
            font-size: 14px;
            font-weight: 500;

            &.commission {
              color: var(--el-color-success);
            }
          }
        }
      }

      .order-actions {
        display: flex;
        justify-content: flex-end;
        gap: 8px;
        opacity: 0;
        transition: opacity 0.2s;
      }

      &:hover .order-actions {
        opacity: 1;
      }
    }
  }

  .load-more {
    text-align: center;
    margin-top: 20px;
  }
}
</style>
```

### 3. 等级管理模块 (Level)

#### 等级配置

```typescript
// 分销员等级
interface AgentLevel {
  id: number
  levelName: string         // 等级名称
  levelCode: string         // 等级编码
  levelGrade: number        // 等级序号 (数字越大等级越高)
  levelColor: string        // 等级颜色
  levelIcon?: string        // 等级图标
  description?: string      // 等级描述
  
  // 升级条件
  upgradeConditions: {
    minCommission?: number      // 最低累计佣金
    minOrders?: number          // 最低订单数
    minTeamSize?: number        // 最低团队规模
    minDirectReferrals?: number // 最低直推人数
    timeRequirement?: number    // 时间要求(天)
    customConditions?: string   // 自定义条件
  }
  
  // 等级权益
  benefits: {
    commissionRate: number      // 基础佣金比例
    bonusRate?: number          // 奖励佣金比例
    maxWithdrawAmount?: number  // 最大提现金额
    withdrawFeeRate?: number    // 提现手续费率
    specialPrivileges?: string[] // 特殊权限
  }
  
  status: number            // 状态 1-启用 0-禁用
  sort: number              // 排序
  createTime: string
  updateTime: string
}

// 等级升级记录
interface LevelUpgradeRecord {
  id: number
  agentId: number
  agentName: string
  fromLevelId: number
  fromLevelName: string
  toLevelId: number
  toLevelName: string
  upgradeType: 'auto' | 'manual' // 升级类型
  upgradeReason: string     // 升级原因
  auditStatus: AuditStatus  // 审核状态
  auditRemark?: string      // 审核备注
  auditTime?: string        // 审核时间
  createTime: string
}
```

### 4. 商品分销配置模块 (Product)

#### 商品分销配置

```typescript
// 商品分销配置
interface ProductDistributionConfig {
  id: number
  productId: number
  productName: string
  productImage: string
  productPrice: number
  
  // 分销配置
  enableDistribution: boolean   // 是否启用分销
  distributionType: 'fixed' | 'percentage' // 佣金类型
  
  // 多级佣金配置
  commissionLevels: {
    level: number               // 级别 (1-一级, 2-二级, 3-三级)
    rate?: number               // 佣金比例 (百分比模式)
    amount?: number             // 固定佣金 (固定金额模式)
    description: string         // 说明
  }[]
  
  // 推广素材
  promotionMaterials: {
    images: string[]            // 推广图片
    videos?: string[]           // 推广视频
    copywriting: string[]       // 推广文案
    shareTitle: string          // 分享标题
    shareDescription: string    // 分享描述
  }
  
  // 分销限制
  restrictions: {
    maxStock?: number           // 最大库存限制
    startTime?: string          // 开始时间
    endTime?: string            // 结束时间
    allowedRegions?: string[]   // 允许地区
    forbiddenRegions?: string[] // 禁止地区
    minAgentLevel?: number      // 最低分销员等级
  }
  
  priority: number              // 优先级
  status: number                // 状态
  createTime: string
  updateTime: string
}
```

### 5. 佣金管理模块 (Commission)

#### 佣金记录

```typescript
// 佣金记录
interface CommissionRecord {
  id: number
  recordNo: string              // 佣金单号
  agentId: number
  agentName: string
  agentLevel: string
  
  // 订单信息
  orderId: number
  orderNo: string
  buyerId: number
  buyerName: string
  
  // 商品信息
  productId: number
  productName: string
  productImage: string
  productPrice: number
  quantity: number
  orderAmount: number
  
  // 佣金信息
  commissionLevel: number       // 佣金级别 (1-直接, 2-间接一级, 3-间接二级)
  commissionType: 'fixed' | 'percentage'
  commissionRate?: number       // 佣金比例
  commissionAmount: number      // 佣金金额
  
  // 状态信息
  status: CommissionStatus
  settlementId?: number         // 结算批次ID
  settlementTime?: string       // 结算时间
  withdrawId?: number           // 提现ID
  withdrawTime?: string         // 提现时间
  
  // 时间信息
  orderTime: string             // 下单时间
  confirmTime?: string          // 确认时间
  createTime: string
  updateTime: string
  
  remark?: string               // 备注
}

enum CommissionStatus {
  PENDING = 0,      // 待确认
  CONFIRMED = 1,    // 已确认
  SETTLED = 2,      // 已结算
  WITHDRAWN = 3,    // 已提现
  FROZEN = 4,       // 已冻结
  CANCELLED = 5     // 已取消
}

// 佣金结算
interface CommissionSettlement {
  id: number
  settlementNo: string          // 结算单号
  settlementType: 'batch' | 'individual' // 结算类型
  agentIds: number[]            // 分销员ID列表
  totalAmount: number           // 总金额
  totalCount: number            // 总笔数
  status: SettlementStatus
  settleTime: string
  remark?: string
  createTime: string
}

enum SettlementStatus {
  PROCESSING = 0,   // 处理中
  COMPLETED = 1,    // 已完成
  FAILED = 2        // 失败
}
```

#### 提现管理

```typescript
// 提现申请
interface WithdrawApplication {
  id: number
  withdrawNo: string            // 提现单号
  agentId: number
  agentName: string
  agentMobile: string
  
  // 提现信息
  withdrawAmount: number        // 提现金额
  feeAmount: number             // 手续费
  actualAmount: number          // 实际到账金额
  
  // 账户信息
  accountType: 'bank' | 'alipay' | 'wechat' // 账户类型
  accountName: string           // 账户名
  accountNumber: string         // 账户号
  bankName?: string             // 银行名称
  
  // 状态信息
  status: WithdrawStatus
  auditStatus: AuditStatus
  auditRemark?: string
  auditTime?: string
  auditBy?: string
  
  // 处理信息
  processStatus?: ProcessStatus
  processTime?: string
  processRemark?: string
  transactionNo?: string        // 交易流水号
  
  applyTime: string
  createTime: string
  updateTime: string
}

enum WithdrawStatus {
  PENDING = 0,      // 待审核
  APPROVED = 1,     // 已通过
  REJECTED = 2,     // 已拒绝
  PROCESSING = 3,   // 处理中
  COMPLETED = 4,    // 已完成
  FAILED = 5        // 失败
}

enum ProcessStatus {
  PENDING = 0,      // 待处理
  PROCESSING = 1,   // 处理中
  SUCCESS = 2,      // 成功
  FAILED = 3        // 失败
}
```

### 6. 数据统计模块 (Statistics)

#### 统计数据结构

```typescript
// 概览统计
interface OverviewStatistics {
  // 基础数据
  totalAgents: number           // 总分销员数
  activeAgents: number          // 活跃分销员数
  newAgentsToday: number        // 今日新增
  newAgentsMonth: number        // 本月新增
  
  // 佣金数据
  totalCommission: number       // 累计佣金
  monthlyCommission: number     // 本月佣金
  dailyCommission: number       // 今日佣金
  pendingCommission: number     // 待结算佣金
  
  // 订单数据
  totalOrders: number           // 累计订单
  monthlyOrders: number         // 本月订单
  dailyOrders: number           // 今日订单
  orderAmount: number           // 订单金额
  
  // 提现数据
  totalWithdraw: number         // 累计提现
  monthlyWithdraw: number       // 本月提现
  pendingWithdraw: number       // 待审核提现
  
  // 商品数据
  distributionProducts: number  // 分销商品数
  topProducts: ProductPerformance[] // 热门商品
  
  // 趋势数据
  agentTrend: TrendData[]       // 分销员趋势
  commissionTrend: TrendData[]  // 佣金趋势
  orderTrend: TrendData[]       // 订单趋势
}

// 趋势数据
interface TrendData {
  date: string
  value: number
  compareValue?: number         // 对比值
  growthRate?: number           // 增长率
}

// 商品表现
interface ProductPerformance {
  productId: number
  productName: string
  productImage: string
  distributionCount: number     // 分销次数
  orderCount: number            // 订单数量
  orderAmount: number           // 订单金额
  commissionAmount: number      // 佣金金额
  conversionRate: number        // 转化率
  ranking: number               // 排名
  rankingChange: number         // 排名变化
}

// 分销员表现
interface AgentPerformance {
  agentId: number
  agentName: string
  agentLevel: string
  orderCount: number
  orderAmount: number
  commissionAmount: number
  teamSize: number
  conversionRate: number
  ranking: number
  rankingChange: number
}
```

## UI设计理念

### 抽屉式详情展示

系统采用抽屉(Drawer)作为主要的详情展示方式，具有以下优势：

- **保持上下文**: 用户可以在查看详情的同时保持对列表页面的感知
- **分块展示**: 使用折叠面板将信息分块展示，用户可按需查看
- **响应式友好**: 抽屉可以根据屏幕尺寸自动调整宽度
- **操作便捷**: 支持快速关闭和多层级抽屉嵌套

### 信息分块原则

- **基础信息块**: 头部展示核心信息和快速操作
- **统计数据块**: 关键指标的卡片式展示
- **详细信息块**: 使用折叠面板组织不同维度的详细信息
- **关联数据块**: 相关联的数据以独立区块形式展示

### 交互设计规范

- **渐进式展示**: 默认展示最重要的信息，其他信息可展开查看
- **快速操作**: 常用操作放在显眼位置，次要操作收纳在下拉菜单
- **状态反馈**: 所有操作都有明确的加载状态和结果反馈
- **键盘支持**: 支持ESC关闭抽屉，Tab键导航等快捷操作

## 组件库设计

### 抽屉组件系列

#### BaseDrawer - 基础抽屉组件

```vue
<template>
  <el-drawer
    v-model="visible"
    :title="title"
    :size="size"
    :before-close="handleBeforeClose"
    :destroy-on-close="destroyOnClose"
    :modal="modal"
    class="base-drawer"
  >
    <!-- 抽屉头部 -->
    <template #header>
      <div class="drawer-header">
        <div class="header-left">
          <h3 class="drawer-title">{{ title }}</h3>
          <div class="header-subtitle" v-if="subtitle">{{ subtitle }}</div>
        </div>
        <div class="header-actions">
          <slot name="header-actions" />
        </div>
      </div>
    </template>

    <!-- 抽屉内容 -->
    <div class="drawer-content" v-loading="loading">
      <slot />
    </div>

    <!-- 抽屉底部 -->
    <template #footer v-if="showFooter">
      <div class="drawer-footer">
        <slot name="footer">
          <el-button @click="handleCancel">{{ cancelText }}</el-button>
          <el-button type="primary" @click="handleConfirm" :loading="confirmLoading">
            {{ confirmText }}
          </el-button>
        </slot>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
interface Props {
  modelValue: boolean
  title: string
  subtitle?: string
  size?: string | number
  loading?: boolean
  showFooter?: boolean
  cancelText?: string
  confirmText?: string
  confirmLoading?: boolean
  destroyOnClose?: boolean
  modal?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: '50%',
  loading: false,
  showFooter: true,
  cancelText: '取消',
  confirmText: '确定',
  confirmLoading: false,
  destroyOnClose: true,
  modal: true
})

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel', 'close'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
</script>
```

#### DetailDrawer - 详情抽屉组件

```vue
<template>
  <BaseDrawer
    v-model="visible"
    :title="title"
    :subtitle="subtitle"
    :size="size"
    :loading="loading"
    :show-footer="false"
    class="detail-drawer"
  >
    <!-- 头部信息区 -->
    <div class="detail-header" v-if="headerData">
      <slot name="header" :data="headerData">
        <div class="default-header">
          <el-avatar :src="headerData.avatar" :size="60" />
          <div class="header-info">
            <h3>{{ headerData.name }}</h3>
            <div class="header-meta">
              <slot name="header-meta" :data="headerData" />
            </div>
          </div>
          <div class="header-actions">
            <slot name="header-actions" :data="headerData" />
          </div>
        </div>
      </slot>
    </div>

    <!-- 快速统计区 -->
    <div class="quick-stats" v-if="statsData?.length">
      <el-row :gutter="12">
        <el-col :span="24 / statsData.length" v-for="stat in statsData" :key="stat.key">
          <div class="stat-item">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 详细信息区 -->
    <div class="detail-sections">
      <el-collapse v-model="activeCollapse" accordion>
        <el-collapse-item
          v-for="section in sections"
          :key="section.key"
          :title="section.title"
          :name="section.key"
        >
          <template #title>
            <div class="section-title">
              <el-icon v-if="section.icon">
                <component :is="section.icon" />
              </el-icon>
              <span>{{ section.title }}</span>
              <el-badge v-if="section.badge" :value="section.badge" class="section-badge" />
            </div>
          </template>
          <component :is="section.component" v-bind="section.props" />
        </el-collapse-item>
      </el-collapse>
    </div>
  </BaseDrawer>
</template>

<script setup lang="ts">
interface StatItem {
  key: string
  label: string
  value: string | number
}

interface SectionItem {
  key: string
  title: string
  icon?: string
  badge?: string | number
  component: Component
  props?: Record<string, any>
}

interface Props {
  modelValue: boolean
  title: string
  subtitle?: string
  size?: string | number
  loading?: boolean
  headerData?: any
  statsData?: StatItem[]
  sections: SectionItem[]
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const activeCollapse = ref(props.sections[0]?.key || '')
</script>
```

### 通用组件

#### StatisticsCard - 统计卡片

```vue
<template>
  <el-card class="statistics-card" :class="{ clickable }" @click="handleClick">
    <div class="card-content">
      <div class="card-icon">
        <el-icon :size="24" :color="iconColor">
          <component :is="icon" />
        </el-icon>
      </div>
      <div class="card-info">
        <div class="card-title">{{ title }}</div>
        <div class="card-value">
          {{ prefix }}{{ formattedValue }}{{ suffix }}
        </div>
        <div v-if="trend" class="card-trend" :class="trendClass">
          <el-icon><trend-charts /></el-icon>
          {{ trend }}
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
interface Props {
  title: string
  value: number | string
  icon: string
  iconColor?: string
  prefix?: string
  suffix?: string
  trend?: string
  format?: 'number' | 'currency' | 'percentage'
  clickable?: boolean
}
</script>
```

#### LevelBadge - 等级徽章

```vue
<template>
  <el-tag
    :type="tagType"
    :color="level.levelColor"
    :style="{ color: textColor }"
    class="level-badge"
  >
    <el-icon v-if="level.levelIcon" class="level-icon">
      <component :is="level.levelIcon" />
    </el-icon>
    {{ level.levelName }}
  </el-tag>
</template>

<script setup lang="ts">
interface Props {
  level: {
    levelName: string
    levelColor: string
    levelIcon?: string
    levelGrade: number
  }
  size?: 'small' | 'default' | 'large'
}
</script>
```

#### RelationshipTree - 关系树

```vue
<template>
  <div class="relationship-tree">
    <el-tree
      :data="treeData"
      :props="treeProps"
      :default-expand-all="false"
      :expand-on-click-node="false"
      node-key="id"
    >
      <template #default="{ node, data }">
        <div class="tree-node">
          <el-avatar :size="32" :src="data.avatar" />
          <div class="node-info">
            <div class="node-name">{{ data.name }}</div>
            <div class="node-stats">
              <LevelBadge :level="data.level" size="small" />
              <span class="team-count">团队: {{ data.teamCount }}</span>
            </div>
          </div>
          <div class="node-actions">
            <el-button size="small" @click="viewDetail(data)">详情</el-button>
          </div>
        </div>
      </template>
    </el-tree>
  </div>
</template>
```

## API接口设计

### RESTful API规范

```typescript
// 基础响应格式
interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
}

// 分页响应格式
interface PageResponse<T> {
  list: T[]
  total: number
  pageNo: number
  pageSize: number
  totalPages: number
}

// 分销员API
const DistributionAgentAPI = {
  // 获取分销员列表
  getAgentList: (params: AgentSearchParams) =>
    request.get<PageResponse<AgentInfo>>('/admin/distribution/agent/list', { params }),
  
  // 获取分销员详情
  getAgentDetail: (id: number) =>
    request.get<AgentInfo>(`/admin/distribution/agent/${id}`),
  
  // 获取分销员关系链
  getAgentRelationship: (id: number) =>
    request.get<{
      parentChain: AgentBasicInfo[]
      currentAgent: AgentInfo
      childrenTree: AgentTreeNode[]
    }>(`/admin/distribution/agent/${id}/relationship`),
  
  // 获取分销员团队订单
  getTeamOrders: (id: number, params: TeamOrderSearchParams) =>
    request.get<PageResponse<TeamOrderInfo>>(`/admin/distribution/agent/${id}/team-orders`, { params }),
  
  // 获取分销员个人订单
  getPersonalOrders: (id: number, params: OrderSearchParams) =>
    request.get<PageResponse<OrderInfo>>(`/admin/distribution/agent/${id}/personal-orders`, { params }),
  
  // 获取团队成员列表
  getTeamMembers: (id: number, params?: { level?: number, keyword?: string }) =>
    request.get<AgentBasicInfo[]>(`/admin/distribution/agent/${id}/team-members`, { params }),
  
  // 创建分销员
  createAgent: (data: CreateAgentRequest) =>
    request.post<AgentInfo>('/admin/distribution/agent', data),
  
  // 更新分销员
  updateAgent: (id: number, data: UpdateAgentRequest) =>
    request.put<AgentInfo>(`/admin/distribution/agent/${id}`, data),
  
  // 批量审核分销员
  batchAuditAgent: (data: BatchAuditRequest) =>
    request.post('/admin/distribution/agent/batch-audit', data),
  
  // 调整分销员等级
  adjustAgentLevel: (data: AdjustLevelRequest) =>
    request.post('/admin/distribution/agent/adjust-level', data),
  
  // 调整分销关系
  adjustAgentRelationship: (data: AdjustRelationshipRequest) =>
    request.post('/admin/distribution/agent/adjust-relationship', data),
  
  // 获取分销员统计
  getAgentStatistics: (id: number, params?: StatisticsParams) =>
    request.get<AgentStatistics>(`/admin/distribution/agent/${id}/statistics`, { params }),
  
  // 获取分销员团队
  getAgentTeam: (id: number) =>
    request.get<AgentTeamTree>(`/admin/distribution/agent/${id}/team`),
  
  // 导出分销员数据
  exportAgentData: (params: AgentSearchParams) =>
    request.get('/admin/distribution/agent/export', { params, responseType: 'blob' }),
  
  // 导出团队订单数据
  exportTeamOrders: (id: number, params: TeamOrderSearchParams) =>
    request.get(`/admin/distribution/agent/${id}/team-orders/export`, { params, responseType: 'blob' })
}

// 团队订单搜索参数
interface TeamOrderSearchParams {
  orderNo?: string          // 订单号
  agentId?: number          // 分销员ID
  distributionLevel?: number // 分销层级
  orderStatus?: OrderStatus // 订单状态
  commissionStatus?: CommissionStatus // 佣金状态
  dateRange?: [string, string] // 时间范围
  pageNo: number
  pageSize: number
}

// 分销关系调整请求
interface AdjustRelationshipRequest {
  agentId: number           // 要调整的分销员ID
  newParentId?: number      // 新的上级ID（null表示设为顶级）
  reason: string            // 调整原因
}

// 佣金API
const DistributionCommissionAPI = {
  // 获取佣金记录
  getCommissionList: (params: CommissionSearchParams) =>
    request.get<PageResponse<CommissionRecord>>('/admin/distribution/commission/list', { params }),
  
  // 获取佣金详情
  getCommissionDetail: (id: number) =>
    request.get<CommissionRecord>(`/admin/distribution/commission/${id}`),
  
  // 批量结算佣金
  batchSettleCommission: (data: BatchSettleRequest) =>
    request.post('/admin/distribution/commission/batch-settle', data),
  
  // 获取佣金统计
  getCommissionStatistics: (params: StatisticsParams) =>
    request.get<CommissionStatistics>('/admin/distribution/commission/statistics', { params }),
  
  // 获取提现申请列表
  getWithdrawList: (params: WithdrawSearchParams) =>
    request.get<PageResponse<WithdrawApplication>>('/admin/distribution/withdraw/list', { params }),
  
  // 审核提现申请
  auditWithdraw: (id: number, data: AuditWithdrawRequest) =>
    request.post(`/admin/distribution/withdraw/${id}/audit`, data),
  
  // 批量审核提现
  batchAuditWithdraw: (data: BatchAuditWithdrawRequest) =>
    request.post('/admin/distribution/withdraw/batch-audit', data)
}
```

## 状态管理设计

### Pinia Store结构

```typescript
// 分销员状态管理
export const useDistributionAgentStore = defineStore('distributionAgent', () => {
  // 状态
  const agentList = ref<AgentInfo[]>([])
  const currentAgent = ref<AgentInfo | null>(null)
  const agentLevels = ref<AgentLevel[]>([])
  const loading = ref(false)
  const searchParams = ref<AgentSearchParams>({
    pageNo: 1,
    pageSize: 20
  })

  // 计算属性
  const totalAgents = computed(() => agentList.value.length)
  const activeAgents = computed(() => 
    agentList.value.filter(agent => agent.status === AgentStatus.ACTIVE).length
  )

  // 方法
  const fetchAgentList = async (params?: Partial<AgentSearchParams>) => {
    loading.value = true
    try {
      if (params) {
        Object.assign(searchParams.value, params)
      }
      const response = await DistributionAgentAPI.getAgentList(searchParams.value)
      agentList.value = response.data.list
      return response.data
    } finally {
      loading.value = false
    }
  }

  const fetchAgentDetail = async (id: number) => {
    const response = await DistributionAgentAPI.getAgentDetail(id)
    currentAgent.value = response.data
    return response.data
  }

  const updateAgentStatus = async (id: number, status: AgentStatus) => {
    await DistributionAgentAPI.updateAgent(id, { status })
    const index = agentList.value.findIndex(agent => agent.id === id)
    if (index !== -1) {
      agentList.value[index].status = status
    }
  }

  return {
    // 状态
    agentList,
    currentAgent,
    agentLevels,
    loading,
    searchParams,
    
    // 计算属性
    totalAgents,
    activeAgents,
    
    // 方法
    fetchAgentList,
    fetchAgentDetail,
    updateAgentStatus
  }
})
```

## 错误处理和用户体验

### 统一错误处理

```typescript
// 错误类型定义
interface BusinessError {
  code: number
  message: string
  details?: any
}

// 错误处理中间件
const errorHandler = (error: any) => {
  if (error.response) {
    const { status, data } = error.response
    
    switch (status) {
      case 400:
        ElMessage.error(data.message || '请求参数错误')
        break
      case 401:
        ElMessage.error('登录已过期，请重新登录')
        router.push('/login')
        break
      case 403:
        ElMessage.error('权限不足')
        break
      case 404:
        ElMessage.error('请求的资源不存在')
        break
      case 500:
        ElMessage.error('服务器内部错误')
        break
      default:
        ElMessage.error(data.message || '网络错误')
    }
  } else {
    ElMessage.error('网络连接失败')
  }
}

// 加载状态管理
const useLoading = () => {
  const loading = ref(false)
  
  const withLoading = async <T>(fn: () => Promise<T>): Promise<T> => {
    loading.value = true
    try {
      return await fn()
    } finally {
      loading.value = false
    }
  }
  
  return { loading, withLoading }
}
```

### 用户体验优化

1. **骨架屏加载**: 数据加载时显示骨架屏
2. **虚拟滚动**: 大列表使用虚拟滚动优化性能
3. **防抖节流**: 搜索和筛选使用防抖处理
4. **乐观更新**: 某些操作先更新UI再发送请求
5. **离线提示**: 网络断开时显示离线状态
6. **操作确认**: 重要操作需要用户确认
7. **进度反馈**: 批量操作显示进度条
8. **快捷键支持**: 常用操作支持快捷键

## 性能优化策略

### 前端优化

1. **代码分割**: 路由级别的代码分割
2. **组件懒加载**: 非关键组件延迟加载
3. **图片优化**: WebP格式、懒加载、压缩
4. **缓存策略**: HTTP缓存、应用缓存
5. **Bundle优化**: Tree shaking、压缩混淆
6. **CDN加速**: 静态资源CDN分发

### 数据优化

1. **分页加载**: 避免一次性加载大量数据
2. **数据预取**: 预加载可能需要的数据
3. **缓存机制**: 合理使用内存缓存
4. **请求合并**: 合并相似的API请求
5. **数据压缩**: 大数据传输使用压缩
6. **增量更新**: 只更新变化的数据

## 安全设计

### 数据安全

1. **输入验证**: 前后端双重验证
2. **XSS防护**: 内容过滤和转义
3. **CSRF防护**: Token验证
4. **敏感数据**: 加密传输和存储
5. **权限控制**: 细粒度权限管理
6. **审计日志**: 操作日志记录

### 业务安全

1. **防刷机制**: 接口频率限制
2. **数据脱敏**: 敏感信息脱敏显示
3. **操作确认**: 重要操作二次确认
4. **异常监控**: 异常行为检测
5. **备份恢复**: 数据备份和恢复机制
