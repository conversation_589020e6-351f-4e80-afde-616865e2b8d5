# 分销管理权限使用指南

## 权限标识规范

分销管理模块遵循项目统一的权限标识规范，格式为：`模块:资源:操作`

### 权限标识列表

#### 分销员管理权限
```typescript
'distribution:agent:query'    // 查询分销员
'distribution:agent:create'   // 创建分销员
'distribution:agent:update'   // 更新分销员
'distribution:agent:delete'   // 删除分销员
'distribution:agent:audit'    // 审核分销员
'distribution:agent:export'   // 导出分销员数据
```

#### 佣金管理权限
```typescript
'distribution:commission:query'   // 查询佣金
'distribution:commission:settle'  // 结算佣金
'distribution:commission:manage'  // 管理佣金
'distribution:commission:export'  // 导出佣金数据
```

#### 提现管理权限
```typescript
'distribution:withdraw:query'     // 查询提现
'distribution:withdraw:audit'     // 审核提现
'distribution:withdraw:transfer'  // 转账操作
'distribution:withdraw:cancel'    // 取消提现
'distribution:withdraw:manage'    // 管理提现
'distribution:withdraw:export'    // 导出提现数据
```

#### 商品配置权限
```typescript
'distribution:goods-config:query'  // 查询配置
'distribution:goods-config:create' // 创建配置
'distribution:goods-config:update' // 更新配置
'distribution:goods-config:delete' // 删除配置
'distribution:goods-config:export' // 导出配置
```

## 使用方式

### 1. 使用 v-hasPermi 指令

这是最常用的权限控制方式，用于控制元素的显示/隐藏。

#### 单个权限
```vue
<el-button 
  type="primary" 
  v-hasPermi="['distribution:agent:create']"
>
  新增分销员
</el-button>
```

#### 多个权限（OR关系）
```vue
<el-button 
  v-hasPermi="['distribution:agent:update', 'distribution:agent:create']"
>
  编辑或新增
</el-button>
```

#### 在表格操作列中使用
```vue
<el-table-column label="操作" align="center">
  <template #default="scope">
    <el-button
      link
      type="primary"
      v-hasPermi="['distribution:agent:query']"
      @click="handleView(scope.row)"
    >
      查看
    </el-button>
    <el-button
      link
      type="primary"
      v-hasPermi="['distribution:agent:update']"
      @click="handleEdit(scope.row)"
    >
      编辑
    </el-button>
  </template>
</el-table-column>
```

### 2. 使用 checkPermi 函数

当需要在模板中进行条件渲染或在脚本中进行权限判断时使用。

#### 导入函数
```typescript
import { checkPermi } from '@/utils/permission'
```

#### 在模板中使用
```vue
<el-form-item 
  label="佣金比例" 
  v-if="checkPermi(['distribution:commission:manage'])"
>
  <el-input-number v-model="formData.commissionRate" />
</el-form-item>
```

#### 在下拉菜单中使用
```vue
<el-dropdown @command="handleCommand">
  <el-button type="primary" link>
    更多 <Icon icon="ep:arrow-down" />
  </el-button>
  <template #dropdown>
    <el-dropdown-menu>
      <el-dropdown-item
        command="adjustLevel"
        v-if="checkPermi(['distribution:agent:update'])"
      >
        调整等级
      </el-dropdown-item>
      <el-dropdown-item
        command="delete"
        v-if="checkPermi(['distribution:agent:delete'])"
      >
        删除
      </el-dropdown-item>
    </el-dropdown-menu>
  </template>
</el-dropdown>
```

#### 在脚本中使用
```typescript
const canExport = () => {
  return checkPermi(['distribution:agent:export'])
}

const handleExport = () => {
  if (!canExport()) {
    message.error('您没有导出权限')
    return
  }
  // 执行导出逻辑
}
```

### 3. 表单字段权限控制

#### 禁用无权限的字段
```vue
<el-form-item label="分销员姓名">
  <el-input 
    v-model="formData.name" 
    :disabled="!checkPermi(['distribution:agent:update'])" 
  />
</el-form-item>
```

#### 隐藏敏感字段
```vue
<el-form-item 
  label="结算账户" 
  v-if="checkPermi(['distribution:withdraw:manage'])"
>
  <el-input v-model="formData.bankAccount" />
</el-form-item>
```

### 4. 标签页权限控制

```vue
<el-tabs v-model="activeTab">
  <el-tab-pane label="基本信息" name="basic">
    <!-- 所有人可见 -->
  </el-tab-pane>
  
  <el-tab-pane 
    label="佣金管理" 
    name="commission"
    v-if="checkPermi(['distribution:commission:query'])"
  >
    <!-- 需要佣金查询权限 -->
  </el-tab-pane>
  
  <el-tab-pane 
    label="系统配置" 
    name="config"
    v-if="checkPermi(['distribution:config:manage'])"
  >
    <!-- 需要配置管理权限 -->
  </el-tab-pane>
</el-tabs>
```

## 最佳实践

### 1. 权限粒度控制

遵循最小权限原则，将权限控制到具体的操作级别：

```vue
<!-- 好的做法：细粒度权限控制 -->
<el-button v-hasPermi="['distribution:agent:audit']">
  审核分销员
</el-button>

<!-- 避免：过于宽泛的权限 -->
<el-button v-hasPermi="['distribution:*:*']">
  审核分销员
</el-button>
```

### 2. 组合权限使用

对于复杂的业务场景，合理组合多个权限：

```vue
<!-- 查看详情需要查询权限 -->
<el-button 
  v-hasPermi="['distribution:agent:query']"
  @click="viewDetail"
>
  查看详情
</el-button>

<!-- 编辑操作需要查询和更新权限 -->
<el-button 
  v-if="checkPermi(['distribution:agent:query']) && 
        checkPermi(['distribution:agent:update'])"
  @click="editAgent"
>
  编辑
</el-button>
```

### 3. 批量操作权限

批量操作通常需要额外的权限控制：

```vue
<template>
  <el-button
    type="danger"
    :disabled="!selection.length"
    v-hasPermi="['distribution:agent:delete']"
    @click="handleBatchDelete"
  >
    批量删除
  </el-button>
</template>

<script setup>
const handleBatchDelete = () => {
  if (selection.value.length === 0) {
    message.warning('请选择要删除的数据')
    return
  }
  
  // 二次确认
  ElMessageBox.confirm(
    `确定要删除选中的 ${selection.value.length} 条数据吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 执行批量删除
  })
}
</script>
```

### 4. 动态权限加载

对于需要根据数据状态动态判断的权限：

```vue
<template>
  <!-- 只有待审核状态才显示审核按钮 -->
  <el-button
    v-if="row.status === 0 && checkPermi(['distribution:agent:audit'])"
    @click="handleAudit(row)"
  >
    审核
  </el-button>
  
  <!-- 已审核的显示其他操作 -->
  <el-button
    v-else-if="row.status === 1 && checkPermi(['distribution:agent:update'])"
    @click="handleEdit(row)"
  >
    编辑
  </el-button>
</template>
```

### 5. 权限提示优化

当用户没有权限时，提供友好的提示：

```typescript
const handleOperation = () => {
  if (!checkPermi(['distribution:agent:update'])) {
    message.warning('您没有执行此操作的权限，请联系管理员')
    return
  }
  
  // 执行操作
}
```

## 权限配置示例

完整的分销员管理页面权限配置示例：

```vue
<template>
  <ContentWrap>
    <!-- 搜索栏 -->
    <el-form :model="queryParams" ref="queryFormRef" :inline="true">
      <!-- 搜索条件 -->
      <el-form-item>
        <!-- 查询按钮无需权限 -->
        <el-button @click="handleQuery">
          <Icon icon="ep:search" /> 搜索
        </el-button>
        
        <!-- 新增需要创建权限 -->
        <el-button 
          type="primary"
          v-hasPermi="['distribution:agent:create']"
          @click="openForm('create')"
        >
          <Icon icon="ep:plus" /> 新增
        </el-button>
        
        <!-- 导出需要导出权限 -->
        <el-button 
          type="success"
          v-hasPermi="['distribution:agent:export']"
          @click="handleExport"
        >
          <Icon icon="ep:download" /> 导出
        </el-button>
        
        <!-- 批量操作需要相应权限 -->
        <el-button 
          type="warning"
          v-hasPermi="['distribution:agent:audit']"
          @click="handleBatchAudit"
          :disabled="!selection.length"
        >
          <Icon icon="ep:check" /> 批量审核
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>
  
  <!-- 数据列表 -->
  <ContentWrap>
    <el-table :data="list" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <!-- 其他列 -->
      <el-table-column label="操作" align="center" width="200">
        <template #default="scope">
          <!-- 基础操作 -->
          <el-button
            link
            type="primary"
            v-hasPermi="['distribution:agent:query']"
            @click="handleView(scope.row)"
          >
            查看
          </el-button>
          
          <!-- 高级操作 -->
          <el-dropdown
            @command="(cmd) => handleCommand(cmd, scope.row)"
            v-hasPermi="[
              'distribution:agent:update',
              'distribution:agent:delete',
              'distribution:agent:audit'
            ]"
          >
            <el-button link type="primary">
              更多 <Icon icon="ep:arrow-down" />
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  command="edit"
                  v-if="checkPermi(['distribution:agent:update'])"
                >
                  编辑
                </el-dropdown-item>
                <el-dropdown-item
                  command="audit"
                  v-if="scope.row.status === 0 && 
                       checkPermi(['distribution:agent:audit'])"
                >
                  审核
                </el-dropdown-item>
                <el-dropdown-item
                  command="delete"
                  v-if="checkPermi(['distribution:agent:delete'])"
                  divided
                >
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>
</template>

<script setup>
import { checkPermi } from '@/utils/permission'

// ... 其他代码
</script>
```

## 注意事项

1. **权限标识必须与后端保持一致**：确保前端使用的权限标识与后端定义的完全匹配
2. **避免硬编码权限**：将权限标识定义为常量，便于维护
3. **合理使用权限粒度**：根据业务需求合理设置权限粒度，避免过细或过粗
4. **权限变更及时同步**：当后端权限发生变化时，及时更新前端代码
5. **做好降级处理**：当用户没有权限时，提供合理的提示或替代方案

## 参考示例

查看完整的权限使用示例：
- `/src/components/distribution/PermissionExample.vue` - 权限使用示例组件
- `/src/views/distribution/agent/index.vue` - 分销员管理页面
- `/src/views/broker/memberUser/index.vue` - 参考实现