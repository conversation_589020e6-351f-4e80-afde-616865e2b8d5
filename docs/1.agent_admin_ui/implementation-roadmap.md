# 分销管理后台实施路线图 - Claude Code 执行版

## 🎯 Claude Code 执行目标

为Claude Code提供清晰的分步执行路线图，确保高效完成分销管理后台开发。

### 📋 执行原则

1. **严格按序**: 必须按Phase顺序执行，不可跳跃
2. **完整复制**: 代码模板必须完整复制，不可省略
3. **立即验证**: 每完成一个组件立即测试功能
4. **错误处理**: 遇到错误立即解决，不可带问题继续

## 📋 快速开始清单

### 前置条件检查

- [ ] Vue 3.5+ 项目环境
- [ ] Element Plus 2.8+ 已安装
- [ ] TypeScript 5.3+ 配置
- [ ] Pinia 状态管理已配置
- [ ] ECharts 图表库已安装

### 核心依赖确认

```json
{
  "vue": "^3.5.2",
  "element-plus": "^2.8.4",
  "typescript": "^5.3.3",
  "pinia": "^2.1.7",
  "echarts": "^5.5.0",
  "axios": "^1.6.8"
}
```

## 🚀 Claude Code 执行阶段

### Phase 1: 基础架构搭建 🔴 (必须首先完成)

**执行时间**: 1-2小时  
**成功标准**: 所有文件创建完成，TypeScript编译无错误

#### 1.1 创建目录结构

```bash
# 需要创建的目录结构
src/
├── views/distribution/
│   ├── dashboard/
│   ├── agent/list/
│   ├── level/config/
│   ├── product/config/
│   ├── commission/records/
│   ├── order/list/
│   ├── statistics/overview/
│   └── settings/basic/
├── api/distribution/
├── components/distribution/
├── stores/distribution/
└── types/distribution/
```

**执行任务**: 创建完整的目录结构和空文件

#### 1.2 类型定义系统

**文件**: `src/types/distribution/agent.ts`

```typescript
// 核心类型定义
export interface AgentInfo {
  id: number
  agentCode: string
  nickname: string
  mobile: string
  avatar?: string
  levelId: number
  levelName: string
  levelColor: string
  parentId?: number
  parentChain: AgentBasicInfo[]
  directChildren: number
  totalTeam: number
  totalCommission: number
  availableCommission: number
  totalOrders: number
  monthlyOrders: number
  conversionRate: number
  status: AgentStatus
  auditStatus: AuditStatus
  registerTime: string
  lastActiveTime?: string
}

export interface AgentBasicInfo {
  id: number
  nickname: string
  avatar?: string
  level: {
    levelName: string
    levelColor: string
    levelGrade: number
  }
}

export enum AgentStatus {
  INACTIVE = 0,
  ACTIVE = 1,
  SUSPENDED = 2,
  DISABLED = 3
}

export enum AuditStatus {
  PENDING = 0,
  APPROVED = 1,
  REJECTED = 2
}
```

**执行任务**: 创建所有必要的TypeScript类型定义

#### 1.3 API接口层

**文件**: `src/api/distribution/agent.ts`

```typescript
import request from '@/utils/request'
import type { AgentInfo, AgentSearchParams, PageResponse } from '@/types/distribution'

export const DistributionAgentAPI = {
  getAgentList: (params: AgentSearchParams) =>
    request.get<PageResponse<AgentInfo>>('/admin/distribution/agent/list', { params }),
  
  getAgentDetail: (id: number) =>
    request.get<AgentInfo>(`/admin/distribution/agent/${id}`),
  
  getAgentRelationship: (id: number) =>
    request.get(`/admin/distribution/agent/${id}/relationship`),
  
  getTeamOrders: (id: number, params: any) =>
    request.get(`/admin/distribution/agent/${id}/team-orders`, { params }),
  
  batchAuditAgent: (data: any) =>
    request.post('/admin/distribution/agent/batch-audit', data)
}
```

**执行任务**: 创建所有API接口文件

### Phase 2: 核心组件开发 🔴 (基础组件)

**执行时间**: 2-3小时  
**成功标准**: 所有基础组件可正常渲染和交互

#### 2.1 基础抽屉组件

**文件**: `src/components/common/BaseDrawer.vue`

```vue
<template>
  <el-drawer
    v-model="visible"
    :title="title"
    :size="size"
    :before-close="handleBeforeClose"
    :destroy-on-close="destroyOnClose"
    class="base-drawer"
  >
    <template #header>
      <div class="drawer-header">
        <h3 class="drawer-title">{{ title }}</h3>
        <div class="header-actions">
          <slot name="header-actions" />
        </div>
      </div>
    </template>

    <div class="drawer-content" v-loading="loading">
      <slot />
    </div>

    <template #footer v-if="showFooter">
      <div class="drawer-footer">
        <slot name="footer">
          <el-button @click="handleCancel">{{ cancelText }}</el-button>
          <el-button type="primary" @click="handleConfirm" :loading="confirmLoading">
            {{ confirmText }}
          </el-button>
        </slot>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
interface Props {
  modelValue: boolean
  title: string
  size?: string | number
  loading?: boolean
  showFooter?: boolean
  cancelText?: string
  confirmText?: string
  confirmLoading?: boolean
  destroyOnClose?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: '50%',
  loading: false,
  showFooter: true,
  cancelText: '取消',
  confirmText: '确定',
  confirmLoading: false,
  destroyOnClose: true
})

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const handleConfirm = () => emit('confirm')
const handleCancel = () => emit('cancel')
const handleBeforeClose = (done: () => void) => {
  emit('update:modelValue', false)
  done()
}
</script>
```

**执行任务**: 创建基础抽屉组件

#### 2.2 统计卡片组件

**文件**: `src/components/distribution/StatisticsCard.vue`

```vue
<template>
  <el-card class="statistics-card" :class="{ clickable }" @click="handleClick">
    <div class="card-content">
      <div class="card-icon">
        <el-icon :size="24" :color="iconColor">
          <component :is="icon" />
        </el-icon>
      </div>
      <div class="card-info">
        <div class="card-title">{{ title }}</div>
        <div class="card-value">
          {{ prefix }}{{ formattedValue }}{{ suffix }}
        </div>
        <div v-if="trend" class="card-trend" :class="trendClass">
          <el-icon><TrendCharts /></el-icon>
          {{ trend }}
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
interface Props {
  title: string
  value: number | string
  icon: string
  iconColor?: string
  prefix?: string
  suffix?: string
  trend?: string
  format?: 'number' | 'currency' | 'percentage'
  clickable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  iconColor: '#409EFF',
  format: 'number',
  clickable: false
})

const emit = defineEmits(['click'])

const formattedValue = computed(() => {
  if (props.format === 'currency') {
    return Number(props.value).toLocaleString()
  }
  return props.value
})

const trendClass = computed(() => {
  if (!props.trend) return ''
  return props.trend.startsWith('+') ? 'trend-up' : 'trend-down'
})

const handleClick = () => {
  if (props.clickable) {
    emit('click')
  }
}
</script>

<style lang="scss" scoped>
.statistics-card {
  .card-content {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .card-info {
    flex: 1;
  }
  
  .card-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }
  
  .card-value {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
  }
  
  .card-trend {
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    
    &.trend-up {
      color: #67C23A;
    }
    
    &.trend-down {
      color: #F56C6C;
    }
  }
  
  &.clickable {
    cursor: pointer;
    transition: all 0.2s;
    
    &:hover {
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
```

**执行任务**: 创建统计卡片组件

### Phase 3: 主要页面实现 🟡 (核心功能)

**执行时间**: 3-4小时  
**成功标准**: 主要页面功能完整，数据交互正常

#### 3.1 分销概览页面

**文件**: `src/views/distribution/dashboard/index.vue`

```vue
<template>
  <div class="distribution-dashboard">
    <!-- 统计卡片区域 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <StatisticsCard
          title="总分销员数"
          :value="dashboardData.totalAgents"
          icon="User"
          trend="+12%"
        />
      </el-col>
      <el-col :span="6">
        <StatisticsCard
          title="本月佣金"
          :value="dashboardData.monthlyCommission"
          icon="Money"
          trend="+8.5%"
          format="currency"
          prefix="¥"
        />
      </el-col>
      <el-col :span="6">
        <StatisticsCard
          title="分销订单"
          :value="dashboardData.distributionOrders"
          icon="ShoppingCart"
          trend="+15.2%"
        />
      </el-col>
      <el-col :span="6">
        <StatisticsCard
          title="待审核"
          :value="dashboardData.pendingAudits"
          icon="Warning"
          :clickable="true"
          @click="goToAudit"
        />
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="16">
        <el-card title="佣金趋势">
          <div ref="commissionChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card title="分销员等级分布">
          <div ref="levelChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 待处理事项 -->
    <el-card title="待处理事项" class="pending-tasks">
      <el-table :data="pendingTasks">
        <el-table-column prop="type" label="类型" />
        <el-table-column prop="count" label="数量" />
        <el-table-column prop="action" label="操作">
          <template #default="scope">
            <el-button size="small" @click="handleTask(scope.row)">
              处理
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import StatisticsCard from '@/components/distribution/StatisticsCard.vue'

defineOptions({ name: 'DistributionDashboard' })

const commissionChartRef = ref()
const levelChartRef = ref()

const dashboardData = reactive({
  totalAgents: 1234,
  monthlyCommission: 56789,
  distributionOrders: 890,
  pendingAudits: 12
})

const pendingTasks = ref([
  { type: '分销员审核', count: 5, action: 'audit' },
  { type: '提现审核', count: 3, action: 'withdraw' },
  { type: '等级升级', count: 2, action: 'upgrade' }
])

const goToAudit = () => {
  // 跳转到审核页面
}

const handleTask = (task: any) => {
  // 处理待办事项
}

onMounted(() => {
  // 初始化图表
  initCharts()
})

const initCharts = () => {
  // 佣金趋势图
  const commissionChart = echarts.init(commissionChartRef.value)
  commissionChart.setOption({
    title: { text: '佣金趋势' },
    xAxis: { type: 'category', data: ['1月', '2月', '3月', '4月', '5月', '6月'] },
    yAxis: { type: 'value' },
    series: [{
      data: [120, 200, 150, 80, 70, 110],
      type: 'line',
      smooth: true
    }]
  })

  // 等级分布图
  const levelChart = echarts.init(levelChartRef.value)
  levelChart.setOption({
    title: { text: '等级分布' },
    series: [{
      type: 'pie',
      data: [
        { value: 335, name: '初级' },
        { value: 310, name: '中级' },
        { value: 234, name: '高级' },
        { value: 135, name: 'VIP' }
      ]
    }]
  })
}
</script>

<style lang="scss" scoped>
.distribution-dashboard {
  .stats-row {
    margin-bottom: 20px;
  }
  
  .charts-row {
    margin-bottom: 20px;
  }
  
  .pending-tasks {
    margin-bottom: 20px;
  }
}
</style>
```

**执行任务**: 创建分销概览页面

#### 3.2 分销员列表页面

**文件**: `src/views/distribution/agent/list/index.vue`

```vue
<template>
  <div class="agent-list-page">
    <!-- 搜索筛选区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input 
            v-model="searchForm.keyword" 
            placeholder="姓名/手机号/编码" 
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="等级">
          <el-select v-model="searchForm.levelId" placeholder="选择等级" clearable>
            <el-option 
              v-for="level in agentLevels" 
              :key="level.id" 
              :label="level.levelName" 
              :value="level.id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="正常" :value="1" />
            <el-option label="暂停" :value="2" />
            <el-option label="禁用" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>分销员列表</span>
          <div class="header-actions">
            <el-button 
              type="primary" 
              @click="openAgentDrawer('create')"
            >
              <el-icon><Plus /></el-icon>
              新增分销员
            </el-button>
            <el-button 
              type="warning" 
              @click="handleBatchAudit"
              :disabled="!selectedAgents.length"
            >
              <el-icon><Check /></el-icon>
              批量审核
            </el-button>
          </div>
        </div>
      </template>

      <el-table 
        :data="agentList" 
        v-loading="loading"
        @selection-change="handleSelectionChange"
        row-key="id"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="分销员信息" width="280" fixed="left">
          <template #default="scope">
            <div class="agent-info-cell">
              <el-avatar :src="scope.row.avatar" :size="40" />
              <div class="info">
                <div class="name-row">
                  <span class="nickname">{{ scope.row.nickname }}</span>
                  <el-tag :type="getLevelTagType(scope.row.levelGrade)" size="small">
                    {{ scope.row.levelName }}
                  </el-tag>
                </div>
                <div class="details">
                  <span class="mobile">{{ scope.row.mobile }}</span>
                  <span class="code">{{ scope.row.agentCode }}</span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="团队数据" width="150">
          <template #default="scope">
            <div class="team-stats">
              <div class="stat-item">
                <span class="label">直属:</span>
                <span class="value">{{ scope.row.directChildren }}</span>
              </div>
              <div class="stat-item">
                <span class="label">团队:</span>
                <span class="value">{{ scope.row.totalTeam }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="业绩数据" width="180">
          <template #default="scope">
            <div class="performance-stats">
              <div class="stat-item">
                <span class="label">累计佣金:</span>
                <span class="value">¥{{ formatNumber(scope.row.totalCommission) }}</span>
              </div>
              <div class="stat-item">
                <span class="label">本月订单:</span>
                <span class="value">{{ scope.row.monthlyOrders }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button 
              size="small" 
              type="primary" 
              @click="openAgentDrawer('detail', scope.row)"
            >
              详情
            </el-button>
            <el-button 
              size="small" 
              @click="openAgentDrawer('edit', scope.row)"
            >
              编辑
            </el-button>
            <el-dropdown @command="(command) => handleCommand(command, scope.row)">
              <el-button size="small">
                更多<el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="adjust-level">调整等级</el-dropdown-item>
                  <el-dropdown-item command="view-team">查看团队</el-dropdown-item>
                  <el-dropdown-item command="suspend" divided>暂停账户</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.pageNo"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 分销员详情抽屉 -->
    <AgentDetailDrawer
      v-model="drawerVisible"
      :agent-id="currentAgentId"
      :mode="drawerMode"
      @refresh="fetchAgentList"
    />
  </div>
</template>

<script setup lang="ts">
import { DistributionAgentAPI } from '@/api/distribution/agent'
import AgentDetailDrawer from '@/components/distribution/AgentDetailDrawer.vue'
import type { AgentInfo } from '@/types/distribution/agent'

defineOptions({ name: 'AgentList' })

const agentList = ref<AgentInfo[]>([])
const agentLevels = ref([])
const selectedAgents = ref<AgentInfo[]>([])
const loading = ref(false)
const drawerVisible = ref(false)
const currentAgentId = ref<number>()
const drawerMode = ref<'detail' | 'edit' | 'create'>('detail')

const searchForm = reactive({
  keyword: '',
  levelId: null,
  status: null
})

const pagination = reactive({
  pageNo: 1,
  pageSize: 20,
  total: 0
})

const fetchAgentList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      ...pagination
    }
    const response = await DistributionAgentAPI.getAgentList(params)
    agentList.value = response.data.list
    pagination.total = response.data.total
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.pageNo = 1
  fetchAgentList()
}

const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    levelId: null,
    status: null
  })
  handleSearch()
}

const openAgentDrawer = (mode: 'detail' | 'edit' | 'create', agent?: AgentInfo) => {
  drawerMode.value = mode
  currentAgentId.value = agent?.id
  drawerVisible.value = true
}

const handleSelectionChange = (selection: AgentInfo[]) => {
  selectedAgents.value = selection
}

const handleBatchAudit = () => {
  // 批量审核逻辑
}

const handleCommand = (command: string, agent: AgentInfo) => {
  // 处理下拉菜单命令
}

const formatNumber = (num: number) => {
  return num.toLocaleString()
}

const getStatusTagType = (status: number) => {
  const types = ['info', 'success', 'warning', 'danger']
  return types[status] || 'info'
}

const getStatusText = (status: number) => {
  const texts = ['未激活', '正常', '暂停', '禁用']
  return texts[status] || '未知'
}

const getLevelTagType = (grade: number) => {
  return grade > 3 ? 'success' : grade > 1 ? 'warning' : 'info'
}

onMounted(() => {
  fetchAgentList()
})
</script>

<style lang="scss" scoped>
.agent-list-page {
  .search-card {
    margin-bottom: 16px;
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-actions {
        display: flex;
        gap: 8px;
      }
    }
  }
  
  .agent-info-cell {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .info {
      flex: 1;
      
      .name-row {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;
        
        .nickname {
          font-weight: 500;
        }
      }
      
      .details {
        display: flex;
        gap: 12px;
        font-size: 12px;
        color: #666;
      }
    }
  }
  
  .team-stats,
  .performance-stats {
    .stat-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;
      font-size: 12px;
      
      .label {
        color: #666;
      }
      
      .value {
        font-weight: 500;
      }
    }
  }
}
</style>
```

**执行任务**: 创建分销员列表页面

## 📝 Claude Code 执行检查点

### ✅ Phase 1 检查清单 (基础架构)

**必须全部完成才能进入Phase 2**

- [ ] **目录结构**: 所有必需目录已创建

  ```bash
  src/views/distribution/
  src/api/distribution/
  src/components/distribution/
  src/types/distribution/
  src/stores/distribution/
  ```

- [ ] **类型定义**: TypeScript类型文件已创建

  ```bash
  src/types/distribution/agent.ts
  src/types/distribution/common.ts
  ```

- [ ] **API接口**: API接口文件已创建

  ```bash
  src/api/distribution/agent.ts
  ```

- [ ] **状态管理**: Pinia store已创建

  ```bash
  src/stores/distribution/agent.ts
  ```

- [ ] **编译检查**: 运行 `npm run ts:check` 无错误

### ✅ Phase 2 检查清单 (核心组件)

**必须全部完成才能进入Phase 3**

- [ ] **基础抽屉**: `BaseDrawer.vue` 组件正常渲染
- [ ] **统计卡片**: `StatisticsCard.vue` 样式和交互正确
- [ ] **等级徽章**: `LevelBadge.vue` 显示效果正常
- [ ] **组件导入**: 所有组件可以正常导入使用
- [ ] **功能测试**: 创建测试页面验证组件功能

### ✅ Phase 3 检查清单 (主要页面)

**完成后即可投入使用**

- [ ] **概览页面**: Dashboard页面图表和数据正常
- [ ] **分销员列表**: 列表页面搜索筛选功能完整
- [ ] **详情抽屉**: 抽屉组件与列表页面集成成功
- [ ] **数据交互**: API调用和数据展示无错误
- [ ] **用户体验**: 页面交互流畅，无明显bug

## 🔧 调试和测试

### 常见问题解决

1. **组件导入错误**: 检查路径和导出方式
2. **类型定义错误**: 确保接口定义完整
3. **API调用失败**: 检查请求路径和参数
4. **样式显示异常**: 确认SCSS配置正确

### 测试用例

```typescript
// 测试分销员列表加载
describe('AgentList', () => {
  it('should load agent list correctly', async () => {
    // 测试逻辑
  })
  
  it('should handle search correctly', async () => {
    // 测试逻辑
  })
})
```

## 🚀 部署准备

### 构建检查

- [ ] TypeScript编译无错误
- [ ] ESLint检查通过
- [ ] 组件单元测试通过
- [ ] 页面功能测试通过

### 性能优化

- [ ] 路由懒加载配置
- [ ] 组件按需导入
- [ ] 图片资源优化
- [ ] 打包体积检查

---

**注意**: 请按照Phase顺序执行，每个阶段完成后进行检查点验证，确保质量后再进入下一阶段。
