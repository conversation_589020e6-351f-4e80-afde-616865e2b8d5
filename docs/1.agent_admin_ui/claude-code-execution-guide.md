# Claude Code 执行指南 - 分销管理后台

## 🎯 执行概述

这是专门为Claude Code设计的分步执行指南，包含完整的代码模板和详细的执行步骤。

## 📋 执行前准备

### 环境检查

确保项目已安装以下依赖：

```json
{
  "vue": "^3.5.2",
  "element-plus": "^2.8.4",
  "typescript": "^5.3.3",
  "pinia": "^2.1.7",
  "echarts": "^5.5.0",
  "axios": "^1.6.8",
  "@element-plus/icons-vue": "^2.1.0"
}
```

### 项目结构确认

确保以下基础目录存在：

- `src/views/`
- `src/components/`
- `src/api/`
- `src/types/`
- `src/stores/`

## 🚀 执行步骤

### Step 1: 创建目录结构

**执行命令**：创建以下目录结构

```bash
# 在 src/ 目录下创建以下结构
src/
├── views/distribution/
│   ├── dashboard/
│   ├── agent/
│   │   ├── list/
│   │   └── audit/
│   ├── level/
│   │   └── config/
│   ├── product/
│   │   └── config/
│   ├── commission/
│   │   ├── records/
│   │   ├── settlement/
│   │   └── withdraw/
│   ├── order/
│   │   └── list/
│   ├── statistics/
│   │   └── overview/
│   └── settings/
│       └── basic/
├── api/distribution/
├── components/
│   ├── common/
│   └── distribution/
├── stores/distribution/
└── types/distribution/
```

### Step 2: 创建TypeScript类型定义

**文件**: `src/types/distribution/agent.ts`

```typescript
// 分销员基本信息
export interface AgentBasicInfo {
  id: number
  nickname: string
  avatar?: string
  level: {
    levelName: string
    levelColor: string
    levelGrade: number
    levelIcon?: string
  }
}

// 分销员完整信息
export interface AgentInfo {
  id: number
  agentCode: string
  nickname: string
  realName?: string
  mobile: string
  avatar?: string
  levelId: number
  levelName: string
  levelColor: string
  levelGrade: number
  
  // 分销关系
  parentId?: number
  parentName?: string
  parentChain: AgentBasicInfo[]
  directChildren: number
  totalTeam: number
  maxDepth: number
  
  // 业绩数据
  totalCommission: number
  availableCommission: number
  frozenCommission: number
  totalOrders: number
  monthlyOrders: number
  teamOrders: number
  personalOrders: number
  conversionRate: number
  
  // 状态信息
  status: AgentStatus
  auditStatus: AuditStatus
  auditRemark?: string
  registerTime: string
  lastActiveTime?: string
  tags?: string[]
}

// 分销员搜索参数
export interface AgentSearchParams {
  keyword?: string
  levelId?: number
  status?: AgentStatus
  auditStatus?: AuditStatus
  parentId?: number
  registerTime?: [string, string]
  lastActiveTime?: [string, string]
  pageNo: number
  pageSize: number
  sortField?: string
  sortOrder?: 'asc' | 'desc'
}

// 团队订单信息
export interface TeamOrderInfo {
  id: number
  orderNo: string
  orderAmount: number
  orderStatus: OrderStatus
  orderTime: string
  
  // 分销员信息
  agent: AgentBasicInfo
  distributionLevel: number
  
  // 商品信息
  product: {
    id: number
    name: string
    image: string
    price: number
  }
  quantity: number
  
  // 佣金信息
  commissionAmount: number
  commissionRate: number
  commissionStatus: CommissionStatus
  
  // 买家信息
  buyer: {
    id: number
    nickname: string
    mobile?: string
  }
}

// 枚举定义
export enum AgentStatus {
  INACTIVE = 0,    // 未激活
  ACTIVE = 1,      // 正常
  SUSPENDED = 2,   // 暂停
  DISABLED = 3     // 禁用
}

export enum AuditStatus {
  PENDING = 0,     // 待审核
  APPROVED = 1,    // 已通过
  REJECTED = 2     // 已拒绝
}

export enum OrderStatus {
  PENDING = 'pending',      // 待付款
  PAID = 'paid',           // 已付款
  SHIPPED = 'shipped',     // 已发货
  COMPLETED = 'completed', // 已完成
  CANCELLED = 'cancelled', // 已取消
  REFUNDED = 'refunded'    // 已退款
}

export enum CommissionStatus {
  PENDING = 0,      // 待确认
  CONFIRMED = 1,    // 已确认
  SETTLED = 2,      // 已结算
  WITHDRAWN = 3,    // 已提现
  FROZEN = 4,       // 已冻结
  CANCELLED = 5     // 已取消
}
```

**文件**: `src/types/distribution/common.ts`

```typescript
// 通用分页响应
export interface PageResponse<T> {
  list: T[]
  total: number
  pageNo: number
  pageSize: number
  totalPages: number
}

// 通用API响应
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
}

// 统计数据项
export interface StatItem {
  key: string
  label: string
  value: string | number
  trend?: string
  format?: 'number' | 'currency' | 'percentage'
}

// 区块配置
export interface SectionConfig {
  key: string
  title: string
  icon?: string
  badge?: string | number
  component: any
  props?: Record<string, any>
}
```

### Step 3: 创建API接口

**文件**: `src/api/distribution/agent.ts`

```typescript
import request from '@/utils/request'
import type { 
  AgentInfo, 
  AgentSearchParams, 
  TeamOrderInfo, 
  PageResponse, 
  ApiResponse 
} from '@/types/distribution'

export const DistributionAgentAPI = {
  // 获取分销员列表
  getAgentList: (params: AgentSearchParams) =>
    request.get<ApiResponse<PageResponse<AgentInfo>>>('/admin/distribution/agent/list', { params }),
  
  // 获取分销员详情
  getAgentDetail: (id: number) =>
    request.get<ApiResponse<AgentInfo>>(`/admin/distribution/agent/${id}`),
  
  // 获取分销员关系链
  getAgentRelationship: (id: number) =>
    request.get<ApiResponse<{
      parentChain: AgentBasicInfo[]
      currentAgent: AgentInfo
      childrenTree: any[]
    }>>(`/admin/distribution/agent/${id}/relationship`),
  
  // 获取团队订单
  getTeamOrders: (id: number, params: any) =>
    request.get<ApiResponse<PageResponse<TeamOrderInfo>>>(
      `/admin/distribution/agent/${id}/team-orders`, 
      { params }
    ),
  
  // 获取个人订单
  getPersonalOrders: (id: number, params: any) =>
    request.get<ApiResponse<PageResponse<TeamOrderInfo>>>(
      `/admin/distribution/agent/${id}/personal-orders`, 
      { params }
    ),
  
  // 获取团队成员
  getTeamMembers: (id: number, params?: any) =>
    request.get<ApiResponse<AgentBasicInfo[]>>(
      `/admin/distribution/agent/${id}/team-members`, 
      { params }
    ),
  
  // 批量审核分销员
  batchAuditAgent: (data: {
    agentIds: number[]
    auditStatus: number
    auditRemark?: string
  }) =>
    request.post<ApiResponse<void>>('/admin/distribution/agent/batch-audit', data),
  
  // 调整分销员等级
  adjustAgentLevel: (data: {
    agentId: number
    levelId: number
    reason: string
  }) =>
    request.post<ApiResponse<void>>('/admin/distribution/agent/adjust-level', data),
  
  // 调整分销关系
  adjustAgentRelationship: (data: {
    agentId: number
    newParentId?: number
    reason: string
  }) =>
    request.post<ApiResponse<void>>('/admin/distribution/agent/adjust-relationship', data),
  
  // 更新分销员信息
  updateAgent: (id: number, data: Partial<AgentInfo>) =>
    request.put<ApiResponse<AgentInfo>>(`/admin/distribution/agent/${id}`, data),
  
  // 创建分销员
  createAgent: (data: Partial<AgentInfo>) =>
    request.post<ApiResponse<AgentInfo>>('/admin/distribution/agent', data),
  
  // 导出分销员数据
  exportAgentData: (params: AgentSearchParams) =>
    request.get('/admin/distribution/agent/export', { 
      params, 
      responseType: 'blob' 
    }),
  
  // 导出团队订单
  exportTeamOrders: (id: number, params: any) =>
    request.get(`/admin/distribution/agent/${id}/team-orders/export`, { 
      params, 
      responseType: 'blob' 
    })
}
```

### Step 4: 创建Pinia状态管理

**文件**: `src/stores/distribution/agent.ts`

```typescript
import { defineStore } from 'pinia'
import { DistributionAgentAPI } from '@/api/distribution/agent'
import type { AgentInfo, AgentSearchParams } from '@/types/distribution/agent'

export const useDistributionAgentStore = defineStore('distributionAgent', () => {
  // 状态
  const agentList = ref<AgentInfo[]>([])
  const currentAgent = ref<AgentInfo | null>(null)
  const agentLevels = ref<any[]>([])
  const loading = ref(false)
  const searchParams = ref<AgentSearchParams>({
    pageNo: 1,
    pageSize: 20
  })

  // 计算属性
  const totalAgents = computed(() => agentList.value.length)
  const activeAgents = computed(() => 
    agentList.value.filter(agent => agent.status === 1).length
  )
  const pendingAudits = computed(() =>
    agentList.value.filter(agent => agent.auditStatus === 0).length
  )

  // 方法
  const fetchAgentList = async (params?: Partial<AgentSearchParams>) => {
    loading.value = true
    try {
      if (params) {
        Object.assign(searchParams.value, params)
      }
      const response = await DistributionAgentAPI.getAgentList(searchParams.value)
      agentList.value = response.data.data.list
      return response.data.data
    } catch (error) {
      console.error('获取分销员列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchAgentDetail = async (id: number) => {
    try {
      const response = await DistributionAgentAPI.getAgentDetail(id)
      currentAgent.value = response.data.data
      return response.data.data
    } catch (error) {
      console.error('获取分销员详情失败:', error)
      throw error
    }
  }

  const updateAgentStatus = async (id: number, status: number) => {
    try {
      await DistributionAgentAPI.updateAgent(id, { status })
      const index = agentList.value.findIndex(agent => agent.id === id)
      if (index !== -1) {
        agentList.value[index].status = status
      }
    } catch (error) {
      console.error('更新分销员状态失败:', error)
      throw error
    }
  }

  const batchAuditAgents = async (agentIds: number[], auditStatus: number, auditRemark?: string) => {
    try {
      await DistributionAgentAPI.batchAuditAgent({
        agentIds,
        auditStatus,
        auditRemark
      })
      // 更新本地状态
      agentList.value.forEach(agent => {
        if (agentIds.includes(agent.id)) {
          agent.auditStatus = auditStatus
          if (auditRemark) {
            agent.auditRemark = auditRemark
          }
        }
      })
    } catch (error) {
      console.error('批量审核失败:', error)
      throw error
    }
  }

  const resetSearchParams = () => {
    searchParams.value = {
      pageNo: 1,
      pageSize: 20
    }
  }

  return {
    // 状态
    agentList,
    currentAgent,
    agentLevels,
    loading,
    searchParams,
    
    // 计算属性
    totalAgents,
    activeAgents,
    pendingAudits,
    
    // 方法
    fetchAgentList,
    fetchAgentDetail,
    updateAgentStatus,
    batchAuditAgents,
    resetSearchParams
  }
})
```

### Step 5: 创建基础组件

**文件**: `src/components/common/BaseDrawer.vue`

```vue
<template>
  <el-drawer
    v-model="visible"
    :title="title"
    :size="size"
    :before-close="handleBeforeClose"
    :destroy-on-close="destroyOnClose"
    :modal="modal"
    class="base-drawer"
  >
    <!-- 抽屉头部 -->
    <template #header>
      <div class="drawer-header">
        <div class="header-left">
          <h3 class="drawer-title">{{ title }}</h3>
          <div class="header-subtitle" v-if="subtitle">{{ subtitle }}</div>
        </div>
        <div class="header-actions">
          <slot name="header-actions" />
        </div>
      </div>
    </template>

    <!-- 抽屉内容 -->
    <div class="drawer-content" v-loading="loading">
      <slot />
    </div>

    <!-- 抽屉底部 -->
    <template #footer v-if="showFooter">
      <div class="drawer-footer">
        <slot name="footer">
          <el-button @click="handleCancel">{{ cancelText }}</el-button>
          <el-button type="primary" @click="handleConfirm" :loading="confirmLoading">
            {{ confirmText }}
          </el-button>
        </slot>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
interface Props {
  modelValue: boolean
  title: string
  subtitle?: string
  size?: string | number
  loading?: boolean
  showFooter?: boolean
  cancelText?: string
  confirmText?: string
  confirmLoading?: boolean
  destroyOnClose?: boolean
  modal?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: '50%',
  loading: false,
  showFooter: true,
  cancelText: '取消',
  confirmText: '确定',
  confirmLoading: false,
  destroyOnClose: true,
  modal: true
})

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel', 'close'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
  emit('update:modelValue', false)
}

const handleBeforeClose = (done: () => void) => {
  emit('close')
  done()
}
</script>

<style lang="scss" scoped>
.base-drawer {
  .drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    
    .header-left {
      flex: 1;
      
      .drawer-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
      
      .header-subtitle {
        margin-top: 4px;
        font-size: 14px;
        color: #666;
      }
    }
    
    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .drawer-content {
    padding: 0;
  }
  
  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 0;
    border-top: 1px solid #e4e7ed;
  }
}
</style>
```

**文件**: `src/components/distribution/StatisticsCard.vue`

```vue
<template>
  <el-card class="statistics-card" :class="{ clickable }" @click="handleClick">
    <div class="card-content">
      <div class="card-icon" v-if="icon">
        <el-icon :size="iconSize" :color="iconColor">
          <component :is="icon" />
        </el-icon>
      </div>
      <div class="card-info">
        <div class="card-title">{{ title }}</div>
        <div class="card-value">
          {{ prefix }}{{ formattedValue }}{{ suffix }}
        </div>
        <div v-if="trend" class="card-trend" :class="trendClass">
          <el-icon><TrendCharts /></el-icon>
          {{ trend }}
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { TrendCharts } from '@element-plus/icons-vue'

interface Props {
  title: string
  value: number | string
  icon?: string
  iconColor?: string
  iconSize?: number
  prefix?: string
  suffix?: string
  trend?: string
  format?: 'number' | 'currency' | 'percentage'
  clickable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  iconColor: '#409EFF',
  iconSize: 24,
  format: 'number',
  clickable: false
})

const emit = defineEmits(['click'])

const formattedValue = computed(() => {
  const val = props.value
  if (props.format === 'currency') {
    return Number(val).toLocaleString()
  }
  if (props.format === 'percentage') {
    return `${val}%`
  }
  return val
})

const trendClass = computed(() => {
  if (!props.trend) return ''
  return props.trend.startsWith('+') ? 'trend-up' : 'trend-down'
})

const handleClick = () => {
  if (props.clickable) {
    emit('click')
  }
}
</script>

<style lang="scss" scoped>
.statistics-card {
  border-radius: 8px;
  transition: all 0.2s;
  
  .card-content {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: rgba(64, 158, 255, 0.1);
    border-radius: 8px;
  }
  
  .card-info {
    flex: 1;
  }
  
  .card-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    line-height: 1;
  }
  
  .card-value {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
    line-height: 1;
  }
  
  .card-trend {
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    
    &.trend-up {
      color: #67C23A;
    }
    
    &.trend-down {
      color: #F56C6C;
    }
  }
  
  &.clickable {
    cursor: pointer;
    
    &:hover {
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }
  }
}
</style>
```

**文件**: `src/components/distribution/LevelBadge.vue`

```vue
<template>
  <el-tag
    :type="tagType"
    :color="level.levelColor"
    :style="{ color: textColor }"
    :size="size"
    class="level-badge"
  >
    <el-icon v-if="level.levelIcon" class="level-icon">
      <component :is="level.levelIcon" />
    </el-icon>
    {{ level.levelName }}
  </el-tag>
</template>

<script setup lang="ts">
interface LevelInfo {
  levelName: string
  levelColor: string
  levelIcon?: string
  levelGrade: number
}

interface Props {
  level: LevelInfo
  size?: 'small' | 'default' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  size: 'default'
})

const tagType = computed(() => {
  const grade = props.level.levelGrade
  if (grade >= 4) return 'success'
  if (grade >= 2) return 'warning'
  return 'info'
})

const textColor = computed(() => {
  const color = props.level.levelColor
  if (!color) return '#333'
  
  // 简单的对比度计算
  const hex = color.replace('#', '')
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)
  const brightness = (r * 299 + g * 587 + b * 114) / 1000
  
  return brightness > 128 ? '#333' : '#fff'
})
</script>

<style lang="scss" scoped>
.level-badge {
  .level-icon {
    margin-right: 4px;
  }
}
</style>
```

### Step 6: 验证基础组件

创建一个测试页面验证组件是否正常工作：

**文件**: `src/views/distribution/test/index.vue`

```vue
<template>
  <div class="test-page">
    <h2>组件测试页面</h2>
    
    <!-- 统计卡片测试 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <StatisticsCard
          title="测试数据"
          :value="1234"
          icon="User"
          trend="+12%"
          :clickable="true"
          @click="handleCardClick"
        />
      </el-col>
    </el-row>
    
    <!-- 等级徽章测试 -->
    <div style="margin-bottom: 20px;">
      <LevelBadge :level="testLevel" />
    </div>
    
    <!-- 抽屉测试 -->
    <el-button @click="drawerVisible = true">打开抽屉</el-button>
    
    <BaseDrawer
      v-model="drawerVisible"
      title="测试抽屉"
      subtitle="这是一个测试抽屉"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    >
      <p>抽屉内容测试</p>
    </BaseDrawer>
  </div>
</template>

<script setup lang="ts">
import StatisticsCard from '@/components/distribution/StatisticsCard.vue'
import LevelBadge from '@/components/distribution/LevelBadge.vue'
import BaseDrawer from '@/components/common/BaseDrawer.vue'

const drawerVisible = ref(false)

const testLevel = {
  levelName: '高级会员',
  levelColor: '#67C23A',
  levelGrade: 3
}

const handleCardClick = () => {
  console.log('卡片被点击')
}

const handleConfirm = () => {
  console.log('确认')
  drawerVisible.value = false
}

const handleCancel = () => {
  console.log('取消')
}
</script>
```

## ✅ 执行验证

### 验证步骤

1. **TypeScript编译检查**

   ```bash
   npm run ts:check
   ```

2. **组件导入测试**
   - 访问测试页面
   - 确认所有组件正常渲染
   - 测试交互功能

3. **API接口测试**
   - 检查API文件导入无错误
   - 确认类型定义正确

4. **状态管理测试**
   - 检查Pinia store导入无错误
   - 测试状态更新功能

### 常见问题解决

1. **导入路径错误**
   - 检查文件路径是否正确
   - 确认文件已创建

2. **类型定义错误**
   - 检查接口定义是否完整
   - 确认导入的类型存在

3. **组件渲染异常**
   - 检查props定义是否正确
   - 确认依赖组件已导入

## 🎯 下一步

完成基础组件验证后，继续执行：

1. **创建分销员详情抽屉组件**
2. **创建分销员列表页面**
3. **创建概览仪表盘页面**
4. **集成所有功能模块**

---

**重要提示**: 请严格按照步骤顺序执行，每完成一个步骤都要进行验证测试，确保无错误后再继续下一步。
