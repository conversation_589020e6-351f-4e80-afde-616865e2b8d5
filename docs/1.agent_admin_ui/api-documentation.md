# 分销管理后台API接口文档

基于项目实际代码分析，整理的完整API接口文档。本文档与实际实现保持同步，包含所有可用的API接口。

## API基础信息

- **基础路径**: `/admin/distribution` (管理后台) / `/distribution` (前台接口)
- **认证方式**: JWT Token
- **响应格式**: JSON
- **请求方式**: RESTful API

### 通用响应格式

```json
{
  "code": 0,
  "msg": "success",
  "data": {},
  "timestamp": 1640995200000
}
```

### 分页响应格式

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "list": [],
    "total": 100,
    "pageNo": 1,
    "pageSize": 20,
    "totalPages": 5
  }
}
```

## 一、分销员管理 API

### 1.1 获取分销员列表

**接口地址**: `GET /admin/distribution/agent/list`

**权限标识**: `distribution:agent:query`

**请求参数**: `DistAgentPageReqVO`

```typescript
interface DistAgentPageReqVO {
    keyword?: string          // 关键词搜索（姓名/手机号/编码）
    levelId?: number         // 等级ID
    status?: number          // 状态（0-禁用 1-启用）
    applyStatus?: number     // 申请状态（0-待审核 1-已通过 2-已拒绝）
    parentId?: number        // 上级ID
    dateRange?: string[]     // 时间范围
    pageNo: number           // 页码
    pageSize: number         // 每页大小
}
```

**响应数据**: `PageResult<DistAgentVO>`

```typescript
interface DistAgentVO {
    id: number
    memberId: number
    agentCode: string         // 分销员编码
    agentName: string         // 分销员姓名
    agentMobile?: string      // 手机号
    levelId: number           // 等级ID
    levelName?: string        // 等级名称
    parentId?: number         // 上级ID
    parentCode?: string       // 上级编码
    parentName?: string       // 上级姓名
    referrerId?: number       // 推荐人ID
    referrerCode?: string     // 推荐人编码
    referrerName?: string     // 推荐人姓名
    agentTags?: string        // 标签
    path?: string             // 关系路径
    depth?: number            // 层级深度
    applyTime?: string        // 申请时间
    approveTime?: string      // 审核时间
    applyStatus: number       // 申请状态
    approveRemark?: string    // 审核备注
    bindTime?: string         // 绑定时间
    status: number            // 状态
    teamCount?: number        // 团队人数
    directCount?: number      // 直属人数
    monthTeamCount?: number   // 本月团队人数
    totalSales?: number       // 累计销售额
    monthSales?: number       // 本月销售额
    totalCommission?: number  // 累计佣金
    availableCommission?: number // 可用佣金
    frozenCommission?: number    // 冻结佣金
    withdrawnCommission?: number // 已提现佣金
    levelUpdateTime?: string     // 等级更新时间
    lastSalesTime?: string       // 最后销售时间
    createTime?: string
    updateTime?: string
}
```

### 1.2 获取分销员详情

**接口地址**: `GET /admin/distribution/agent/get`

**权限标识**: `distribution:agent:query`

**请求参数**:

- `id`: 分销员ID

**响应数据**: `DistAgentVO`

### 1.3 新增分销员

**接口地址**: `POST /admin/distribution/agent/create`

**权限标识**: `distribution:agent:create`

**请求参数**: `DistAgentVO`

### 1.4 修改分销员

**接口地址**: `PUT /admin/distribution/agent/update`

**权限标识**: `distribution:agent:update`

**请求参数**: `DistAgentVO`

### 1.5 删除分销员

**接口地址**: `DELETE /admin/distribution/agent/delete`

**权限标识**: `distribution:agent:delete`

**请求参数**:

- `id`: 分销员ID

### 1.6 修改分销员状态

**接口地址**: `PUT /admin/distribution/agent/update-status`

**权限标识**: `distribution:agent:update`

**请求参数**:

```json
{
  "id": 1,
  "status": 1    // 0-禁用 1-启用
}
```

### 1.7 审核分销员申请

**接口地址**: `PUT /admin/distribution/agent/audit`

**权限标识**: `distribution:agent:audit`

**请求参数**:

```json
{
  "id": 1,
  "applyStatus": 1,         // 0-待审核 1-已通过 2-已拒绝
  "approveRemark": "审核备注"
}
```

### 1.8 修改分销员标签

**接口地址**: `PUT /admin/distribution/agent/update-tags`

**权限标识**: `distribution:agent:update`

**请求参数**:

```json
{
  "id": 1,
  "tagIds": [1, 2, 3]
}
```

### 1.9 获取分销员统计信息

**接口地址**: `GET /admin/distribution/agent/statistics`

**权限标识**: `distribution:agent:query`

**请求参数**:

- `id`: 分销员ID

**响应数据**: 分销员统计信息

### 1.10 获取团队成员列表

**接口地址**: `GET /admin/distribution/agent/team-list`

**权限标识**: `distribution:agent:query`

**请求参数**:

- `parentId`: 上级分销员ID
- 其他分页参数

### 1.11 导出分销员数据

**接口地址**: `GET /admin/distribution/agent/export`

**权限标识**: `distribution:agent:export`

**请求参数**: `DistAgentPageReqVO`

**响应**: Excel文件下载
parentId?: number           // 上级ID
parentName?: string         // 上级姓名
status: number              // 状态
availableAmount: number     // 可用佣金
teamCount: number           // 团队人数
directCount: number         // 直推人数
createTime: string
}

```

### 1.4 获取分销员详情

**接口地址**: `GET /distribution/agent/get`

**权限标识**: `distribution:agent:query`

**请求参数**:

- `id`: 分销员ID

**响应数据**: `DistAgentDetailRespVO`

```typescript
interface DistAgentDetailRespVO {
  id: number
  memberId: number
  memberNickname: string
  memberAvatar: string
  realName: string
  mobile: string
  levelId: number
  levelName?: string
  parentId?: number
  parentName?: string
  status: number
  availableAmount: number      // 可用佣金
  frozenAmount: number         // 冻结佣金
  teamCount: number            // 团队总数
  directCount: number          // 直推人数
  totalCommission: number      // 累计佣金
  totalOrder: number           // 累计订单
  createTime: string
  // ... 更多详细信息
}
```

### 1.5 更新分销员等级

**接口地址**: `PUT /distribution/agent/update-level`

**权限标识**: `distribution:agent:update`

**请求参数**: `DistAgentUpdateLevelReqVO`

```json
{
  "id": 1,
  "levelId": 2
}
```

### 1.6 更新分销员状态

**接口地址**: `PUT /distribution/agent/update-status`

**权限标识**: `distribution:agent:update`

**请求参数**: `DistAgentUpdateStatusReqVO`

```json
{
  "id": 1,
  "status": 1    // 0-禁用 1-启用
}
```

### 1.7 获取分销员统计信息

**接口地址**: `GET /distribution/agent/statistics`

**权限标识**: `distribution:agent:query`

**请求参数**:

- `id`: 分销员ID

**响应数据**: `DistAgentStatisticsRespVO`

```typescript
interface DistAgentStatisticsRespVO {
    totalCommission: number      // 累计佣金
    monthCommission: number      // 本月佣金
    todayCommission: number      // 今日佣金
    totalOrder: number           // 累计订单
    monthOrder: number           // 本月订单
    todayOrder: number           // 今日订单
    teamSize: number             // 团队规模
    directTeamSize: number       // 直推人数
    // ... 更多统计数据
}
```

### 1.8 获取团队成员列表

**接口地址**: `GET /distribution/agent/team-members`

**权限标识**: `distribution:agent:query`

**请求参数**: `DistAgentTeamMemberPageReqVO`

- `agentId`: 分销员ID
- `level`: 层级（1-直属，2-二级等）
- `pageNo`: 页码
- `pageSize`: 每页大小

**响应数据**: `PageResult<DistAgentTeamMemberRespVO>`

### 1.9 批量更新分销员状态

**接口地址**: `POST /distribution/agent/batch-update-status`

**权限标识**: `distribution:agent:update`

**请求参数**: `DistAgentBatchUpdateStatusReqVO`

```json
{
  "ids": [1, 2, 3],
  "status": 1
}
```

### 1.10 删除分销员

**接口地址**: `DELETE /distribution/agent/delete`

**权限标识**: `distribution:agent:delete`

**请求参数**:

- `id`: 分销员ID

### 1.11 导出分销员数据

**接口地址**: `GET /distribution/agent/export`

**权限标识**: `distribution:agent:export`

**请求参数**: `DistAgentExportReqVO`

- 与分页查询参数相同

## 二、佣金管理 API

### 2.1 获取佣金账单列表

**接口地址**: `GET /admin/distribution/commission/bill/list`

**权限标识**: `distribution:commission:query`

**请求参数**: `CommissionBillPageReqVO`

```typescript
interface CommissionBillPageReqVO {
    agentId?: number          // 分销员ID
    billType?: number         // 账单类型
    status?: number           // 状态
    freezeStatus?: number     // 冻结状态
    dateRange?: string[]      // 时间范围
    pageNo: number            // 页码
    pageSize: number          // 每页大小
}
```

**响应数据**: `PageResult<CommissionBillVO>`

```typescript
interface CommissionBillVO {
    id: number
    billNo: string            // 账单号
    agentId: number           // 分销员ID
    agentName: string         // 分销员姓名
    agentLevelId: number      // 分销员等级ID
    billType: number          // 账单类型
    bizType?: number          // 业务类型
    bizId: number             // 业务ID
    bizNo: string             // 业务编号
    orderId?: number          // 订单ID
    orderAmount?: number      // 订单金额
    spuId?: number            // 商品ID
    spuName?: string          // 商品名称
    sourceAgentId?: number    // 来源分销员ID
    sourceAgentName?: string  // 来源分销员姓名
    traceLevel?: number       // 追踪层级
    amount: number            // 佣金金额
    baseAmount: number        // 基础金额
    rate?: number             // 佣金比例
    schemeId: number          // 方案ID
    configType?: string       // 配置类型
    configSnapshot?: any      // 配置快照
    freezeStatus: number      // 冻结状态
    freezeEndTime?: string    // 冻结结束时间
    unfreezeTime?: string     // 解冻时间
    status: number            // 状态
    settleTime?: string       // 结算时间
    settleBatchNo?: string    // 结算批次号
    remark?: string           // 备注
    billTime: string          // 账单时间
    createTime?: string
    updateTime?: string
}
```

### 2.2 获取佣金账单详情

**接口地址**: `GET /admin/distribution/commission/bill/get`

**权限标识**: `distribution:commission:query`

**请求参数**:

- `id`: 佣金账单ID

**响应数据**: `CommissionBillVO`

### 2.3 批量结算佣金

**接口地址**: `POST /admin/distribution/commission/batch-settle`

**权限标识**: `distribution:commission:settle`

**请求参数**:

```json
{
  "billIds": [1, 2, 3],
  "settleBatchNo": "SETTLE20240101001",
  "remark": "批量结算备注"
}
```

### 2.4 取消佣金账单

**接口地址**: `PUT /admin/distribution/commission/cancel`

**权限标识**: `distribution:commission:manage`

**请求参数**:

```json
{
  "id": 1,
  "remark": "取消原因"
}
```

### 2.5 导出佣金账单

**接口地址**: `GET /admin/distribution/commission/bill/export`

**权限标识**: `distribution:commission:export`

**请求参数**: `CommissionBillPageReqVO`

**响应**: Excel文件下载

### 2.3 获取佣金统计

**接口地址**: `GET /distribution/admin/commission/statistics`

**权限标识**: `distribution:commission:query`

**请求参数**: `DistCommissionStatisticsReqVO`

- `agentId`: 分销员ID（可选）
- `startTime`: 开始时间
- `endTime`: 结束时间

**响应数据**: `DistCommissionStatisticsRespVO`

```typescript
interface DistCommissionStatisticsRespVO {
    totalAmount: number         // 总佣金
    settledAmount: number       // 已结算金额
    unsettledAmount: number     // 未结算金额
    withdrawnAmount: number     // 已提现金额
    orderCount: number          // 订单数量
    agentCount: number          // 分销员数量
}
```

### 2.4 获取待结算佣金分页

**接口地址**: `GET /distribution/admin/commission/pending`

**权限标识**: `distribution:commission:settle`

**请求参数**: `DistCommissionPendingPageReqVO`

### 2.5 结算佣金

**接口地址**: `POST /distribution/admin/commission/settle`

**权限标识**: `distribution:commission:settle`

**请求参数**: `DistCommissionSettleReqVO`

```json
{
  "commissionIds": [1, 2, 3]
}
```

### 2.6 解冻佣金

**接口地址**: `POST /distribution/admin/commission/unfreeze/{id}`

**权限标识**: `distribution:commission:manage`

**请求参数**:

- `id`: 佣金记录ID

### 2.7 取消订单佣金

**接口地址**: `POST /distribution/admin/commission/cancel/{orderId}`

**权限标识**: `distribution:commission:manage`

**请求参数**:

- `orderId`: 订单ID
- `reason`: 取消原因（可选）

### 2.8 退款佣金

**接口地址**: `POST /distribution/admin/commission/refund`

**权限标识**: `distribution:commission:manage`

**请求参数**: `DistCommissionRefundReqVO`

```json
{
  "orderId": 1,
  "refundAmount": 100.00,
  "reason": "退款原因"
}
```

### 2.9 获取佣金排行榜

**接口地址**: `GET /distribution/admin/commission/ranking`

**权限标识**: `distribution:commission:query`

**请求参数**:

- `type`: 排行类型（month/week/day）
- `limit`: 数量限制

**响应数据**: `List<DistCommissionRankingVO>`

### 2.10 获取佣金汇总信息

**接口地址**: `GET /distribution/admin/commission/summary`

**权限标识**: `distribution:commission:query`

**请求参数**: `DistCommissionSummaryReqVO`

- `agentId`: 分销员ID
- `startTime`: 开始时间
- `endTime`: 结束时间

**响应数据**: `DistCommissionSummaryVO`

### 2.11 导出佣金记录

**接口地址**: `GET /distribution/admin/commission/export`

**权限标识**: `distribution:commission:export`

**请求参数**: `DistCommissionExportReqVO`

### 2.12 批量解冻佣金

**接口地址**: `GET /distribution/admin/commission/batch-unfreeze`

**权限标识**: `distribution:commission:manage`

**说明**: 解冻7天前的佣金

## 三、提现管理 API

### 3.1 获取提现记录列表

**接口地址**: `GET /admin/distribution/withdraw/list`

**权限标识**: `distribution:withdraw:query`

**请求参数**: `WithdrawPageReqVO`

```typescript
interface WithdrawPageReqVO {
    agentId?: number          // 分销员ID
    status?: number           // 提现状态
    dateRange?: string[]      // 时间范围
    pageNo: number            // 页码
    pageSize: number          // 每页大小
}
```

**响应数据**: `PageResult<WithdrawRecordVO>`

```typescript
interface WithdrawRecordVO {
    id: number
    withdrawNo: string        // 提现单号
    agentId: number           // 分销员ID
    agentName: string         // 分销员姓名
    amount: number            // 提现金额
    fee?: number              // 手续费
    actualAmount: number      // 实际到账金额
    accountType: number       // 账户类型
    accountName: string       // 账户名称
    accountNo: string         // 账户号码
    bankName?: string         // 银行名称
    applyTime: string         // 申请时间
    auditTime?: string        // 审核时间
    auditUser?: string        // 审核人
    auditRemark?: string      // 审核备注
    paymentTime?: string      // 打款时间
    paymentNo?: string        // 打款流水号
    status: number            // 状态
    failReason?: string       // 失败原因
    createTime?: string
    updateTime?: string
}
```

### 3.2 获取提现记录详情

**接口地址**: `GET /admin/distribution/withdraw/get`

**权限标识**: `distribution:withdraw:query`

**请求参数**:

- `id`: 提现记录ID

**响应数据**: `WithdrawRecordVO`

### 3.3 审核提现申请

**接口地址**: `POST /admin/distribution/withdraw/{withdrawId}/audit`

**权限标识**: `distribution:withdraw:audit`

**请求参数**:

```json
{
  "action": "pass",         // pass-通过 reject-拒绝
  "remark": "审核备注"
}
```

### 3.4 批量审核提现申请

**接口地址**: `POST /admin/distribution/withdraw/batch-audit`

**权限标识**: `distribution:withdraw:audit`

**请求参数**:

```json
{
  "withdrawIds": [1, 2, 3],
  "action": "pass",         // pass-通过 reject-拒绝
  "remark": "批量审核备注"
}
```

### 3.5 重新打款

**接口地址**: `POST /admin/distribution/withdraw/{withdrawId}/retry-payment`

**权限标识**: `distribution:withdraw:manage`

**请求参数**:

- `withdrawId`: 提现记录ID

### 3.6 导出提现记录

**接口地址**: `GET /admin/distribution/withdraw/export`

**权限标识**: `distribution:withdraw:export`

**请求参数**: `WithdrawPageReqVO`

**响应**: Excel文件下载

### 3.4 批量审核提现申请

**接口地址**: `POST /distribution/admin/withdraw/batch-audit`

**权限标识**: `distribution:withdraw:audit`

**请求参数**: `DistWithdrawBatchAuditReqVO`

```json
{
  "withdrawIds": [1, 2, 3],
  "auditStatus": 1,
  "auditRemark": "批量审核备注"
}
```

### 3.5 标记为已转账

**接口地址**: `POST /distribution/admin/withdraw/mark-transferred/{id}`

**权限标识**: `distribution:withdraw:transfer`

**请求参数**: `DistWithdrawTransferReqVO`

```json
{
  "transferNo": "转账流水号",
  "transferTime": "2024-01-01 12:00:00"
}
```

### 3.6 取消提现申请

**接口地址**: `POST /distribution/admin/withdraw/cancel/{id}`

**权限标识**: `distribution:withdraw:cancel`

**请求参数**:

- `id`: 提现记录ID
- `reason`: 取消原因（可选）

### 3.7 获取提现统计信息

**接口地址**: `GET /distribution/admin/withdraw/statistics`

**权限标识**: `distribution:withdraw:query`

**请求参数**: `DistWithdrawStatsReqVO`

- `agentId`: 分销员ID（可选）
- `startTime`: 开始时间
- `endTime`: 结束时间

**响应数据**: `DistWithdrawStatsRespVO`

```typescript
interface DistWithdrawStatsRespVO {
    totalAmount: number         // 总提现金额
    totalCount: number          // 总提现次数
    successAmount: number       // 成功金额
    successCount: number        // 成功次数
    pendingAmount: number       // 待处理金额
    pendingCount: number        // 待处理次数
}
```

### 3.8 导出提现记录

**接口地址**: `GET /distribution/admin/withdraw/export`

**权限标识**: `distribution:withdraw:export`

**请求参数**: 与分页查询参数相同

### 3.9 检查超时提现申请

**接口地址**: `POST /distribution/admin/withdraw/check-timeout`

**权限标识**: `distribution:withdraw:manage`

**说明**: 检查并处理超时的提现申请

## 四、商品分销配置 API

### 4.1 创建分销商品配置

**接口地址**: `POST /distribution/goods-config/create`

**权限标识**: `distribution:goods-config:create`

**请求参数**: `DistGoodsConfigSaveReqVO`

```json
{
  "goodsId": 1,
  "enableDist": true,
  "firstCommissionRate": 10,    // 一级佣金比例
  "secondCommissionRate": 5,    // 二级佣金比例
  "thirdCommissionRate": 2,     // 三级佣金比例
  "status": 1
}
```

### 4.2 更新分销商品配置

**接口地址**: `PUT /distribution/goods-config/update`

**权限标识**: `distribution:goods-config:update`

**请求参数**: 同创建，需要包含id字段

### 4.3 删除分销商品配置

**接口地址**: `DELETE /distribution/goods-config/delete`

**权限标识**: `distribution:goods-config:delete`

**请求参数**:

- `id`: 配置ID

### 4.4 获取分销商品配置详情

**接口地址**: `GET /distribution/goods-config/get`

**权限标识**: `distribution:goods-config:query`

**请求参数**:

- `id`: 配置ID

**响应数据**: `DistGoodsConfigRespVO`

### 4.5 根据商品ID获取配置

**接口地址**: `GET /distribution/goods-config/get-by-goods-id`

**权限标识**: `distribution:goods-config:query`

**请求参数**:

- `goodsId`: 商品ID

### 4.6 获取分销商品配置分页

**接口地址**: `GET /distribution/goods-config/page`

**权限标识**: `distribution:goods-config:query`

**请求参数**: `DistGoodsConfigPageReqVO`

- `goodsIds`: 商品ID列表
- `enableDist`: 是否启用分销
- `status`: 状态
- `pageNo`: 页码
- `pageSize`: 每页大小

### 4.7 导出分销商品配置

**接口地址**: `GET /distribution/goods-config/export-excel`

**权限标识**: `distribution:goods-config:export`

### 4.8 批量更新商品分销状态

**接口地址**: `POST /distribution/goods-config/batch-update-dist-status`

**权限标识**: `distribution:goods-config:update`

**请求参数**: `DistGoodsConfigBatchUpdateStatusReqVO`

```json
{
  "goodsIds": [1, 2, 3],
  "enableDist": true
}
```

### 4.9 检查商品是否开启分销

**接口地址**: `GET /distribution/goods-config/check-dist-enabled`

**权限标识**: `distribution:goods-config:query`

**请求参数**:

- `goodsId`: 商品ID

**响应数据**: `boolean`

### 4.10 获取可分销商品分页

**接口地址**: `GET /distribution/goods-config/distributable-page`

**权限标识**: `distribution:goods-config:query`

**请求参数**: `DistGoodsConfigDistributablePageReqVO`

- `agentLevelId`: 分销员等级ID
- `pageNo`: 页码
- `pageSize`: 每页大小

## 五、状态枚举定义

### 分销员状态

```typescript
enum DistAgentStatus {
    DISABLED = 0,    // 禁用
    ENABLED = 1      // 启用
}
```

### 审核状态

```typescript
enum AuditStatus {
    PENDING = 0,     // 待审核
    APPROVED = 1,    // 已通过
    REJECTED = 2     // 已拒绝
}
```

### 佣金状态

```typescript
enum CommissionStatus {
    PENDING = 0,     // 待结算
    SETTLED = 1,     // 已结算
    WITHDRAWN = 2,   // 已提现
    FROZEN = 3       // 已冻结
}
```

### 提现状态

```typescript
enum WithdrawStatus {
    PENDING = 0,     // 待审核
    APPROVED = 1,    // 已审核
    PROCESSING = 2,  // 处理中
    SUCCESS = 3,     // 成功
    FAILED = 4       // 失败
}
```

## 六、权限标识汇总

### 分销员管理

- `distribution:agent:query` - 查询权限
- `distribution:agent:create` - 创建权限
- `distribution:agent:update` - 更新权限
- `distribution:agent:delete` - 删除权限
- `distribution:agent:audit` - 审核权限
- `distribution:agent:export` - 导出权限

### 佣金管理

- `distribution:commission:query` - 查询权限
- `distribution:commission:settle` - 结算权限
- `distribution:commission:manage` - 管理权限
- `distribution:commission:export` - 导出权限

### 提现管理

- `distribution:withdraw:query` - 查询权限
- `distribution:withdraw:audit` - 审核权限
- `distribution:withdraw:transfer` - 转账权限
- `distribution:withdraw:cancel` - 取消权限
- `distribution:withdraw:manage` - 管理权限
- `distribution:withdraw:export` - 导出权限

### 商品配置

- `distribution:goods-config:query` - 查询权限
- `distribution:goods-config:create` - 创建权限
- `distribution:goods-config:update` - 更新权限
- `distribution:goods-config:delete` - 删除权限
- `distribution:goods-config:export` - 导出权限

## 七、注意事项

1. **路径规范**：
    - 佣金和提现的路径包含 `/admin/`：`/distribution/admin/commission/`、`/distribution/admin/withdraw/`
    - 分销员和商品配置的路径不包含 `/admin/`：`/distribution/agent/`、`/distribution/goods-config/`

2. **响应格式**：
    - 所有接口返回统一的 `CommonResult` 格式
    - 分页数据返回 `PageResult` 格式

3. **权限控制**：
    - 所有接口都需要相应的权限标识
    - 使用 `@PreAuthorize` 注解进行权限控制

4. **参数验证**：
    - 请求参数使用 `@Valid` 注解进行验证
    - VO类中定义了详细的验证规则

5. **更新时间**：2025-07-21
