# 分销员关系链展示需求文档

## 一、需求概述

在分销员管理页面中，需要展示分销员的上下级关系链，帮助管理员直观了解分销网络结构。展示内容包括：
- 当前分销员信息
- 上级分销员（最多2层）
- 下级分销员树（限制2层）

## 二、接口说明

### API 接口
```
GET /admin/distribution/agent/relationship?agentId={agentId}
```

### 响应数据结构
```typescript
interface AgentRelationshipResponse {
  // 分销员基本信息
  agentId: number
  agentName: string
  memberNickname?: string
  memberAvatar?: string
  levelName?: string
  directCount: number
  teamCount: number
  
  // 上级分销员列表（从近到远）
  parentChain: ParentInfo[]
  
  // 下级分销员树（限制2层）
  childrenTree: ChildInfo[]
}

interface ParentInfo {
  agentId: number
  agentName: string
  levelName?: string
  level: number  // 1表示直接上级，2表示上上级
}

interface ChildInfo {
  agentId: number
  agentName: string
  memberNickname?: string
  memberAvatar?: string
  levelName?: string
  status: number  // 0-待审核，1-正常，2-冻结，3-注销
  directCount: number
  teamCount: number
  hasMoreChildren: boolean  // 是否有更深层子节点
  children?: ChildInfo[]    // 子节点列表（只展示1层）
}
```

## 三、UI 设计要求

### 3.1 整体布局

建议使用 **组织架构图** 的形式展示关系链，分为三个区域：

1. **上级区域**（顶部）：展示上级分销员
2. **当前分销员**（中间）：突出显示
3. **下级区域**（底部）：树形展示下级

### 3.2 组件选择

推荐使用以下组件实现：

#### 方案一：使用 Vue3 + Element Plus + Vue-Flow（推荐）
```vue
<template>
  <div class="agent-relationship-container">
    <!-- 使用 vue-flow 展示关系图 -->
    <VueFlow 
      :nodes="nodes" 
      :edges="edges"
      :default-viewport="{ x: 0, y: 0, zoom: 1 }"
      fit-view-on-init
    >
      <template #node-custom="{ data }">
        <AgentNode :agent="data" @click="handleNodeClick" />
      </template>
    </VueFlow>
  </div>
</template>
```

#### 方案二：自定义树形组件
```vue
<template>
  <div class="relationship-tree">
    <!-- 上级展示 -->
    <div class="parent-section">
      <el-row :gutter="20" justify="center">
        <el-col v-for="parent in parentChain" :key="parent.agentId" :span="8">
          <AgentCard :agent="parent" :level="parent.level" />
        </el-col>
      </el-row>
    </div>
    
    <!-- 当前分销员 -->
    <div class="current-agent">
      <AgentCard :agent="currentAgent" :is-current="true" />
    </div>
    
    <!-- 下级树 -->
    <div class="children-section">
      <el-tree 
        :data="childrenTree"
        :props="treeProps"
        default-expand-all
        :expand-on-click-node="false"
      >
        <template #default="{ node, data }">
          <AgentTreeNode :agent="data" @load-more="loadMoreChildren" />
        </template>
      </el-tree>
    </div>
  </div>
</template>
```

### 3.3 分销员卡片组件设计

```vue
<template>
  <el-card 
    class="agent-card" 
    :class="{ 'is-current': isCurrent }"
    shadow="hover"
  >
    <div class="agent-info">
      <el-avatar :src="agent.memberAvatar" :size="60">
        {{ agent.agentName?.charAt(0) }}
      </el-avatar>
      <div class="agent-details">
        <h4>{{ agent.agentName }}</h4>
        <p class="nickname">{{ agent.memberNickname }}</p>
        <el-tag size="small" type="primary">{{ agent.levelName }}</el-tag>
      </div>
    </div>
    
    <div class="agent-stats">
      <div class="stat-item">
        <span class="label">直推</span>
        <span class="value">{{ agent.directCount }}</span>
      </div>
      <div class="stat-item">
        <span class="label">团队</span>
        <span class="value">{{ agent.teamCount }}</span>
      </div>
    </div>
    
    <!-- 状态标识 -->
    <el-tag 
      v-if="agent.status !== undefined" 
      :type="getStatusType(agent.status)"
      size="small"
      class="status-tag"
    >
      {{ getStatusText(agent.status) }}
    </el-tag>
  </el-card>
</template>
```

### 3.4 交互功能

1. **点击分销员卡片**
   - 跳转到该分销员详情页
   - 或在当前页面刷新，以该分销员为中心重新展示关系链

2. **加载更多下级**
   - 当 `hasMoreChildren` 为 true 时，显示"加载更多"按钮
   - 点击后调用接口获取该分销员的关系链

3. **展开/收起**
   - 下级树支持展开/收起操作
   - 默认展开第一层

4. **搜索功能**（可选）
   - 提供搜索框，可快速定位到某个分销员

## 四、样式建议

```scss
.agent-relationship-container {
  min-height: 600px;
  padding: 20px;
  
  .agent-card {
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      transform: translateY(-2px);
    }
    
    &.is-current {
      border: 2px solid var(--el-color-primary);
      box-shadow: 0 0 10px rgba(64, 158, 255, 0.3);
    }
  }
  
  .parent-section, .children-section {
    margin: 30px 0;
  }
  
  .current-agent {
    display: flex;
    justify-content: center;
    margin: 40px 0;
  }
  
  // 连接线样式
  .connection-line {
    stroke: #dcdfe6;
    stroke-width: 2;
    fill: none;
  }
}
```

## 五、性能优化建议

1. **懒加载**：下级数据按需加载，避免一次性加载过多数据
2. **虚拟滚动**：当下级数量较多时，使用虚拟滚动优化渲染性能
3. **缓存**：对已加载的关系数据进行缓存，避免重复请求

## 六、参考实现

可参考以下开源组件：
- [Vue Flow](https://vueflow.dev/) - 流程图/关系图组件
- [Vue3 Organization Chart](https://github.com/ScoutYin/vue-org-tree) - 组织架构图组件
- [Element Plus Tree](https://element-plus.org/zh-CN/component/tree.html) - 树形控件

## 七、注意事项

1. 需要处理数据量大的情况，避免页面卡顿
2. 移动端适配：考虑在移动设备上的展示效果
3. 空数据处理：当没有上级或下级时，显示友好提示
4. 错误处理：接口请求失败时的提示和重试机制