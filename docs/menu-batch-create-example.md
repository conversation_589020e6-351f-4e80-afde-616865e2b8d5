# 菜单批量创建按钮功能使用示例

## 功能说明
在创建菜单时，可以同时批量创建该菜单下的按钮，避免多次操作。

## API 接口
**POST** `/system/menu/create`

## 请求示例

### 1. 创建菜单同时创建按钮
```json
{
  "name": "用户管理",
  "type": 2,  // 菜单类型：2-菜单
  "parentId": 1,
  "path": "/user",
  "component": "system/user/index",
  "componentName": "SystemUser",
  "icon": "user",
  "sort": 1,
  "status": 1,
  "visible": true,
  "keepAlive": true,
  "alwaysShow": false,
  "buttons": [
    {
      "name": "新增用户",
      "permission": "system:user:create",
      "sort": 1,
      "status": 1
    },
    {
      "name": "编辑用户",
      "permission": "system:user:update",
      "sort": 2,
      "status": 1
    },
    {
      "name": "删除用户",
      "permission": "system:user:delete",
      "sort": 3,
      "status": 1
    },
    {
      "name": "导出用户",
      "permission": "system:user:export",
      "sort": 4,
      "status": 1
    }
  ]
}
```

### 2. 仅创建菜单（不创建按钮）
```json
{
  "name": "角色管理",
  "type": 2,  // 菜单类型：2-菜单
  "parentId": 1,
  "path": "/role",
  "component": "system/role/index",
  "componentName": "SystemRole",
  "icon": "role",
  "sort": 2,
  "status": 1,
  "visible": true,
  "keepAlive": true,
  "alwaysShow": false
  // 不传 buttons 字段或传空数组
}
```

## 注意事项

1. **按钮批量创建仅在菜单类型为"菜单"(type=2)时生效**
   - 目录(type=1)和按钮(type=3)类型不支持批量创建按钮

2. **按钮属性说明**
   - `name`: 按钮名称（必填）
   - `permission`: 权限标识（必填）
   - `sort`: 排序（必填）
   - `status`: 状态（必填，1-启用，0-禁用）

3. **按钮创建后的特点**
   - 按钮的 `parentId` 自动设置为创建的菜单ID
   - 按钮的 `type` 自动设置为3（按钮类型）
   - 按钮的 `visible` 默认为 true
   - 按钮不需要设置 `path`、`component`、`icon` 等属性

## 响应示例

成功响应：
```json
{
  "code": 0,
  "data": 1001,  // 返回创建的菜单ID
  "msg": "success"
}
```

## 实现优势

1. **提高效率**：一次请求完成菜单和按钮的创建，减少网络请求
2. **事务一致性**：使用事务确保菜单和按钮要么全部创建成功，要么全部失败
3. **简化操作**：前端只需一次提交，避免多次操作的复杂性
4. **灵活可选**：按钮列表为可选字段，不影响原有的菜单创建功能