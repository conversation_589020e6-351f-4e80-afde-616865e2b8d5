# 分销代理系统设计文档

## 1. 系统概述

### 1.1 设计目标
基于需求分析，设计一个简洁、稳定、可维护的分销代理系统，解决现有模块的编译错误和架构问题。

### 1.2 设计原则
- **简单优先**：采用传统三层架构，避免过度设计
- **渐进实现**：先实现核心功能，再逐步完善
- **代码规范**：遵循现有项目规范，确保一致性
- **性能优化**：合理使用缓存和索引，提升系统性能

### 1.3 技术选型
- **框架**：Spring Boot 2.x + MyBatis Plus
- **数据库**：MySQL 8.0
- **缓存**：Redis
- **对象转换**：BeanUtils（框架自带，统一使用，不创建单独的 convert 包）
- **架构模式**：传统三层架构

## 2. 系统架构

### 2.1 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                        前端应用层                            │
├─────────────────────┬───────────────────────────────────────┤
│     管理后台        │              移动端H5                 │
│  - 分销员管理       │  - 分销员申请                         │
│  - 佣金管理         │  - 商品推广                           │
│  - 统计分析         │  - 佣金查询                           │
│  - 系统配置         │  - 团队管理                           │
└─────────────────────┴───────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      应用服务层                              │
├─────────────────────┬───────────────────────────────────────┤
│   Controller 层     │              Service 层               │
│  - 参数验证         │  - 业务逻辑处理                       │
│  - 权限控制         │  - 事务管理                           │
│  - 响应封装         │  - 缓存管理                           │
└─────────────────────┴───────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      数据访问层                              │
├─────────────────────┬───────────────────────────────────────┤
│    Mapper 层        │              数据存储                 │
│  - SQL 映射         │  - MySQL 主库                         │
│  - 结果映射         │  - Redis 缓存                         │
│  - 分页查询         │  - 文件存储                           │
└─────────────────────┴───────────────────────────────────────┘
```

### 2.2 包结构设计

```
com.yitong.octopus.module.distribution
├── controller/                    # 控制器层
│   ├── admin/                    # 管理端控制器
│   │   ├── agent/               # 分销员管理
│   │   ├── commission/          # 佣金管理
│   │   ├── goods/               # 商品管理
│   │   ├── statistics/          # 统计分析
│   │   └── system/              # 系统配置
│   └── app/                     # 移动端控制器
│       ├── agent/               # 分销员应用
│       ├── goods/               # 商品分销
│       └── user/                # 用户中心
├── service/                      # 服务层
│   ├── agent/                   # 分销员服务
│   ├── commission/              # 佣金服务
│   ├── goods/                   # 商品服务
│   ├── statistics/              # 统计服务
│   └── system/                  # 系统服务
├── dal/                         # 数据访问层
│   ├── dataobject/             # 数据对象
│   └── mapper/                 # 映射器
└── dto/                        # 数据传输对象
    ├── req/                    # 请求对象
    └── resp/                   # 响应对象
    # 注意：不创建 convert 包，统一使用 BeanUtils 进行对象转换
```

## 3. 核心模块设计

### 3.1 分销员管理模块

#### 3.1.1 数据模型

**分销员信息表 (yt_dist_agent)**
```sql
CREATE TABLE `yt_dist_agent` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分销员ID',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `agent_code` varchar(32) NOT NULL COMMENT '分销员编码',
  `agent_name` varchar(64) NOT NULL COMMENT '分销员名称',
  `level_id` bigint(20) NOT NULL COMMENT '等级ID',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '上级分销员ID',
  `path` varchar(512) DEFAULT NULL COMMENT '分销路径',
  `depth` int(11) DEFAULT '1' COMMENT '层级深度',
  `apply_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '申请状态：0-待审核，1-通过，2-拒绝',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `total_commission` decimal(15,2) DEFAULT '0.00' COMMENT '累计佣金',
  `available_commission` decimal(15,2) DEFAULT '0.00' COMMENT '可提现佣金',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_member_id` (`member_id`),
  UNIQUE KEY `uk_agent_code` (`agent_code`)
) ENGINE=InnoDB COMMENT='分销员信息表';
```

**分销员等级表 (yt_dist_level)**
```sql
CREATE TABLE `yt_dist_level` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '等级ID',
  `level_name` varchar(64) NOT NULL COMMENT '等级名称',
  `level_grade` int(11) NOT NULL COMMENT '等级级别',
  `commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '佣金比例',
  `upgrade_condition` json DEFAULT NULL COMMENT '升级条件',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='分销员等级表';
```

#### 3.1.2 服务设计

**AgentService 接口**
```java
public interface AgentService {
    // 申请成为分销员
    Long applyAgent(AgentApplyReqVO reqVO);
    
    // 审核分销员申请
    void auditAgent(Long agentId, Boolean approved, String remark);
    
    // 绑定上下级关系
    void bindRelation(Long agentId, String inviteCode);
    
    // 获取分销员信息
    AgentRespVO getAgent(Long agentId);
    
    // 分页查询分销员
    PageResult<AgentRespVO> getAgentPage(AgentPageReqVO reqVO);
}
```

### 3.2 商品分销模块

#### 3.2.1 数据模型

**商品分销配置表 (yt_dist_goods_config)**
```sql
CREATE TABLE `yt_dist_goods_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `spu_id` bigint(20) NOT NULL COMMENT 'SPU ID',
  `enable_distribution` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用分销',
  `commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '佣金比例',
  `commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '固定佣金',
  `commission_type` tinyint(4) DEFAULT '1' COMMENT '佣金类型：1-比例，2-固定',
  `priority` int(11) DEFAULT '0' COMMENT '优先级',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_spu_id` (`spu_id`)
) ENGINE=InnoDB COMMENT='商品分销配置表';
```

#### 3.2.2 服务设计

**GoodsService 接口**
```java
public interface GoodsService {
    // 配置商品分销
    void configGoods(GoodsConfigReqVO reqVO);
    
    // 获取分销商品列表
    PageResult<GoodsRespVO> getDistributionGoods(GoodsPageReqVO reqVO);
    
    // 计算商品佣金
    BigDecimal calculateCommission(Long spuId, BigDecimal orderAmount, Long agentId);
}
```

### 3.3 佣金管理模块

#### 3.3.1 数据模型

**佣金记录表 (yt_dist_commission)**
```sql
CREATE TABLE `yt_dist_commission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `spu_id` bigint(20) NOT NULL COMMENT 'SPU ID',
  `commission_amount` decimal(10,2) NOT NULL COMMENT '佣金金额',
  `commission_type` tinyint(4) NOT NULL COMMENT '佣金类型：1-销售佣金，2-推荐佣金',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-冻结，1-可提现，2-已提现',
  `settle_time` datetime DEFAULT NULL COMMENT '结算时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB COMMENT='佣金记录表';
```

**提现记录表 (yt_dist_withdraw)**
```sql
CREATE TABLE `yt_dist_withdraw` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `account_type` tinyint(4) NOT NULL COMMENT '账户类型：1-微信，2-支付宝',
  `account_no` varchar(128) NOT NULL COMMENT '账户号',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待审核，1-已通过，2-已拒绝',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_agent_id` (`agent_id`)
) ENGINE=InnoDB COMMENT='提现记录表';
```

#### 3.3.2 服务设计

**CommissionService 接口**
```java
public interface CommissionService {
    // 计算佣金
    void calculateCommission(Long orderId);
    
    // 结算佣金
    void settleCommission(Long agentId);
    
    // 申请提现
    Long applyWithdraw(WithdrawApplyReqVO reqVO);
    
    // 审核提现
    void auditWithdraw(Long withdrawId, Boolean approved, String remark);
    
    // 获取佣金明细
    PageResult<CommissionRespVO> getCommissionPage(CommissionPageReqVO reqVO);
}
```

### 3.4 统计分析模块

#### 3.4.1 数据模型

**统计数据表 (yt_dist_statistics)**
```sql
CREATE TABLE `yt_dist_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `order_count` int(11) DEFAULT '0' COMMENT '订单数量',
  `order_amount` decimal(15,2) DEFAULT '0.00' COMMENT '订单金额',
  `commission_amount` decimal(15,2) DEFAULT '0.00' COMMENT '佣金金额',
  `customer_count` int(11) DEFAULT '0' COMMENT '客户数量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agent_date` (`agent_id`, `stat_date`)
) ENGINE=InnoDB COMMENT='统计数据表';
```

#### 3.4.2 服务设计

**StatisticsService 接口**
```java
public interface StatisticsService {
    // 获取个人统计
    AgentStatisticsRespVO getAgentStatistics(Long agentId, StatisticsReqVO reqVO);
    
    // 获取团队统计
    TeamStatisticsRespVO getTeamStatistics(Long agentId, StatisticsReqVO reqVO);
    
    // 获取排行榜
    List<RankingRespVO> getRanking(RankingReqVO reqVO);
    
    // 刷新统计数据
    void refreshStatistics(Date statDate);
}
```

## 4. 接口设计

### 4.1 RESTful API 设计原则

- 使用标准的 HTTP 方法：GET、POST、PUT、DELETE
- URL 设计遵循 RESTful 规范
- 统一的响应格式：CommonResult<T>
- 统一的错误处理和异常码

### 4.2 核心接口列表

#### 4.2.1 分销员管理接口

```
POST   /admin/distribution/agent/apply          # 申请分销员
PUT    /admin/distribution/agent/{id}/audit     # 审核分销员
GET    /admin/distribution/agent/{id}           # 获取分销员详情
GET    /admin/distribution/agent/page           # 分页查询分销员
POST   /admin/distribution/agent/bind-relation  # 绑定关系
```

#### 4.2.2 商品分销接口

```
POST   /admin/distribution/goods/config         # 配置商品分销
GET    /admin/distribution/goods/page           # 分页查询分销商品
GET    /app/distribution/goods/list             # 获取分销商品列表
GET    /app/distribution/goods/{id}/commission  # 计算商品佣金
```

#### 4.2.3 佣金管理接口

```
GET    /admin/distribution/commission/page      # 分页查询佣金记录
POST   /admin/distribution/commission/settle    # 结算佣金
POST   /app/distribution/withdraw/apply         # 申请提现
PUT    /admin/distribution/withdraw/{id}/audit  # 审核提现
GET    /app/distribution/commission/my          # 我的佣金
```

#### 4.2.4 统计分析接口

```
GET    /app/distribution/statistics/personal    # 个人统计
GET    /app/distribution/statistics/team        # 团队统计
GET    /admin/distribution/statistics/ranking   # 排行榜
GET    /admin/distribution/statistics/overview  # 概览统计
```

## 5. 数据流设计

### 5.1 分销员申请流程

```
用户提交申请 -> 创建申请记录 -> 管理员审核 -> 审核通过 -> 生成分销员编码 -> 发送通知
```

### 5.2 佣金计算流程

```
订单支付成功 -> 查找分销员 -> 获取商品配置 -> 计算佣金 -> 创建佣金记录 -> 更新分销员余额
```

### 5.3 提现处理流程

```
分销员申请提现 -> 验证余额 -> 创建提现记录 -> 管理员审核 -> 处理提现 -> 更新状态
```

## 6. 缓存设计

### 6.1 缓存策略

- **分销员信息**：缓存 30 分钟，更新时清除
- **商品配置**：缓存 1 小时，配置变更时清除
- **统计数据**：缓存 10 分钟，定时刷新
- **等级配置**：缓存 24 小时，很少变更

### 6.2 缓存键设计

```
dist:agent:{agentId}                    # 分销员信息
dist:goods:config:{spuId}               # 商品配置
dist:statistics:{agentId}:{date}        # 统计数据
dist:level:{levelId}                    # 等级信息
```

## 7. 安全设计

### 7.1 权限控制

- 管理端接口需要管理员权限
- 移动端接口需要用户登录
- 敏感操作需要二次验证
- 接口访问频率限制

### 7.2 数据安全

- 敏感数据加密存储
- 关键操作记录审计日志
- 防止 SQL 注入和 XSS 攻击
- 数据传输使用 HTTPS

### 7.3 业务安全

- 防止恶意刷单
- 佣金计算防篡改
- 提现金额验证
- 异常行为监控

## 8. 性能优化

### 8.1 数据库优化

- 合理设计索引
- 避免 N+1 查询问题
- 使用分页查询
- 读写分离

### 8.2 应用优化

- 合理使用缓存
- 异步处理耗时操作
- 连接池配置优化
- JVM 参数调优

### 8.3 监控告警

- 接口响应时间监控
- 数据库性能监控
- 缓存命中率监控
- 业务指标监控

## 9. 部署架构

### 9.1 环境规划

- **开发环境**：单机部署，便于调试
- **测试环境**：模拟生产环境配置
- **生产环境**：高可用部署，负载均衡

### 9.2 容器化部署

- 使用 Docker 容器化
- Kubernetes 编排管理
- 配置文件外部化
- 日志统一收集

## 10. 扩展性设计

### 10.1 水平扩展

- 无状态服务设计
- 数据库分库分表
- 缓存集群部署
- 消息队列解耦

### 10.2 功能扩展

- 插件化架构设计
- 配置化业务规则
- 多租户支持
- 国际化支持

## 11. 前端开发设计

### 11.1 管理系统前端

**技术栈：**
- Vue.js 2.x + Element UI
- 与现有管理系统保持技术栈一致
- 使用 Axios 进行 API 调用
- 集成 ECharts 进行数据可视化

**页面结构：**
```
分销管理
├── 分销员管理
│   ├── 分销员列表
│   ├── 分销员详情
│   ├── 申请审核
│   └── 关系管理
├── 商品分销
│   ├── 分销配置
│   ├── 商品列表
│   └── 佣金设置
├── 佣金管理
│   ├── 佣金记录
│   ├── 提现管理
│   └── 结算管理
└── 统计分析
    ├── 概览统计
    ├── 排行榜
    └── 业绩分析
```

### 11.2 移动端H5前端

**技术栈：**
- Vue.js 2.x + Vant UI
- 适配移动端响应式设计
- 集成微信分享SDK
- 支持PWA特性

**页面结构：**
```
分销中心
├── 个人中心
│   ├── 申请分销员
│   ├── 我的信息
│   └── 等级展示
├── 商品推广
│   ├── 商品列表
│   ├── 商品详情
│   └── 分享页面
├── 我的收益
│   ├── 佣金明细
│   ├── 提现申请
│   └── 收益统计
└── 团队管理
    ├── 我的团队
    ├── 邀请海报
    └── 业绩统计
```

### 11.3 前后端交互规范

**API 调用规范：**
- 统一使用 RESTful API
- 请求和响应格式统一
- 错误处理标准化
- 接口版本管理

**数据转换：**
- 前端接收到的数据直接使用，不需要额外转换
- 后端使用 BeanUtils 统一处理 DO 和 VO 的转换
- 保持前后端数据结构的一致性

## 12. 风险控制

### 12.1 技术风险

- 数据一致性保证
- 高并发处理能力
- 系统稳定性保障
- 灾备恢复方案
- 前后端开发进度同步

### 12.2 业务风险

- 佣金计算准确性
- 财务数据安全性
- 合规性要求
- 作弊行为防范