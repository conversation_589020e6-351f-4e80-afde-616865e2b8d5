# 分销代理系统需求规范

## 1. 项目概述

### 1.1 项目背景
基于现有 yitong-module-distribution 模块存在的编译错误、架构混乱等问题，需要重新设计和实现一个稳定、可维护的分销代理系统。

### 1.2 项目目标
- 修复现有编译错误，确保模块可正常运行
- 建立清晰的三层架构，符合现有项目规范
- 实现核心分销业务功能
- 提供良好的扩展性和维护性

### 1.3 设计原则
- **简单优先**：避免过度工程化，优先实现核心功能
- **渐进式开发**：先实现基础功能，再逐步扩展
- **架构一致性**：遵循现有项目的三层架构模式
- **代码规范**：单个类不超过 500 行，职责单一

## 2. 核心需求

### 2.1 分销员管理

#### 需求 1：分销员申请与审核
**用户故事：** 作为普通用户，我希望能够申请成为分销员，以便开始推广商品获得佣金。

**验收标准：**
1. WHEN 用户提交分销员申请 THEN 系统应创建申请记录并设置为待审核状态
2. WHEN 管理员审核申请 THEN 系统应更新申请状态为通过或拒绝
3. WHEN 申请通过 THEN 系统应生成唯一的分销员编码
4. IF 设置了自动审核 THEN 系统应自动通过符合条件的申请

#### 需求 2：分销关系管理
**用户故事：** 作为分销员，我希望能够邀请其他用户成为我的下级分销员，以便获得团队分润。

**验收标准：**
1. WHEN 分销员生成邀请码 THEN 系统应创建包含分销员信息的邀请链接
2. WHEN 用户通过邀请码申请 THEN 系统应自动建立上下级关系
3. WHEN 建立关系 THEN 系统应更新分销路径和层级深度
4. IF 层级超过3级 THEN 系统应拒绝建立关系

#### 需求 3：分销员等级管理
**用户故事：** 作为分销员，我希望通过业绩提升等级，以便获得更高的佣金比例。

**验收标准：**
1. WHEN 分销员业绩达到升级条件 THEN 系统应自动提升等级
2. WHEN 等级变更 THEN 系统应发送通知给分销员
3. WHEN 等级提升 THEN 系统应更新佣金配置
4. IF 开启自动降级 AND 业绩不达标 THEN 系统应降低等级

### 2.2 商品分销管理

#### 需求 4：商品分销配置
**用户故事：** 作为商家，我希望能够设置哪些商品参与分销，以便控制分销范围和成本。

**验收标准：**
1. WHEN 商家启用商品分销 THEN 系统应在分销商品列表中显示该商品
2. WHEN 设置分销优先级 THEN 系统应按优先级排序显示商品
3. WHEN 配置佣金提示 THEN 分销员应能看到预估佣金信息
4. IF 商品禁用分销 THEN 系统应停止该商品的佣金计算

#### 需求 5：分销商品展示
**用户故事：** 作为分销员，我希望能够查看可分销的商品列表，以便选择合适的商品进行推广。

**验收标准：**
1. WHEN 分销员查看商品列表 THEN 系统应显示可分销的商品
2. WHEN 查看商品详情 THEN 系统应显示预估佣金信息
3. WHEN 分享商品 THEN 系统应生成包含分销员信息的分享链接
4. IF 商品有限制条件 THEN 只有符合条件的分销员能看到

### 2.3 佣金管理

#### 需求 6：佣金计算
**用户故事：** 作为分销员，我希望系统能够准确计算我的佣金，以便了解我的收益情况。

**验收标准：**
1. WHEN 订单支付成功 THEN 系统应计算相关分销员的佣金
2. WHEN 计算佣金 THEN 系统应根据配置选择最优的奖励方案
3. WHEN 存在多级分润 THEN 系统应按层级分别计算上级佣金
4. IF 订单金额低于门槛 THEN 系统应不计算佣金

#### 需求 7：佣金结算
**用户故事：** 作为分销员，我希望能够提现我的佣金，以便获得实际收益。

**验收标准：**
1. WHEN 佣金解冻 THEN 分销员应能看到可提现金额
2. WHEN 申请提现 THEN 系统应创建提现记录并扣减可提现金额
3. WHEN 管理员审核提现 THEN 系统应更新提现状态
4. IF 提现失败 THEN 系统应退回可提现金额

### 2.4 邀请海报

#### 需求 8：海报生成
**用户故事：** 作为分销员，我希望能够生成专属的邀请海报，以便在社交媒体上推广。

**验收标准：**
1. WHEN 分销员请求生成海报 THEN 系统应合成包含专属二维码的海报
2. WHEN 生成海报 THEN 系统应记录海报信息和有效期
3. WHEN 用户扫码 THEN 系统应记录扫码统计数据
4. IF 海报过期 THEN 系统应提示重新生成

### 2.5 数据统计

#### 需求 9：个人统计
**用户故事：** 作为分销员，我希望能够查看我的业绩统计，以便了解推广效果。

**验收标准：**
1. WHEN 分销员查看统计 THEN 系统应显示销售额、佣金、客户数等数据
2. WHEN 查看趋势 THEN 系统应显示按时间维度的数据变化
3. WHEN 查看排名 THEN 系统应显示在同等级中的排名情况
4. IF 数据更新 THEN 统计信息应实时或准实时更新

#### 需求 10：团队统计
**用户故事：** 作为分销员，我希望能够查看我的团队业绩，以便管理我的下级分销员。

**验收标准：**
1. WHEN 查看团队统计 THEN 系统应显示团队总人数、总业绩等数据
2. WHEN 查看下级列表 THEN 系统应显示直属下级的基本信息和业绩
3. WHEN 查看团队层级 THEN 系统应显示完整的团队结构树
4. IF 团队有变动 THEN 统计数据应及时更新

## 3. 技术需求

### 3.1 架构要求
- 采用传统三层架构：Controller -> Service -> Mapper
- 使用 Spring Boot + MyBatis Plus 技术栈
- 遵循现有项目的包结构和命名规范
- 使用 BeanUtils 进行对象转换，避免复杂的转换器

### 3.2 性能要求
- 接口响应时间不超过 2 秒
- 支持并发用户数不少于 1000
- 数据库查询优化，避免 N+1 问题
- 合理使用缓存提升性能

### 3.3 安全要求
- 所有接口需要进行权限验证
- 敏感数据需要加密存储
- 防止恶意刷单和作弊行为
- 记录关键操作的审计日志

### 3.4 可维护性要求
- 单个类文件不超过 500 行
- 方法复杂度控制在合理范围
- 提供完整的单元测试
- 代码注释清晰，便于理解

## 4. 非功能性需求

### 4.1 可用性
- 系统可用性不低于 99.5%
- 支持灰度发布和快速回滚
- 提供完善的监控和告警机制

### 4.2 扩展性
- 支持多租户架构
- 支持水平扩展
- 配置化的业务规则，便于调整

### 4.3 兼容性
- 兼容现有的用户体系
- 兼容现有的商品体系
- 兼容现有的订单体系

## 5. 约束条件

### 5.1 技术约束
- 必须使用现有的技术栈
- 不能破坏现有的系统架构
- 需要考虑数据库性能影响

### 5.2 业务约束
- 分销层级不超过 3 级
- 佣金计算必须准确无误
- 需要符合相关法律法规

### 5.3 时间约束
- 第一阶段（修复编译错误）：1 周
- 第二阶段（核心功能实现）：3 周
- 第三阶段（完善和优化）：2 周

## 6. 验收标准

### 6.1 功能验收
- 所有核心功能正常工作
- 通过完整的业务流程测试
- 数据准确性验证通过

### 6.2 性能验收
- 接口性能测试通过
- 并发测试通过
- 数据库性能测试通过

### 6.3 质量验收
- 代码审查通过
- 单元测试覆盖率达到 80%
- 集成测试通过

## 7. 风险评估

### 7.1 技术风险
- **数据迁移风险**：现有数据可能需要迁移或转换
- **性能风险**：复杂的佣金计算可能影响性能
- **并发风险**：高并发场景下的数据一致性

### 7.2 业务风险
- **合规风险**：分销模式需要符合相关法规
- **作弊风险**：需要防范恶意刷单等行为
- **财务风险**：佣金计算错误可能造成损失

### 7.3 项目风险
- **时间风险**：功能复杂度可能超出预期
- **资源风险**：开发和测试资源是否充足
- **依赖风险**：对其他系统的依赖可能影响进度