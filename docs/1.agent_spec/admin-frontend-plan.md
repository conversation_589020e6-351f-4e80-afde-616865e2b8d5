# 分销管理后台前端开发计划

## 项目概述

### 开发信息
- **项目名称**：分销管理后台前端
- **技术栈**：Vue.js 2.x + Element UI + Axios + ECharts
- **开发周期**：5个工作日
- **开发模式**：与后端API并行开发，前后端分离

### 设计原则
- **与现有管理系统保持一致**：使用相同的技术栈和UI风格
- **响应式设计**：适配不同屏幕尺寸
- **用户体验优先**：操作简洁，反馈及时
- **数据可视化**：使用图表展示关键数据

## 页面架构设计

### 菜单结构
```
分销管理
├── 概览统计                    # 首页仪表盘
├── 分销员管理
│   ├── 分销员列表              # 分销员查询和管理
│   ├── 申请审核                # 分销员申请审核
│   ├── 等级管理                # 分销员等级配置
│   └── 关系管理                # 分销关系树查看
├── 商品分销
│   ├── 商品配置                # 商品分销开关和佣金配置
│   ├── 分销商品                # 已启用分销的商品列表
│   └── 佣金规则                # 佣金计算规则配置
├── 佣金管理
│   ├── 佣金记录                # 佣金明细查询
│   ├── 结算管理                # 佣金结算操作
│   └── 佣金统计                # 佣金数据统计
├── 提现管理
│   ├── 提现审核                # 提现申请审核
│   ├── 提现记录                # 提现历史记录
│   └── 提现统计                # 提现数据统计
└── 数据分析
    ├── 业绩排行                # 分销员业绩排行榜
    ├── 趋势分析                # 数据趋势图表
    └── 数据导出                # 数据导出功能
```

## 第一天：项目搭建和概览页面

### 上午任务（4小时）

#### Task 1.1: 项目初始化（2小时）
- [ ] **创建Vue项目**
  ```bash
  vue create distribution-admin
  cd distribution-admin
  npm install element-ui axios echarts vue-router vuex
  ```
- [ ] **配置项目结构**
  ```
  src/
  ├── api/                    # API接口
  │   ├── agent.js           # 分销员相关接口
  │   ├── commission.js      # 佣金相关接口
  │   ├── goods.js           # 商品相关接口
  │   ├── withdraw.js        # 提现相关接口
  │   └── statistics.js      # 统计相关接口
  ├── components/            # 公共组件
  │   ├── Charts/           # 图表组件
  │   ├── Tables/           # 表格组件
  │   └── Forms/            # 表单组件
  ├── views/                # 页面组件
  │   └── distribution/     # 分销管理页面
  ├── router/               # 路由配置
  ├── store/                # 状态管理
  └── utils/                # 工具函数
  ```
- [ ] **配置开发环境**
  - 配置代理，连接后端API
  - 配置Element UI主题
  - 配置路由和状态管理
- [ ] **验收标准**：项目能正常启动，Element UI样式正确

#### Task 1.2: 基础配置和公共组件（2小时）
- [ ] **API配置**
  ```javascript
  // src/api/request.js
  import axios from 'axios'
  import { Message } from 'element-ui'
  
  const request = axios.create({
    baseURL: process.env.VUE_APP_API_BASE_URL,
    timeout: 10000
  })
  
  // 请求拦截器
  request.interceptors.request.use(config => {
    // 添加token等
    return config
  })
  
  // 响应拦截器
  request.interceptors.response.use(
    response => response.data,
    error => {
      Message.error(error.message)
      return Promise.reject(error)
    }
  )
  ```
- [ ] **创建公共组件**
  - `PageHeader.vue` - 页面头部组件
  - `DataTable.vue` - 数据表格组件
  - `SearchForm.vue` - 搜索表单组件
- [ ] **验收标准**：API调用正常，公共组件可复用

### 下午任务（4小时）

#### Task 1.3: 概览统计页面开发（4小时）
- [ ] **页面文件**：`src/views/distribution/Dashboard.vue`
- [ ] **功能实现**：
  ```vue
  <template>
    <div class="dashboard">
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-cards">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ stats.totalAgents }}</div>
              <div class="stat-label">总分销员数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">¥{{ stats.totalCommission }}</div>
              <div class="stat-label">总佣金金额</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ stats.totalOrders }}</div>
              <div class="stat-label">分销订单数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ stats.pendingWithdraws }}</div>
              <div class="stat-label">待审核提现</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 图表区域 -->
      <el-row :gutter="20" class="charts-row">
        <el-col :span="12">
          <el-card title="佣金趋势">
            <commission-trend-chart :data="trendData" />
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card title="分销员等级分布">
            <agent-level-chart :data="levelData" />
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 待处理事项 -->
      <el-card title="待处理事项" class="pending-tasks">
        <el-table :data="pendingTasks">
          <el-table-column prop="type" label="类型" />
          <el-table-column prop="count" label="数量" />
          <el-table-column prop="action" label="操作">
            <template slot-scope="scope">
              <el-button size="mini" @click="handleTask(scope.row)">
                处理
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </template>
  ```
- [ ] **图表组件**：
  - `CommissionTrendChart.vue` - 佣金趋势图
  - `AgentLevelChart.vue` - 等级分布饼图
- [ ] **验收标准**：概览页面数据展示完整，图表渲染正常

## 第二天：分销员管理页面

### 上午任务（4小时）

#### Task 2.1: 分销员列表页面（4小时）
- [ ] **页面文件**：`src/views/distribution/agent/AgentList.vue`
- [ ] **功能实现**：
  ```vue
  <template>
    <div class="agent-list">
      <!-- 搜索表单 -->
      <el-card class="search-card">
        <el-form :model="searchForm" inline>
          <el-form-item label="分销员姓名">
            <el-input v-model="searchForm.agentName" placeholder="请输入姓名" />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态">
              <el-option label="全部" value="" />
              <el-option label="待审核" value="0" />
              <el-option label="已通过" value="1" />
              <el-option label="已拒绝" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="等级">
            <el-select v-model="searchForm.levelId" placeholder="请选择等级">
              <el-option 
                v-for="level in levels" 
                :key="level.id" 
                :label="level.levelName" 
                :value="level.id" 
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      
      <!-- 数据表格 -->
      <el-card class="table-card">
        <div slot="header" class="card-header">
          <span>分销员列表</span>
          <el-button type="primary" size="small" @click="handleExport">
            导出数据
          </el-button>
        </div>
        
        <el-table :data="tableData" v-loading="loading">
          <el-table-column prop="agentCode" label="分销员编码" />
          <el-table-column prop="agentName" label="姓名" />
          <el-table-column prop="agentMobile" label="手机号" />
          <el-table-column prop="levelName" label="等级">
            <template slot-scope="scope">
              <el-tag :type="getLevelTagType(scope.row.levelGrade)">
                {{ scope.row.levelName }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="totalCommission" label="累计佣金">
            <template slot-scope="scope">
              ¥{{ scope.row.totalCommission }}
            </template>
          </el-table-column>
          <el-table-column prop="teamCount" label="团队人数" />
          <el-table-column prop="status" label="状态">
            <template slot-scope="scope">
              <el-tag :type="getStatusTagType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="申请时间" />
          <el-table-column label="操作" width="200">
            <template slot-scope="scope">
              <el-button size="mini" @click="handleView(scope.row)">
                查看
              </el-button>
              <el-button 
                size="mini" 
                type="primary" 
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-dropdown @command="handleCommand">
                <el-button size="mini">
                  更多<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :command="{action: 'level', row: scope.row}">
                    调整等级
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'disable', row: scope.row}">
                    禁用账户
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </el-card>
    </div>
  </template>
  ```
- [ ] **验收标准**：列表页面功能完整，搜索筛选正常，分页正常

### 下午任务（4小时）

#### Task 2.2: 分销员详情页面（2小时）
- [ ] **页面文件**：`src/views/distribution/agent/AgentDetail.vue`
- [ ] **功能实现**：
  - 基本信息展示
  - 业绩统计图表
  - 团队关系树
  - 操作记录时间线
- [ ] **验收标准**：详情页面信息完整，图表展示清晰

#### Task 2.3: 分销员编辑页面（2小时）
- [ ] **页面文件**：`src/views/distribution/agent/AgentEdit.vue`
- [ ] **功能实现**：
  - 基本信息编辑表单
  - 等级调整
  - 状态管理
  - 备注信息
- [ ] **验收标准**：编辑功能正常，表单验证完整

## 第三天：申请审核和商品管理

### 上午任务（4小时）

#### Task 3.1: 申请审核页面（4小时）
- [ ] **页面文件**：`src/views/distribution/agent/AgentAudit.vue`
- [ ] **功能实现**：
  ```vue
  <template>
    <div class="agent-audit">
      <!-- 待审核列表 -->
      <el-card title="待审核申请">
        <el-table :data="pendingList" v-loading="loading">
          <el-table-column prop="agentName" label="申请人" />
          <el-table-column prop="agentMobile" label="手机号" />
          <el-table-column prop="inviteCode" label="邀请码" />
          <el-table-column prop="applyTime" label="申请时间" />
          <el-table-column label="操作" width="200">
            <template slot-scope="scope">
              <el-button 
                size="mini" 
                type="success" 
                @click="handleAudit(scope.row, 1)"
              >
                通过
              </el-button>
              <el-button 
                size="mini" 
                type="danger" 
                @click="handleAudit(scope.row, 2)"
              >
                拒绝
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      
      <!-- 批量操作 -->
      <el-card title="批量审核" class="batch-audit">
        <el-checkbox-group v-model="selectedIds">
          <el-checkbox 
            v-for="item in pendingList" 
            :key="item.id" 
            :label="item.id"
          >
            {{ item.agentName }}
          </el-checkbox>
        </el-checkbox-group>
        <div class="batch-actions">
          <el-button type="success" @click="handleBatchAudit(1)">
            批量通过
          </el-button>
          <el-button type="danger" @click="handleBatchAudit(2)">
            批量拒绝
          </el-button>
        </div>
      </el-card>
    </div>
  </template>
  ```
- [ ] **验收标准**：审核功能完整，支持批量操作

### 下午任务（4小时）

#### Task 3.2: 商品分销配置页面（4小时）
- [ ] **页面文件**：`src/views/distribution/goods/GoodsConfig.vue`
- [ ] **功能实现**：
  ```vue
  <template>
    <div class="goods-config">
      <!-- 商品搜索 -->
      <el-card class="search-card">
        <el-form :model="searchForm" inline>
          <el-form-item label="商品名称">
            <el-input v-model="searchForm.goodsName" placeholder="请输入商品名称" />
          </el-form-item>
          <el-form-item label="分销状态">
            <el-select v-model="searchForm.enableDistribution">
              <el-option label="全部" value="" />
              <el-option label="已启用" :value="true" />
              <el-option label="未启用" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleBatchConfig">批量配置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
      
      <!-- 商品列表 -->
      <el-card class="table-card">
        <el-table :data="tableData" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" />
          <el-table-column prop="goodsName" label="商品名称" />
          <el-table-column prop="goodsPrice" label="商品价格">
            <template slot-scope="scope">
              ¥{{ scope.row.goodsPrice }}
            </template>
          </el-table-column>
          <el-table-column prop="enableDistribution" label="分销状态">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.enableDistribution"
                @change="handleStatusChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="commissionRate" label="佣金比例">
            <template slot-scope="scope">
              <span v-if="scope.row.enableDistribution">
                {{ scope.row.commissionRate }}%
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" />
          <el-table-column label="操作" width="150">
            <template slot-scope="scope">
              <el-button 
                size="mini" 
                @click="handleConfig(scope.row)"
                :disabled="!scope.row.enableDistribution"
              >
                配置
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      
      <!-- 配置弹窗 -->
      <el-dialog title="分销配置" :visible.sync="configDialogVisible">
        <el-form :model="configForm" :rules="configRules" ref="configForm">
          <el-form-item label="佣金类型" prop="commissionType">
            <el-radio-group v-model="configForm.commissionType">
              <el-radio :label="1">百分比</el-radio>
              <el-radio :label="2">固定金额</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item 
            v-if="configForm.commissionType === 1"
            label="佣金比例" 
            prop="commissionRate"
          >
            <el-input-number 
              v-model="configForm.commissionRate" 
              :min="0" 
              :max="100" 
              :precision="2"
            />
            <span>%</span>
          </el-form-item>
          <el-form-item 
            v-if="configForm.commissionType === 2"
            label="固定佣金" 
            prop="commissionAmount"
          >
            <el-input-number 
              v-model="configForm.commissionAmount" 
              :min="0" 
              :precision="2"
            />
            <span>元</span>
          </el-form-item>
          <el-form-item label="优先级" prop="priority">
            <el-input-number v-model="configForm.priority" :min="0" />
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button @click="configDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveConfig">确定</el-button>
        </div>
      </el-dialog>
    </div>
  </template>
  ```
- [ ] **验收标准**：商品配置功能完整，支持批量操作

## 第四天：佣金和提现管理

### 上午任务（4小时）

#### Task 4.1: 佣金记录页面（2小时）
- [ ] **页面文件**：`src/views/distribution/commission/CommissionList.vue`
- [ ] **功能实现**：
  - 佣金记录列表，支持多维度筛选
  - 佣金详情查看
  - 数据导出功能
- [ ] **验收标准**：佣金记录查询功能完整

#### Task 4.2: 佣金统计页面（2小时）
- [ ] **页面文件**：`src/views/distribution/commission/CommissionStats.vue`
- [ ] **功能实现**：
  - 佣金统计图表
  - 分时段统计
  - 分等级统计
- [ ] **验收标准**：统计图表展示清晰

### 下午任务（4小时）

#### Task 4.3: 提现审核页面（2小时）
- [ ] **页面文件**：`src/views/distribution/withdraw/WithdrawAudit.vue`
- [ ] **功能实现**：
  - 待审核提现列表
  - 提现审核操作
  - 批量审核功能
- [ ] **验收标准**：提现审核功能完整

#### Task 4.4: 提现记录页面（2小时）
- [ ] **页面文件**：`src/views/distribution/withdraw/WithdrawList.vue`
- [ ] **功能实现**：
  - 提现记录查询
  - 提现状态管理
  - 提现统计报表
- [ ] **验收标准**：提现管理功能完整

## 第五天：数据分析和系统优化

### 上午任务（4小时）

#### Task 5.1: 业绩排行页面（2小时）
- [ ] **页面文件**：`src/views/distribution/statistics/Ranking.vue`
- [ ] **功能实现**：
  - 销售额排行榜
  - 佣金收入排行榜
  - 团队规模排行榜
  - 支持分等级排名
- [ ] **验收标准**：排行榜功能完整，数据准确

#### Task 5.2: 趋势分析页面（2小时）
- [ ] **页面文件**：`src/views/distribution/statistics/Trend.vue`
- [ ] **功能实现**：
  - 数据趋势图表
  - 多维度分析
  - 时间范围选择
- [ ] **验收标准**：趋势分析图表美观，交互友好

### 下午任务（4小时）

#### Task 5.3: 系统优化（2小时）
- [ ] **性能优化**：
  - 路由懒加载
  - 组件按需加载
  - 图片懒加载
  - 接口缓存
- [ ] **用户体验优化**：
  - Loading状态
  - 错误处理
  - 空状态页面
  - 操作反馈
- [ ] **验收标准**：页面加载速度快，用户体验良好

#### Task 5.4: 测试和部署准备（2小时）
- [ ] **功能测试**：
  - 所有页面功能测试
  - 浏览器兼容性测试
  - 响应式测试
- [ ] **部署准备**：
  - 生产环境配置
  - 打包优化
  - 部署文档
- [ ] **验收标准**：所有功能测试通过，可正常部署

## API接口对接

### 接口列表
```javascript
// src/api/agent.js
export const agentApi = {
  // 分销员管理
  getAgentList: (params) => request.get('/admin/distribution/agent/page', { params }),
  getAgentDetail: (id) => request.get(`/admin/distribution/agent/${id}`),
  auditAgent: (data) => request.put('/admin/distribution/agent/audit', data),
  updateAgent: (data) => request.put('/admin/distribution/agent/update', data),
  
  // 等级管理
  getLevelList: () => request.get('/admin/distribution/level/list'),
  updateAgentLevel: (data) => request.put('/admin/distribution/agent/level', data)
}

// src/api/commission.js
export const commissionApi = {
  getCommissionList: (params) => request.get('/admin/distribution/commission/page', { params }),
  getCommissionStats: (params) => request.get('/admin/distribution/commission/statistics', { params }),
  settleCommission: (data) => request.post('/admin/distribution/commission/settle', data)
}

// src/api/goods.js
export const goodsApi = {
  getGoodsList: (params) => request.get('/admin/distribution/goods/page', { params }),
  configGoods: (data) => request.post('/admin/distribution/goods/config', data),
  batchConfigGoods: (data) => request.post('/admin/distribution/goods/batch-config', data)
}

// src/api/withdraw.js
export const withdrawApi = {
  getWithdrawList: (params) => request.get('/admin/distribution/withdraw/page', { params }),
  auditWithdraw: (data) => request.put('/admin/distribution/withdraw/audit', data),
  batchAuditWithdraw: (data) => request.post('/admin/distribution/withdraw/batch-audit', data)
}

// src/api/statistics.js
export const statisticsApi = {
  getOverviewStats: () => request.get('/admin/distribution/statistics/overview'),
  getRanking: (params) => request.get('/admin/distribution/statistics/ranking', { params }),
  getTrendData: (params) => request.get('/admin/distribution/statistics/trend', { params })
}
```

## 验收标准

### 功能验收
- [ ] **页面完整性**：所有规划页面开发完成
- [ ] **功能完整性**：CRUD操作、搜索筛选、批量操作功能正常
- [ ] **数据展示**：列表、详情、统计图表展示正确
- [ ] **交互体验**：操作流畅，反馈及时

### 性能验收
- [ ] **加载速度**：首页加载<3秒，其他页面<2秒
- [ ] **响应时间**：用户操作响应<500ms
- [ ] **兼容性**：支持Chrome、Firefox、Safari、Edge

### 质量验收
- [ ] **代码规范**：符合Vue.js和Element UI最佳实践
- [ ] **组件复用**：公共组件抽取合理，复用性好
- [ ] **错误处理**：异常情况处理完善
- [ ] **用户体验**：界面美观，操作便捷

## 部署和维护

### 部署配置
```javascript
// vue.config.js
module.exports = {
  publicPath: process.env.NODE_ENV === 'production' ? '/admin/' : '/',
  outputDir: 'dist',
  assetsDir: 'static',
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      }
    }
  },
  configureWebpack: {
    optimization: {
      splitChunks: {
        chunks: 'all'
      }
    }
  }
}
```

### 维护计划
- [ ] **定期更新**：依赖包定期更新
- [ ] **性能监控**：页面性能定期检查
- [ ] **用户反馈**：收集用户使用反馈，持续优化
- [ ] **功能迭代**：根据业务需求持续迭代

通过这个详细的管理后台前端开发计划，可以在5个工作日内完成一个功能完整、用户体验良好的分销管理后台系统。