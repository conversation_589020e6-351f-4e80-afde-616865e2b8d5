// 更新后的 DistGoodsConfigDO 实体类示例

package cn.jianwoo.octopus.admin.module.dist.dal.dataobject.goods;

import cn.jianwoo.octopus.admin.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品分销配置 DO
 */
@TableName("yt_dist_goods_config")
@KeySequence("yt_dist_goods_config_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DistGoodsConfigDO extends BaseDO {

    /**
     * 配置ID
     */
    @TableId
    private Long id;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * SPU编码
     */
    private String spuCode;

    /**
     * 应用ID
     */
    private Long appId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品类型
     */
    private Integer goodsType;

    /**
     * 是否开启分销
     */
    private Boolean enableDist;

    /**
     * 佣金模式：1-固定金额 2-百分比 3-阶梯式
     */
    private Integer commissionMode;

    /**
     * 一级佣金比例(%)
     */
    private BigDecimal firstCommissionRate;

    /**
     * 一级固定佣金金额
     */
    private BigDecimal firstCommissionAmount;

    /**
     * 二级佣金比例(%)
     */
    private BigDecimal secondCommissionRate;

    /**
     * 二级固定佣金金额
     */
    private BigDecimal secondCommissionAmount;

    /**
     * 三级佣金比例(%)
     */
    private BigDecimal thirdCommissionRate;

    /**
     * 三级固定佣金金额
     */
    private BigDecimal thirdCommissionAmount;

    /**
     * 佣金计算基准：1-商品原价 2-实付金额
     */
    private Integer commissionBaseType;

    /**
     * 最低分销价格
     */
    private BigDecimal minDistPrice;

    /**
     * 分销开始时间
     */
    private LocalDateTime distStartTime;

    /**
     * 分销结束时间
     */
    private LocalDateTime distEndTime;

    /**
     * 分销库存(-1表示不限)
     */
    private Integer distStock;

    /**
     * 分销已售数量
     */
    private Integer distSoldCount;

    /**
     * 限购数量(0表示不限)
     */
    private Integer limitBuyCount;

    /**
     * 最低分销等级ID
     */
    private Long minLevelId;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 状态：0-禁用 1-启用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    // 以下字段保留但建议后续废弃
    @Deprecated
    private BigDecimal commissionRate;
    
    @Deprecated
    private BigDecimal commissionAmount;
    
    @Deprecated
    private String tieredConfig;
    
    @Deprecated
    private String levelConfig;
    
    @Deprecated
    private Integer distLimit;
    
    @Deprecated
    private String limitConfig;
    
    @Deprecated
    private Integer soldCount;
    
    @Deprecated
    private BigDecimal totalCommission;
    
    @Deprecated
    private LocalDateTime startTime;
    
    @Deprecated
    private LocalDateTime endTime;
}