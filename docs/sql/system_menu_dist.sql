-- ===========================================================================================
-- 分销管理V4 菜单权限初始化SQL
-- 生成时间: 2025-07-29
-- 说明: 初始化分销管理V4模块的所有菜单和按钮权限
-- ===========================================================================================

-- 开始事务
BEGIN;

-- 设置父菜单ID变量（需要根据实际情况调整）
SET @parentId = 0;

-- ===========================================================================================
-- 1. 创建一级菜单：分销管理V4
-- ===========================================================================================
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`,
                           `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`,
                           `create_time`, `updater`, `update_time`, `deleted`)
VALUES ('分销管理', '', 1, 500, @parentId, '/distribution-v4', 'ep:coin',
        'Layout', 'DistributionV4', 0, b'1', b'1', b'1', 'admin',
        NOW(), 'admin', NOW(), b'0');

-- 获取刚插入的菜单ID作为父级ID
SET @distributionV4Id = LAST_INSERT_ID();

-- ===========================================================================================
-- 2. 分销员管理
-- ===========================================================================================
-- 2.1 分销员管理菜单
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`,
                           `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`,
                           `create_time`, `updater`, `update_time`, `deleted`)
VALUES ('分销员管理', '', 1, 1, @distributionV4Id, 'agent', 'ep:user',
        'distribution-v4/agent/index', 'DistributionV4Agent', 0, b'1', b'1', b'0', 'admin',
        NOW(), 'admin', NOW(), b'0');

SET @agentMenuId = LAST_INSERT_ID();

-- 2.2 分销员管理按钮权限
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`,
                           `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`,
                           `create_time`, `updater`, `update_time`, `deleted`) VALUES
                                                                                   ('分销员查询', 'distribution:agent:query', 3, 1, @agentMenuId, '', '#', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('分销员新增', 'distribution:agent:create', 3, 2, @agentMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('分销员修改', 'distribution:agent:update', 3, 3, @agentMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('分销员删除', 'distribution:agent:delete', 3, 4, @agentMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('分销员导出', 'distribution:agent:export', 3, 5, @agentMenuId, '', '#', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('分销员审核', 'distribution:agent:audit', 3, 6, @agentMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('分销员冻结', 'distribution:agent:freeze', 3, 7, @agentMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('分销员解冻', 'distribution:agent:unfreeze', 3, 8, @agentMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('分销员等级调整', 'distribution:agent:level:update', 3, 9, @agentMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('分销员上级调整', 'distribution:agent:parent:update', 3, 10, @agentMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0');

-- ===========================================================================================
-- 3. 分销员标签
-- ===========================================================================================
-- 3.1 分销员标签菜单
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`,
                           `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`,
                           `create_time`, `updater`, `update_time`, `deleted`)
VALUES ('分销员标签', '', 2, 2, @distributionV4Id, 'agent-tag', 'ep:collection-tag',
        'distribution-v4/tag/index', 'DistributionV4AgentTag', 0, b'1', b'1', b'0', 'admin',
        NOW(), 'admin', NOW(), b'0');

SET @tagMenuId = LAST_INSERT_ID();

-- 3.2 分销员标签按钮权限
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`,
                           `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`,
                           `create_time`, `updater`, `update_time`, `deleted`) VALUES
                                                                                   ('标签查询', 'distribution:agent:tag:query', 3, 1, @tagMenuId, '', '#', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('标签新增', 'distribution:agent:tag:create', 3, 2, @tagMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('标签修改', 'distribution:agent:tag:update', 3, 3, @tagMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('标签删除', 'distribution:agent:tag:delete', 3, 4, @tagMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('标签导出', 'distribution:agent:tag:export', 3, 5, @tagMenuId, '', '#', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0');

-- ===========================================================================================
-- 4. 佣金管理
-- ===========================================================================================
-- 4.1 佣金管理菜单
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`,
                           `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`,
                           `create_time`, `updater`, `update_time`, `deleted`)
VALUES ('佣金管理', '', 2, 3, @distributionV4Id, 'commission', 'ep:money',
        'distribution-v4/commission/index', 'DistributionV4Commission', 0, b'1', b'1', b'0', 'admin',
        NOW(), 'admin', NOW(), b'0');

SET @commissionMenuId = LAST_INSERT_ID();

-- 4.2 佣金管理按钮权限
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`,
                           `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`,
                           `create_time`, `updater`, `update_time`, `deleted`) VALUES
                                                                                   ('佣金查询', 'distribution:commission:query', 3, 1, @commissionMenuId, '', '#', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('佣金修改', 'distribution:commission:update', 3, 2, @commissionMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('佣金结算', 'distribution:commission:settle', 3, 3, @commissionMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('佣金冻结', 'distribution:commission:freeze', 3, 4, @commissionMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('佣金解冻', 'distribution:commission:unfreeze', 3, 5, @commissionMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('佣金取消', 'distribution:commission:cancel', 3, 6, @commissionMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('佣金导出', 'distribution:commission:export', 3, 7, @commissionMenuId, '', '#', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('佣金重算', 'distribution:commission:recalculate', 3, 8, @commissionMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0');

-- ===========================================================================================
-- 5. 商品分销配置
-- ===========================================================================================
-- 5.1 商品分销配置菜单
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`,
                           `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`,
                           `create_time`, `updater`, `update_time`, `deleted`)
VALUES ('商品分销配置', '', 2, 4, @distributionV4Id, 'goods', 'ep:goods',
        'distribution-v4/goods/index', 'DistributionV4Goods', 0, b'1', b'1', b'0', 'admin',
        NOW(), 'admin', NOW(), b'0');

SET @goodsMenuId = LAST_INSERT_ID();

-- 5.2 商品分销配置按钮权限
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`,
                           `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`,
                           `create_time`, `updater`, `update_time`, `deleted`) VALUES
                                                                                   ('商品配置查询', 'distribution:goods:query', 3, 1, @goodsMenuId, '', '#', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('商品配置设置', 'distribution:goods:config', 3, 2, @goodsMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('商品配置修改', 'distribution:goods:update', 3, 3, @goodsMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('商品配置导出', 'distribution:goods:export', 3, 4, @goodsMenuId, '', '#', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0');

-- ===========================================================================================
-- 6. 等级管理
-- ===========================================================================================
-- 6.1 等级管理菜单
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`,
                           `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`,
                           `create_time`, `updater`, `update_time`, `deleted`)
VALUES ('等级管理', '', 2, 5, @distributionV4Id, 'level', 'ep:rank',
        'distribution-v4/level/index', 'DistributionV4Level', 0, b'1', b'1', b'0', 'admin',
        NOW(), 'admin', NOW(), b'0');

SET @levelMenuId = LAST_INSERT_ID();

-- 6.2 等级管理按钮权限
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`,
                           `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`,
                           `create_time`, `updater`, `update_time`, `deleted`) VALUES
                                                                                   ('等级查询', 'distribution:level:query', 3, 1, @levelMenuId, '', '#', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('等级新增', 'distribution:level:create', 3, 2, @levelMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('等级修改', 'distribution:level:update', 3, 3, @levelMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('等级删除', 'distribution:level:delete', 3, 4, @levelMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('等级导出', 'distribution:level:export', 3, 5, @levelMenuId, '', '#', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0');

-- ===========================================================================================
-- 7. 奖励方案
-- ===========================================================================================
-- 7.1 奖励方案菜单
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`,
                           `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`,
                           `create_time`, `updater`, `update_time`, `deleted`)
VALUES ('奖励方案', '', 2, 6, @distributionV4Id, 'reward', 'ep:trophy',
        'distribution-v4/reward/index', 'DistributionV4Reward', 0, b'1', b'1', b'0', 'admin',
        NOW(), 'admin', NOW(), b'0');

SET @rewardMenuId = LAST_INSERT_ID();

-- 7.2 奖励方案按钮权限
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`,
                           `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`,
                           `create_time`, `updater`, `update_time`, `deleted`) VALUES
                                                                                   ('奖励方案查询', 'distribution:reward:query', 3, 1, @rewardMenuId, '', '#', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('奖励方案新增', 'distribution:reward:create', 3, 2, @rewardMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('奖励方案修改', 'distribution:reward:update', 3, 3, @rewardMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('奖励方案删除', 'distribution:reward:delete', 3, 4, @rewardMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('奖励方案导出', 'distribution:reward:export', 3, 5, @rewardMenuId, '', '#', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0');

-- ===========================================================================================
-- 8. 提现管理
-- ===========================================================================================
-- 8.1 提现管理菜单
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`,
                           `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`,
                           `create_time`, `updater`, `update_time`, `deleted`)
VALUES ('提现管理', '', 2, 7, @distributionV4Id, 'withdraw', 'ep:wallet',
        'distribution-v4/withdraw/index', 'DistributionV4Withdraw', 0, b'1', b'1', b'0', 'admin',
        NOW(), 'admin', NOW(), b'0');

SET @withdrawMenuId = LAST_INSERT_ID();

-- 8.2 提现管理按钮权限
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`,
                           `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`,
                           `create_time`, `updater`, `update_time`, `deleted`) VALUES
                                                                                   ('提现查询', 'distribution:withdraw:query', 3, 1, @withdrawMenuId, '', '#', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('提现审核', 'distribution:withdraw:audit', 3, 2, @withdrawMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('提现确认', 'distribution:withdraw:confirm', 3, 3, @withdrawMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('提现拒绝', 'distribution:withdraw:reject', 3, 4, @withdrawMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('提现导出', 'distribution:withdraw:export', 3, 5, @withdrawMenuId, '', '#', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0');

-- ===========================================================================================
-- 9. 海报管理
-- ===========================================================================================
-- 9.1 海报管理菜单
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`,
                           `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`,
                           `create_time`, `updater`, `update_time`, `deleted`)
VALUES ('海报管理', '', 2, 8, @distributionV4Id, 'poster', 'ep:picture',
        'distribution-v4/poster/index', 'DistributionV4Poster', 0, b'1', b'1', b'0', 'admin',
        NOW(), 'admin', NOW(), b'0');

SET @posterMenuId = LAST_INSERT_ID();

-- 9.2 海报管理按钮权限
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`,
                           `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`,
                           `create_time`, `updater`, `update_time`, `deleted`) VALUES
                                                                                   ('海报查询', 'distribution:poster:query', 3, 1, @posterMenuId, '', '#', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('海报新增', 'distribution:poster:create', 3, 2, @posterMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('海报修改', 'distribution:poster:update', 3, 3, @posterMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('海报删除', 'distribution:poster:delete', 3, 4, @posterMenuId, '', '', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('海报导出', 'distribution:poster:export', 3, 5, @posterMenuId, '', '#', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0');

-- ===========================================================================================
-- 10. 数据统计
-- ===========================================================================================
-- 10.1 数据统计菜单
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`,
                           `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`,
                           `create_time`, `updater`, `update_time`, `deleted`)
VALUES ('数据统计', '', 2, 9, @distributionV4Id, 'statistics', 'ep:data-analysis',
        'distribution-v4/statistics/index', 'DistributionV4Statistics', 0, b'1', b'1', b'0', 'admin',
        NOW(), 'admin', NOW(), b'0');

SET @statisticsMenuId = LAST_INSERT_ID();

-- 10.2 数据统计按钮权限
INSERT INTO `system_menu` (`name`, `permission`, `type`, `sort`, `parent_id`, `path`, `icon`,
                           `component`, `component_name`, `status`, `visible`, `keep_alive`, `always_show`, `creator`,
                           `create_time`, `updater`, `update_time`, `deleted`) VALUES
                                                                                   ('统计查询', 'distribution:statistics:query', 3, 1, @statisticsMenuId, '', '#', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0'),
                                                                                   ('统计导出', 'distribution:statistics:export', 3, 2, @statisticsMenuId, '', '#', '', NULL, 0, b'1', b'1', b'1', 'admin', NOW(), 'admin', NOW(), b'0');

-- ===========================================================================================
-- 注意事项：
-- 1. @parentId 需要根据实际系统的父级菜单ID进行调整
-- 2. 菜单ID采用自增方式，实际使用时系统会自动生成
-- 3. 权限标识符遵循 模块:功能:操作 的命名规范
-- 4. type字段说明：1=菜单，2=按钮
-- 5. status字段说明：0=正常，1=停用
-- ===========================================================================================

-- 提交事务
COMMIT;