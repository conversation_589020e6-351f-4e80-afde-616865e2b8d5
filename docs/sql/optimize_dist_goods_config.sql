-- 优化 yt_dist_goods_config 表结构
-- 支持三级分销体系

-- 1. 添加缺失字段
ALTER TABLE `yt_dist_goods_config`
    -- SPU编码
    ADD COLUMN `spu_code` varchar(50) DEFAULT NULL COMMENT 'SPU编码' AFTER `goods_id`,
    
    -- 三级分销佣金配置
    ADD COLUMN `first_commission_rate` decimal(5,2) DEFAULT NULL COMMENT '一级佣金比例(%)' AFTER `commission_mode`,
    ADD COLUMN `first_commission_amount` decimal(10,2) DEFAULT NULL COMMENT '一级固定佣金金额' AFTER `first_commission_rate`,
    ADD COLUMN `second_commission_rate` decimal(5,2) DEFAULT NULL COMMENT '二级佣金比例(%)' AFTER `first_commission_amount`,
    ADD COLUMN `second_commission_amount` decimal(10,2) DEFAULT NULL COMMENT '二级固定佣金金额' AFTER `second_commission_rate`,
    ADD COLUMN `third_commission_rate` decimal(5,2) DEFAULT NULL COMMENT '三级佣金比例(%)' AFTER `second_commission_amount`,
    ADD COLUMN `third_commission_amount` decimal(10,2) DEFAULT NULL COMMENT '三级固定佣金金额' AFTER `third_commission_rate`,
    
    -- 佣金计算基准
    ADD COLUMN `commission_base_type` tinyint DEFAULT 1 COMMENT '佣金计算基准：1-商品原价 2-实付金额' AFTER `third_commission_amount`,
    
    -- 分销价格限制
    ADD COLUMN `min_dist_price` decimal(10,2) DEFAULT NULL COMMENT '最低分销价格' AFTER `commission_base_type`,
    
    -- 分销时间范围（使用新字段名避免冲突）
    ADD COLUMN `dist_start_time` datetime DEFAULT NULL COMMENT '分销开始时间' AFTER `min_dist_price`,
    ADD COLUMN `dist_end_time` datetime DEFAULT NULL COMMENT '分销结束时间' AFTER `dist_start_time`,
    
    -- 分销已售数量（使用新字段名）
    ADD COLUMN `dist_sold_count` int DEFAULT 0 COMMENT '分销已售数量' AFTER `dist_stock`,
    
    -- 限购配置
    ADD COLUMN `limit_buy_count` int DEFAULT 0 COMMENT '限购数量(0表示不限)' AFTER `dist_sold_count`,
    
    -- 最低等级要求
    ADD COLUMN `min_level_id` bigint DEFAULT NULL COMMENT '最低分销等级ID' AFTER `limit_buy_count`,
    
    -- 备注
    ADD COLUMN `remark` varchar(500) DEFAULT NULL COMMENT '备注' AFTER `status`;

-- 2. 数据迁移（将原有佣金配置迁移到一级佣金）
UPDATE `yt_dist_goods_config` 
SET 
    `first_commission_rate` = `commission_rate`,
    `first_commission_amount` = `commission_amount`,
    `dist_start_time` = `start_time`,
    `dist_end_time` = `end_time`,
    `dist_sold_count` = `sold_count`
WHERE `deleted` = 0;

-- 3. 删除旧字段（可选，建议在确认数据迁移无误后执行）
-- ALTER TABLE `yt_dist_goods_config`
--     DROP COLUMN `commission_rate`,
--     DROP COLUMN `commission_amount`,
--     DROP COLUMN `tiered_config`,
--     DROP COLUMN `level_config`,
--     DROP COLUMN `dist_limit`,
--     DROP COLUMN `limit_config`,
--     DROP COLUMN `sold_count`,
--     DROP COLUMN `total_commission`,
--     DROP COLUMN `start_time`,
--     DROP COLUMN `end_time`;

-- 4. 优化索引
-- 删除可能不再需要的索引
ALTER TABLE `yt_dist_goods_config`
    DROP INDEX `idx_time_range`;

-- 添加新的复合索引以支持查询
ALTER TABLE `yt_dist_goods_config`
    ADD INDEX `idx_goods_app_tenant` (`goods_id`, `app_id`, `tenant_id`, `deleted`),
    ADD INDEX `idx_dist_time_range` (`dist_start_time`, `dist_end_time`),
    ADD INDEX `idx_spu_code` (`spu_code`),
    ADD INDEX `idx_min_level` (`min_level_id`);

-- 5. 添加表注释说明
ALTER TABLE `yt_dist_goods_config` 
    COMMENT='商品分销配置表 - 支持三级分销体系';