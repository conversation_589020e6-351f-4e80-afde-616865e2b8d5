# 商品分销配置表优化迁移计划

## 1. 背景说明
现有的 `yt_dist_goods_config` 表结构不支持三级分销体系，需要进行结构优化以满足业务需求。

## 2. 主要变更内容

### 2.1 新增字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| spu_code | varchar(50) | SPU编码 |
| first_commission_rate | decimal(5,2) | 一级佣金比例(%) |
| first_commission_amount | decimal(10,2) | 一级固定佣金金额 |
| second_commission_rate | decimal(5,2) | 二级佣金比例(%) |
| second_commission_amount | decimal(10,2) | 二级固定佣金金额 |
| third_commission_rate | decimal(5,2) | 三级佣金比例(%) |
| third_commission_amount | decimal(10,2) | 三级固定佣金金额 |
| commission_base_type | tinyint | 佣金计算基准 |
| min_dist_price | decimal(10,2) | 最低分销价格 |
| dist_start_time | datetime | 分销开始时间 |
| dist_end_time | datetime | 分销结束时间 |
| dist_sold_count | int | 分销已售数量 |
| limit_buy_count | int | 限购数量 |
| min_level_id | bigint | 最低分销等级ID |
| remark | varchar(500) | 备注 |

### 2.2 数据迁移映射
- `commission_rate` → `first_commission_rate`
- `commission_amount` → `first_commission_amount`
- `start_time` → `dist_start_time`
- `end_time` → `dist_end_time`
- `sold_count` → `dist_sold_count`

### 2.3 索引优化
- 新增复合索引：`idx_goods_app_tenant` (goods_id, app_id, tenant_id, deleted)
- 新增索引：`idx_dist_time_range`, `idx_spu_code`, `idx_min_level`

## 3. 执行步骤

### 步骤1：备份数据（重要！）
```sql
-- 创建备份表
CREATE TABLE yt_dist_goods_config_backup_20250104 LIKE yt_dist_goods_config;
INSERT INTO yt_dist_goods_config_backup_20250104 SELECT * FROM yt_dist_goods_config;
```

### 步骤2：执行表结构变更
执行 `optimize_dist_goods_config.sql` 中的 ALTER TABLE 语句

### 步骤3：验证数据迁移
```sql
-- 验证数据迁移是否正确
SELECT 
    COUNT(*) as total,
    SUM(CASE WHEN first_commission_rate IS NOT NULL OR first_commission_amount IS NOT NULL THEN 1 ELSE 0 END) as migrated
FROM yt_dist_goods_config 
WHERE deleted = 0;
```

### 步骤4：更新应用代码
- 更新 `DistGoodsConfigDO` 实体类
- 更新 Mapper XML 文件中的 SQL 语句
- 更新相关的 Service 实现

### 步骤5：清理旧字段（可选）
确认应用运行正常后，可以删除不再使用的旧字段

## 4. 回滚方案
如果出现问题，可以使用备份表恢复：
```sql
-- 恢复数据
DROP TABLE yt_dist_goods_config;
RENAME TABLE yt_dist_goods_config_backup_20250104 TO yt_dist_goods_config;
```

## 5. 注意事项
1. 执行前必须先备份数据
2. 建议在低峰期执行
3. 执行后需要验证数据完整性
4. 更新代码时注意保持兼容性
5. 监控应用日志，确保没有异常

## 6. 性能影响评估
- 新增字段会增加存储空间，但影响较小
- 新索引会提升查询性能，特别是针对 goods_id + app_id + tenant_id 的查询
- 建议在执行后运行 ANALYZE TABLE 更新统计信息