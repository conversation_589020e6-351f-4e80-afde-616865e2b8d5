# 商品分销配置表性能优化建议

## 1. 索引优化说明

### 1.1 核心查询索引
根据提供的查询：
```sql
WHERE deleted = 0 AND (goods_id IN (?, ?) AND app_id = ?) AND tenant_id = 1
```

推荐的复合索引：
```sql
-- 主查询索引（覆盖最常见的查询条件）
idx_goods_app_tenant (goods_id, app_id, tenant_id, deleted)
```

### 1.2 辅助索引
- `idx_spu_code` - 支持按SPU编码查询
- `idx_dist_time_range` - 支持时间范围查询
- `idx_min_level` - 支持按等级筛选

## 2. 查询优化建议

### 2.1 批量查询优化
```sql
-- 使用 IN 查询时，限制数量
SELECT * FROM yt_dist_goods_config 
WHERE goods_id IN (1,2,3,4,5) -- 建议不超过100个
AND app_id = ? 
AND tenant_id = ?
AND deleted = 0;
```

### 2.2 时间范围查询优化
```sql
-- 使用时间索引进行范围查询
SELECT * FROM yt_dist_goods_config 
WHERE dist_start_time <= NOW() 
AND dist_end_time >= NOW()
AND status = 1
AND deleted = 0;
```

### 2.3 分页查询优化
```sql
-- 使用主键进行分页
SELECT * FROM yt_dist_goods_config 
WHERE id > ? -- 上一页最后一条记录的ID
AND deleted = 0
ORDER BY id 
LIMIT 20;
```

## 3. 应用层优化

### 3.1 缓存策略
- 对频繁查询的商品配置使用 Redis 缓存
- 缓存 Key：`dist:goods:config:{appId}:{goodsId}`
- 缓存时间：30分钟
- 更新策略：更新数据时删除对应缓存

### 3.2 批量操作
```java
// 批量查询示例
List<Long> goodsIds = Arrays.asList(1L, 2L, 3L);
List<DistGoodsConfigDO> configs = distGoodsConfigMapper.selectBatchIds(goodsIds);

// 使用 Map 减少查询次数
Map<Long, DistGoodsConfigDO> configMap = configs.stream()
    .collect(Collectors.toMap(DistGoodsConfigDO::getGoodsId, Function.identity()));
```

### 3.3 异步处理
- 佣金统计等非实时操作使用异步任务
- 使用消息队列处理大批量更新

## 4. 数据库配置优化

### 4.1 表分区（数据量大时考虑）
```sql
-- 按月分区示例
ALTER TABLE yt_dist_goods_config
PARTITION BY RANGE (YEAR(create_time) * 100 + MONTH(create_time)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    -- ...
);
```

### 4.2 定期维护
```sql
-- 定期分析表
ANALYZE TABLE yt_dist_goods_config;

-- 定期优化表（低峰期执行）
OPTIMIZE TABLE yt_dist_goods_config;
```

## 5. 监控指标

### 5.1 关键监控项
- 慢查询日志（执行时间 > 100ms）
- 索引使用率
- 表碎片率
- 缓存命中率

### 5.2 告警阈值
- 查询响应时间 > 500ms
- 缓存命中率 < 80%
- 表大小 > 10GB（考虑分表）

## 6. 扩展性考虑

### 6.1 水平分表策略
当数据量超过1000万时，考虑按 app_id 分表：
- yt_dist_goods_config_1 (app_id = 1)
- yt_dist_goods_config_2 (app_id = 2)

### 6.2 读写分离
- 主库：处理写操作
- 从库：处理查询操作
- 使用 ShardingSphere 或 MyCAT 实现