# 分销员统计接口添加appId参数修改说明

## 修改概述
为 `getOverallStatistics` 接口添加了 `appId` 参数支持，实现基于应用ID的数据隔离。

## 修改内容

### 1. Controller层修改
**文件**: `DistAgentController.java`
```java
// 修改前
public CommonResult<DistAgentOverallStatisticsRespVO> getOverallStatistics() {
    DistAgentOverallStatisticsRespVO statistics = distAgentService.getOverallStatistics();
    return success(statistics);
}

// 修改后
public CommonResult<DistAgentOverallStatisticsRespVO> getOverallStatistics(
        @RequestParam("appId") @NotNull(message = "应用ID不能为空") Long appId) {
    DistAgentOverallStatisticsRespVO statistics = distAgentService.getOverallStatistics(appId);
    return success(statistics);
}
```

### 2. Service接口修改
**文件**: `DistAgentService.java`
```java
// 修改前
DistAgentOverallStatisticsRespVO getOverallStatistics();

// 修改后
DistAgentOverallStatisticsRespVO getOverallStatistics(Long appId);
```

### 3. Service实现修改
**文件**: `DistAgentServiceImpl.java`
- 在所有查询中添加了 `appId` 过滤条件
- 分销员总数、活跃分销员数、待审核分销员数等统计都基于指定的appId

## 待完成的工作

### 1. Mapper方法修改
需要修改以下Mapper方法以支持appId参数：

**DistAgentMapper.java**:
- `sumTotalCommission()` → `sumTotalCommission(Long appId)`
- `sumAvailableCommission()` → `sumAvailableCommission(Long appId)`
- `sumFrozenCommission()` → `sumFrozenCommission(Long appId)`
- `sumWithdrawnCommission()` → `sumWithdrawnCommission(Long appId)`
- `sumTeamMemberCount()` → `sumTeamMemberCount(Long appId)`
- `sumDirectMemberCount()` → `sumDirectMemberCount(Long appId)`

**DistCommissionMapper.java**:
- `sumCommissionByTimeRange()` → 需要添加appId参数

### 2. SQL修改
需要修改对应的SQL查询，添加appId条件：
```sql
-- 示例：修改前
SELECT SUM(total_commission) FROM yt_dist_agent WHERE deleted = 0

-- 示例：修改后
SELECT SUM(total_commission) FROM yt_dist_agent WHERE app_id = #{appId} AND deleted = 0
```

### 3. 前端调用修改
前端调用该接口时需要传递appId参数：
```javascript
// 修改前
GET /distribution/agent/overall-statistics

// 修改后
GET /distribution/agent/overall-statistics?appId=2
```

## 设计思考

### 为什么需要appId？
1. **多租户隔离**: 系统支持多个应用（租户），每个应用的数据需要隔离
2. **数据安全**: 防止跨应用访问数据
3. **统计准确性**: 确保统计数据只包含特定应用的分销员

### 其他接口的appId使用模式
- 分页查询接口（`getAgentPage`）已经使用了带appId的`DistAgentPageReqVO`
- 申请成为分销员（`applyAgent`）方法签名中包含appId参数
- 数据模型`DistAgentDO`包含appId字段

### 数据一致性保证
通过在所有查询中添加appId过滤条件，确保：
1. 统计数据的准确性
2. 不同应用之间的数据隔离
3. 与系统其他部分的一致性

## 测试建议
1. 测试不同appId返回的统计数据是否正确隔离
2. 测试appId为空时的错误处理
3. 测试appId不存在时的返回结果
4. 性能测试：确保添加appId过滤后查询性能不受影响