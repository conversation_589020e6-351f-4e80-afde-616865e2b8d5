# 分销商品配置历史记录功能方案 v2.0

## 一、需求概述

在现有商品分销配置功能基础上，增加配置历史记录和操作日志查看功能，便于审计和问题追踪。

## 二、功能设计

### 2.1 配置历史记录

#### 功能说明
- 记录每次配置变更的详细信息
- 支持查看变更前后的数据对比
- 支持按时间、操作类型筛选

#### 展示内容
1. **基本信息**：版本号、操作时间、操作人、操作类型
2. **变更内容**：简要描述主要变更点
3. **详细数据**：变更前后的完整配置数据

### 2.2 操作日志

#### 功能说明
- 记录所有配置相关的操作行为
- 包含成功和失败的操作记录
- 便于问题排查和操作审计

#### 展示内容
1. **操作信息**：操作类型、操作时间、操作人
2. **操作对象**：商品名称、配置ID
3. **操作结果**：成功/失败、错误信息（如有）
4. **耗时统计**：操作执行时间

## 三、界面设计

### 3.1 入口设计

在商品列表页面的操作列增加"历史记录"按钮：

```vue
<!-- index.vue 商品列表操作列 -->
<el-table-column label="操作" align="center" width="200" fixed="right">
  <template #default="scope">
    <el-button link type="primary" @click="openConfig(scope.row)">
      配置
    </el-button>
    <el-button link type="info" @click="openHistory(scope.row)">
      历史记录
    </el-button>
  </template>
</el-table-column>
```

### 3.2 历史记录弹窗组件

新建 `ConfigHistoryDialog.vue` 组件：

```vue
<template>
  <el-dialog
    v-model="dialogVisible"
    title="配置历史记录"
    width="90%"
    @close="handleClose"
  >
    <!-- 标签页切换 -->
    <el-tabs v-model="activeTab">
      <el-tab-pane label="配置历史" name="history">
        <!-- 筛选条件 -->
        <el-form :inline="true" @submit.prevent="loadHistory">
          <el-form-item label="操作类型">
            <el-select v-model="queryParams.operationType" clearable>
              <el-option label="全部" value="" />
              <el-option label="创建配置" :value="1" />
              <el-option label="更新配置" :value="2" />
              <el-option label="删除配置" :value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadHistory">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 历史记录表格 -->
        <el-table v-loading="loading" :data="historyList">
          <el-table-column label="版本" prop="version" width="80" />
          <el-table-column label="操作类型" width="100">
            <template #default="scope">
              <el-tag :type="getOperationTypeTag(scope.row.operationType)">
                {{ getOperationTypeName(scope.row.operationType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="变更说明" prop="changeDescription" show-overflow-tooltip />
          <el-table-column label="操作人" prop="operatorName" width="120" />
          <el-table-column label="操作时间" prop="createTime" width="180" />
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="scope">
              <el-button link type="primary" @click="viewDetail(scope.row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <el-pagination
          v-model:current-page="queryParams.pageNo"
          v-model:page-size="queryParams.pageSize"
          :total="total"
          layout="total, sizes, prev, pager, next"
          @size-change="loadHistory"
          @current-change="loadHistory"
        />
      </el-tab-pane>

      <el-tab-pane label="操作日志" name="logs">
        <!-- 操作日志表格 -->
        <el-table v-loading="logLoading" :data="logList">
          <el-table-column label="操作类型" prop="actionType" width="120" />
          <el-table-column label="操作描述" prop="actionDesc" show-overflow-tooltip />
          <el-table-column label="结果" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.success ? 'success' : 'danger'">
                {{ scope.row.success ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="错误信息" prop="errorMsg" show-overflow-tooltip />
          <el-table-column label="操作人" prop="operatorName" width="120" />
          <el-table-column label="操作时间" prop="createTime" width="180" />
          <el-table-column label="耗时(ms)" prop="duration" width="100" />
        </el-table>

        <!-- 分页 -->
        <el-pagination
          v-model:current-page="logQueryParams.pageNo"
          v-model:page-size="logQueryParams.pageSize"
          :total="logTotal"
          layout="total, sizes, prev, pager, next"
          @size-change="loadLogs"
          @current-change="loadLogs"
        />
      </el-tab-pane>
    </el-tabs>
  </el-dialog>

  <!-- 详情弹窗 -->
  <ConfigDetailDialog ref="detailDialogRef" />
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { getConfigHistory, getOperationLogs } from '@/api/distribution-v4/goods-config'
import ConfigDetailDialog from './ConfigDetailDialog.vue'

const dialogVisible = ref(false)
const activeTab = ref('history')
const loading = ref(false)
const logLoading = ref(false)

// 当前商品信息
const currentGoods = ref<any>({})

// 历史记录相关
const historyList = ref([])
const total = ref(0)
const dateRange = ref([])
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  operationType: '',
  startTime: '',
  endTime: ''
})

// 操作日志相关
const logList = ref([])
const logTotal = ref(0)
const logQueryParams = reactive({
  pageNo: 1,
  pageSize: 10
})

// 打开弹窗
const open = (goods: any) => {
  currentGoods.value = goods
  dialogVisible.value = true
  loadHistory()
  loadLogs()
}

// 加载历史记录
const loadHistory = async () => {
  loading.value = true
  try {
    // 处理时间范围
    if (dateRange.value?.length === 2) {
      queryParams.startTime = dateRange.value[0]
      queryParams.endTime = dateRange.value[1]
    }
    
    const { data } = await getConfigHistory({
      goodsId: currentGoods.value.id,
      ...queryParams
    })
    historyList.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 加载操作日志
const loadLogs = async () => {
  logLoading.value = true
  try {
    const { data } = await getOperationLogs({
      goodsId: currentGoods.value.id,
      ...logQueryParams
    })
    logList.value = data.list
    logTotal.value = data.total
  } finally {
    logLoading.value = false
  }
}

// 查看详情
const detailDialogRef = ref()
const viewDetail = (row: any) => {
  detailDialogRef.value?.open(row)
}

// 获取操作类型标签
const getOperationTypeTag = (type: number) => {
  const typeMap: Record<number, string> = {
    1: 'success',
    2: 'warning',
    3: 'danger'
  }
  return typeMap[type] || 'info'
}

// 获取操作类型名称
const getOperationTypeName = (type: number) => {
  const nameMap: Record<number, string> = {
    1: '创建配置',
    2: '更新配置', 
    3: '删除配置'
  }
  return nameMap[type] || '未知'
}

// 重置查询
const resetQuery = () => {
  queryParams.operationType = ''
  dateRange.value = []
  queryParams.startTime = ''
  queryParams.endTime = ''
  loadHistory()
}

// 关闭弹窗
const handleClose = () => {
  historyList.value = []
  logList.value = []
}

defineExpose({ open })
</script>
```

### 3.3 配置详情弹窗

新建 `ConfigDetailDialog.vue` 组件：

```vue
<template>
  <el-dialog
    v-model="dialogVisible"
    title="配置详情"
    width="70%"
    append-to-body
  >
    <!-- 基本信息 -->
    <el-descriptions :column="2" border class="mb-20px">
      <el-descriptions-item label="版本号">{{ detail.version }}</el-descriptions-item>
      <el-descriptions-item label="操作类型">{{ detail.operationType }}</el-descriptions-item>
      <el-descriptions-item label="操作人">{{ detail.operatorName }}</el-descriptions-item>
      <el-descriptions-item label="操作时间">{{ detail.createTime }}</el-descriptions-item>
    </el-descriptions>

    <!-- 配置数据对比 -->
    <el-tabs v-model="activeTab">
      <el-tab-pane label="变更前" name="before">
        <el-scrollbar height="400px">
          <pre class="config-json">{{ formatJson(detail.beforeData) }}</pre>
        </el-scrollbar>
      </el-tab-pane>
      <el-tab-pane label="变更后" name="after">
        <el-scrollbar height="400px">
          <pre class="config-json">{{ formatJson(detail.afterData) }}</pre>
        </el-scrollbar>
      </el-tab-pane>
      <el-tab-pane label="差异对比" name="diff">
        <el-scrollbar height="400px">
          <div class="diff-container" v-html="diffHtml"></div>
        </el-scrollbar>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { getConfigDetail } from '@/api/distribution-v4/goods-config'

const dialogVisible = ref(false)
const activeTab = ref('after')
const detail = ref<any>({})

// 打开弹窗
const open = async (history: any) => {
  // 获取详细数据
  const { data } = await getConfigDetail(history.id)
  detail.value = data
  dialogVisible.value = true
  activeTab.value = 'after'
}

// 格式化JSON
const formatJson = (data: any) => {
  if (!data) return ''
  try {
    return JSON.stringify(data, null, 2)
  } catch {
    return data
  }
}

// 差异对比（简单实现）
const diffHtml = computed(() => {
  // 这里可以使用 diff 库来生成更好的对比效果
  return '<div class="text-center text-gray-500">差异对比功能待实现</div>'
})

defineExpose({ open })
</script>

<style lang="scss" scoped>
.config-json {
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 13px;
  line-height: 1.5;
  overflow: auto;
}

.diff-container {
  padding: 12px;
  
  :deep(.diff-add) {
    background-color: #e6ffed;
    color: #24292e;
  }
  
  :deep(.diff-remove) {
    background-color: #ffeef0;
    color: #24292e;
  }
}
</style>
```

## 四、API接口

### 4.1 获取配置历史列表

```typescript
// 获取配置历史
export const getConfigHistory = (params: {
  goodsId: number
  pageNo?: number
  pageSize?: number
  operationType?: number
  startTime?: string
  endTime?: string
}) => {
  return request.get({ url: '/distribution/goods/config-history', params })
}
```

### 4.2 获取操作日志列表

```typescript
// 获取操作日志
export const getOperationLogs = (params: {
  goodsId: number
  pageNo?: number
  pageSize?: number
}) => {
  return request.get({ url: '/distribution/goods/operation-logs', params })
}
```

### 4.3 获取配置详情

```typescript
// 获取配置详情
export const getConfigDetail = (historyId: number) => {
  return request.get({ url: `/distribution/goods/config-history/${historyId}` })
}
```

## 五、使用方式

### 5.1 在商品列表页引入组件

```vue
<!-- index.vue -->
<template>
  <!-- 商品列表 -->
  <el-table :data="list">
    <!-- ... 其他列 ... -->
    <el-table-column label="操作" align="center" width="200" fixed="right">
      <template #default="scope">
        <el-button link type="primary" @click="openConfig(scope.row)">
          配置
        </el-button>
        <el-button link type="info" @click="openHistory(scope.row)">
          历史记录
        </el-button>
      </template>
    </el-table-column>
  </el-table>

  <!-- 历史记录弹窗 -->
  <ConfigHistoryDialog ref="historyDialogRef" />
</template>

<script setup lang="ts">
import ConfigHistoryDialog from './components/ConfigHistoryDialog.vue'

const historyDialogRef = ref()

// 打开历史记录
const openHistory = (row: any) => {
  historyDialogRef.value?.open(row)
}
</script>
```

## 六、注意事项

1. **性能优化**
   - 历史记录和日志数据量可能较大，需要分页加载
   - 配置详情数据较大时考虑懒加载

2. **用户体验**
   - 提供清晰的筛选条件，方便快速定位
   - 重要操作在日志中高亮显示
   - 失败操作显示具体错误信息

3. **数据安全**
   - 历史记录只读，不可修改
   - 敏感数据脱敏展示

## 七、后续优化

1. **增强对比功能**：使用专业的 diff 库实现配置对比
2. **导出功能**：支持导出历史记录和操作日志
3. **配置回滚**：支持回滚到指定历史版本（需后端支持）
4. **订阅通知**：重要配置变更时通知相关人员