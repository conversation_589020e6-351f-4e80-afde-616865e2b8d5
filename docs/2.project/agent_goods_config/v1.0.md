# 分销商品配置前端改造方案 v1.0

## 一、概述

根据后端数据结构的重构，前端需要调整商品分销配置的UI界面，以支持灵活的等级配置存储。主要改造点包括：
1. 新增统一佣金/等级差异化佣金的切换
2. 支持每个等级独立配置佣金、父级奖励、祖父级奖励
3. 增加配置历史记录查看功能
4. 优化批量配置流程

## 二、现状分析

### 2.1 当前UI结构
```
/views/distribution-v4/goods/
├── index.vue                      # 商品列表主页面
├── components/
│   ├── GoodsConfigDrawer.vue      # 商品配置抽屉（核心配置组件）
│   ├── AddLevelConfigDialog.vue   # 添加等级配置弹窗
│   ├── UnifiedLevelConfigDialog.vue # 统一配置弹窗
│   ├── BatchConfigDialog.vue      # 批量配置弹窗（已废弃）
│   ├── ConfigHistoryDialog.vue    # 配置历史弹窗
│   └── GoodsSelectDialog.vue      # 商品选择弹窗
```

### 2.2 现有问题
1. **固定佣金结构**：只支持一级、二级、三级固定佣金配置
2. **缺少灵活性**：无法为不同等级设置不同佣金
3. **缺少审计功能**：没有配置历史记录和操作日志查看

### 2.3 新数据结构对应关系
```javascript
// 旧结构
{
  firstCommissionRate: 10,      // 一级佣金比例
  secondCommissionRate: 8,      // 二级佣金比例
  thirdCommissionRate: 4        // 三级佣金比例
}

// 新结构
{
  commissionType: 1,            // 1-统一佣金，2-等级差异化
  commissionValue: 10,          // 统一佣金值
  enableLevelConfig: true,      // 是否启用等级配置
  levelConfigs: [               // 等级配置数组
    {
      levelId: "1",
      levelName: "青铜会员1",
      commissionValue: 10,
      parentReward: 8,
      grandParentReward: 4,
      applicableTags: []
    }
  ]
}
```

## 三、详细改造方案

### 3.1 GoodsConfigDrawer.vue 改造

#### 3.1.1 新增佣金模式切换
```vue
<!-- 基础配置部分新增 -->
<el-form-item label="佣金模式" prop="commissionMode">
  <el-radio-group v-model="formData.commissionMode" @change="handleCommissionModeChange">
    <el-radio :value="1">统一佣金（所有等级相同）</el-radio>
    <el-radio :value="2">等级差异化佣金</el-radio>
  </el-radio-group>
</el-form-item>

<!-- 统一佣金配置（commissionMode === 1 时显示） -->
<el-form-item v-if="formData.commissionMode === 1" label="统一佣金" prop="commissionValue">
  <el-input-number
    v-model="formData.commissionValue"
    :min="0"
    :max="formData.commissionType === 1 ? 100 : undefined"
    :precision="formData.commissionType === 2 ? 2 : 1"
    :step="formData.commissionType === 2 ? 0.1 : 1"
    class="!w-200px"
  />
  <span class="ml-10px">{{ formData.commissionType === 2 ? '元' : '%' }}</span>
</el-form-item>
```

#### 3.1.2 等级配置表格改造
```vue
<!-- 等级配置（commissionMode === 2 时显示） -->
<el-card v-if="formData.commissionMode === 2" class="mb-20px" shadow="never">
  <template #header>
    <div class="card-header">
      <span>等级佣金配置</span>
      <div class="flex gap-10px">
        <el-button type="primary" link @click="openUnifiedConfig">
          <Icon icon="ep:setting" class="mr-5px" />
          统一设置
        </el-button>
        <el-button type="primary" link @click="addLevelConfig">
          <Icon icon="ep:plus" class="mr-5px" />
          添加等级
        </el-button>
        <el-button type="info" link @click="importLevelConfig">
          <Icon icon="ep:upload" class="mr-5px" />
          导入配置
        </el-button>
      </div>
    </div>
  </template>

  <el-table :data="formData.levelConfigs" style="width: 100%" border>
    <el-table-column label="分销等级" prop="levelName" width="150" fixed="left">
      <template #default="scope">
        <div class="flex items-center">
          <span>{{ scope.row.levelName }}</span>
          <el-tag v-if="scope.row.isDefault" size="small" class="ml-5px">默认</el-tag>
        </div>
      </template>
    </el-table-column>
    
    <el-table-column label="佣金设置" align="center">
      <el-table-column label="本级佣金" width="120">
        <template #default="scope">
          <el-input-number
            v-model="scope.row.commissionValue"
            :min="0"
            :max="formData.commissionType === 1 ? 100 : undefined"
            :precision="2"
            :controls="false"
            size="small"
            style="width: 80px"
          />
          <span class="ml-5px">{{ formData.commissionType === 2 ? '元' : '%' }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="上级奖励" width="120">
        <template #default="scope">
          <el-input-number
            v-model="scope.row.parentReward"
            :min="0"
            :max="formData.commissionType === 1 ? 100 : undefined"
            :precision="2"
            :controls="false"
            size="small"
            style="width: 80px"
            placeholder="0"
          />
          <span class="ml-5px">{{ formData.commissionType === 2 ? '元' : '%' }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="上上级奖励" width="120">
        <template #default="scope">
          <el-input-number
            v-model="scope.row.grandParentReward"
            :min="0"
            :max="formData.commissionType === 1 ? 100 : undefined"
            :precision="2"
            :controls="false"
            size="small"
            style="width: 80px"
            placeholder="0"
          />
          <span class="ml-5px">{{ formData.commissionType === 2 ? '元' : '%' }}</span>
        </template>
      </el-table-column>
    </el-table-column>
    
    <el-table-column label="适用标签" width="200">
      <template #default="scope">
        <el-select
          v-model="scope.row.applicableTags"
          multiple
          placeholder="不限标签"
          size="small"
          style="width: 100%"
        >
          <el-option
            v-for="tag in availableTags"
            :key="tag.id"
            :label="tag.name"
            :value="tag.id"
          />
        </el-select>
      </template>
    </el-table-column>
    
    <el-table-column label="订单金额限制" align="center">
      <el-table-column label="最小金额" width="100">
        <template #default="scope">
          <el-input-number
            v-model="scope.row.minOrderAmount"
            :min="0"
            :precision="2"
            :controls="false"
            size="small"
            style="width: 80px"
            placeholder="不限"
          />
        </template>
      </el-table-column>
      
      <el-table-column label="最大金额" width="100">
        <template #default="scope">
          <el-input-number
            v-model="scope.row.maxOrderAmount"
            :min="0"
            :precision="2"
            :controls="false"
            size="small"
            style="width: 80px"
            placeholder="不限"
          />
        </template>
      </el-table-column>
    </el-table-column>
    
    <el-table-column label="状态" width="80" align="center">
      <template #default="scope">
        <el-switch
          v-model="scope.row.enabled"
          size="small"
        />
      </template>
    </el-table-column>
    
    <el-table-column label="操作" width="100" align="center" fixed="right">
      <template #default="scope">
        <el-button
          link
          type="primary"
          size="small"
          @click="editLevelConfig(scope.$index)"
        >
          编辑
        </el-button>
        <el-button
          link
          type="danger"
          size="small"
          @click="removeLevelConfig(scope.$index)"
          :disabled="formData.levelConfigs.length <= 1"
        >
          删除
        </el-button>
      </template>
    </el-table-column>
  </el-table>
  
  <!-- 批量操作提示 -->
  <el-alert
    v-if="formData.levelConfigs.length > 3"
    type="info"
    :closable="false"
    class="mt-10px"
  >
    提示：配置项较多时，建议使用"统一设置"或"导入配置"功能进行批量操作
  </el-alert>
</el-card>
```

#### 3.1.3 新增方法实现
```typescript
// 处理佣金模式切换
const handleCommissionModeChange = (value: number) => {
  if (value === 1) {
    // 切换到统一佣金，清空等级配置
    formData.levelConfigs = []
    formData.enableLevelConfig = false
  } else {
    // 切换到等级差异化，初始化默认等级配置
    formData.enableLevelConfig = true
    if (!formData.levelConfigs?.length) {
      initDefaultLevelConfigs()
    }
  }
}

// 初始化默认等级配置
const initDefaultLevelConfigs = async () => {
  try {
    const levels = await getLevelList(currentAppId.value)
    formData.levelConfigs = levels.map(level => ({
      levelId: level.id,
      levelName: level.name,
      commissionValue: formData.commissionValue || 0,
      parentReward: 0,
      grandParentReward: 0,
      applicableTags: [],
      minOrderAmount: undefined,
      maxOrderAmount: undefined,
      enabled: true
    }))
  } catch (error) {
    message.error('获取等级列表失败')
  }
}

// 导入配置
const importLevelConfig = () => {
  // 打开导入配置弹窗
  importConfigDialogRef.value?.open({
    appId: currentAppId.value,
    commissionType: formData.commissionType
  })
}

// 编辑等级配置
const editLevelConfig = (index: number) => {
  // 打开编辑弹窗
  levelConfigEditDialogRef.value?.open({
    config: formData.levelConfigs[index],
    index,
    commissionType: formData.commissionType
  })
}
```

### 3.2 新增 ConfigHistoryDialog.vue 组件

```vue
<template>
  <el-dialog
    v-model="dialogVisible"
    title="配置历史记录"
    width="80%"
    @close="handleClose"
  >
    <div v-loading="loading">
      <!-- 搜索条件 -->
      <el-form :inline="true" @submit.prevent="handleSearch">
        <el-form-item label="操作类型">
          <el-select v-model="queryParams.changeType" clearable placeholder="全部">
            <el-option label="创建" :value="1" />
            <el-option label="更新" :value="2" />
            <el-option label="删除" :value="3" />
            <el-option label="启用" :value="4" />
            <el-option label="禁用" :value="5" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作时间">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 历史记录列表 -->
      <el-table :data="historyList" style="width: 100%">
        <el-table-column label="版本" prop="version" width="80" />
        <el-table-column label="操作类型" width="100">
          <template #default="scope">
            <el-tag :type="getChangeTypeTag(scope.row.changeType)">
              {{ getChangeTypeName(scope.row.changeType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="变更内容" min-width="300">
          <template #default="scope">
            <div class="change-content">
              {{ generateChangeDescription(scope.row) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作人" prop="operatorName" width="120" />
        <el-table-column label="操作时间" prop="operateTime" width="180" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button link type="primary" @click="viewDetail(scope.row)">
              查看详情
            </el-button>
            <el-button 
              v-if="scope.$index > 0"
              link 
              type="warning" 
              @click="compareVersion(scope.row, historyList[scope.$index - 1])"
            >
              对比
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="queryParams.pageNo"
        v-model:page-size="queryParams.pageSize"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-dialog>

  <!-- 详情对话框 -->
  <el-dialog
    v-model="detailVisible"
    title="配置详情"
    width="60%"
    append-to-body
  >
    <el-descriptions :column="2" border>
      <el-descriptions-item label="版本号">{{ currentDetail.version }}</el-descriptions-item>
      <el-descriptions-item label="操作类型">
        {{ getChangeTypeName(currentDetail.changeType) }}
      </el-descriptions-item>
      <el-descriptions-item label="操作人">{{ currentDetail.operatorName }}</el-descriptions-item>
      <el-descriptions-item label="操作时间">{{ currentDetail.operateTime }}</el-descriptions-item>
      <el-descriptions-item label="变更原因" :span="2">
        {{ currentDetail.changeReason || '-' }}
      </el-descriptions-item>
    </el-descriptions>

    <el-divider>配置数据</el-divider>

    <el-tabs v-model="activeTab">
      <el-tab-pane label="变更前" name="before">
        <pre class="config-json">{{ formatJson(currentDetail.beforeData) }}</pre>
      </el-tab-pane>
      <el-tab-pane label="变更后" name="after">
        <pre class="config-json">{{ formatJson(currentDetail.afterData) }}</pre>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>

  <!-- 版本对比对话框 -->
  <ConfigCompareDialog ref="compareDialogRef" />
</template>

<script setup lang="ts">
// 实现代码...
</script>
```

### 3.3 新增 LevelConfigImportDialog.vue 组件

```vue
<template>
  <el-dialog
    v-model="dialogVisible"
    title="导入等级配置"
    width="600px"
    @close="handleClose"
  >
    <el-steps :active="activeStep" align-center class="mb-20px">
      <el-step title="选择导入方式" />
      <el-step title="配置数据" />
      <el-step title="确认导入" />
    </el-steps>

    <!-- Step 1: 选择导入方式 -->
    <div v-if="activeStep === 0">
      <el-radio-group v-model="importType" class="import-type-group">
        <el-radio-button value="template">从模板导入</el-radio-button>
        <el-radio-button value="history">从历史版本导入</el-radio-button>
        <el-radio-button value="copy">从其他商品复制</el-radio-button>
        <el-radio-button value="excel">Excel导入</el-radio-button>
      </el-radio-group>

      <!-- 模板选择 -->
      <el-card v-if="importType === 'template'" class="mt-20px" shadow="never">
        <template #header>选择配置模板</template>
        <el-radio-group v-model="selectedTemplate">
          <el-radio v-for="tpl in templates" :key="tpl.id" :label="tpl.id" class="template-item">
            <div>
              <div class="font-medium">{{ tpl.name }}</div>
              <div class="text-sm text-gray-500">{{ tpl.description }}</div>
            </div>
          </el-radio>
        </el-radio-group>
      </el-card>

      <!-- Excel上传 -->
      <el-card v-if="importType === 'excel'" class="mt-20px" shadow="never">
        <template #header>
          <div class="flex justify-between items-center">
            <span>上传Excel文件</span>
            <el-button type="primary" link @click="downloadTemplate">
              <Icon icon="ep:download" class="mr-5px" />
              下载模板
            </el-button>
          </div>
        </template>
        <el-upload
          ref="uploadRef"
          drag
          :limit="1"
          accept=".xlsx,.xls"
          :auto-upload="false"
          :on-change="handleFileChange"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">只支持 .xlsx 和 .xls 格式文件</div>
          </template>
        </el-upload>
      </el-card>
    </div>

    <!-- Step 2: 配置数据预览 -->
    <div v-if="activeStep === 1">
      <el-alert type="info" :closable="false" class="mb-20px">
        请检查导入的配置数据，确认无误后点击"确认导入"
      </el-alert>

      <el-table :data="previewData" style="width: 100%" max-height="400">
        <el-table-column label="等级名称" prop="levelName" />
        <el-table-column label="佣金值" prop="commissionValue">
          <template #default="scope">
            {{ scope.row.commissionValue }}{{ commissionType === 1 ? '%' : '元' }}
          </template>
        </el-table-column>
        <el-table-column label="上级奖励" prop="parentReward">
          <template #default="scope">
            {{ scope.row.parentReward || 0 }}{{ commissionType === 1 ? '%' : '元' }}
          </template>
        </el-table-column>
        <el-table-column label="上上级奖励" prop="grandParentReward">
          <template #default="scope">
            {{ scope.row.grandParentReward || 0 }}{{ commissionType === 1 ? '%' : '元' }}
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- Step 3: 确认导入 -->
    <div v-if="activeStep === 2">
      <el-result icon="success" title="配置已准备就绪">
        <template #sub-title>
          即将导入 {{ previewData.length }} 个等级的配置数据
        </template>
      </el-result>
    </div>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button v-if="activeStep > 0" @click="prevStep">上一步</el-button>
      <el-button 
        v-if="activeStep < 2" 
        type="primary" 
        @click="nextStep"
        :disabled="!canNext"
      >
        下一步
      </el-button>
      <el-button 
        v-if="activeStep === 2" 
        type="primary" 
        @click="handleConfirm"
        :loading="importing"
      >
        确认导入
      </el-button>
    </template>
  </el-dialog>
</template>
```

### 3.4 API 接口调整

#### 3.4.1 新增类型定义
```typescript
// types.ts 新增
export interface LevelConfig {
  levelId: string | number
  levelName: string
  commissionValue: number
  parentReward?: number
  grandParentReward?: number
  applicableTags?: string[]
  minOrderAmount?: number
  maxOrderAmount?: number
  enabled: boolean
}

export interface GoodsConfigVO {
  // 原有字段...
  commissionMode?: 1 | 2  // 1-统一佣金，2-等级差异化
  commissionValue?: number  // 统一佣金值
  enableLevelConfig?: boolean  // 是否启用等级配置
  levelConfigs?: LevelConfig[]  // 等级配置列表
  version?: number  // 版本号
}

export interface ConfigHistory {
  id: number
  goodsConfigId: number
  version: number
  changeType: number
  changeReason?: string
  operatorId: number
  operatorName: string
  operateTime: string
  beforeData?: any
  afterData?: any
}

export interface OperationLog {
  id: number
  goodsConfigId: number
  spuId: number
  spuName: string
  operationType: number
  operationTypeName: string
  operationDetail: string
  operationResult: number
  errorMessage?: string
  operatorName: string
  operateTime: string
  costTime: number
}
```

#### 3.4.2 新增API方法
```typescript
// goods-config.ts 新增

// 获取配置历史记录
export const getConfigHistoryApi = (params: {
  goodsConfigId: number
  pageNo?: number
  pageSize?: number
  changeType?: number
  startTime?: string
  endTime?: string
}) => {
  return request.get({
    url: '/distribution/product/config-history',
    params
  })
}

// 获取操作日志
export const getOperationLogApi = (params: {
  goodsConfigId?: number
  spuId?: number
  pageNo?: number
  pageSize?: number
  operationType?: number
  startTime?: string
  endTime?: string
}) => {
  return request.get({
    url: '/distribution/product/operation-log',
    params
  })
}

// 导入等级配置
export const importLevelConfigApi = (data: {
  goodsConfigId: number
  levelConfigs: LevelConfig[]
  source: string  // template | history | copy | excel
}) => {
  return request.post({
    url: '/distribution/product/import-level-config',
    data
  })
}

// 导出等级配置模板
export const exportLevelConfigTemplateApi = () => {
  return request.download({
    url: '/distribution/product/level-config-template'
  })
}

// 获取配置模板列表
export const getConfigTemplatesApi = () => {
  return request.get({
    url: '/distribution/product/config-templates'
  })
}

// 比较两个版本的配置
export const compareConfigVersionsApi = (params: {
  goodsConfigId: number
  version1: number
  version2: number
}) => {
  return request.get({
    url: '/distribution/product/compare-versions',
    params
  })
}
```

### 3.5 数据迁移提示组件

```vue
<!-- MigrationAlert.vue -->
<template>
  <el-alert
    v-if="showMigrationAlert"
    type="warning"
    :closable="false"
    class="mb-20px"
  >
    <template #title>
      <div class="flex items-center justify-between">
        <span>系统检测到您的配置使用旧版数据结构</span>
        <el-button type="primary" size="small" @click="handleMigrate">
          立即迁移
        </el-button>
      </div>
    </template>
    <div class="mt-10px">
      <p>新版本支持更灵活的等级配置，建议您迁移到新版数据结构：</p>
      <ul class="mt-5px ml-20px">
        <li>• 支持为每个等级单独设置佣金</li>
        <li>• 支持配置上级和上上级奖励</li>
        <li>• 支持按标签和订单金额设置条件</li>
      </ul>
    </div>
  </el-alert>
</template>

<script setup lang="ts">
const props = defineProps<{
  configData: any
}>()

const emit = defineEmits<{
  (e: 'migrate'): void
}>()

const showMigrationAlert = computed(() => {
  // 检测是否为旧版数据结构
  return props.configData && 
    (props.configData.firstCommissionRate !== undefined ||
     props.configData.firstCommissionAmount !== undefined)
})

const handleMigrate = () => {
  emit('migrate')
}
</script>
```

## 四、实施计划

### 4.1 第一阶段：核心功能改造（2天）
1. 修改 GoodsConfigDrawer.vue，支持新的数据结构
2. 实现统一佣金和等级差异化佣金的切换
3. 完成等级配置的增删改查功能

### 4.2 第二阶段：高级功能（2天）
1. 实现配置历史记录查看功能
2. 添加配置导入导出功能
3. 实现版本对比功能

### 4.3 第三阶段：优化和迁移（1天）
1. 添加数据迁移提示和自动迁移功能
2. 优化用户体验，添加操作引导
3. 性能优化和错误处理

## 五、注意事项

### 5.1 向后兼容性
- 保持对旧数据结构的读取兼容
- 提供数据迁移工具
- 迁移过程可逆

### 5.2 用户体验
- 提供清晰的模式切换说明
- 批量操作功能降低配置复杂度
- 实时验证和错误提示

### 5.3 性能考虑
- 等级配置数据采用虚拟滚动（超过20条时）
- 历史记录分页加载
- 配置导入采用后台异步处理

### 5.4 安全性
- 所有配置变更记录操作日志
- 敏感操作需要二次确认
- 支持配置回滚功能

## 六、测试要点

### 6.1 功能测试
- [ ] 统一佣金模式正常工作
- [ ] 等级差异化配置正常保存
- [ ] 历史记录正确记录所有变更
- [ ] 导入导出功能正常
- [ ] 数据迁移正确无误

### 6.2 兼容性测试
- [ ] 旧数据能正常显示
- [ ] 新旧数据切换无异常
- [ ] API接口向后兼容

### 6.3 性能测试
- [ ] 大量等级配置时页面响应正常
- [ ] 历史记录查询速度合理
- [ ] 批量操作不会造成卡顿

## 七、后续优化建议

1. **配置模板市场**：提供行业通用配置模板
2. **智能推荐**：基于历史数据推荐最优配置
3. **A/B测试**：支持多版本配置同时运行
4. **配置继承**：支持配置继承和覆盖机制
5. **可视化分析**：配置效果的可视化展示