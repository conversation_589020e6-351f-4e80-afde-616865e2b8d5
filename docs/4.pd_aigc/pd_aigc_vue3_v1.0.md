# AIGC功能模块迁移方案 v1.0

## 一、项目概述

### 1.1 迁移背景
将派单管理系统(paidan-admin-web)中的AIGC功能模块完整迁移到易通八爪鱼管理系统(yitong-octopus-admin-ui)。
源项目：/Users/<USER>/workspace_city/paidan-admin-web
源功能模块：/Users/<USER>/workspace_city/paidan-admin-web/src/pages/aigc
### 1.2 技术栈对比

| 项目 | 源项目 (paidan-admin-web) | 目标项目 (yitong-octopus-admin-ui) |
|------|---------------------------|-------------------------------------|
| 框架 | Vue 3.5.13 | Vue 3.5.2 |
| UI组件库 | TDesign Vue Next 1.8.1 | Element Plus 2.8.4 |
| 构建工具 | Vite | Vite 5.1.4 |
| 状态管理 | Pinia 2.1.7 | Pinia 2.1.7 |
| 路由 | Vue Router 4.5.0 | Vue Router 4.2.5 |
| HTTP客户端 | Axios 1.6.7 | Axios 1.6.5 |
| CSS框架 | TDesign样式 | UnoCSS 0.58.5 + Element Plus样式 |

## 二、AIGC模块功能清单

### 2.1 核心功能模块

1. **公式管理** (formulaManagement)
   - 公式列表展示
   - 新增/编辑/删除公式
   - 公式状态管理（草稿/发布）
   - 变量管理

2. **素材管理** (materialsManage)
   - 图片素材管理
   - 视频素材管理
   - 音频素材管理
   - 字幕管理
   - 补丁管理
   - 素材分组
   - 文件夹管理

3. **模型管理** (modelManage)
   - AI模型配置
   - 模型使用记录
   - 模型详情查看

4. **任务管理** (taskManagement)
   - 任务列表
   - 任务详情
   - 任务执行状态跟踪

5. **发布管理** (publish)
   - 发布记录
   - 发布编辑

6. **标签管理** (tagsManage)
   - 标签分组
   - 标签关联

7. **AI对话** (aiChat)
   - AI聊天功能

### 2.2 文件结构分析

```
# 源项目结构
src/pages/aigc/
├── components/
│   ├── formulaManagement/     # 公式管理组件
│   │   ├── AddMaterialOrFormula.vue
│   │   ├── AddVariable.vue
│   │   ├── CustomTabs.vue
│   │   ├── ImageCard.vue
│   │   ├── ImageMontage.vue
│   │   ├── ImageProcessing.vue
│   │   ├── ImageTextMontage.vue
│   │   ├── SelectDependencies.vue
│   │   ├── SelectEffect.vue
│   │   ├── SelectFirstImage.vue
│   │   ├── SelectFormula.vue
│   │   ├── SelectMateria.vue
│   │   ├── SelectMaterialOrFormula.vue
│   │   ├── SelectTags.vue
│   │   ├── SelectTimbre.vue
│   │   ├── SelectTransition.vue
│   │   ├── SelectVariable.vue
│   │   ├── SettingVoice.vue
│   │   ├── TagListSelector.vue
│   │   ├── TextMontage.vue
│   │   ├── VariableAndTag.vue
│   │   ├── VideoMosaic.vue
│   │   ├── VideoWithVoiceover.vue
│   │   └── WholeFilmEditing.vue
│   ├── materials/             # 素材管理组件
│   │   ├── SelectEffectIn.vue
│   │   ├── SelectFlowertext.vue
│   │   ├── TagConditionDialog.vue
│   │   ├── TagSelector.vue
│   │   ├── VideoTextPatches.vue
│   │   ├── Videopatch.vue
│   │   ├── accountSelector.vue
│   │   ├── article.vue
│   │   ├── audio.vue
│   │   ├── batchTagsRelate.vue
│   │   ├── groups.vue
│   │   ├── image.vue
│   │   ├── imageTextPatches.vue
│   │   ├── materialGroup.vue
│   │   ├── materialRelateTags.vue
│   │   ├── model.vue
│   │   ├── patch.vue
│   │   ├── patches.vue
│   │   ├── publishEdit.vue
│   │   ├── store-selector.vue
│   │   ├── subTitle.vue
│   │   ├── timbre.vue
│   │   ├── uploadMaterial.vue
│   │   └── video.vue
│   ├── modelsUsed/           # 模型使用组件
│   │   ├── modelsUsedAdd.vue
│   │   └── modelsUsedDetail.vue
│   └── taskManagement/       # 任务管理组件
│       └── taskDetail.vue
├── aiChat.vue                # AI对话
├── formulaManagement.js      # 公式管理配置
├── formulaManagement.vue     # 公式管理主页
├── formulaManagementAdd.vue  # 新增公式
├── materialGroup.vue         # 素材分组
├── materialGroupList.vue     # 素材分组列表
├── materialsManage.vue       # 素材管理
├── modelManage.vue           # 模型管理
├── modelManagement.vue       # 模型管理（另一版本）
├── modelsUsed.vue           # 模型使用
├── plans.vue                # 计划管理
├── publish.vue              # 发布管理
├── tagsManage.vue           # 标签管理
└── taskManagement.vue       # 任务管理
```

## 三、迁移策略

### 3.1 整体迁移原则

1. **保持功能完整性**：确保所有AIGC功能在新系统中正常运行
2. **适配新UI框架**：将TDesign组件转换为Element Plus组件
3. **保持API兼容**：维持与后端API的兼容性
4. **遵循目标项目规范**：遵循易通八爪鱼系统的代码规范和架构模式
5. **最大化复用现有架构**：
   - 使用现有的 `@/config/axios` 请求封装
   - 复用 Pinia 状态管理模式
   - 遵循现有的 API 模块组织方式
   - 保持一致的代码风格和命名规范

### 3.2 分阶段迁移计划

#### 第一阶段：基础架构搭建（2天）
1. 创建AIGC模块目录结构
2. 迁移API服务层（复用 @/config/axios）
3. 配置状态管理（复用 Pinia 架构）
4. 配置路由（后期通过菜单管理动态配置）

#### 第二阶段：核心功能迁移（5天）
1. 公式管理模块
2. 素材管理模块
3. 模型管理模块
4. 任务管理模块

#### 第三阶段：辅助功能迁移（2天）
1. 发布管理模块
2. 标签管理模块
3. AI对话模块
4. 其他辅助功能

#### 第四阶段：测试与优化（2天）
1. 功能测试
2. 性能优化
3. Bug修复
4. 文档完善

## 四、技术改造要点

### 4.1 组件库转换映射

| TDesign组件 | Element Plus组件 | 说明 |
|------------|-----------------|------|
| t-card | el-card | 卡片容器 |
| t-row/t-col | el-row/el-col | 栅格布局 |
| t-button | el-button | 按钮 |
| t-input | el-input | 输入框 |
| t-select | el-select | 下拉选择 |
| t-table | el-table | 表格 |
| t-dialog | el-dialog | 对话框 |
| t-form | el-form | 表单 |
| t-upload | el-upload | 文件上传 |
| t-pagination | el-pagination | 分页 |
| t-message | ElMessage | 消息提示 |
| t-tabs | el-tabs | 标签页 |
| t-tree | el-tree | 树形控件 |

### 4.2 API服务层（复用现有架构）

**使用项目现有的 axios 封装：**
```javascript
// 使用现有的 request 工具 (@/config/axios)
import request from '@/config/axios'

// AIGC API 配置示例
const AIGC_BASE_URL = 'https://aigc.paidangongju.com'

// 创建AIGC专用的API模块，遵循现有模式
export const FormulaApi = {
  // 查询列表分页
  getPage: async (params: any) => {
    return await request.get({ 
      url: `${AIGC_BASE_URL}/formula/page`, 
      params 
    })
  },
  
  // 保存公式
  create: async (data: FormulaVO) => {
    return await request.post({ 
      url: `${AIGC_BASE_URL}/formula`, 
      data 
    })
  },
  
  // 删除公式
  delete: async (id: number) => {
    return await request.delete({ 
      url: `${AIGC_BASE_URL}/formula?id=${id}` 
    })
  }
}
```

**注意事项：**
- 复用 `@/config/axios` 中的 request 实例
- 保持与现有 API 模块一致的代码风格
- 无需重写请求拦截器，使用现有配置

### 4.3 路由配置

```javascript
// 注意：本系统使用菜单管理模块动态配置路由，无需手动创建路由文件
// 以下仅作为组件路径参考，实际路由将通过菜单管理系统配置

// AIGC模块组件路径规划（放在独立目录以便区分）：
// @/pages/aigc_media/formula-management/index.vue    - 公式管理
// @/pages/aigc_media/materials-manage/index.vue       - 素材管理  
// @/pages/aigc_media/model-manage/index.vue           - 模型管理
// @/pages/aigc_media/task-management/index.vue        - 任务管理
// @/pages/aigc_media/publish/index.vue                - 发布管理
// @/pages/aigc_media/tags-manage/index.vue            - 标签管理
// @/pages/aigc_media/ai-chat/index.vue                - AI对话

// 路由配置将在菜单管理中设置，包括：
// - 路径(path)
// - 组件地址(component) 
// - 菜单名称(title)
// - 图标(icon)
// - 权限标识(permissions)
```

### 4.4 状态管理（复用 Pinia 架构）

```javascript
// 在 src/store/modules/ 下创建 aigc_media/ 目录
// 遵循现有的 Pinia store 模式

// src/store/modules/aigc_media/formula.ts
import { defineStore } from 'pinia'
import { FormulaApi } from '@/api/aigc_media/formula'

export const useFormulaStore = defineStore('aigc-media-formula', {
  state: () => ({
    formulaList: [],
    currentFormula: null,
    loading: false
  }),
  
  actions: {
    // 获取公式列表
    async fetchFormulaList(params: any) {
      this.loading = true
      try {
        const data = await FormulaApi.getPage(params)
        this.formulaList = data.list
        return data
      } finally {
        this.loading = false
      }
    },
    
    // 设置当前公式
    setCurrentFormula(formula: any) {
      this.currentFormula = formula
    }
  }
})

// 类似地创建其他 store 模块：
// - useMaterialStore  (素材管理)
// - useModelStore     (模型管理)
// - useTaskStore      (任务管理)
```

**注意事项：**
- 遵循项目现有的 Pinia 使用规范
- 保持与其他模块一致的命名规则
- 复用现有的状态管理模式，不重新发明轮子

## 五、具体迁移步骤

### 5.1 创建目录结构

```bash
# 在目标项目中创建AIGC模块目录（使用独立目录便于管理）
src/
├── pages/aigc_media/              # AIGC媒体管理视图组件（独立目录）
│   ├── formula-management/        # 公式管理
│   ├── materials-manage/          # 素材管理
│   ├── model-manage/              # 模型管理
│   ├── task-management/           # 任务管理
│   ├── publish/                   # 发布管理
│   ├── tags-manage/               # 标签管理
│   └── ai-chat/                   # AI对话
├── api/aigc_media/                # AIGC API服务
├── store/modules/aigc_media/      # AIGC状态管理
└── components/aigc_media/         # AIGC公共组件

# 注：路由通过菜单管理系统动态配置，无需创建router文件
```

### 5.2 组件迁移示例

以公式管理列表页为例：

**原代码（TDesign）:**
```vue
<template>
  <t-card>
    <t-row :gutter="[16, 16]">
      <t-col :span="3">
        <t-input v-model="searchForm.key" placeholder="公式标题" />
      </t-col>
      <t-col :span="3">
        <t-button theme="primary" @click="handleSearch">搜索</t-button>
      </t-col>
    </t-row>
    <t-table :data="tableData" :columns="columns" />
  </t-card>
</template>
```

**转换后（Element Plus）:**
```vue
<template>
  <el-card>
    <el-row :gutter="16">
      <el-col :span="6">
        <el-input v-model="searchForm.key" placeholder="公式标题" />
      </el-col>
      <el-col :span="6">
        <el-button type="primary" @click="handleSearch">搜索</el-button>
      </el-col>
    </el-row>
    <el-table :data="tableData">
      <el-table-column v-for="col in columns" :key="col.prop" v-bind="col" />
    </el-table>
  </el-card>
</template>
```

### 5.3 样式处理

1. **移除TDesign样式依赖**
2. **使用UnoCSS原子化CSS**
3. **必要时编写局部样式覆盖**

```scss
// 在组件中使用scoped样式
<style lang="scss" scoped>
.aigc-container {
  // 自定义样式
}
</style>
```

## 六、注意事项

### 6.1 兼容性问题

1. **组件事件差异**
   - TDesign和Element Plus的事件名称可能不同
   - 需要逐个检查和调整事件处理

2. **组件属性差异**
   - 某些组件属性名称和用法可能不同
   - 需要参考Element Plus文档进行调整

3. **样式冲突**
   - 避免全局样式污染
   - 使用scoped样式或CSS Module

### 6.2 性能优化

1. **按需加载**
   - 使用动态import实现路由懒加载
   - 大型组件库按需引入

2. **代码分割**
   - 合理拆分代码块
   - 避免单个chunk过大

3. **缓存策略**
   - 利用浏览器缓存
   - 实现接口数据缓存

### 6.3 测试要点

1. **功能测试**
   - 所有CRUD操作正常
   - 文件上传下载功能正常
   - 分页、搜索、筛选功能正常

2. **兼容性测试**
   - 不同浏览器兼容性
   - 响应式布局测试

3. **性能测试**
   - 页面加载速度
   - 大数据量渲染性能

## 七、风险评估

### 7.1 技术风险

| 风险项 | 风险等级 | 应对措施 |
|--------|---------|----------|
| API接口不兼容 | 中 | 提前与后端沟通，必要时做适配层 |
| 组件功能差异 | 低 | 充分测试，必要时自定义组件 |
| 性能问题 | 中 | 做好性能监控和优化 |
| 样式冲突 | 低 | 使用模块化CSS方案 |

### 7.2 时间风险

- 总预估时间：11个工作日
- 建议预留20%缓冲时间
- 关键路径：API适配和核心功能迁移

## 八、后续优化建议

1. **代码重构**
   - 提取公共组件
   - 优化代码结构
   - 统一编码规范

2. **功能增强**
   - 添加更多的AI模型支持
   - 优化用户体验
   - 增加批量操作功能

3. **性能优化**
   - 实现虚拟滚动
   - 优化大文件上传
   - 添加缓存机制

4. **文档完善**
   - 编写详细的使用文档
   - 添加代码注释
   - 创建开发指南

## 九、迁移进度跟踪

### 当前进度：第二阶段进行中 (2025-01-10)

| 阶段 | 任务 | 状态 | 完成时间 | 备注 |
|------|------|------|----------|------|
| **第一阶段** | | | | |
| | 创建AIGC模块目录结构 | ✅ 完成 | 2025-01-10 | 已创建所有必要目录 |
| | 迁移API服务层 | ✅ 完成 | 2025-01-10 | 已创建formula和materials API |
| | 配置状态管理 | ✅ 完成 | 2025-01-10 | 已创建formula和materials store |
| **第二阶段** | | | | |
| | 迁移公式管理模块 | ✅ 完成 | 2025-01-10 | 已完成主页面和4个子组件 |
| | 迁移素材管理模块 | ✅ 完成 | 2025-01-10 | 已完成所有素材管理子组件 |
| | 迁移模型管理模块 | ✅ 完成 | 2025-01-10 | 已完成模型管理功能和两步创建工作流 |
| | 迁移任务管理模块 | ✅ 完成 | 2025-01-10 | 已完成任务管理、详情页和预览功能 |
| **第三阶段** | | | | |
| | 迁移发布管理模块 | ✅ 完成 | 2025-01-10 | 已完成发布管理和账号/门店配置功能 |
| | 迁移标签管理模块 | ✅ 完成 | 2025-01-10 | 已完成标签管理主页面和API |
| | 迁移AI对话模块 | ✅ 完成 | 2025-01-10 | 已完成AI对话功能和模拟SSE流式响应 |
| **第四阶段** | | | | |
| | 功能测试 | ⏳ 待开始 | - | - |
| | 性能优化 | ⏳ 待开始 | - | - |
| | Bug修复 | ⏳ 待开始 | - | - |
| | 文档完善 | ⏳ 待开始 | - | - |

### 已完成文件清单

#### 目录结构
```
✅ src/pages/aigc_media/
   ├── formula-management/
   ├── materials-manage/
   ├── model-manage/
   ├── task-management/
   ├── publish/
   ├── tags-manage/
   └── ai-chat/

✅ src/api/aigc_media/
   ├── config.ts
   ├── formula/index.ts
   ├── materials/index.ts
   └── ...

✅ src/store/modules/aigc_media/
   ├── formula.ts
   ├── materials.ts
   └── ...

✅ src/components/aigc_media/
   ├── formula/
   ├── materials/
   └── common/
```

#### API文件
- ✅ `/src/api/aigc_media/config.ts` - API配置
- ✅ `/src/api/aigc_media/formula/index.ts` - 公式管理API
- ✅ `/src/api/aigc_media/materials/index.ts` - 素材管理API
- ✅ `/src/api/aigc_media/tags/index.ts` - 标签管理API
- ✅ `/src/api/aigc_media/model/index.ts` - 模型管理API
- ✅ `/src/api/aigc_media/task/index.ts` - 任务管理API
- ✅ `/src/api/aigc_media/publish/index.ts` - 发布管理API
- ✅ `/src/api/aigc_media/chat/index.ts` - AI对话API

#### Store文件
- ✅ `/src/store/modules/aigc_media/formula.ts` - 公式管理状态
- ✅ `/src/store/modules/aigc_media/materials.ts` - 素材管理状态
- ✅ `/src/store/modules/aigc_media/tags.ts` - 标签管理状态
- ✅ `/src/store/modules/aigc_media/model.ts` - 模型管理状态
- ✅ `/src/store/modules/aigc_media/task.ts` - 任务管理状态
- ✅ `/src/store/modules/aigc_media/publish.ts` - 发布管理状态
- ✅ `/src/store/modules/aigc_media/chat.ts` - AI对话状态

#### 页面组件文件
- ✅ `/src/pages/aigc_media/formula-management/index.vue` - 公式管理主页面
- ✅ `/src/pages/aigc_media/formula-management/add.vue` - 公式添加/编辑页面
- ✅ `/src/pages/aigc_media/formula-management/components/VideoWithVoiceover.vue` - 视频配音组件
- ✅ `/src/pages/aigc_media/formula-management/components/TextMontage.vue` - 文案拼接组件
- ✅ `/src/pages/aigc_media/formula-management/components/ImageProcessing.vue` - 图片处理组件
- ✅ `/src/pages/aigc_media/formula-management/components/ImageMontage.vue` - 图片拼接组件
- ✅ `/src/pages/aigc_media/materials-manage/index.vue` - 素材管理主页面
- ✅ `/src/pages/aigc_media/materials-manage/components/Groups.vue` - 分组树组件
- ✅ `/src/pages/aigc_media/materials-manage/components/VideoContainer.vue` - 视频素材管理组件
- ✅ `/src/pages/aigc_media/materials-manage/components/ImageContainer.vue` - 图片素材管理组件
- ✅ `/src/pages/aigc_media/materials-manage/components/AudioContainer.vue` - 音频素材管理组件
- ✅ `/src/pages/aigc_media/materials-manage/components/ArticleContainer.vue` - 文案素材管理组件
- ✅ `/src/pages/aigc_media/materials-manage/components/VideoTextPatches.vue` - 视频文字样式管理组件
- ✅ `/src/pages/aigc_media/materials-manage/components/ImageTextPatches.vue` - 图片文字样式管理组件
- ✅ `/src/pages/aigc_media/materials-manage/components/TimbreContainer.vue` - 音色管理组件
- ✅ `/src/pages/aigc_media/materials-manage/components/SubtitleContainer.vue` - 字幕管理组件
- ✅ `/src/pages/aigc_media/tags-manage/index.vue` - 标签管理主页面
- ✅ `/src/pages/aigc_media/model-manage/index.vue` - 模型管理主页面
- ✅ `/src/pages/aigc_media/model-manage/components/ModelDialog.vue` - 模型创建/编辑对话框
- ✅ `/src/pages/aigc_media/task-manage/index.vue` - 任务管理主页面
- ✅ `/src/pages/aigc_media/task-manage/detail.vue` - 任务详情页面
- ✅ `/src/pages/aigc_media/task-manage/components/TaskLogDialog.vue` - 任务日志对话框
- ✅ `/src/pages/aigc_media/publish-manage/index.vue` - 发布管理主页面
- ✅ `/src/pages/aigc_media/publish-manage/components/PublishRuleDialog.vue` - 发布规则对话框
- ✅ `/src/pages/aigc_media/publish-manage/components/AccountSelector.vue` - 账号选择器
- ✅ `/src/pages/aigc_media/publish-manage/components/StoreSelector.vue` - 门店选择器
- ✅ `/src/pages/aigc_media/ai-chat/index.vue` - AI对话主页面
- ✅ `/src/components/UploadExcel/index.vue` - Excel上传组件
- ✅ `/src/utils/mockSSE.ts` - 模拟SSE响应工具类

## 十、总结

AIGC模块迁移是一个系统性工程，需要：

1. **充分的前期准备**：深入理解源系统和目标系统的技术架构
2. **合理的迁移策略**：分阶段、分模块逐步推进
3. **严格的质量控制**：充分测试，确保功能完整性
4. **持续的优化改进**：迁移完成后持续优化和完善

通过遵循本方案，可以有效降低迁移风险，确保AIGC功能在新系统中的成功落地。

---

文档版本：v1.0  
创建日期：2025-01-10  
作者：Claude Code Assistant
