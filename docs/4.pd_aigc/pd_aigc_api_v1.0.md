# AIGC Media 模块集成方案 v1.0

## 一、模块概述

### 1.1 功能定位
AIGC Media 模块是一个专门用于小红书笔记内容生成的AI内容生产系统，提供了从素材管理、内容生成到发布管理的完整解决方案。
进度文档：/docs/4.pd_aigc/AIGC_MEDIA_INTEGRATION_PROGRESS.md

### 1.2 核心功能
- **素材管理**：图片、视频、文本等多媒体素材的存储和管理
- **内容生成**：基于AI模型的笔记内容自动生成
- **公式配置**：灵活的内容生成公式和模板配置
- **任务调度**：分布式任务执行和调度管理
- **发布管理**：多平台内容发布和账号管理

## 二、问题分析与解决方案

### 2.1 包结构问题

#### 现状问题
1. **包名不一致**：
   - API层使用 `com.yitong.octopus.module.aigc`
   - BIZ层使用 `com.yitong.octopus.module.aigc_media`
   - 不符合Java包命名规范（不应使用下划线）

2. **重复的基础类**：
   - 自定义的 `BaseCrudDO`、`BaseDelDO` 与框架的 `BaseDO`、`BaseCuDO` 功能重复
   - 字段类型不一致（Long vs String for creator/updater）

#### 解决方案
```java
// 统一包名为：com.yitong.octopus.module.aigc_media
// 将API层的 aigc 包名改为 aigc_media，保持一致性

// 使用框架提供的基础类
// 将 BaseCrudDO 替换为 BaseDO
// 将 BaseDelDO 替换为 BaseCuDO
```

### 2.2 框架组件复用

#### 可复用组件清单

| 组件类型 | 框架提供 | 当前模块 | 集成方案 |
|---------|---------|----------|---------|
| **基础实体** | BaseDO, BaseCuDO | BaseCrudDO, BaseDelDO | 替换为框架基础类 |
| **响应包装** | CommonResult | 自定义响应 | 使用 CommonResult |
| **分页处理** | PageParam, PageResult | PageReq | 替换为框架分页类 |
| **异常处理** | GlobalExceptionHandler | CustomExceptionHandler | 使用框架异常处理 |
| **Redis配置** | RedisTemplateConfiguration | RedisConfiguration | 使用框架Redis配置 |
| **MyBatis配置** | MybatisPlusAutoConfiguration | MybatisPlusConfig | 使用框架配置 |
| **安全认证** | SecurityFrameworkUtils | 自定义权限 | 集成框架安全组件 |
| **文件上传** | FileApi | IUploadService | 使用框架文件API |
| **字典管理** | DictDataApi | ISysDictDataService | 使用框架字典API |

### 2.3 数据模型改造

#### 字段映射方案
```sql
-- 原字段映射到框架字段
-- createBy (Long) -> creator (String)
-- updateBy (Long) -> updater (String)
-- createTime -> createTime (保持不变)
-- updateTime -> updateTime (保持不变)
-- deleted (Integer) -> deleted (Boolean)

-- 数据迁移SQL示例
ALTER TABLE aigc_* MODIFY COLUMN creator VARCHAR(64) COMMENT '创建者';
ALTER TABLE aigc_* MODIFY COLUMN updater VARCHAR(64) COMMENT '更新者';
ALTER TABLE aigc_* MODIFY COLUMN deleted BIT(1) DEFAULT b'0' COMMENT '是否删除';
```

### 2.4 服务层改造

#### 服务接口规范
```java
// 遵循项目统一的服务命名规范
// 接口：*Service
// 实现：*ServiceImpl

// 示例改造：
public interface AigcMediaService {
    // 统一使用框架的分页参数
    PageResult<AigcMediaRespVO> getMediaPage(AigcMediaPageReqVO pageReqVO);
    
    // 统一返回 CommonResult
    CommonResult<Long> createMedia(AigcMediaCreateReqVO createReqVO);
}
```

## 三、集成步骤

### 3.1 第一阶段：基础框架对接
1. **包名重构**
   - 将API层的 `aigc` 包名改为 `aigc_media`
   - 统一所有模块使用 `com.yitong.octopus.module.aigc_media` 包名

2. **基础类替换**
   - 替换所有 DO 基类
   - 修改字段类型和名称
   - 更新 Mapper XML 文件

3. **配置类清理**
   - 删除重复的配置类
   - 使用框架提供的配置

### 3.2 第二阶段：业务功能改造
1. **Controller层改造**
   - 统一使用 `@Tag` 注解
   - 统一使用 `CommonResult` 返回
   - 集成 `@PreAuthorize` 权限控制

2. **Service层改造**
   - 使用框架的事务管理
   - 集成租户隔离（如需要）
   - 使用框架的缓存管理

3. **数据访问层改造**
   - 使用框架的 MyBatis Plus 配置
   - 集成数据权限（如需要）

### 3.3 第三阶段：功能增强
1. **集成操作日志**
   - 使用 `@OperateLog` 注解记录操作

2. **集成数据字典**
   - 将枚举转换为字典数据
   - 使用 DictDataApi 管理

3. **集成文件管理**
   - 使用 FileApi 替代自定义文件服务
   - 统一文件存储路径

## 四、API 规范

### 4.1 RESTful 接口规范

#### 基础 CRUD 接口
```java
@RestController
@RequestMapping("/admin/aigc/media")
@Tag(name = "管理后台 - AIGC媒体管理")
@Validated
public class AigcMediaController {
    
    @PostMapping("/create")
    @Operation(summary = "创建媒体内容")
    @PreAuthorize("@ss.hasPermission('aigc:media:create')")
    public CommonResult<Long> createMedia(@Valid @RequestBody AigcMediaCreateReqVO createReqVO) {
        return success(mediaService.createMedia(createReqVO));
    }
    
    @PutMapping("/update")
    @Operation(summary = "更新媒体内容")
    @PreAuthorize("@ss.hasPermission('aigc:media:update')")
    public CommonResult<Boolean> updateMedia(@Valid @RequestBody AigcMediaUpdateReqVO updateReqVO) {
        mediaService.updateMedia(updateReqVO);
        return success(true);
    }
    
    @DeleteMapping("/delete")
    @Operation(summary = "删除媒体内容")
    @Parameter(name = "id", description = "媒体ID", required = true)
    @PreAuthorize("@ss.hasPermission('aigc:media:delete')")
    public CommonResult<Boolean> deleteMedia(@RequestParam("id") Long id) {
        mediaService.deleteMedia(id);
        return success(true);
    }
    
    @GetMapping("/get")
    @Operation(summary = "获取媒体详情")
    @Parameter(name = "id", description = "媒体ID", required = true)
    @PreAuthorize("@ss.hasPermission('aigc:media:query')")
    public CommonResult<AigcMediaRespVO> getMedia(@RequestParam("id") Long id) {
        return success(mediaService.getMedia(id));
    }
    
    @GetMapping("/page")
    @Operation(summary = "获取媒体分页")
    @PreAuthorize("@ss.hasPermission('aigc:media:query')")
    public CommonResult<PageResult<AigcMediaRespVO>> getMediaPage(@Valid AigcMediaPageReqVO pageVO) {
        return success(mediaService.getMediaPage(pageVO));
    }
}
```

### 4.2 核心业务接口

#### 任务管理接口
```java
@PostMapping("/task/submit")
@Operation(summary = "提交生成任务")
@PreAuthorize("@ss.hasPermission('aigc:task:create')")
public CommonResult<Long> submitTask(@Valid @RequestBody AigcTaskSubmitReqVO submitVO) {
    return success(taskService.submitTask(submitVO));
}

@GetMapping("/task/status")
@Operation(summary = "查询任务状态")
@Parameter(name = "taskId", description = "任务ID", required = true)
public CommonResult<AigcTaskStatusRespVO> getTaskStatus(@RequestParam("taskId") Long taskId) {
    return success(taskService.getTaskStatus(taskId));
}

@PostMapping("/task/cancel")
@Operation(summary = "取消任务")
@Parameter(name = "taskId", description = "任务ID", required = true)
@PreAuthorize("@ss.hasPermission('aigc:task:cancel')")
public CommonResult<Boolean> cancelTask(@RequestParam("taskId") Long taskId) {
    taskService.cancelTask(taskId);
    return success(true);
}
```

#### 素材管理接口
```java
@PostMapping("/material/upload")
@Operation(summary = "上传素材")
@PreAuthorize("@ss.hasPermission('aigc:material:upload')")
public CommonResult<AigcMaterialUploadRespVO> uploadMaterial(
        @RequestParam("file") MultipartFile file,
        @RequestParam("type") String type) {
    return success(materialService.uploadMaterial(file, type));
}

@GetMapping("/material/list")
@Operation(summary = "获取素材列表")
@PreAuthorize("@ss.hasPermission('aigc:material:query')")
public CommonResult<List<AigcMaterialRespVO>> getMaterialList(
        @RequestParam("type") String type) {
    return success(materialService.getMaterialList(type));
}
```

#### 公式配置接口
```java
@PostMapping("/formula/create")
@Operation(summary = "创建生成公式")
@PreAuthorize("@ss.hasPermission('aigc:formula:create')")
public CommonResult<Long> createFormula(@Valid @RequestBody AigcFormulaCreateReqVO createVO) {
    return success(formulaService.createFormula(createVO));
}

@PostMapping("/formula/preview")
@Operation(summary = "预览公式效果")
@PreAuthorize("@ss.hasPermission('aigc:formula:preview')")
public CommonResult<AigcFormulaPreviewRespVO> previewFormula(
        @Valid @RequestBody AigcFormulaPreviewReqVO previewVO) {
    return success(formulaService.previewFormula(previewVO));
}
```

## 五、数据库设计优化

### 5.1 表名规范化
为了与现有项目表区分，所有 AIGC Media 模块的表需要添加 `yt_` 前缀：

| 原表名 | 新表名 |
|--------|--------|
| aigc_formula | yt_aigc_formula |
| aigc_formula_track | yt_aigc_formula_track |
| aigc_media | yt_aigc_media |
| aigc_model | yt_aigc_model |
| aigc_task | yt_aigc_task |
| aigc_publish | yt_aigc_publish |
| aigc_oss_file | yt_aigc_oss_file |
| aigc_tag_group | yt_aigc_tag_group |
| 其他表... | yt_aigc_... |

### 5.2 表结构规范化

#### 5.2.1 添加租户支持
所有业务表需要添加 `tenant_id` 字段，支持多租户隔离：

```sql
-- 为所有表添加租户字段
ALTER TABLE `yt_aigc_*` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_*` ADD INDEX `idx_tenant_id` (`tenant_id`);
```

#### 5.2.2 字段标准化改造
```sql
-- 示例：yt_aigc_task 表改造
CREATE TABLE `yt_aigc_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID',
  `name` varchar(255) DEFAULT NULL COMMENT '任务名称',
  `model_id` bigint(20) DEFAULT NULL COMMENT '模型ID',
  `type` tinyint(4) DEFAULT NULL COMMENT '生成方式',
  `total_num` int(11) DEFAULT NULL COMMENT '生成总数量',
  `used_num` int(11) DEFAULT NULL COMMENT '已使用数量',
  `generated_num` int(11) DEFAULT NULL COMMENT '已生成数量',
  `cancel_num` int(11) DEFAULT '0' COMMENT '已作废数量',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态',
  `remark` varchar(512) DEFAULT NULL COMMENT '任务描述',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_tenant_model` (`tenant_id`, `model_id`),
  KEY `idx_tenant_status` (`tenant_id`, `status`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AIGC任务表';
```

### 5.3 数据迁移脚本

#### 5.3.1 批量重命名表
```sql
-- 批量重命名所有aigc表，添加yt_前缀
RENAME TABLE 
  aigc_formula TO yt_aigc_formula,
  aigc_formula_track TO yt_aigc_formula_track,
  aigc_formula_track_clip TO yt_aigc_formula_track_clip,
  aigc_formula_track_materialset_ratio TO yt_aigc_formula_track_materialset_ratio,
  aigc_media TO yt_aigc_media,
  aigc_media_producing_job TO yt_aigc_media_producing_job,
  aigc_media_producing_job_variable_value TO yt_aigc_media_producing_job_variable_value,
  aigc_media_track TO yt_aigc_media_track,
  aigc_media_track_material TO yt_aigc_media_track_material,
  aigc_model TO yt_aigc_model,
  aigc_model_item TO yt_aigc_model_item,
  aigc_model_scene TO yt_aigc_model_scene,
  aigc_model_scene_storyboard TO yt_aigc_model_scene_storyboard,
  aigc_model_varible TO yt_aigc_model_varible,
  aigc_oss_file TO yt_aigc_oss_file,
  aigc_publish TO yt_aigc_publish,
  aigc_publish_account TO yt_aigc_publish_account,
  aigc_publish_record TO yt_aigc_publish_record,
  aigc_publish_task TO yt_aigc_publish_task,
  aigc_speech_voice TO yt_aigc_speech_voice,
  aigc_speech_voice_emotion TO yt_aigc_speech_voice_emotion,
  aigc_tag_category TO yt_aigc_tag_category,
  aigc_tag_group TO yt_aigc_tag_group,
  aigc_tag_info_keys TO yt_aigc_tag_info_keys,
  aigc_tag_info_rel TO yt_aigc_tag_info_rel,
  aigc_tag_info_values TO yt_aigc_tag_info_values,
  aigc_tag_object_rel TO yt_aigc_tag_object_rel,
  aigc_tag_object_types TO yt_aigc_tag_object_types,
  aigc_tag_rel_types TO yt_aigc_tag_rel_types,
  aigc_task TO yt_aigc_task,
  aigc_task_media_his TO yt_aigc_task_media_his,
  aigc_task_record TO yt_aigc_task_record,
  aigc_task_variable TO yt_aigc_task_variable,
  aigc_transition TO yt_aigc_transition,
  aigc_variable TO yt_aigc_variable,
  aigc_vfx_config TO yt_aigc_vfx_config,
  aigc_word_style TO yt_aigc_word_style,
  font_config TO yt_font_config;
```

#### 5.3.2 添加租户字段
```sql
-- 为所有业务表添加租户ID字段
ALTER TABLE yt_aigc_formula ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_formula_track ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_media ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_model ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_task ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_publish ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_oss_file ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_tag_group ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `group_id`;
-- 继续为其他表添加...

-- 添加租户索引
ALTER TABLE yt_aigc_formula ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE yt_aigc_formula_track ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE yt_aigc_media ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE yt_aigc_model ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE yt_aigc_task ADD INDEX `idx_tenant_id` (`tenant_id`);
-- 继续为其他表添加索引...
```

#### 5.3.3 字段类型转换
```sql
-- 转换 create_by/update_by 从 bigint 到 varchar
ALTER TABLE yt_aigc_formula 
  MODIFY COLUMN `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  MODIFY COLUMN `update_by` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_media 
  MODIFY COLUMN `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  MODIFY COLUMN `update_by` varchar(64) DEFAULT '' COMMENT '更新者';

-- 转换 del_flag 到 deleted
ALTER TABLE yt_aigc_media 
  CHANGE COLUMN `del_flag` `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除';

ALTER TABLE yt_aigc_model 
  CHANGE COLUMN `del_flag` `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除';

-- 为所有表执行类似转换...
```

#### 5.3.4 重命名字段为框架标准
```sql
-- 统一字段命名规范
ALTER TABLE yt_aigc_formula 
  CHANGE COLUMN `create_by` `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  CHANGE COLUMN `update_by` `updater` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_media 
  CHANGE COLUMN `create_by` `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  CHANGE COLUMN `update_by` `updater` varchar(64) DEFAULT '' COMMENT '更新者';

-- 继续为其他表执行...
```

### 5.4 索引优化策略

#### 5.4.1 租户相关索引
```sql
-- 所有查询都需要包含tenant_id，建立复合索引
ALTER TABLE yt_aigc_task ADD INDEX `idx_tenant_status` (`tenant_id`, `status`);
ALTER TABLE yt_aigc_task ADD INDEX `idx_tenant_model` (`tenant_id`, `model_id`);
ALTER TABLE yt_aigc_media ADD INDEX `idx_tenant_type` (`tenant_id`, `type`);
ALTER TABLE yt_aigc_media ADD INDEX `idx_tenant_formula` (`tenant_id`, `formula_id`);
```

#### 5.4.2 业务查询优化索引
```sql
-- 任务查询优化
ALTER TABLE yt_aigc_task_record ADD INDEX `idx_tenant_task_status` (`tenant_id`, `task_id`, `status`);
ALTER TABLE yt_aigc_publish_record ADD INDEX `idx_tenant_publish` (`tenant_id`, `publish_id`);

-- 媒体查询优化
ALTER TABLE yt_aigc_media_track ADD INDEX `idx_tenant_media` (`tenant_id`, `media_id`);
ALTER TABLE yt_aigc_media_track_material ADD INDEX `idx_tenant_track` (`tenant_id`, `track_id`);
```

### 5.5 数据字典迁移方案

#### 5.5.1 需要迁移到系统字典的数据

以下配置类数据将迁移到系统字典表 `system_dict_type` 和 `system_dict_data`：

| 原表/枚举 | 字典类型 | 说明 |
|-----------|----------|------|
| 语音引擎类型 | aigc_speech_engine_type | TTS引擎配置 |
| 媒体类型 | aigc_media_type | 媒体文件类型 |
| 媒体状态 | aigc_media_status | 处理状态 |
| 公式状态 | aigc_formula_status | 公式发布状态 |
| 任务状态 | aigc_task_status | 任务执行状态 |
| 模型类型/状态 | aigc_model_type/status | AI模型配置 |
| 发布平台 | aigc_platform_type | 发布渠道 |
| 素材类型 | aigc_material_type | 素材分类 |
| 转场类型 | aigc_transition_type | 转场特效类型（仅字典） |
| 特效类型 | aigc_vfx_type | 特效类型（仅字典） |
| 字体样式类型 | aigc_font_style_type | 字体样式 |
| 背景类型 | aigc_background_type | 背景配置 |
| 对齐方式 | aigc_align_type | 文字对齐 |

#### 5.5.2 保留独立表的数据

以下表因为包含复杂配置，保留为独立表：

| 表名 | 处理方式 | 原因 |
|------|----------|------|
| yt_font_config | 保留独立表+添加tenant_id | 包含字体文件URL等复杂配置 |
| yt_aigc_speech_voice | 保留独立表+添加tenant_id | 音色配置复杂，包含多语言支持 |
| yt_aigc_word_style | 保留独立表+添加tenant_id | 样式配置复杂，包含多个样式参数 |
| yt_aigc_transition | 保留独立表+添加tenant_id | 转场特效配置，包含特效文件 |
| yt_aigc_vfx_config | 保留独立表+添加tenant_id | 视觉特效配置，包含特效资源 |

```sql
-- font_config 保留为独立表，添加租户支持
ALTER TABLE yt_font_config ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户ID(0表示系统级)' AFTER `id`;
ALTER TABLE yt_font_config ADD INDEX `idx_tenant_id` (`tenant_id`);

-- 字段标准化
ALTER TABLE yt_font_config 
  CHANGE COLUMN `create_by` `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  CHANGE COLUMN `update_by` `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  CHANGE COLUMN `del_flag` `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除';
```

#### 5.5.3 字典数据示例

```sql
-- 创建AIGC相关字典类型
INSERT INTO system_dict_type (dict_name, dict_type, status, remark, creator, create_time)
VALUES 
  ('AIGC媒体类型', 'aigc_media_type', 0, 'AIGC媒体类型配置', '1', NOW()),
  ('AIGC任务状态', 'aigc_task_status', 0, 'AIGC任务执行状态', '1', NOW()),
  ('AIGC发布平台', 'aigc_platform_type', 0, 'AIGC内容发布平台', '1', NOW());

-- 插入字典数据
INSERT INTO system_dict_data (dict_type, dict_label, dict_value, dict_sort, status, remark)
VALUES 
  ('aigc_media_type', '视频', 'VIDEO', 1, 0, '视频类型'),
  ('aigc_media_type', '图片', 'IMAGE', 2, 0, '图片类型'),
  ('aigc_task_status', '草稿', '1', 1, 0, '任务草稿'),
  ('aigc_task_status', '执行中', '2', 2, 0, '任务执行中'),
  ('aigc_platform_type', '小红书', '1', 1, 0, '小红书平台'),
  ('aigc_platform_type', '抖音', '2', 2, 0, '抖音平台');
```

## 六、性能优化建议

### 6.1 缓存策略
- 使用 Redis 缓存热点数据
- 实现多级缓存架构
- 合理设置缓存过期时间

### 6.2 异步处理
- 使用消息队列处理耗时任务
- 实现任务状态异步通知
- 批量处理优化性能

### 6.3 分布式处理
- 使用分布式锁避免重复执行
- 实现任务分片处理
- 支持横向扩展

## 七、安全性增强

### 7.1 权限控制
- 细粒度的功能权限控制
- 数据权限隔离
- 操作审计日志

### 7.2 数据安全
- 敏感数据加密存储
- 传输层加密
- SQL注入防护

## 八、监控与运维

### 8.1 监控指标
- 任务执行成功率
- 平均处理时间
- 系统资源使用率

### 8.2 告警机制
- 任务失败告警
- 性能阈值告警
- 异常日志告警

## 九、迁移计划

### 9.1 迁移时间表
- **第1周**：基础框架对接，包名重构
- **第2周**：数据模型改造，服务层改造
- **第3周**：功能测试，问题修复
- **第4周**：性能优化，上线准备

### 9.2 风险控制
- 保留原始代码备份
- 分模块逐步迁移
- 充分的测试覆盖

## 十、后续优化计划

### 10.1 短期优化（1-2个月）
- 完成框架集成
- 优化性能瓶颈
- 完善监控体系

### 10.2 长期规划（3-6个月）
- 支持更多AI模型
- 扩展更多内容平台
- 提升系统可扩展性

## 附录：改造清单

### 需要修改的文件列表
1. **包名修改**：API层 Java 文件的包名从 `aigc` 改为 `aigc_media`
2. **基类替换**：所有 DO 文件继承框架基类
3. **配置文件**：删除重复配置，使用框架配置
4. **Mapper XML**：更新字段映射
5. **Controller**：统一返回类型和注解

### 数据库迁移脚本
```sql
-- 批量修改表结构的脚本将在实施时提供
-- 包括字段类型转换、索引优化等
```

---

**文档版本**：v1.0  
**更新日期**：2025-08-10  
**作者**：AIGC团队  
**审核**：待审核