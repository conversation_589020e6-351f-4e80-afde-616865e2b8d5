# 分销系统重写实施计划

## 项目信息

- **模块位置**：`yitong-module-promotion/yitong-module-distribution`
- **包路径**：`com.yitong.octopus.module.distribution`
- **总工期**：15个工作日（3周）
- **开发策略**：完全重写，任务驱动，每日交付

## 实施策略

**核心原则**：
1. **完全重写**：在 `yitong-module-promotion` 下创建新的 `yitong-module-distribution` 模块
2. **任务驱动**：每个任务都是独立的编码任务，完成后必须能编译通过
3. **最小可运行**：从最简单的功能开始，逐步增加复杂度
4. **避免丢失代码**：每个任务完成后立即提交，确保代码不丢失
5. **单一职责**：每个类不超过500行，功能职责单一

**服务层规范**：
- 服务接口必须继承 `IService<对应的DO>`，例如：`DistAgentService extends IService<DistAgentDO>`
- 服务实现类必须继承 `ServiceImpl<对应的Mapper, 对应的DO>` 并实现对应的服务接口
- 例如：`DistAgentServiceImpl extends ServiceImpl<DistAgentMapper, DistAgentDO> implements DistAgentService`
- 这样可以自动获得MyBatis-Plus提供的基础CRUD方法

**模块结构规划**：
```
yitong-module-promotion/
├── yitong-module-activity/     # 现有活动模块
├── yitong-module-booking/      # 现有预约模块
├── yitong-module-lottery/      # 现有抽奖模块
├── yitong-module-topic/        # 现有话题模块
└── yitong-module-distribution/ # 新建分销模块
    ├── pom.xml
    ├── yitong-module-distribution-api/
    │   ├── pom.xml
    │   └── src/main/java/com/yitong/octopus/module/distribution/
    │       ├── api/              # 对外API接口
    │       ├── enums/            # 枚举定义
    │       └── dto/              # 数据传输对象
    └── yitong-module-distribution-biz/
        ├── pom.xml
        └── src/main/java/com/yitong/octopus/module/distribution/
            ├── controller/
            │   ├── admin/        # 管理端控制器
            │   └── app/          # 移动端控制器
            ├── service/          # 服务层
            │   ├── agent/        # 分销员服务
            │   ├── commission/   # 佣金服务
            │   ├── goods/        # 商品服务
            │   └── withdraw/     # 提现服务
            ├── dal/              # 数据访问层
            │   ├── dataobject/   # 数据对象
            │   └── mysql/        # Mapper接口
            └── convert/          # 对象转换（使用BeanUtils）
```

## 第零阶段：模块初始化（0.5天）✅

### 模块创建和配置

- [x] **Task 0.1: 创建模块目录结构** ✅
  - 在 `yitong-module-promotion` 下创建 `yitong-module-distribution` 目录
  - 创建 `yitong-module-distribution-api` 子模块
  - 创建 `yitong-module-distribution-biz` 子模块
  - 验证：目录结构创建完成
  - 时间：30分钟
  - 完成时间：2025-07-21

- [x] **Task 0.2: 配置模块pom.xml** ✅
  - 配置 `yitong-module-distribution/pom.xml` 父模块配置
  - 配置 `yitong-module-distribution-api/pom.xml`
  - 配置 `yitong-module-distribution-biz/pom.xml`
  - 在 `yitong-module-promotion/pom.xml` 中添加子模块引用
  - 验证：Maven编译通过
  - 时间：1小时
  - 完成时间：2025-07-21

- [x] **Task 0.3: 创建基础包结构** ✅
  - 创建API模块包结构：`com.yitong.octopus.module.distribution.api/enums/dto`
  - 创建BIZ模块包结构：`com.yitong.octopus.module.distribution.controller/service/dal`
  - 创建 `package-info.java` 文件
  - 验证：包结构正确
  - 时间：30分钟
  - 完成时间：2025-07-21

## 第一阶段：基础数据层（3天）

### Day 1: 创建基础数据对象 ✅

- [x] **Task 1.1: 创建分销员数据对象** ✅
  - 文件：`yitong-module-distribution-biz/src/main/java/com/yitong/octopus/module/distribution/dal/dataobject/agent/DistAgentDO.java`
  - 内容：分销员基本信息（id, memberId, inviteCode, realName, mobile, levelId, parentId, status等）
  - 验证：类能正常编译，字段定义完整
  - 时间：1小时
  - 完成时间：2025-07-21

- [x] **Task 1.2: 创建分销等级数据对象** ✅
  - 文件：`yitong-module-distribution-biz/src/main/java/com/yitong/octopus/module/distribution/dal/dataobject/level/DistLevelDO.java`
  - 内容：等级信息（id, levelCode, levelName, levelGrade, commissionRate等）
  - 验证：类能正常编译
  - 时间：30分钟
  - 完成时间：2025-07-21

- [x] **Task 1.3: 创建佣金记录数据对象** ✅
  - 文件：`yitong-module-distribution-biz/src/main/java/com/yitong/octopus/module/distribution/dal/dataobject/commission/DistCommissionDO.java`
  - 内容：佣金记录（id, agentId, orderId, orderAmount, commissionAmount, status等）
  - 验证：类能正常编译
  - 时间：1小时
  - 完成时间：2025-07-21

- [x] **Task 1.4: 创建商品配置数据对象** ✅
  - 文件：`yitong-module-distribution-biz/src/main/java/com/yitong/octopus/module/distribution/dal/dataobject/goods/DistGoodsConfigDO.java`
  - 内容：商品分销配置（id, goodsId, enableDist, commissionRate, priority等）
  - 验证：类能正常编译
  - 时间：30分钟
  - 完成时间：2025-07-21

- [x] **Task 1.5: 创建提现记录数据对象** ✅
  - 文件：`yitong-module-distribution-biz/src/main/java/com/yitong/octopus/module/distribution/dal/dataobject/withdraw/DistWithdrawDO.java`
  - 内容：提现记录（id, agentId, amount, bankInfo, status, auditTime等）
  - 验证：类能正常编译
  - 时间：30分钟
  - 完成时间：2025-07-21

### Day 2: 创建Mapper接口 ✅

- [x] **Task 2.1: 创建分销员Mapper接口** ✅
  - 文件：`yitong-module-distribution-biz/src/main/java/com/yitong/octopus/module/distribution/dal/mysql/agent/DistAgentMapper.java`
  - 内容：基础CRUD方法（insert, selectById, selectPage, update）及业务方法
  - 验证：接口定义完整，无编译错误
  - 时间：1小时
  - 完成时间：2025-07-21

- [x] **Task 2.2: 创建分销员Mapper XML** ✅
  - 文件：`yitong-module-distribution-biz/src/main/resources/mapper/agent/DistAgentMapper.xml`
  - 内容：实现基础SQL（插入、查询、更新）
  - 验证：XML格式正确，SQL语法正确
  - 时间：2小时
  - 完成时间：2025-07-21

- [x] **Task 2.3: 创建其他Mapper接口** ✅
  - 文件：
    - `dal/mysql/level/DistLevelMapper.java`
    - `dal/mysql/commission/DistCommissionMapper.java`
    - `dal/mysql/goods/DistGoodsConfigMapper.java`
    - `dal/mysql/withdraw/DistWithdrawMapper.java`
  - 内容：各实体的基础CRUD接口
  - 验证：接口编译通过
  - 时间：1小时
  - 完成时间：2025-07-21

- [x] **Task 2.4: 创建其他Mapper XML** ✅
  - 文件：
    - `resources/mapper/level/DistLevelMapper.xml`
    - `resources/mapper/commission/DistCommissionMapper.xml`
    - `resources/mapper/goods/DistGoodsConfigMapper.xml`
    - `resources/mapper/withdraw/DistWithdrawMapper.xml`
  - 内容：基础SQL实现
  - 验证：XML格式正确
  - 时间：2小时
  - 完成时间：2025-07-21

### Day 3: 数据库初始化和枚举定义

- [x] **Task 3.1: 创建枚举类** ✅
  - 文件位置：`yitong-module-distribution-api/src/main/java/com/yitong/octopus/module/distribution/enums/`
  - 创建文件：
    - `DistAgentStatusEnum.java` - 分销员状态枚举
    - `DistCommissionStatusEnum.java` - 佣金状态枚举
    - `DistWithdrawStatusEnum.java` - 提现状态枚举
    - `DistLevelGradeEnum.java` - 等级枚举
    - `DistCommissionModeEnum.java` - 佣金模式枚举
    - `ErrorCodeConstants.java` - 错误码常量
  - 验证：枚举类编译通过
  - 时间：1小时
  - 完成时间：2025-07-21

- [x] **Task 3.2: 创建数据库表结构脚本** ✅
  - 文件：`sql/mysql/distribution.sql`
  - 内容：创建5个核心表（yt_dist_agent, yt_dist_level, yt_dist_commission, yt_dist_goods_config, yt_dist_withdraw）
  - 验证：SQL能正常执行，表结构创建成功
  - 时间：2小时
  - 完成时间：2025-07-21

- [x] **Task 3.3: 创建初始化数据脚本** ✅
  - 文件：`sql/mysql/distribution-init.sql`
  - 内容：插入默认等级数据（青铜、白银、黄金、铂金、钻石）、系统菜单、字典数据、配置参数
  - 验证：数据插入成功
  - 时间：1小时
  - 完成时间：2025-07-21

- [x] **Task 3.4: 验证数据层可用性** ✅
  - 编写简单的单元测试
  - 验证Mapper能正常工作
  - 确保数据层编译通过
  - 时间：1小时
  - 完成时间：2025-07-21

## 第二阶段：核心服务层（5天）

### Day 4: 分销员管理服务

- [x] **Task 4.1: 创建分销员服务接口** ✅
  - 文件：`service/agent/DistAgentService.java`
  - 内容：定义核心方法（applyAgent, auditAgent, getAgentInfo, updateAgentLevel）
  - 注意：接口必须继承 `IService<DistAgentDO>`
  - 验证：接口编译通过
  - 时间：30分钟
  - 完成时间：2025-07-21

- [x] **Task 4.2: 实现分销员申请功能** ✅
  - 文件：`service/agent/impl/DistAgentServiceImpl.java`
  - 注意：实现类必须继承 `ServiceImpl<DistAgentMapper, DistAgentDO>` 并实现 `DistAgentService`
  - 方法：`applyAgent(AgentApplyReqVO reqVO)`
  - 逻辑：验证用户资格、生成分销码、创建申请记录
  - 验证：方法能正常执行，数据正确保存
  - 时间：2小时
  - 完成时间：2025-07-21

- [x] **Task 4.3: 实现分销员审核功能** ✅
  - 方法：`auditAgent(Long agentId, Integer status, String remark)`
  - 逻辑：更新审核状态、建立上下级关系、初始化佣金账户
  - 验证：审核流程完整，关系正确建立
  - 时间：2小时
  - 完成时间：2025-07-21

- [x] **Task 4.4: 实现分销员查询功能** ✅
  - 方法：`getAgentInfo(Long agentId)`, `getAgentPage(AgentPageReqVO reqVO)`
  - 逻辑：查询分销员信息、分页查询
  - 验证：数据查询正确
  - 时间：1小时
  - 完成时间：2025-07-21

### Day 5: 分销关系管理 ✅

- [x] **Task 5.1: 实现邀请码生成** ✅
  - 方法：`generateInviteCode(Long agentId)`
  - 逻辑：生成6位唯一邀请码
  - 验证：邀请码唯一性
  - 时间：1小时
  - 完成时间：2025-07-21（已在Task 4.2中实现）

- [x] **Task 5.2: 实现关系绑定功能** ✅
  - 方法：`bindRelation(Long agentId, String inviteCode)`
  - 逻辑：验证邀请码、建立上下级关系、更新路径
  - 验证：关系正确建立，层级不超过3级
  - 时间：2小时
  - 完成时间：2025-07-21

- [x] **Task 5.3: 实现团队查询功能** ✅
  - 方法：`getTeamMembers(Long agentId, Integer level)`
  - 逻辑：查询下级分销员、支持多级查询
  - 验证：团队数据正确
  - 时间：2小时
  - 完成时间：2025-07-21

### Day 6: 佣金计算服务

- [x] **Task 6.1: 创建佣金服务接口** ✅
  - 文件：`service/commission/DistCommissionService.java`
  - 内容：定义方法（calculateCommission, settleCommission, getCommissionList）
  - 注意：接口必须继承 `IService<DistCommissionDO>`
  - 时间：30分钟
  - 完成时间：2025-07-21

- [x] **Task 6.2: 实现订单佣金计算** ✅
  - 文件：`service/commission/impl/DistCommissionServiceImpl.java`
  - 注意：实现类必须继承 `ServiceImpl<DistCommissionMapper, DistCommissionDO>` 并实现 `DistCommissionService`
  - 方法：`calculateCommission(OrderInfo order)`
  - 逻辑：根据商品配置计算佣金、支持多级分润
  - 验证：佣金计算准确
  - 时间：3小时
  - 完成时间：2025-07-21

- [x] **Task 6.3: 实现佣金记录保存** ✅
  - 方法：`saveCommissionRecord(CommissionRecord record)`
  - 逻辑：保存佣金记录、更新分销员统计
  - 验证：数据保存成功
  - 时间：1小时
  - 完成时间：2025-07-21（已在Task 6.2中一并实现）

### Day 7: 商品分销配置 ✅

- [x] **Task 7.1: 创建商品配置服务** ✅
  - 文件：`service/goods/DistGoodsConfigService.java`
  - 内容：定义方法（configGoods, getDistGoods, updateGoodsConfig）
  - 注意：接口必须继承 `IService<DistGoodsConfigDO>`
  - 时间：30分钟
  - 完成时间：2025-07-21

- [x] **Task 7.2: 实现商品配置功能** ✅
  - 文件：`service/goods/impl/DistGoodsConfigServiceImpl.java`
  - 注意：实现类必须继承 `ServiceImpl<DistGoodsConfigMapper, DistGoodsConfigDO>` 并实现 `DistGoodsConfigService`
  - 方法：`configGoods(GoodsConfigReqVO reqVO)`
  - 逻辑：设置商品分销属性、佣金比例
  - 验证：配置保存成功
  - 时间：2小时
  - 完成时间：2025-07-21

- [x] **Task 7.3: 实现分销商品查询** ✅
  - 方法：`getDistributionGoods(GoodsPageReqVO reqVO)`
  - 逻辑：查询可分销商品、按优先级排序
  - 验证：查询结果正确
  - 时间：1小时
  - 完成时间：2025-07-21

### Day 8: 提现管理服务 ✅

- [x] **Task 8.1: 创建提现服务接口** ✅
  - 文件：`service/withdraw/DistWithdrawService.java`
  - 内容：定义方法（applyWithdraw, auditWithdraw, getWithdrawList）
  - 注意：接口必须继承 `IService<DistWithdrawDO>`
  - 时间：30分钟
  - 完成时间：2025-07-21

- [x] **Task 8.2: 实现提现申请** ✅
  - 文件：`service/withdraw/impl/DistWithdrawServiceImpl.java`
  - 注意：实现类必须继承 `ServiceImpl<DistWithdrawMapper, DistWithdrawDO>` 并实现 `DistWithdrawService`
  - 方法：`applyWithdraw(WithdrawReqVO reqVO)`
  - 逻辑：验证可提现金额、创建提现记录
  - 验证：申请创建成功
  - 时间：2小时
  - 完成时间：2025-07-21

- [x] **Task 8.3: 实现提现审核** ✅
  - 方法：`auditWithdraw(Long withdrawId, Integer status)`
  - 逻辑：更新审核状态、扣减可提现金额
  - 验证：审核流程完整
  - 时间：2小时
  - 完成时间：2025-07-21

## 第三阶段：API接口层（4天）

### Day 9: 管理端API - 分销员管理 ✅

- [x] **Task 9.1: 创建分销员管理控制器** ✅
  - 文件：`controller/admin/distribution/DistAgentController.java`
  - 接口：申请列表、审核、分销员查询
  - 验证：接口能正常访问
  - 时间：2小时
  - 完成时间：2025-07-21

- [x] **Task 9.2: 创建请求响应VO** ✅
  - 文件：`controller/admin/distribution/vo/agent/*VO.java`
  - 内容：AgentApplyReqVO, AgentPageReqVO, AgentRespVO等
  - 验证：VO定义完整
  - 时间：2小时
  - 完成时间：2025-07-21

- [x] **Task 9.3: 实现分销员管理接口** ✅
  - 方法：申请审核、等级调整、状态管理
  - 验证：接口功能正常
  - 时间：2小时
  - 完成时间：2025-07-21

### Day 10: 管理端API - 佣金管理 ✅

- [x] **Task 10.1: 创建佣金管理控制器** ✅
  - 文件：`controller/admin/commission/DistCommissionController.java`
  - 接口：佣金列表、佣金统计、结算管理、批量解冻等
  - 包含VO类：DistCommissionPageReqVO、DistCommissionRespVO、DistCommissionSettleReqVO、DistCommissionRefundReqVO、DistCommissionSummaryReqVO
  - 时间：2小时
  - 完成时间：2025-07-21

- [x] **Task 10.2: 创建提现管理控制器** ✅
  - 文件：`controller/admin/withdraw/DistWithdrawController.java`
  - 接口：提现列表、提现审核、批量审核、转账标记等
  - 包含VO类：DistWithdrawPageReqVO、DistWithdrawRespVO、DistWithdrawAuditReqVO、DistWithdrawBatchAuditReqVO、DistWithdrawTransferReqVO
  - 时间：2小时
  - 完成时间：2025-07-21

- [x] **Task 10.3: 创建商品配置控制器** ✅
  - 文件：`controller/admin/goods/DistProductConfigController.java`
  - 接口：商品配置、批量设置、佣金预估、分销信息查询等
  - 包含VO类：DistProductConfigBatchSetReqVO、DistProductConfigSaveReqVO、DistProductConfigBatchUpdateSchemeReqVO、DistProductCommissionEstimateRespVO、ProductDistributionInfoRespVO
  - 时间：2小时
  - 完成时间：2025-07-21

### Day 11: 移动端API - 分销员功能 ✅

- [x] **Task 11.1: 创建分销员应用控制器** ✅
  - 文件：`controller/app/distribution/AppDistAgentController.java`
  - 接口：申请成为分销员、查看个人信息、团队信息
  - 时间：3小时
  - 完成时间：2025-07-21

- [x] **Task 11.2: 创建佣金查询控制器** ✅
  - 文件：`controller/app/distribution/AppDistCommissionController.java`
  - 接口：我的佣金、收益统计、提现申请
  - 时间：2小时
  - 完成时间：2025-07-21

### Day 12: 移动端API - 商品分销 ✅

- [x] **Task 12.1: 创建商品分销控制器** ✅
  - 文件：`controller/app/distribution/AppDistGoodsController.java`
  - 接口：分销商品列表、生成分享链接
  - 时间：2小时
  - 完成时间：2025-07-21

- [x] **Task 12.2: 实现分享功能** ✅
  - 方法：生成专属链接、记录分享行为
  - 验证：链接生成正确
  - 时间：2小时
  - 完成时间：2025-07-21

## 第四阶段：业务完善（3天）

### Day 13: 统计功能

- [ ] **Task 13.1: 实现个人统计**
  - 方法：销售统计、佣金统计、团队统计
  - 验证：统计数据准确
  - 时间：3小时

- [ ] **Task 13.2: 实现排行榜功能**
  - 方法：销售排行、佣金排行
  - 验证：排名正确
  - 时间：2小时

### Day 14: 性能优化

- [ ] **Task 14.1: 添加Redis缓存**
  - 缓存分销员信息、商品配置
  - 验证：缓存生效
  - 时间：3小时

- [ ] **Task 14.2: 优化查询性能**
  - 添加数据库索引
  - 优化分页查询
  - 时间：2小时

### Day 15: 测试和修复

- [ ] **Task 15.1: 编写核心功能测试**
  - 测试分销员申请流程
  - 测试佣金计算准确性
  - 时间：3小时

- [ ] **Task 15.2: 修复发现的问题**
  - 解决测试中发现的bug
  - 确保所有功能正常
  - 时间：3小时

## 验收标准

### 每日验收
- [ ] 当天的代码能编译通过
- [ ] 新增功能有基本测试
- [ ] 代码已提交到版本控制

### 阶段验收
- [ ] 第一阶段：数据层完整，能进行基本CRUD
- [ ] 第二阶段：核心业务逻辑实现，服务可调用
- [ ] 第三阶段：API接口完整，可通过Postman测试
- [ ] 第四阶段：功能完善，性能达标

### 最终验收
- [ ] 分销员申请审核流程完整
- [ ] 佣金计算准确无误
- [ ] 提现功能正常工作
- [ ] 所有接口响应时间<2秒
- [ ] 代码质量符合规范

## 注意事项

1. **避免代码丢失**
   - 每完成一个Task立即保存
   - 每天结束前确保代码提交
   - 保持代码可编译状态

2. **逐步推进**
   - 不要跳跃式开发
   - 确保前置任务完成
   - 遇到问题及时记录

3. **质量控制**
   - 单个类不超过500行
   - 方法不超过50行
   - 保持代码简洁清晰

4. **依赖管理**
   - 确保pom.xml配置正确
   - 检查包路径是否正确
   - 验证Spring注解使用

5. **VO命名规范**
   - 管理端VO：直接使用业务名称，如 `DistAgentStatisticsRespVO`
   - 移动端VO：统一添加"App"前缀，如 `AppDistAgentStatisticsRespVO`
   - 避免不同模块间VO类名冲突，提高代码可读性
   - 示例对比：
     - 管理端：`controller/admin/agent/vo/DistAgentStatisticsRespVO`
     - 移动端：`controller/app/agent/vo/AppDistAgentStatisticsRespVO`

6. **对象转换规范**
   - 禁止使用MapStruct Convert模式
   - 统一使用 `BeanUtils.toBean()` 进行对象转换
   - 复杂关联数据转换使用手动转换方法
   - 在控制器层处理VO转换，保持服务层的纯净性

## 当前进度总结（2025-07-21）

### 已完成任务
- ✅ 第零阶段：模块初始化（3个任务全部完成）
- ✅ 第一阶段 Day 1：创建基础数据对象（5个任务全部完成）
- ✅ 第一阶段 Day 2：创建Mapper接口（4个任务全部完成）
- ✅ 第一阶段 Day 3：数据库初始化和枚举定义（4个任务全部完成）
- ✅ 第二阶段 Day 4：分销员管理服务（4个任务全部完成）
- ✅ 第二阶段 Day 5：分销关系管理（3个任务全部完成）
- ✅ 第二阶段 Day 6：佣金计算服务（3个任务全部完成）
- ✅ 第二阶段 Day 7：商品分销配置（3个任务全部完成）
- ✅ 第二阶段 Day 8：提现管理服务（3个任务全部完成）
- ✅ 第三阶段 Day 9：管理端API - 分销员管理（3个任务全部完成）
- ✅ 第三阶段 Day 10：管理端API - 佣金管理（3个任务全部完成）
- ✅ 第三阶段 Day 11：移动端API - 分销员功能（2个任务全部完成）
- ✅ 第三阶段 Day 12：移动端API - 商品分销（2个任务全部完成）

### 待完成任务
- ⏳ 第四阶段：业务完善（Day 13-15）

### 下一步工作
进入第四阶段，实现Day 13的统计功能

## 分销码和邀请码设计统一调整（2025-07-21）

### 调整背景
根据文档要求"每个分销员都只有一个全局唯一的分销码，邀请码其实就是邀请他成为分销员的分销码"，对系统中分销码和邀请码的概念进行统一。

### 核心设计原则
1. **统一概念**：分销码即邀请码，一码两用
2. **全局唯一**：每个分销员只有一个分销码
3. **简化逻辑**：移除复杂的邀请码类型解析
4. **用户友好**：分销码可选填，支持自主注册

### 已完成的调整

#### 1. 统一分销码生成格式
- **文件**：`AgentCoreServiceImpl.java:355`
- **修改前**：`AGENT_` + 雪花ID（13位）
- **修改后**：`DIS` + 雪花ID前8位
- **示例**：`DIS12345678`

#### 2. 简化邀请码解析逻辑
- **文件**：`DistInviteServiceImpl.java:46`
- **修改**：移除等级邀请码（LVL）和活动邀请码（ACT）的解析
- **统一处理**：所有邀请码都按分销码解析

#### 3. 移除等级邀请码相关代码
- **数据对象**：删除 `DistLevelDO.inviteCode` 字段
- **Mapper方法**：删除 `DistLevelMapper.selectByInviteCode()` 方法
- **服务方法**：删除 `DistLevelService.getLevelByInviteCode()` 方法
- **错误码**：删除 `DIST_LEVEL_INVITE_CODE_DUPLICATE` 错误码

#### 4. 清理废弃方法
- 删除 `parseLevelInviteCode()` 方法
- 删除 `parseActivityInviteCode()` 方法

### 用户注册和邀请码绑定流程
1. **可选填写分销码**：用户注册时可选择填写分销码（推荐人的分销码）
2. **验证有效性**：如填写，系统验证分销码有效性并建立上下级关系
3. **自主注册**：如不填写，用户直接注册，后续可申请成为分销员
4. **后续申请**：已注册用户可后续申请成为分销员，获得自己的分销码
5. **补填机会**：用户成为分销员后，如果没有上级，还有一次补填邀请码的机会
6. **关系固定**：一旦绑定上级关系，不可更改

### 业务逻辑简化
- 邀请码解析：统一按分销码处理
- 关系绑定：通过分销码查找上级分销员
- 数据一致性：避免多种码类型造成的混淆
- 系统维护：降低代码复杂度

### 补填邀请码功能（2025-07-21）

#### 业务规则
1. **补填时机**：用户成为分销员后，如果没有上级，可以补填一次邀请码
2. **状态要求**：只有状态为"正常"的分销员才能补填
3. **一次机会**：一旦绑定上级关系，不可更改
4. **层级限制**：最多支持3级分销关系
5. **循环检测**：防止形成循环绑定关系

#### 实现细节
1. **服务接口**：`DistAgentService.supplementInviteCode(Long agentId, String inviteCode)`
2. **控制器接口**：`POST /distribution/app/agent/supplement-invite-code`
3. **字段含义**：
   - `agentCode`：分销员自己的分销码（系统生成）
   - `inviteCode`：推荐人的分销码（用户填写）

### 数据字段说明
- **DistAgentDO.agentCode**：分销员的唯一分销码，用于推广
- **DistAgentDO.inviteCode**：用户申请时填写的推荐人分销码，可为空
- **DistAgentDO.parentId**：上级分销员ID，建立层级关系

## 与现有代码的关系

1. **完全重写**：不修改 `yitong-module-distribution` 中的现有代码
2. **参考借鉴**：可以参考现有代码的业务逻辑，但要重新编写
3. **独立运行**：新模块应该能独立编译和运行
4. **避免冲突**：使用不同的包路径和类名，避免与现有代码冲突

## 快速开始

1. **第一步**：完成 Task 0.1-0.3，创建模块结构
2. **第二步**：完成 Task 1.1-1.5，创建基础数据对象
3. **第三步**：逐个任务推进，每完成一个验证编译
4. **每日目标**：至少完成3-4个Task，保证有可运行代码

通过这个详细的任务计划，我们可以一步步完成分销系统的重写，确保每个阶段都有可验证的成果。