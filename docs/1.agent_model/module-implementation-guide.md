# 分销系统模块化实施指南

## 概述

本文档提供分销系统的模块化实施指南，帮助开发团队按模块逐步实现完整的分销系统。每个模块都有明确的边界、接口定义和实施步骤。

## 模块分解

### 1. 分销员管理模块 (Agent Module)

#### 1.1 模块职责
- 分销员申请和审核
- 分销员等级管理
- 上下级关系管理
- 分销员标签管理

#### 1.2 核心服务类
```java
// 包路径：com.yitong.octopus.module.distribution.service.agent
├── AgentApplicationService       // 分销员申请服务
├── AgentAuditService            // 分销员审核服务
├── AgentLevelService            // 分销员等级服务
├── AgentRelationService         // 分销员关系服务
├── AgentTagService              // 分销员标签服务
└── AgentStatisticsService       // 分销员统计服务
```

#### 1.3 数据表设计
```sql
-- 核心表
yt_dist_agent              -- 分销员信息表
yt_dist_level              -- 分销等级表
yt_dist_agent_tag          -- 分销员标签表
yt_dist_agent_tag_rel      -- 分销员标签关系表
```

#### 1.4 API接口
```
# 管理端
GET    /admin/distribution/agent/applications    # 申请列表
POST   /admin/distribution/agent/{id}/audit      # 审核申请
GET    /admin/distribution/agent/list            # 分销员列表
PUT    /admin/distribution/agent/{id}/level      # 调整等级

# 移动端
POST   /app/distribution/agent/apply             # 提交申请
GET    /app/distribution/agent/profile           # 个人信息
GET    /app/distribution/agent/team              # 团队信息
```

#### 1.5 实施步骤
1. **第1天**：创建基础DO和Mapper
2. **第2-3天**：实现申请和审核服务
3. **第4-5天**：实现等级管理服务
4. **第6-7天**：实现关系管理服务
5. **第8-9天**：实现标签管理功能
6. **第10天**：集成测试和优化

#### 1.6 关键业务逻辑
```java
@Service
public class AgentApplicationServiceImpl implements AgentApplicationService {
    
    @Transactional
    public Long submitApplication(AgentApplicationReqVO reqVO) {
        // 1. 验证申请资格
        validateApplicationEligibility(reqVO);
        
        // 2. 验证邀请码
        AgentDO parent = validateInviteCode(reqVO.getInviteCode());
        
        // 3. 创建申请记录
        AgentDO agent = createAgentRecord(reqVO, parent);
        
        // 4. 自动审核判断
        if (shouldAutoApprove(agent)) {
            approveAgent(agent.getId());
        }
        
        return agent.getId();
    }
}
```

### 2. 佣金管理模块 (Commission Module)

#### 2.1 模块职责
- 佣金计算引擎
- 智能归因机制
- 奖励配置管理
- 佣金结算流程

#### 2.2 核心服务类
```java
// 包路径：com.yitong.octopus.module.distribution.service.commission
├── CommissionCalculateService    // 佣金计算服务
├── CommissionFreezeService       // 冻结解冻服务
├── CommissionSettleService       // 结算服务
└── CommissionTraceService        // 追溯服务

// 包路径：com.yitong.octopus.module.distribution.service.attribution
├── AttributionService            // 归因服务
├── AttributionConfigService      // 配置解析服务
├── AttributionCalculateService   // 归因计算服务
└── AttributionConflictService    // 冲突检测服务
```

#### 2.3 数据表设计
```sql
-- 核心表
yt_dist_reward_scheme         -- 奖励方案表
yt_dist_reward_level_config   -- 等级奖励配置表
yt_dist_reward_tag_config     -- 标签奖励配置表
yt_dist_reward_personal_config -- 个人奖励配置表
yt_dist_commission_bill       -- 佣金账单表
yt_dist_config_trace          -- 配置追踪表
```

#### 2.4 实施步骤
1. **第1-2天**：实现配置管理服务
2. **第3-4天**：实现智能归因算法
3. **第5-7天**：实现佣金计算引擎
4. **第8-9天**：实现结算和冻结机制
5. **第10天**：性能优化和测试

#### 2.5 关键算法实现
```java
@Service
public class AttributionServiceImpl implements AttributionService {
    
    public AttributionResult smartAttribution(AttributionReqVO reqVO) {
        // 1. 收集所有可能适用的配置
        List<RewardConfig> configs = collectApplicableConfigs(reqVO);
        
        // 2. 按优先级排序
        configs = sortByPriority(configs);
        
        // 3. 选择最优配置
        RewardConfig bestConfig = selectBestConfig(configs, reqVO);
        
        // 4. 记录归因过程
        recordAttribution(reqVO, bestConfig);
        
        return AttributionResult.builder()
            .config(bestConfig)
            .reason("智能归因选择最优配置")
            .build();
    }
}
```

### 3. 商品分销模块 (Product Module)

#### 3.1 模块职责
- 商品分销配置
- 分销商品展示
- 分销链接生成
- 分销数据统计

#### 3.2 核心服务类
```java
// 包路径：com.yitong.octopus.module.distribution.service.product
├── ProductDistributionService    // 商品分销服务
├── ProductConfigService          // 商品配置服务
├── ProductDisplayService         // 商品展示服务
└── ProductLinkService           // 分销链接服务
```

#### 3.3 实施步骤
1. **第1-2天**：实现商品配置管理
2. **第3-4天**：实现分销商品展示
3. **第5-6天**：实现分销链接生成
4. **第7天**：集成测试

### 4. 海报管理模块 (Poster Module)

#### 4.1 模块职责
- 海报模板管理
- 个人海报生成
- 二维码生成和识别
- 海报分享追踪

#### 4.2 核心服务类
```java
// 包路径：com.yitong.octopus.module.distribution.service.poster
├── PosterTemplateService         // 海报模板服务
├── PosterGenerateService         // 海报生成服务
├── PosterTrackService           // 海报追踪服务
└── QRCodeService               // 二维码服务
```

#### 4.3 实施步骤
1. **第1-2天**：实现模板管理功能
2. **第3-4天**：实现海报生成服务
3. **第5-6天**：实现分享追踪功能
4. **第7天**：集成测试

### 5. 统计分析模块 (Statistics Module)

#### 5.1 模块职责
- 个人业绩统计
- 团队数据分析
- 排行榜功能
- 数据报表生成

#### 5.2 核心服务类
```java
// 包路径：com.yitong.octopus.module.distribution.service.statistics
├── AgentPersonalStatisticsService   // 个人统计服务
├── AgentTeamStatisticsService       // 团队统计服务
├── AgentPerformanceStatisticsService // 业绩统计服务
├── AgentTrendAnalysisService        // 趋势分析服务
└── AgentRankingService              // 排行榜服务
```

#### 5.3 实施步骤
1. **第1-2天**：实现个人统计功能
2. **第3-4天**：实现团队统计功能
3. **第5-6天**：实现排行榜功能
4. **第7天**：性能优化

### 6. 提现管理模块 (Withdraw Module)

#### 6.1 模块职责
- 提现申请流程
- 提现审核机制
- 银行转账集成
- 提现记录管理

#### 6.2 核心服务类
```java
// 包路径：com.yitong.octopus.module.distribution.service.withdraw
├── WithdrawApplicationService    // 提现申请服务
├── WithdrawAuditService         // 提现审核服务
├── WithdrawProcessService       // 提现处理服务
└── WithdrawRecordService        // 提现记录服务
```

#### 6.3 实施步骤
1. **第1-2天**：实现申请和审核流程
2. **第3-4天**：实现转账处理逻辑
3. **第5-6天**：实现记录管理功能
4. **第7天**：集成测试

## 模块间集成

### 1. 依赖关系

```
分销员管理模块 (核心模块)
    ↓
佣金管理模块 (依赖分销员信息)
    ↓
商品分销模块 (依赖佣金配置)
    ↓
统计分析模块 (依赖所有数据)
    ↓
提现管理模块 (依赖佣金数据)
    ↓
海报管理模块 (独立模块，可并行开发)
```

### 2. 接口定义

```java
// 分销员管理模块对外接口
public interface AgentService {
    AgentInfo getAgentInfo(Long agentId);
    List<AgentInfo> getAgentParents(Long agentId);
    boolean isValidAgent(Long agentId);
}

// 佣金管理模块对外接口
public interface CommissionService {
    CommissionResult calculateCommission(CommissionRequest request);
    BigDecimal getWithdrawableAmount(Long agentId);
    void freezeCommission(Long billId);
}

// 统计分析模块对外接口
public interface StatisticsService {
    AgentStats getAgentStats(Long agentId);
    TeamStats getTeamStats(Long agentId);
    void updateAgentStats(Long agentId);
}
```

### 3. 事件驱动集成

```java
// 分销员申请通过事件
@EventListener
public void handleAgentApproved(AgentApprovedEvent event) {
    // 初始化佣金账户
    commissionService.initCommissionAccount(event.getAgentId());
    // 初始化统计数据
    statisticsService.initAgentStats(event.getAgentId());
}

// 订单完成事件
@EventListener
public void handleOrderCompleted(OrderCompletedEvent event) {
    // 计算佣金
    commissionService.calculateCommission(event.getOrderId());
    // 更新统计数据
    statisticsService.updateSalesStats(event.getAgentId());
}
```

## 开发规范

### 1. 代码规范

#### 1.1 命名规范
```java
// 服务类命名
AgentApplicationService        // 接口
AgentApplicationServiceImpl    // 实现类

// VO类命名
AgentCreateReqVO              // 请求VO
AgentCreateRespVO             // 响应VO
AgentPageReqVO                // 分页请求VO

// DO类命名
AgentDO                       // 数据对象
AgentTagDO                    // 标签数据对象
```

#### 1.2 包结构规范
```
com.yitong.octopus.module.distribution
├── controller/
│   ├── admin/              # 管理端控制器
│   │   ├── agent/         # 分销员管理
│   │   ├── commission/    # 佣金管理
│   │   └── ...
│   └── app/               # 移动端控制器
│       ├── agent/         # 分销员应用
│       ├── commission/    # 佣金查询
│       └── ...
├── service/
│   ├── agent/             # 分销员服务
│   ├── commission/        # 佣金服务
│   └── ...
├── dal/
│   ├── dataobject/        # 数据对象
│   │   ├── agent/         # 分销员相关DO
│   │   ├── commission/    # 佣金相关DO
│   │   └── ...
│   └── mysql/             # MySQL映射器
│       ├── agent/         # 分销员相关Mapper
│       ├── commission/    # 佣金相关Mapper
│       └── ...
```

### 2. 对象转换规范

统一使用 `BeanUtils` 进行对象转换：

```java
@RestController
public class AgentController {
    
    @PostMapping("/create")
    public CommonResult<Long> createAgent(@Valid @RequestBody AgentCreateReqVO reqVO) {
        // VO转换为Service参数
        AgentCreateCommand command = BeanUtils.toBean(reqVO, AgentCreateCommand.class);
        Long agentId = agentService.createAgent(command);
        return success(agentId);
    }
    
    @GetMapping("/list")
    public CommonResult<List<AgentRespVO>> getAgentList(AgentListReqVO reqVO) {
        List<AgentDO> agents = agentService.getAgentList(reqVO);
        // DO列表转换为VO列表
        List<AgentRespVO> respList = BeanUtils.toBean(agents, AgentRespVO.class);
        return success(respList);
    }
    
    @GetMapping("/page")
    public CommonResult<PageResult<AgentRespVO>> getAgentPage(AgentPageReqVO reqVO) {
        PageResult<AgentDO> pageResult = agentService.getAgentPage(reqVO);
        // 分页对象转换
        return success(BeanUtils.toBean(pageResult, AgentRespVO.class));
    }
}
```

### 3. 异常处理规范

```java
// 业务异常定义
public class DistributionException extends RuntimeException {
    private final ErrorCode errorCode;
    
    public DistributionException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }
}

// 具体业务异常
public class AgentApplicationException extends DistributionException {
    public static final ErrorCode ALREADY_AGENT = new ErrorCode(1001, "用户已是分销员");
    public static final ErrorCode INVALID_INVITE_CODE = new ErrorCode(1002, "邀请码无效");
    
    public AgentApplicationException(ErrorCode errorCode) {
        super(errorCode);
    }
}

// 使用示例
@Service
public class AgentApplicationServiceImpl {
    
    public Long submitApplication(AgentApplicationReqVO reqVO) {
        // 检查是否已是分销员
        if (agentMapper.selectByMemberId(reqVO.getMemberId()) != null) {
            throw new AgentApplicationException(AgentApplicationException.ALREADY_AGENT);
        }
        
        // 验证邀请码
        if (!isValidInviteCode(reqVO.getInviteCode())) {
            throw new AgentApplicationException(AgentApplicationException.INVALID_INVITE_CODE);
        }
        
        // 正常业务逻辑
        return createAgent(reqVO);
    }
}
```

### 4. 单元测试规范

```java
@ExtendWith(MockitoExtension.class)
class AgentApplicationServiceTest {
    
    @Mock
    private AgentMapper agentMapper;
    
    @Mock
    private LevelService levelService;
    
    @InjectMocks
    private AgentApplicationServiceImpl agentApplicationService;
    
    @Test
    @DisplayName("正常提交申请应该成功")
    void shouldSubmitApplicationSuccessfully() {
        // given
        AgentApplicationReqVO reqVO = buildValidRequest();
        when(agentMapper.selectByMemberId(reqVO.getMemberId())).thenReturn(null);
        when(levelService.getDefaultLevel()).thenReturn(buildDefaultLevel());
        
        // when
        Long agentId = agentApplicationService.submitApplication(reqVO);
        
        // then
        assertThat(agentId).isNotNull();
        verify(agentMapper).insert(any(AgentDO.class));
        verify(levelService).getDefaultLevel();
    }
    
    @Test
    @DisplayName("重复申请应该抛出异常")
    void shouldThrowExceptionWhenAlreadyAgent() {
        // given
        AgentApplicationReqVO reqVO = buildValidRequest();
        when(agentMapper.selectByMemberId(reqVO.getMemberId())).thenReturn(new AgentDO());
        
        // when & then
        AgentApplicationException exception = assertThrows(
            AgentApplicationException.class,
            () -> agentApplicationService.submitApplication(reqVO)
        );
        
        assertThat(exception.getErrorCode()).isEqualTo(AgentApplicationException.ALREADY_AGENT);
    }
    
    private AgentApplicationReqVO buildValidRequest() {
        AgentApplicationReqVO reqVO = new AgentApplicationReqVO();
        reqVO.setMemberId(1L);
        reqVO.setAgentName("测试分销员");
        reqVO.setAgentMobile("13800138000");
        reqVO.setInviteCode("INVITE123");
        return reqVO;
    }
}
```

## 质量检查清单

### 1. 代码质量检查

- [ ] 单文件不超过500行
- [ ] 单方法不超过50行
- [ ] 类的单一职责原则
- [ ] 方法命名清晰表达意图
- [ ] 适当的注释和文档
- [ ] 统一使用BeanUtils转换对象
- [ ] 正确的异常处理
- [ ] 完整的单元测试

### 2. 业务逻辑检查

- [ ] 业务规则正确实现
- [ ] 边界条件处理
- [ ] 数据一致性保证
- [ ] 并发安全考虑
- [ ] 性能优化实现

### 3. 接口设计检查

- [ ] RESTful API设计规范
- [ ] 统一的请求响应格式
- [ ] 完整的参数验证
- [ ] 适当的HTTP状态码
- [ ] 完整的接口文档

### 4. 数据库设计检查

- [ ] 表结构设计合理
- [ ] 索引设计优化
- [ ] 外键约束正确
- [ ] 数据类型选择合适
- [ ] 字段命名规范

通过这个模块化实施指南，开发团队可以按照明确的步骤和规范，高质量地完成分销系统的开发工作。