# 分销系统 API 接口设计

## 概述

本文档提供分销系统的完整API接口设计，包括管理端和移动端的所有接口定义、请求响应格式、业务逻辑说明和错误处理。

## 1. 通用规范

### 1.1 接口规范

#### 1.1.1 请求格式
- **协议**：HTTPS
- **方法**：RESTful 风格（GET、POST、PUT、DELETE）
- **Content-Type**：application/json
- **字符编码**：UTF-8

#### 1.1.2 统一响应格式
```json
{
  "code": 0,                    // 响应码：0-成功，其他-失败
  "msg": "success",             // 响应消息
  "data": {},                   // 响应数据
  "timestamp": 1640995200000    // 时间戳
}
```

#### 1.1.3 分页响应格式
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 100,               // 总记录数
    "pageNo": 1,                // 当前页码
    "pageSize": 20,             // 每页大小
    "pages": 5,                 // 总页数
    "list": []                  // 数据列表
  }
}
```

#### 1.1.4 错误码规范
```
1000-1999: 分销员相关错误
2000-2999: 佣金相关错误
3000-3999: 商品分销相关错误
4000-4999: 海报相关错误
5000-5999: 提现相关错误
6000-6999: 统计相关错误
9000-9999: 系统相关错误
```

### 1.2 认证和权限

#### 1.2.1 请求头
```
Authorization: Bearer {JWT_TOKEN}
X-Tenant-Id: {TENANT_ID}
```

#### 1.2.2 权限码
```
# 分销员管理
distribution:agent:query          # 查询分销员
distribution:agent:create         # 创建分销员
distribution:agent:update         # 更新分销员
distribution:agent:audit          # 审核分销员

# 佣金管理
distribution:commission:query     # 查询佣金
distribution:commission:settle    # 佣金结算
distribution:commission:freeze    # 冻结佣金

# 配置管理
distribution:config:query         # 查询配置
distribution:config:create        # 创建配置
distribution:config:update        # 更新配置
```

## 2. 管理端 API

### 2.1 分销员管理

#### 2.1.1 查询分销员申请列表

**接口信息**
```
GET /admin/distribution/agent/applications
```

**请求参数**
```json
{
  "keyword": "张三",              // 关键词（姓名/手机号/分销码）
  "applyStatus": 0,              // 申请状态：0-待审核，1-审核通过，2-审核拒绝
  "applyTimeStart": "2024-01-01", // 申请开始时间
  "applyTimeEnd": "2024-01-31",   // 申请结束时间
  "pageNo": 1,                   // 页码
  "pageSize": 20                 // 每页大小
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 50,
    "pageNo": 1,
    "pageSize": 20,
    "list": [
      {
        "id": 1,
        "memberId": 1001,
        "agentName": "张三",
        "agentMobile": "***********",
        "applyTime": "2024-01-15 10:30:00",
        "applyStatus": 0,
        "applyStatusName": "待审核",
        "inviteCode": "ABC123",
        "inviterName": "李四",
        "remark": "希望成为分销员"
      }
    ]
  }
}
```

#### 2.1.2 审核分销员申请

**接口信息**
```
POST /admin/distribution/agent/{agentId}/audit
```

**请求参数**
```json
{
  "action": "pass",              // 审核动作：pass-通过，reject-拒绝
  "levelId": 1,                  // 分配等级ID（通过时必填）
  "remark": "符合条件，审核通过"    // 审核备注
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "审核成功",
  "data": null
}
```

**业务逻辑**
1. 验证申请状态（只能审核待审核状态）
2. 通过时：创建分销员记录、分配等级、绑定上下级关系
3. 拒绝时：更新申请状态和备注
4. 发送审核结果通知

#### 2.1.3 查询分销员列表

**接口信息**
```
GET /admin/distribution/agent/list
```

**请求参数**
```json
{
  "keyword": "张三",              // 关键词
  "levelId": 1,                  // 等级ID
  "status": 1,                   // 状态
  "parentId": 100,               // 上级ID
  "tagIds": [1, 2],              // 标签ID列表
  "registerTimeStart": "2024-01-01",
  "registerTimeEnd": "2024-01-31",
  "sortField": "totalSales",     // 排序字段
  "sortOrder": "desc",           // 排序方向
  "pageNo": 1,
  "pageSize": 20
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 200,
    "list": [
      {
        "id": 1,
        "agentCode": "DS000001",
        "agentName": "张三",
        "agentMobile": "***********",
        "levelId": 2,
        "levelName": "白银分销员",
        "levelColor": "#C0C0C0",
        "parentId": 0,
        "parentName": null,
        "tagList": [
          {
            "tagId": 1,
            "tagName": "KOL达人",
            "tagColor": "#FF6B6B"
          }
        ],
        "teamCount": 25,
        "directCount": 8,
        "totalSales": 50000.00,
        "monthSales": 8000.00,
        "totalCommission": 5000.00,
        "availableCommission": 1500.00,
        "status": 1,
        "statusName": "正常",
        "registerTime": "2024-01-15 10:30:00",
        "lastSalesTime": "2024-01-20 15:20:00"
      }
    ]
  }
}
```

#### 2.1.4 获取分销员详情

**接口信息**
```
GET /admin/distribution/agent/{agentId}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "memberId": 1001,
    "agentCode": "DS000001",
    "agentName": "张三",
    "agentMobile": "***********",
    "levelInfo": {
      "levelId": 2,
      "levelName": "白银分销员",
      "levelGrade": 2,
      "levelColor": "#C0C0C0",
      "levelIcon": "/icons/silver.png"
    },
    "relationInfo": {
      "parentId": 100,
      "parentName": "李四",
      "parentCode": "DS000100",
      "referrerId": 100,
      "referrerName": "李四",
      "referrerCode": "DS000100",
      "path": "/100/1/",
      "depth": 2
    },
    "tagList": [
      {
        "tagId": 1,
        "tagName": "KOL达人",
        "tagColor": "#FF6B6B"
      }
    ],
    "statsInfo": {
      "teamCount": 25,
      "directCount": 8,
      "monthTeamCount": 3,
      "totalSales": 50000.00,
      "monthSales": 8000.00,
      "totalCommission": 5000.00,
      "availableCommission": 1500.00,
      "frozenCommission": 500.00,
      "withdrawnCommission": 3000.00
    },
    "timeInfo": {
      "applyTime": "2024-01-15 10:30:00",
      "approveTime": "2024-01-15 14:20:00",
      "bindTime": "2024-01-15 14:20:00",
      "levelUpdateTime": "2024-01-20 09:15:00",
      "lastSalesTime": "2024-01-25 16:30:00"
    },
    "status": 1,
    "statusName": "正常"
  }
}
```

#### 2.1.5 调整分销员等级

**接口信息**
```
PUT /admin/distribution/agent/{agentId}/level
```

**请求参数**
```json
{
  "newLevelId": 3,
  "adjustReason": "销售业绩优秀，提升等级",
  "isNotify": true               // 是否发送通知
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "等级调整成功",
  "data": null
}
```

#### 2.1.6 设置分销员标签

**接口信息**
```
POST /admin/distribution/agent/{agentId}/tags
```

**请求参数**
```json
{
  "tagIds": [1, 2, 3],
  "setReason": "根据业绩表现设置KOL标签"
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "标签设置成功",
  "data": null
}
```

#### 2.1.7 获取分销员团队信息

**接口信息**
```
GET /admin/distribution/agent/{agentId}/team
```

**请求参数**
```json
{
  "depth": 2,                    // 查询层级深度
  "pageNo": 1,
  "pageSize": 20
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "agentInfo": {
      "id": 1,
      "agentName": "张三",
      "agentCode": "DS000001",
      "levelName": "白银分销员"
    },
    "teamStats": {
      "totalCount": 25,
      "directCount": 8,
      "level1Count": 8,
      "level2Count": 12,
      "level3Count": 5,
      "totalSales": 120000.00,
      "totalCommission": 12000.00
    },
    "teamMembers": {
      "total": 25,
      "list": [
        {
          "id": 101,
          "agentName": "王五",
          "agentCode": "DS000101",
          "levelName": "青铜分销员",
          "parentName": "张三",
          "depth": 2,
          "directCount": 3,
          "totalSales": 15000.00,
          "totalCommission": 1500.00,
          "joinTime": "2024-01-20 11:00:00"
        }
      ]
    }
  }
}
```

### 2.2 佣金配置管理

#### 2.2.1 创建奖励方案

**接口信息**
```
POST /admin/distribution/reward/scheme
```

**请求参数**
```json
{
  "schemeCode": "SPRING_2024",
  "schemeName": "春季促销方案",
  "schemeDesc": "春季促销活动专用分销方案",
  "applyScope": 2,               // 适用范围：1-全部商品，2-指定商品，3-指定类目，4-指定品牌
  "scopeIds": [1001, 1002, 1003], // 范围ID列表
  "enableSalesReward": true,     // 启用销售奖励
  "enableProfitSharing": true,   // 启用分润奖励
  "profitTarget": 3,             // 分润目标：1-上级，2-介绍人，3-上级+介绍人
  "maxTraceLevel": 3,            // 最大追溯层级
  "levelConfigMode": 2,          // 等级配置模式：1-继承默认，2-单独配置
  "defaultSalesMode": 2,         // 默认销售佣金模式：1-固定金额，2-百分比
  "defaultSalesRate": 10.0,      // 默认销售佣金比例
  "defaultProfitConfig": {
    "1": {"mode": 2, "rate": 5.0},
    "2": {"mode": 2, "rate": 3.0},
    "3": {"mode": 2, "rate": 1.0}
  },
  "minOrderAmount": 100.0,       // 最低订单金额
  "maxCommission": 1000.0,       // 单笔最高佣金
  "triggerStage": 1,             // 触发阶段：1-支付后，2-核销后，3-确认收货后
  "freezeDays": 7,               // 冻结天数
  "effectiveType": 1,            // 生效类型：1-立即生效，2-指定时间
  "effectiveTime": "2024-03-01 00:00:00",
  "expireTime": "2024-05-31 23:59:59",
  "priority": 200
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "创建成功",
  "data": {
    "id": 1,
    "schemeCode": "SPRING_2024"
  }
}
```

#### 2.2.2 配置等级奖励

**接口信息**
```
POST /admin/distribution/reward/scheme/{schemeId}/level-config
```

**请求参数**
```json
{
  "levelId": 2,
  "salesCommissionMode": 2,
  "salesCommissionRate": 15.0,
  "parentProfitConfig": {
    "1": {"mode": 2, "rate": 8.0},
    "2": {"mode": 2, "rate": 5.0},
    "3": {"mode": 2, "rate": 2.0}
  },
  "referrerProfitConfig": {
    "1": {"mode": 2, "rate": 3.0}
  },
  "minOrderAmount": 50.0,
  "maxCommission": 2000.0
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "配置成功",
  "data": null
}
```

#### 2.2.3 查询奖励方案列表

**接口信息**
```
GET /admin/distribution/reward/scheme/list
```

**请求参数**
```json
{
  "keyword": "春季",
  "applyScope": 2,
  "status": 1,
  "pageNo": 1,
  "pageSize": 20
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 10,
    "list": [
      {
        "id": 1,
        "schemeCode": "SPRING_2024",
        "schemeName": "春季促销方案",
        "applyScope": 2,
        "applyScopeName": "指定商品",
        "enableSalesReward": true,
        "enableProfitSharing": true,
        "defaultSalesRate": 10.0,
        "maxTraceLevel": 3,
        "minOrderAmount": 100.0,
        "maxCommission": 1000.0,
        "priority": 200,
        "status": 1,
        "statusName": "启用",
        "effectiveTime": "2024-03-01 00:00:00",
        "expireTime": "2024-05-31 23:59:59",
        "createTime": "2024-02-15 10:00:00"
      }
    ]
  }
}
```

### 2.3 佣金管理

#### 2.3.1 查询佣金账单列表

**接口信息**
```
GET /admin/distribution/commission/bills
```

**请求参数**
```json
{
  "agentId": 1,
  "agentName": "张三",
  "orderNo": "ORD20240120001",
  "commissionType": 1,           // 佣金类型：1-销售佣金，2-分润佣金
  "billStatus": 2,               // 账单状态：1-冻结中，2-可结算，3-已结算，4-已提现，5-已取消
  "createTimeStart": "2024-01-01",
  "createTimeEnd": "2024-01-31",
  "amountMin": 10.0,
  "amountMax": 1000.0,
  "pageNo": 1,
  "pageSize": 20
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 150,
    "summary": {
      "totalAmount": 15000.00,
      "availableAmount": 8000.00,
      "frozenAmount": 2000.00,
      "settledAmount": 5000.00
    },
    "list": [
      {
        "id": 1,
        "billNo": "BILL20240120001",
        "agentId": 1,
        "agentName": "张三",
        "agentCode": "DS000001",
        "orderId": 1001,
        "orderNo": "ORD20240120001",
        "productId": 2001,
        "productName": "苹果手机",
        "productSku": "IPHONE15-128G",
        "orderAmount": 5999.00,
        "commissionType": 1,
        "commissionTypeName": "销售佣金",
        "commissionLevel": null,
        "commissionAmount": 299.95,
        "commissionRate": 5.0,
        "sourceAgentId": null,
        "sourceAgentName": null,
        "configSource": "level",
        "configSourceName": "等级配置",
        "freezeDays": 7,
        "freezeUntil": "2024-01-27 15:30:00",
        "billStatus": 1,
        "billStatusName": "冻结中",
        "createTime": "2024-01-20 15:30:00",
        "settleTime": null,
        "withdrawTime": null
      }
    ]
  }
}
```

#### 2.3.2 批量结算佣金

**接口信息**
```
POST /admin/distribution/commission/batch-settle
```

**请求参数**
```json
{
  "billIds": [1, 2, 3, 4, 5],
  "settleRemark": "批量结算操作"
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "结算成功",
  "data": {
    "successCount": 5,
    "failCount": 0,
    "totalAmount": 1500.00
  }
}
```

#### 2.3.3 冻结/解冻佣金

**接口信息**
```
POST /admin/distribution/commission/freeze
```

**请求参数**
```json
{
  "billIds": [1, 2, 3],
  "action": "freeze",            // 操作类型：freeze-冻结，unfreeze-解冻
  "freezeDays": 30,              // 冻结天数（冻结时必填）
  "reason": "订单异常，临时冻结佣金"
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": null
}
```

#### 2.3.4 佣金计算预览

**接口信息**
```
POST /admin/distribution/commission/preview
```

**请求参数**
```json
{
  "agentId": 1,
  "productId": 2001,
  "orderAmount": 5999.00,
  "quantity": 1
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "agentInfo": {
      "agentId": 1,
      "agentName": "张三",
      "levelName": "白银分销员"
    },
    "productInfo": {
      "productId": 2001,
      "productName": "苹果手机",
      "orderAmount": 5999.00
    },
    "commissionPreview": {
      "salesCommission": {
        "amount": 299.95,
        "rate": 5.0,
        "configSource": "level"
      },
      "profitCommissions": [
        {
          "level": 1,
          "agentId": 100,
          "agentName": "李四",
          "amount": 179.97,
          "rate": 3.0,
          "configSource": "level"
        }
      ],
      "totalCommission": 479.92
    },
    "applicableConfig": {
      "configId": 1,
      "configType": "level",
      "configName": "白银分销员配置",
      "minOrderAmount": 50.0,
      "maxCommission": 2000.0,
      "freezeDays": 7
    }
  }
}
```

### 2.4 提现管理

#### 2.4.1 查询提现记录列表

**接口信息**
```
GET /admin/distribution/withdraw/records
```

**请求参数**
```json
{
  "agentName": "张三",
  "withdrawNo": "WD20240120001",
  "withdrawStatus": 1,
  "withdrawType": 1,
  "applyTimeStart": "2024-01-01",
  "applyTimeEnd": "2024-01-31",
  "amountMin": 100.0,
  "amountMax": 10000.0,
  "pageNo": 1,
  "pageSize": 20
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 50,
    "summary": {
      "totalAmount": 50000.00,
      "pendingAmount": 15000.00,
      "successAmount": 30000.00,
      "failedAmount": 5000.00
    },
    "list": [
      {
        "id": 1,
        "withdrawNo": "WD20240120001",
        "agentId": 1,
        "agentName": "张三",
        "agentMobile": "***********",
        "withdrawAmount": 1000.00,
        "feeAmount": 2.00,
        "actualAmount": 998.00,
        "withdrawType": 1,
        "withdrawTypeName": "银行卡",
        "bankName": "招商银行",
        "bankAccount": "****1234",
        "accountName": "张三",
        "applyTime": "2024-01-20 10:30:00",
        "withdrawStatus": 1,
        "withdrawStatusName": "待审核",
        "auditTime": null,
        "transferTime": null,
        "arriveTime": null,
        "auditRemark": null,
        "operator": null
      }
    ]
  }
}
```

#### 2.4.2 审核提现申请

**接口信息**
```
POST /admin/distribution/withdraw/{withdrawId}/audit
```

**请求参数**
```json
{
  "action": "pass",              // 审核动作：pass-通过，reject-拒绝
  "auditRemark": "审核通过，准备转账"
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "审核成功",
  "data": null
}
```

#### 2.4.3 批量审核提现

**接口信息**
```
POST /admin/distribution/withdraw/batch-audit
```

**请求参数**
```json
{
  "withdrawIds": [1, 2, 3],
  "action": "pass",
  "auditRemark": "批量审核通过"
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "批量审核成功",
  "data": {
    "successCount": 3,
    "failCount": 0,
    "totalAmount": 3000.00
  }
}
```

### 2.5 统计分析

#### 2.5.1 分销总览

**接口信息**
```
GET /admin/distribution/statistics/overview
```

**请求参数**
```json
{
  "timeRange": "month",          // 时间范围：today, week, month, quarter, year
  "compareWith": "lastMonth"     // 对比时间：lastMonth, lastQuarter, lastYear
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "agentStats": {
      "totalAgents": 1250,
      "activeAgents": 850,         // 本月有销售的分销员
      "newAgents": 80,             // 本月新增分销员
      "agentGrowthRate": 6.8       // 分销员增长率
    },
    "salesStats": {
      "totalSales": 2500000.00,
      "salesGrowthRate": 15.2,
      "orderCount": 3200,
      "avgOrderAmount": 781.25
    },
    "commissionStats": {
      "totalCommission": 125000.00,
      "commissionRate": 5.0,
      "paidCommission": 95000.00,
      "pendingCommission": 30000.00
    },
    "withdrawStats": {
      "totalWithdraw": 85000.00,
      "withdrawCount": 120,
      "avgWithdrawAmount": 708.33,
      "withdrawRate": 89.5         // 提现成功率
    },
    "trendData": [
      {
        "date": "2024-01-01",
        "sales": 80000.00,
        "commission": 4000.00,
        "agents": 40
      }
    ]
  }
}
```

#### 2.5.2 分销员排行榜

**接口信息**
```
GET /admin/distribution/statistics/agent-ranking
```

**请求参数**
```json
{
  "rankType": "sales",           // 排行类型：sales-销售排行，commission-佣金排行，team-团队排行
  "timeRange": "month",
  "levelId": 2,                  // 按等级筛选
  "limit": 20                    // 返回数量
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "rankType": "sales",
    "timeRange": "month",
    "updateTime": "2024-01-20 12:00:00",
    "rankings": [
      {
        "rank": 1,
        "agentId": 1,
        "agentName": "张三",
        "agentCode": "DS000001",
        "levelName": "钻石分销员",
        "avatar": "/avatars/agent_1.jpg",
        "salesAmount": 150000.00,
        "commissionAmount": 7500.00,
        "orderCount": 85,
        "teamCount": 50,
        "growthRate": 25.6           // 环比增长率
      }
    ]
  }
}
```

## 3. 移动端 API

### 3.1 分销员申请

#### 3.1.1 提交分销员申请

**接口信息**
```
POST /app/distribution/agent/apply
```

**请求参数**
```json
{
  "agentName": "张三",
  "agentMobile": "***********",
  "inviteCode": "ABC123",        // 邀请码（可选）
  "applyReason": "希望通过分销赚取收益",
  "agreedProtocol": true         // 是否同意分销协议
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "申请提交成功",
  "data": {
    "agentId": 1,
    "applyStatus": 0,
    "applyStatusName": "待审核",
    "estimatedAuditTime": "1-3个工作日"
  }
}
```

#### 3.1.2 查询申请状态

**接口信息**
```
GET /app/distribution/agent/apply-status
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "hasApplied": true,
    "applyStatus": 1,
    "applyStatusName": "审核通过",
    "applyTime": "2024-01-15 10:30:00",
    "approveTime": "2024-01-15 14:20:00",
    "approveRemark": "符合条件，审核通过",
    "agentInfo": {
      "agentId": 1,
      "agentCode": "DS000001",
      "levelName": "青铜分销员"
    }
  }
}
```

### 3.2 个人中心

#### 3.2.1 获取个人信息

**接口信息**
```
GET /app/distribution/agent/profile
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "agentInfo": {
      "agentId": 1,
      "agentCode": "DS000001",
      "agentName": "张三",
      "agentMobile": "***********",
      "avatar": "/avatars/agent_1.jpg",
      "qrCode": "/qrcodes/agent_1.png"
    },
    "levelInfo": {
      "currentLevel": {
        "levelId": 2,
        "levelName": "白银分销员",
        "levelGrade": 2,
        "levelColor": "#C0C0C0",
        "levelIcon": "/icons/silver.png",
        "benefits": "提升佣金率，享受专属培训"
      },
      "nextLevel": {
        "levelId": 3,
        "levelName": "黄金分销员",
        "levelGrade": 3,
        "upgradeProgress": {
          "salesProgress": 68.5,     // 销售额进度
          "teamProgress": 75.0,      // 团队人数进度
          "needSales": 15000.00,     // 还需销售额
          "needTeam": 5              // 还需团队人数
        }
      }
    },
    "relationInfo": {
      "inviteCode": "ABC123",
      "parentName": "李四",
      "inviteLink": "https://app.example.com/invite/ABC123"
    }
  }
}
```

#### 3.2.2 获取个人统计

**接口信息**
```
GET /app/distribution/agent/statistics
```

**请求参数**
```json
{
  "timeRange": "month"           // 时间范围：today, week, month, all
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "commissionStats": {
      "totalCommission": 5000.00,
      "availableCommission": 1500.00,
      "frozenCommission": 500.00,
      "withdrawnCommission": 3000.00,
      "monthCommission": 800.00,
      "todayCommission": 50.00
    },
    "salesStats": {
      "totalSales": 50000.00,
      "monthSales": 8000.00,
      "todaySales": 500.00,
      "orderCount": 45,
      "monthOrderCount": 8,
      "todayOrderCount": 1
    },
    "teamStats": {
      "totalTeam": 25,
      "directTeam": 8,
      "monthNewTeam": 3,
      "teamSales": 120000.00,
      "teamCommission": 6000.00
    },
    "rankingInfo": {
      "salesRank": 15,
      "commissionRank": 12,
      "teamRank": 8,
      "totalAgents": 500
    }
  }
}
```

#### 3.2.3 获取收益趋势

**接口信息**
```
GET /app/distribution/agent/income-trend
```

**请求参数**
```json
{
  "timeRange": "month",          // 时间范围：week, month, quarter
  "dataType": "commission"       // 数据类型：commission, sales, team
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "timeRange": "month",
    "dataType": "commission",
    "totalAmount": 800.00,
    "avgAmount": 25.81,
    "growthRate": 15.6,
    "trendData": [
      {
        "date": "2024-01-01",
        "amount": 50.00
      },
      {
        "date": "2024-01-02",
        "amount": 80.00
      }
    ]
  }
}
```

### 3.3 团队管理

#### 3.3.1 获取团队概览

**接口信息**
```
GET /app/distribution/agent/team/overview
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "teamStats": {
      "totalCount": 25,
      "directCount": 8,
      "level1Count": 8,
      "level2Count": 12,
      "level3Count": 5,
      "monthNewCount": 3,
      "activeCount": 18            // 本月活跃成员
    },
    "performanceStats": {
      "totalSales": 120000.00,
      "monthSales": 20000.00,
      "totalCommission": 6000.00,
      "monthCommission": 1000.00,
      "avgPerformance": 4800.00    // 人均业绩
    },
    "topMembers": [
      {
        "agentId": 101,
        "agentName": "王五",
        "levelName": "青铜分销员",
        "avatar": "/avatars/agent_101.jpg",
        "monthSales": 8000.00,
        "monthCommission": 400.00,
        "teamCount": 3
      }
    ]
  }
}
```

#### 3.3.2 获取团队成员列表

**接口信息**
```
GET /app/distribution/agent/team/members
```

**请求参数**
```json
{
  "levelFilter": 1,              // 层级筛选：1-直属，2-二级，3-三级
  "sortField": "monthSales",     // 排序字段
  "sortOrder": "desc",
  "pageNo": 1,
  "pageSize": 20
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 25,
    "list": [
      {
        "agentId": 101,
        "agentName": "王五",
        "agentCode": "DS000101",
        "levelName": "青铜分销员",
        "levelColor": "#CD7F32",
        "avatar": "/avatars/agent_101.jpg",
        "parentName": "张三",
        "depth": 2,
        "relation": "直属下级",
        "joinTime": "2024-01-20 11:00:00",
        "stats": {
          "totalSales": 15000.00,
          "monthSales": 8000.00,
          "totalCommission": 750.00,
          "monthCommission": 400.00,
          "teamCount": 3,
          "orderCount": 12
        },
        "isActive": true,
        "lastOrderTime": "2024-01-25 14:30:00"
      }
    ]
  }
}
```

### 3.4 商品分销

#### 3.4.1 获取分销商品列表

**接口信息**
```
GET /app/distribution/products
```

**请求参数**
```json
{
  "categoryId": 1001,
  "keyword": "手机",
  "sortField": "commission",     // 排序字段：commission-佣金，sales-销量，price-价格
  "sortOrder": "desc",
  "minCommission": 10.0,
  "maxCommission": 500.0,
  "pageNo": 1,
  "pageSize": 20
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 100,
    "list": [
      {
        "productId": 2001,
        "productName": "苹果iPhone 15",
        "productImage": "/images/iphone15.jpg",
        "productPrice": 5999.00,
        "originalPrice": 6299.00,
        "salesCount": 1250,
        "categoryName": "手机数码",
        "brandName": "苹果",
        "commissionInfo": {
          "rate": 5.0,
          "amount": 299.95,
          "minAmount": 50.00,
          "maxAmount": 500.00
        },
        "distributionInfo": {
          "priority": 100,
          "tags": ["热销", "新品"],
          "shareCount": 850,
          "orderCount": 120
        },
        "isDistributable": true
      }
    ]
  }
}
```

#### 3.4.2 获取商品分销信息

**接口信息**
```
GET /app/distribution/product/{productId}/distribution-info
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "productInfo": {
      "productId": 2001,
      "productName": "苹果iPhone 15",
      "productPrice": 5999.00,
      "productImages": ["/images/iphone15_1.jpg"]
    },
    "commissionInfo": {
      "salesCommission": {
        "rate": 5.0,
        "amount": 299.95,
        "configSource": "level"
      },
      "profitCommissions": [
        {
          "level": 1,
          "rate": 3.0,
          "amount": 179.97
        }
      ],
      "totalCommission": 299.95,
      "minOrderAmount": 50.00,
      "maxCommission": 500.00
    },
    "shareInfo": {
      "shareUrl": "https://app.example.com/product/2001?agent=DS000001",
      "shareText": "推荐好物，限时优惠",
      "qrCode": "/qrcodes/product_2001_agent_1.png"
    },
    "statsInfo": {
      "myShareCount": 5,
      "myOrderCount": 2,
      "myCommission": 599.90,
      "totalShareCount": 850,
      "totalOrderCount": 120
    }
  }
}
```

#### 3.4.3 生成分销链接

**接口信息**
```
POST /app/distribution/product/{productId}/share
```

**请求参数**
```json
{
  "shareChannel": "wechat",      // 分享渠道：wechat, moments, weibo, link
  "customText": "这款手机真的很不错"
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "shareUrl": "https://app.example.com/product/2001?agent=DS000001&channel=wechat",
    "qrCode": "/qrcodes/share_temp_123.png",
    "shareText": "【张三推荐】苹果iPhone 15，限时优惠！这款手机真的很不错",
    "shareImage": "/images/share_iphone15.jpg",
    "expireTime": "2024-01-21 15:30:00"
  }
}
```

### 3.5 佣金管理

#### 3.5.1 获取佣金账单

**接口信息**
```
GET /app/distribution/commission/bills
```

**请求参数**
```json
{
  "billStatus": 2,               // 账单状态
  "commissionType": 1,           // 佣金类型
  "timeRange": "month",
  "pageNo": 1,
  "pageSize": 20
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 50,
    "summary": {
      "totalAmount": 2000.00,
      "availableAmount": 1200.00,
      "frozenAmount": 300.00,
      "withdrawnAmount": 500.00
    },
    "list": [
      {
        "id": 1,
        "billNo": "BILL20240120001",
        "orderNo": "ORD20240120001",
        "productName": "苹果iPhone 15",
        "productImage": "/images/iphone15_thumb.jpg",
        "orderAmount": 5999.00,
        "commissionType": 1,
        "commissionTypeName": "销售佣金",
        "commissionAmount": 299.95,
        "commissionRate": 5.0,
        "billStatus": 2,
        "billStatusName": "可提现",
        "freezeUntil": null,
        "createTime": "2024-01-20 15:30:00",
        "settleTime": "2024-01-27 15:30:00",
        "canWithdraw": true
      }
    ]
  }
}
```

#### 3.5.2 获取佣金汇总

**接口信息**
```
GET /app/distribution/commission/summary
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "totalCommission": 5000.00,
    "availableCommission": 1500.00,
    "frozenCommission": 500.00,
    "withdrawnCommission": 3000.00,
    "todayCommission": 50.00,
    "yesterdayCommission": 80.00,
    "monthCommission": 800.00,
    "lastMonthCommission": 750.00,
    "commissionTrend": [
      {
        "date": "2024-01-01",
        "amount": 50.00
      }
    ],
    "recentBills": [
      {
        "billNo": "BILL20240120001",
        "productName": "苹果iPhone 15",
        "commissionAmount": 299.95,
        "createTime": "2024-01-20 15:30:00"
      }
    ]
  }
}
```

### 3.6 提现管理

#### 3.6.1 获取提现信息

**接口信息**
```
GET /app/distribution/withdraw/info
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "withdrawableAmount": 1500.00,
    "minWithdrawAmount": 100.00,
    "maxWithdrawAmount": 50000.00,
    "dailyWithdrawLimit": 10000.00,
    "dailyWithdrawCount": 3,
    "todayWithdrawn": 0.00,
    "todayCount": 0,
    "feeRate": 0.2,                // 手续费率
    "accountInfo": {
      "hasAccount": true,
      "bankName": "招商银行",
      "bankAccount": "****1234",
      "accountName": "张三"
    },
    "withdrawRules": [
      "每日最多可提现3次",
      "单次提现金额100-50000元",
      "提现手续费0.2%，最低2元",
      "工作日16:00前申请当日到账"
    ]
  }
}
```

#### 3.6.2 提交提现申请

**接口信息**
```
POST /app/distribution/withdraw/apply
```

**请求参数**
```json
{
  "withdrawAmount": 1000.00,
  "withdrawType": 1,             // 提现类型：1-银行卡，2-支付宝，3-微信
  "accountInfo": {
    "bankName": "招商银行",
    "bankAccount": "****************",
    "accountName": "张三",
    "bankCode": "CMB"
  },
  "payPassword": "123456"        // 支付密码
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "提现申请成功",
  "data": {
    "withdrawId": 1,
    "withdrawNo": "WD20240120001",
    "withdrawAmount": 1000.00,
    "feeAmount": 2.00,
    "actualAmount": 998.00,
    "estimatedArrival": "2024-01-20 18:00:00"
  }
}
```

#### 3.6.3 获取提现记录

**接口信息**
```
GET /app/distribution/withdraw/records
```

**请求参数**
```json
{
  "status": 4,                   // 提现状态筛选
  "pageNo": 1,
  "pageSize": 20
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 20,
    "list": [
      {
        "id": 1,
        "withdrawNo": "WD20240120001",
        "withdrawAmount": 1000.00,
        "feeAmount": 2.00,
        "actualAmount": 998.00,
        "withdrawType": 1,
        "withdrawTypeName": "银行卡",
        "bankName": "招商银行",
        "bankAccount": "****1234",
        "applyTime": "2024-01-20 10:30:00",
        "withdrawStatus": 4,
        "withdrawStatusName": "已到账",
        "arriveTime": "2024-01-20 16:30:00",
        "processingTime": "6小时"
      }
    ]
  }
}
```

### 3.7 海报管理

#### 3.7.1 获取海报模板列表

**接口信息**
```
GET /app/distribution/poster/templates
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "posterId": 1,
      "posterName": "新春招募海报",
      "posterType": 1,
      "posterTypeName": "通用海报",
      "templateUrl": "/posters/template_1.jpg",
      "previewUrl": "/posters/preview_1.jpg",
      "description": "新春主题招募海报，适合朋友圈分享",
      "usageCount": 150,
      "canUse": true
    }
  ]
}
```

#### 3.7.2 生成个人海报

**接口信息**
```
POST /app/distribution/poster/generate
```

**请求参数**
```json
{
  "posterId": 1,
  "customText": "一起来赚钱吧！"    // 自定义文案（可选）
}
```

**响应结果**
```json
{
  "code": 0,
  "msg": "海报生成成功",
  "data": {
    "agentPosterId": 1,
    "posterUrl": "/posters/agent_1_poster_1.jpg",
    "qrcodeUrl": "/qrcodes/agent_1.png",
    "shareUrl": "https://app.example.com/invite/DS000001",
    "expireTime": "2024-12-31 23:59:59"
  }
}
```

#### 3.7.3 获取海报分享统计

**接口信息**
```
GET /app/distribution/poster/{agentPosterId}/statistics
```

**响应结果**
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "agentPosterId": 1,
    "posterName": "新春招募海报",
    "generateTime": "2024-01-15 10:00:00",
    "stats": {
      "shareCount": 25,
      "scanCount": 120,
      "registerCount": 8,
      "conversionRate": 6.67       // 转化率
    },
    "dailyStats": [
      {
        "date": "2024-01-15",
        "shareCount": 3,
        "scanCount": 15,
        "registerCount": 1
      }
    ],
    "channelStats": [
      {
        "channel": "微信朋友圈",
        "shareCount": 15,
        "scanCount": 80,
        "registerCount": 5
      }
    ]
  }
}
```

## 4. 错误码定义

### 4.1 分销员相关错误码 (1000-1999)

```
1001: 用户已是分销员
1002: 邀请码无效
1003: 邀请码已过期
1004: 申请已存在，请勿重复申请
1005: 分销员不存在
1006: 分销员状态异常
1007: 等级配置不存在
1008: 无法设置为自己的上级
1009: 层级关系错误
1010: 标签不存在
```

### 4.2 佣金相关错误码 (2000-2999)

```
2001: 奖励方案不存在
2002: 奖励方案已过期
2003: 订单不符合佣金条件
2004: 佣金计算失败
2005: 佣金状态不允许操作
2006: 佣金已结算
2007: 配置冲突
2008: 归因失败
2009: 佣金金额异常
2010: 冻结期未满
```

### 4.3 商品分销相关错误码 (3000-3999)

```
3001: 商品未开启分销
3002: 商品分销配置不存在
3003: 商品已下架
3004: 商品库存不足
3005: 分销链接生成失败
3006: 分销权限不足
```

### 4.4 海报相关错误码 (4000-4999)

```
4001: 海报模板不存在
4002: 海报生成失败
4003: 海报权限不足
4004: 海报已过期
4005: 二维码生成失败
```

### 4.5 提现相关错误码 (5000-5999)

```
5001: 可提现金额不足
5002: 提现金额超出限制
5003: 提现次数超出限制
5004: 银行卡信息不完整
5005: 提现申请不存在
5006: 提现状态不允许操作
5007: 支付密码错误
5008: 账户信息验证失败
```

### 4.6 统计相关错误码 (6000-6999)

```
6001: 统计数据生成中
6002: 统计时间范围无效
6003: 数据权限不足
6004: 报表导出失败
```

通过这套完整的API接口设计，可以支撑分销系统的所有业务功能，为前端开发和第三方集成提供清晰的接口规范。