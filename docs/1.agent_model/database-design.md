# 分销系统数据库设计

## 概述

本文档提供分销系统的完整数据库设计，包括表结构、索引、约束和初始化数据。设计遵循业务需求和性能优化原则。

## 1. 核心业务表

### 1.1 分销等级表 (yt_dist_level)

```sql
CREATE TABLE `yt_dist_level` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '等级ID',
  `level_code` varchar(32) NOT NULL COMMENT '等级编码',
  `level_name` varchar(64) NOT NULL COMMENT '等级名称',
  `level_grade` int(11) NOT NULL COMMENT '等级级别，数值越大等级越高',
  `invite_code` varchar(32) DEFAULT NULL COMMENT '等级邀请码',
  `icon_url` varchar(256) DEFAULT NULL COMMENT '等级图标',
  `color` varchar(32) DEFAULT NULL COMMENT '等级颜色',
  `description` text COMMENT '等级说明',
  `benefits` text COMMENT '等级权益描述',
  `upgrade_conditions` json DEFAULT NULL COMMENT '升级条件（JSON格式）',
  `downgrade_conditions` json DEFAULT NULL COMMENT '降级条件（JSON格式）',
  `auto_upgrade` tinyint(1) DEFAULT '1' COMMENT '是否自动升级',
  `auto_downgrade` tinyint(1) DEFAULT '0' COMMENT '是否自动降级',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序序号',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_level_code` (`level_code`,`tenant_id`,`deleted`),
  UNIQUE KEY `uk_invite_code` (`invite_code`,`tenant_id`,`deleted`),
  KEY `idx_level_grade` (`level_grade`,`tenant_id`),
  KEY `idx_sort_order` (`sort_order`,`tenant_id`),
  KEY `idx_status` (`status`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销等级表';
```

### 1.2 分销员信息表 (yt_dist_agent)

```sql
CREATE TABLE `yt_dist_agent` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分销员ID',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `agent_code` varchar(32) NOT NULL COMMENT '分销员编码（分销码）',
  `agent_name` varchar(64) NOT NULL COMMENT '分销员名称',
  `agent_mobile` varchar(16) DEFAULT NULL COMMENT '分销员手机号',
  `level_id` bigint(20) NOT NULL COMMENT '等级ID',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '直接上级分销员ID',
  `parent_code` varchar(32) DEFAULT NULL COMMENT '上级分销码',
  `referrer_id` bigint(20) DEFAULT NULL COMMENT '介绍人ID',
  `referrer_code` varchar(32) DEFAULT NULL COMMENT '介绍人分销码',
  `agent_tags` varchar(512) DEFAULT NULL COMMENT '分销员标签（逗号分隔）',
  `path` varchar(512) DEFAULT NULL COMMENT '分销路径，如：/1/2/3/',
  `depth` int(11) DEFAULT '1' COMMENT '层级深度，顶级为1',
  `apply_time` datetime DEFAULT NULL COMMENT '申请时间',
  `approve_time` datetime DEFAULT NULL COMMENT '审批通过时间',
  `apply_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '申请状态：0-待审核，1-审核通过，2-审核拒绝',
  `approve_remark` varchar(256) DEFAULT NULL COMMENT '审批备注',
  `bind_time` datetime DEFAULT NULL COMMENT '绑定上级时间',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `team_count` int(11) DEFAULT '0' COMMENT '团队人数（包含所有下级）',
  `direct_count` int(11) DEFAULT '0' COMMENT '直属下级人数',
  `month_team_count` int(11) DEFAULT '0' COMMENT '本月新增团队人数',
  `total_sales` decimal(15,2) DEFAULT '0.00' COMMENT '累计销售额',
  `month_sales` decimal(15,2) DEFAULT '0.00' COMMENT '本月销售额',
  `total_commission` decimal(15,2) DEFAULT '0.00' COMMENT '累计佣金',
  `available_commission` decimal(15,2) DEFAULT '0.00' COMMENT '可提现佣金',
  `frozen_commission` decimal(15,2) DEFAULT '0.00' COMMENT '冻结佣金',
  `withdrawn_commission` decimal(15,2) DEFAULT '0.00' COMMENT '已提现佣金',
  `level_update_time` datetime DEFAULT NULL COMMENT '等级更新时间',
  `last_sales_time` datetime DEFAULT NULL COMMENT '最后销售时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_member_id` (`member_id`,`tenant_id`,`deleted`),
  UNIQUE KEY `uk_agent_code` (`agent_code`,`tenant_id`,`deleted`),
  KEY `idx_parent_id` (`parent_id`,`tenant_id`),
  KEY `idx_referrer_id` (`referrer_id`,`tenant_id`),
  KEY `idx_level_id` (`level_id`,`tenant_id`),
  KEY `idx_status_apply` (`status`,`apply_status`,`tenant_id`),
  KEY `idx_agent_tags` (`agent_tags`(100),`tenant_id`),
  KEY `idx_path` (`path`(100),`tenant_id`),
  KEY `idx_sales_time` (`last_sales_time`,`tenant_id`),
  KEY `idx_total_sales` (`total_sales`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员信息表';
```

### 1.3 分销员标签表 (yt_dist_agent_tag)

```sql
CREATE TABLE `yt_dist_agent_tag` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `tag_code` varchar(32) NOT NULL COMMENT '标签编码',
  `tag_name` varchar(64) NOT NULL COMMENT '标签名称',
  `tag_color` varchar(32) DEFAULT NULL COMMENT '标签颜色',
  `tag_desc` varchar(256) DEFAULT NULL COMMENT '标签描述',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序序号',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tag_code` (`tag_code`,`tenant_id`,`deleted`),
  KEY `idx_status` (`status`,`tenant_id`),
  KEY `idx_sort_order` (`sort_order`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员标签表';
```

### 1.4 分销员标签关系表 (yt_dist_agent_tag_rel)

```sql
CREATE TABLE `yt_dist_agent_tag_rel` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  `set_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '设置时间',
  `set_reason` varchar(256) DEFAULT NULL COMMENT '设置原因',
  `operator` varchar(64) DEFAULT NULL COMMENT '操作人',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agent_tag` (`agent_id`,`tag_id`,`tenant_id`),
  KEY `idx_tag_id` (`tag_id`,`tenant_id`),
  KEY `idx_set_time` (`set_time`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员标签关系表';
```

## 2. 商品分销表

### 2.1 商品分销配置表 (yt_dist_product_config)

```sql
CREATE TABLE `yt_dist_product_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `product_name` varchar(128) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(64) DEFAULT NULL COMMENT '商品SKU',
  `category_id` bigint(20) DEFAULT NULL COMMENT '商品类目ID',
  `brand_id` bigint(20) DEFAULT NULL COMMENT '品牌ID',
  `enable_distribution` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用分销',
  `distribution_priority` int(11) DEFAULT '0' COMMENT '分销优先级',
  `product_tags` varchar(256) DEFAULT NULL COMMENT '商品标签（逗号分隔）',
  `commission_info` varchar(512) DEFAULT NULL COMMENT '佣金提示信息',
  `min_price` decimal(10,2) DEFAULT NULL COMMENT '最低价格',
  `max_price` decimal(10,2) DEFAULT NULL COMMENT '最高价格',
  `sales_count` int(11) DEFAULT '0' COMMENT '销量',
  `commission_total` decimal(15,2) DEFAULT '0.00' COMMENT '累计佣金支出',
  `last_sales_time` datetime DEFAULT NULL COMMENT '最后销售时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_product_id` (`product_id`,`tenant_id`,`deleted`),
  KEY `idx_category_id` (`category_id`,`tenant_id`),
  KEY `idx_brand_id` (`brand_id`,`tenant_id`),
  KEY `idx_enable_priority` (`enable_distribution`,`distribution_priority`,`tenant_id`),
  KEY `idx_product_tags` (`product_tags`(100),`tenant_id`),
  KEY `idx_sales_count` (`sales_count`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分销配置表';
```

## 3. 奖励配置表

### 3.1 奖励方案表 (yt_dist_reward_scheme)

```sql
CREATE TABLE `yt_dist_reward_scheme` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '方案ID',
  `scheme_code` varchar(32) NOT NULL COMMENT '方案编码',
  `scheme_name` varchar(128) NOT NULL COMMENT '方案名称',
  `scheme_desc` varchar(512) DEFAULT NULL COMMENT '方案描述',
  `apply_scope` tinyint(4) NOT NULL DEFAULT '1' COMMENT '适用范围：1-全部商品，2-指定商品，3-指定类目，4-指定品牌',
  `scope_ids` varchar(1024) DEFAULT NULL COMMENT '范围ID列表（逗号分隔）',
  `enable_sales_reward` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用销售奖励',
  `enable_profit_sharing` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用分润奖励',
  `profit_target` tinyint(4) DEFAULT '3' COMMENT '分润目标：1-上级，2-介绍人，3-上级+介绍人',
  `max_trace_level` int(11) DEFAULT '3' COMMENT '最大追溯层级',
  `level_config_mode` tinyint(4) DEFAULT '1' COMMENT '等级配置模式：1-继承默认，2-单独配置',
  `default_sales_mode` tinyint(4) DEFAULT '2' COMMENT '默认销售佣金模式：1-固定金额，2-百分比',
  `default_sales_rate` decimal(5,2) DEFAULT '0.00' COMMENT '默认销售佣金比例',
  `default_sales_amount` decimal(10,2) DEFAULT '0.00' COMMENT '默认销售佣金金额',
  `default_profit_config` json DEFAULT NULL COMMENT '默认分润配置（JSON格式）',
  `min_order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最低订单金额',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金',
  `trigger_stage` tinyint(4) NOT NULL DEFAULT '1' COMMENT '触发阶段：1-支付后，2-核销后，3-确认收货后',
  `freeze_days` int(11) DEFAULT '0' COMMENT '冻结天数',
  `effective_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '生效类型：1-立即生效，2-指定时间',
  `effective_time` datetime DEFAULT NULL COMMENT '生效时间',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `priority` int(11) DEFAULT '100' COMMENT '优先级，数值越大优先级越高',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_code` (`scheme_code`,`tenant_id`,`deleted`),
  KEY `idx_apply_scope` (`apply_scope`,`tenant_id`),
  KEY `idx_status_priority` (`status`,`priority`,`tenant_id`),
  KEY `idx_effective_time` (`effective_time`,`expire_time`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='奖励方案表';
```

### 3.2 等级奖励配置表 (yt_dist_reward_level_config)

```sql
CREATE TABLE `yt_dist_reward_level_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `scheme_id` bigint(20) NOT NULL COMMENT '方案ID',
  `level_id` bigint(20) NOT NULL COMMENT '等级ID',
  `sales_commission_mode` tinyint(4) DEFAULT '2' COMMENT '销售佣金模式：1-固定金额，2-百分比',
  `sales_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '销售佣金比例',
  `sales_commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '销售佣金金额',
  `parent_profit_config` json DEFAULT NULL COMMENT '上级分润配置（JSON格式）',
  `referrer_profit_config` json DEFAULT NULL COMMENT '介绍人分润配置（JSON格式）',
  `min_order_amount` decimal(10,2) DEFAULT NULL COMMENT '最低订单金额（覆盖默认）',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金（覆盖默认）',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_level` (`scheme_id`,`level_id`,`tenant_id`,`deleted`),
  KEY `idx_level_id` (`level_id`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='等级奖励配置表';
```

### 3.3 标签奖励配置表 (yt_dist_reward_tag_config)

```sql
CREATE TABLE `yt_dist_reward_tag_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `scheme_id` bigint(20) NOT NULL COMMENT '方案ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  `sales_commission_mode` tinyint(4) DEFAULT '2' COMMENT '销售佣金模式：1-固定金额，2-百分比',
  `sales_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '销售佣金比例',
  `sales_commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '销售佣金金额',
  `parent_profit_config` json DEFAULT NULL COMMENT '上级分润配置（JSON格式）',
  `referrer_profit_config` json DEFAULT NULL COMMENT '介绍人分润配置（JSON格式）',
  `min_order_amount` decimal(10,2) DEFAULT NULL COMMENT '最低订单金额（覆盖默认）',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金（覆盖默认）',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_tag` (`scheme_id`,`tag_id`,`tenant_id`,`deleted`),
  KEY `idx_tag_id` (`tag_id`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签奖励配置表';
```

### 3.4 个人奖励配置表 (yt_dist_reward_personal_config)

```sql
CREATE TABLE `yt_dist_reward_personal_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `scheme_id` bigint(20) DEFAULT NULL COMMENT '基础方案ID（可为空）',
  `config_name` varchar(128) NOT NULL COMMENT '配置名称',
  `config_desc` varchar(512) DEFAULT NULL COMMENT '配置描述',
  `apply_scope` tinyint(4) NOT NULL DEFAULT '1' COMMENT '适用范围：1-全部商品，2-指定商品，3-指定类目，4-指定品牌',
  `scope_ids` varchar(1024) DEFAULT NULL COMMENT '范围ID列表（逗号分隔）',
  `sales_commission_mode` tinyint(4) DEFAULT '2' COMMENT '销售佣金模式：1-固定金额，2-百分比',
  `sales_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '销售佣金比例',
  `sales_commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '销售佣金金额',
  `parent_profit_config` json DEFAULT NULL COMMENT '上级分润配置（JSON格式）',
  `referrer_profit_config` json DEFAULT NULL COMMENT '介绍人分润配置（JSON格式）',
  `min_order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最低订单金额',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金',
  `effective_time` datetime NOT NULL COMMENT '生效时间',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `priority` int(11) DEFAULT '1000' COMMENT '优先级，数值越大优先级越高',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_agent_id` (`agent_id`,`tenant_id`),
  KEY `idx_scheme_id` (`scheme_id`,`tenant_id`),
  KEY `idx_apply_scope` (`apply_scope`,`tenant_id`),
  KEY `idx_status_priority` (`status`,`priority`,`tenant_id`),
  KEY `idx_effective_time` (`effective_time`,`expire_time`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个人奖励配置表';
```

## 4. 佣金账单表

### 4.1 佣金账单表 (yt_dist_commission_bill)

```sql
CREATE TABLE `yt_dist_commission_bill` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '账单ID',
  `bill_no` varchar(64) NOT NULL COMMENT '账单编号',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_no` varchar(64) NOT NULL COMMENT '订单号',
  `order_item_id` bigint(20) DEFAULT NULL COMMENT '订单商品ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `product_name` varchar(128) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(64) DEFAULT NULL COMMENT '商品SKU',
  `order_amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `commission_type` tinyint(4) NOT NULL COMMENT '佣金类型：1-销售佣金，2-分润佣金',
  `commission_level` int(11) DEFAULT NULL COMMENT '分润层级（销售佣金时为NULL）',
  `commission_amount` decimal(10,2) NOT NULL COMMENT '佣金金额',
  `commission_rate` decimal(5,2) DEFAULT NULL COMMENT '佣金比例',
  `source_agent_id` bigint(20) DEFAULT NULL COMMENT '来源分销员ID（分润时记录直接销售的分销员）',
  `config_source` varchar(64) NOT NULL COMMENT '配置来源：scheme-方案，level-等级，tag-标签，personal-个人',
  `config_id` bigint(20) NOT NULL COMMENT '配置ID',
  `trace_info` json DEFAULT NULL COMMENT '追溯信息（JSON格式）',
  `freeze_days` int(11) DEFAULT '0' COMMENT '冻结天数',
  `freeze_until` datetime DEFAULT NULL COMMENT '冻结到期时间',
  `bill_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '账单状态：1-冻结中，2-可结算，3-已结算，4-已提现，5-已取消',
  `settle_time` datetime DEFAULT NULL COMMENT '结算时间',
  `withdraw_time` datetime DEFAULT NULL COMMENT '提现时间',
  `cancel_reason` varchar(256) DEFAULT NULL COMMENT '取消原因',
  `remark` varchar(512) DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bill_no` (`bill_no`,`tenant_id`,`deleted`),
  KEY `idx_agent_id` (`agent_id`,`tenant_id`),
  KEY `idx_order_id` (`order_id`,`tenant_id`),
  KEY `idx_product_id` (`product_id`,`tenant_id`),
  KEY `idx_source_agent` (`source_agent_id`,`tenant_id`),
  KEY `idx_bill_status` (`bill_status`,`tenant_id`),
  KEY `idx_freeze_until` (`freeze_until`,`tenant_id`),
  KEY `idx_commission_type` (`commission_type`,`commission_level`,`tenant_id`),
  KEY `idx_create_time` (`create_time`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='佣金账单表';
```

## 5. 提现管理表

### 5.1 提现记录表 (yt_dist_withdraw_record)

```sql
CREATE TABLE `yt_dist_withdraw_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '提现记录ID',
  `withdraw_no` varchar(64) NOT NULL COMMENT '提现单号',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `agent_name` varchar(64) NOT NULL COMMENT '分销员姓名',
  `agent_mobile` varchar(16) DEFAULT NULL COMMENT '分销员手机号',
  `withdraw_amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `fee_amount` decimal(10,2) DEFAULT '0.00' COMMENT '手续费',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实际到账金额',
  `bank_name` varchar(128) DEFAULT NULL COMMENT '银行名称',
  `bank_account` varchar(64) DEFAULT NULL COMMENT '银行账号',
  `account_name` varchar(64) DEFAULT NULL COMMENT '账户姓名',
  `withdraw_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '提现类型：1-银行卡，2-支付宝，3-微信',
  `account_info` json DEFAULT NULL COMMENT '账户信息（JSON格式）',
  `apply_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `transfer_time` datetime DEFAULT NULL COMMENT '转账时间',
  `arrive_time` datetime DEFAULT NULL COMMENT '到账时间',
  `withdraw_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '提现状态：1-待审核，2-审核通过，3-转账中，4-已到账，5-审核拒绝，6-转账失败',
  `audit_remark` varchar(512) DEFAULT NULL COMMENT '审核备注',
  `transfer_remark` varchar(512) DEFAULT NULL COMMENT '转账备注',
  `failure_reason` varchar(512) DEFAULT NULL COMMENT '失败原因',
  `operator` varchar(64) DEFAULT NULL COMMENT '操作人',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_withdraw_no` (`withdraw_no`,`tenant_id`,`deleted`),
  KEY `idx_agent_id` (`agent_id`,`tenant_id`),
  KEY `idx_withdraw_status` (`withdraw_status`,`tenant_id`),
  KEY `idx_apply_time` (`apply_time`,`tenant_id`),
  KEY `idx_audit_time` (`audit_time`,`tenant_id`),
  KEY `idx_withdraw_amount` (`withdraw_amount`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现记录表';
```

## 6. 海报管理表

### 6.1 邀请海报表 (yt_dist_invite_poster)

```sql
CREATE TABLE `yt_dist_invite_poster` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '海报ID',
  `poster_code` varchar(32) NOT NULL COMMENT '海报编码',
  `poster_name` varchar(128) NOT NULL COMMENT '海报名称',
  `poster_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '海报类型：1-通用海报，2-等级海报，3-活动海报',
  `target_level_id` bigint(20) DEFAULT NULL COMMENT '目标等级ID（等级海报专用）',
  `template_url` varchar(512) NOT NULL COMMENT '模板图片URL',
  `qrcode_position` json DEFAULT NULL COMMENT '二维码位置信息（JSON格式）',
  `text_config` json DEFAULT NULL COMMENT '文字配置（JSON格式）',
  `poster_desc` varchar(512) DEFAULT NULL COMMENT '海报描述',
  `usage_count` int(11) DEFAULT '0' COMMENT '使用次数',
  `share_count` int(11) DEFAULT '0' COMMENT '分享次数',
  `scan_count` int(11) DEFAULT '0' COMMENT '扫码次数',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序序号',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_poster_code` (`poster_code`,`tenant_id`,`deleted`),
  KEY `idx_poster_type` (`poster_type`,`tenant_id`),
  KEY `idx_target_level` (`target_level_id`,`tenant_id`),
  KEY `idx_status_sort` (`status`,`sort_order`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邀请海报表';
```

### 6.2 分销员海报表 (yt_dist_agent_poster)

```sql
CREATE TABLE `yt_dist_agent_poster` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分销员海报ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `poster_id` bigint(20) NOT NULL COMMENT '海报模板ID',
  `poster_url` varchar(512) NOT NULL COMMENT '生成的海报URL',
  `qrcode_url` varchar(512) NOT NULL COMMENT '二维码URL',
  `qrcode_content` varchar(512) NOT NULL COMMENT '二维码内容',
  `generate_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生成时间',
  `share_count` int(11) DEFAULT '0' COMMENT '分享次数',
  `scan_count` int(11) DEFAULT '0' COMMENT '扫码次数',
  `register_count` int(11) DEFAULT '0' COMMENT '注册人数',
  `last_share_time` datetime DEFAULT NULL COMMENT '最后分享时间',
  `last_scan_time` datetime DEFAULT NULL COMMENT '最后扫码时间',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-失效，1-有效',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_agent_id` (`agent_id`,`tenant_id`),
  KEY `idx_poster_id` (`poster_id`,`tenant_id`),
  KEY `idx_generate_time` (`generate_time`,`tenant_id`),
  KEY `idx_share_count` (`share_count`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员海报表';
```

## 7. 配置追踪表

### 7.1 配置追踪表 (yt_dist_config_trace)

```sql
CREATE TABLE `yt_dist_config_trace` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '追踪记录ID',
  `trace_no` varchar(64) NOT NULL COMMENT '追踪编号',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `trace_type` tinyint(4) NOT NULL COMMENT '追踪类型：1-归因决策，2-佣金计算，3-配置冲突',
  `trace_stage` varchar(32) NOT NULL COMMENT '追踪阶段',
  `input_data` json DEFAULT NULL COMMENT '输入数据（JSON格式）',
  `output_data` json DEFAULT NULL COMMENT '输出数据（JSON格式）',
  `decision_process` json DEFAULT NULL COMMENT '决策过程（JSON格式）',
  `final_config` json DEFAULT NULL COMMENT '最终配置（JSON格式）',
  `execution_time` int(11) DEFAULT NULL COMMENT '执行时间（毫秒）',
  `trace_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '追踪时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_trace_no` (`trace_no`,`tenant_id`),
  KEY `idx_agent_id` (`agent_id`,`tenant_id`),
  KEY `idx_order_id` (`order_id`,`tenant_id`),
  KEY `idx_product_id` (`product_id`,`tenant_id`),
  KEY `idx_trace_type` (`trace_type`,`tenant_id`),
  KEY `idx_trace_time` (`trace_time`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置追踪表';
```

## 8. 性能优化索引

### 8.1 复合索引设计

```sql
-- 分销员查询优化
CREATE INDEX idx_agent_level_status ON yt_dist_agent(level_id, status, tenant_id);
CREATE INDEX idx_agent_parent_depth ON yt_dist_agent(parent_id, depth, tenant_id);
CREATE INDEX idx_agent_sales_stats ON yt_dist_agent(total_sales, total_commission, tenant_id);

-- 佣金账单查询优化
CREATE INDEX idx_bill_agent_status_time ON yt_dist_commission_bill(agent_id, bill_status, create_time, tenant_id);
CREATE INDEX idx_bill_order_type_level ON yt_dist_commission_bill(order_id, commission_type, commission_level, tenant_id);
CREATE INDEX idx_bill_settle_withdraw ON yt_dist_commission_bill(bill_status, settle_time, withdraw_time, tenant_id);

-- 提现记录查询优化
CREATE INDEX idx_withdraw_agent_status_time ON yt_dist_withdraw_record(agent_id, withdraw_status, apply_time, tenant_id);
CREATE INDEX idx_withdraw_amount_time ON yt_dist_withdraw_record(withdraw_amount, apply_time, tenant_id);

-- 海报使用统计优化
CREATE INDEX idx_poster_usage_stats ON yt_dist_agent_poster(poster_id, share_count, scan_count, tenant_id);
CREATE INDEX idx_agent_poster_time ON yt_dist_agent_poster(agent_id, generate_time, tenant_id);
```

### 8.2 分区表设计

```sql
-- 佣金账单表按月分区（示例）
ALTER TABLE yt_dist_commission_bill 
PARTITION BY RANGE (TO_DAYS(create_time)) (
    PARTITION p202401 VALUES LESS THAN (TO_DAYS('2024-02-01')),
    PARTITION p202402 VALUES LESS THAN (TO_DAYS('2024-03-01')),
    PARTITION p202403 VALUES LESS THAN (TO_DAYS('2024-04-01')),
    PARTITION p202404 VALUES LESS THAN (TO_DAYS('2024-05-01')),
    PARTITION p202405 VALUES LESS THAN (TO_DAYS('2024-06-01')),
    PARTITION p202406 VALUES LESS THAN (TO_DAYS('2024-07-01')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 配置追踪表按季度分区（示例）
ALTER TABLE yt_dist_config_trace
PARTITION BY RANGE (TO_DAYS(trace_time)) (
    PARTITION p2024q1 VALUES LESS THAN (TO_DAYS('2024-04-01')),
    PARTITION p2024q2 VALUES LESS THAN (TO_DAYS('2024-07-01')),
    PARTITION p2024q3 VALUES LESS THAN (TO_DAYS('2024-10-01')),
    PARTITION p2024q4 VALUES LESS THAN (TO_DAYS('2025-01-01')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 9. 初始化数据

### 9.1 基础等级数据

```sql
INSERT INTO `yt_dist_level` (`level_code`, `level_name`, `level_grade`, `icon_url`, `color`, `description`, `benefits`, `sort_order`, `status`, `tenant_id`) VALUES
('BRONZE', '青铜分销员', 1, '/icons/level_bronze.png', '#CD7F32', '入门级分销员', '基础佣金率，享受新手指导', 1, 1, 0),
('SILVER', '白银分销员', 2, '/icons/level_silver.png', '#C0C0C0', '进阶级分销员', '提升佣金率，享受专属培训', 2, 1, 0),
('GOLD', '黄金分销员', 3, '/icons/level_gold.png', '#FFD700', '高级分销员', '高佣金率，享受专属客服', 3, 1, 0),
('DIAMOND', '钻石分销员', 4, '/icons/level_diamond.png', '#B9F2FF', '顶级分销员', '最高佣金率，享受VIP服务', 4, 1, 0);
```

### 9.2 基础标签数据

```sql
INSERT INTO `yt_dist_agent_tag` (`tag_code`, `tag_name`, `tag_color`, `tag_desc`, `sort_order`, `status`, `tenant_id`) VALUES
('KOL', 'KOL达人', '#FF6B6B', '社交媒体影响力较大的分销员', 1, 1, 0),
('NEWBIE', '新手分销员', '#4ECDC4', '新注册的分销员', 2, 1, 0),
('ACTIVE', '活跃分销员', '#45B7D1', '近期活跃度较高的分销员', 3, 1, 0),
('TOP_SELLER', '销售冠军', '#96CEB4', '销售业绩优秀的分销员', 4, 1, 0),
('TEAM_LEADER', '团队领袖', '#FFEAA7', '团队管理能力强的分销员', 5, 1, 0);
```

### 9.3 默认奖励方案

```sql
INSERT INTO `yt_dist_reward_scheme` (`scheme_code`, `scheme_name`, `scheme_desc`, `apply_scope`, `enable_sales_reward`, `enable_profit_sharing`, `profit_target`, `max_trace_level`, `default_sales_mode`, `default_sales_rate`, `default_profit_config`, `min_order_amount`, `trigger_stage`, `freeze_days`, `priority`, `status`, `tenant_id`) VALUES
('DEFAULT', '默认分销方案', '系统默认的分销奖励方案，适用于所有商品', 1, 1, 1, 3, 3, 2, 5.00, '{"1":{"mode":2,"rate":3.0},"2":{"mode":2,"rate":2.0},"3":{"mode":2,"rate":1.0}}', 0.01, 1, 7, 100, 1, 0);
```

## 10. 数据维护

### 10.1 定期清理策略

```sql
-- 清理过期的配置追踪记录（保留3个月）
DELETE FROM yt_dist_config_trace 
WHERE trace_time < DATE_SUB(NOW(), INTERVAL 3 MONTH);

-- 清理失效的分销员海报（保留1年）
DELETE FROM yt_dist_agent_poster 
WHERE status = 0 AND create_time < DATE_SUB(NOW(), INTERVAL 1 YEAR);

-- 归档历史佣金账单（移到历史表）
INSERT INTO yt_dist_commission_bill_history 
SELECT * FROM yt_dist_commission_bill 
WHERE create_time < DATE_SUB(NOW(), INTERVAL 2 YEAR);

DELETE FROM yt_dist_commission_bill 
WHERE create_time < DATE_SUB(NOW(), INTERVAL 2 YEAR);
```

### 10.2 统计数据更新

```sql
-- 更新分销员统计数据
UPDATE yt_dist_agent a SET 
  team_count = (
    SELECT COUNT(*) FROM yt_dist_agent b 
    WHERE b.path LIKE CONCAT(a.path, a.id, '/%') AND b.deleted = 0
  ),
  direct_count = (
    SELECT COUNT(*) FROM yt_dist_agent b 
    WHERE b.parent_id = a.id AND b.deleted = 0
  ),
  total_commission = (
    SELECT COALESCE(SUM(commission_amount), 0) FROM yt_dist_commission_bill b 
    WHERE b.agent_id = a.id AND b.bill_status IN (2,3,4) AND b.deleted = 0
  ),
  available_commission = (
    SELECT COALESCE(SUM(commission_amount), 0) FROM yt_dist_commission_bill b 
    WHERE b.agent_id = a.id AND b.bill_status = 2 AND b.deleted = 0
  ),
  frozen_commission = (
    SELECT COALESCE(SUM(commission_amount), 0) FROM yt_dist_commission_bill b 
    WHERE b.agent_id = a.id AND b.bill_status = 1 AND b.deleted = 0
  ),
  withdrawn_commission = (
    SELECT COALESCE(SUM(commission_amount), 0) FROM yt_dist_commission_bill b 
    WHERE b.agent_id = a.id AND b.bill_status = 4 AND b.deleted = 0
  )
WHERE a.deleted = 0;
```

## 11. 监控和告警

### 11.1 性能监控SQL

```sql
-- 监控数据库表大小
SELECT 
  table_name,
  ROUND(((data_length + index_length) / 1024 / 1024), 2) AS table_size_mb,
  table_rows
FROM information_schema.tables 
WHERE table_schema = 'yitong_distribution' 
  AND table_name LIKE 'yt_dist_%'
ORDER BY table_size_mb DESC;

-- 监控慢查询相关表
SELECT 
  table_schema,
  table_name,
  index_name,
  cardinality,
  pages
FROM information_schema.statistics 
WHERE table_schema = 'yitong_distribution' 
  AND table_name LIKE 'yt_dist_%'
ORDER BY cardinality DESC;
```

### 11.2 数据一致性检查

```sql
-- 检查分销员佣金统计数据一致性
SELECT 
  a.id,
  a.agent_name,
  a.total_commission AS agent_total,
  COALESCE(SUM(b.commission_amount), 0) AS bill_total,
  ABS(a.total_commission - COALESCE(SUM(b.commission_amount), 0)) AS diff
FROM yt_dist_agent a
LEFT JOIN yt_dist_commission_bill b ON a.id = b.agent_id AND b.bill_status IN (2,3,4) AND b.deleted = 0
WHERE a.deleted = 0
GROUP BY a.id, a.agent_name, a.total_commission
HAVING ABS(a.total_commission - COALESCE(SUM(b.commission_amount), 0)) > 0.01;

-- 检查分销关系一致性
SELECT 
  a.id,
  a.agent_name,
  a.parent_id,
  a.path,
  CASE 
    WHEN a.parent_id = 0 AND a.depth != 1 THEN '顶级分销员深度错误'
    WHEN a.parent_id != 0 AND p.id IS NULL THEN '上级分销员不存在'
    WHEN a.parent_id != 0 AND a.depth != p.depth + 1 THEN '深度计算错误'
    ELSE '正常'
  END AS check_result
FROM yt_dist_agent a
LEFT JOIN yt_dist_agent p ON a.parent_id = p.id AND p.deleted = 0
WHERE a.deleted = 0 
  AND (
    (a.parent_id = 0 AND a.depth != 1) OR
    (a.parent_id != 0 AND p.id IS NULL) OR
    (a.parent_id != 0 AND a.depth != p.depth + 1)
  );
```

通过这个完整的数据库设计，可以支撑分销系统的所有业务功能，并保证系统的性能和数据一致性。