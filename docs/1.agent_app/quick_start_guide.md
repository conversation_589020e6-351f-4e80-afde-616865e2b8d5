# 分销管理前端应用化改造快速开始指南

## 一、快速了解

### 1.1 改造目标
- 前端支持多应用数据隔离展示
- 提供便捷的应用切换功能
- 所有分销操作限定在单一应用内

### 1.2 核心变化
1. **界面变化**：增加应用选择器
2. **数据变化**：所有数据按应用过滤
3. **操作变化**：批量操作限制同应用

## 二、快速开始步骤

### Step 1: 创建基础组件（30分钟）

1. **复制应用选择组件**
```bash
# 从示例文件创建实际组件
cp src/views/distribution-v4/components/selects/AppSelect.vue \
   src/views/distribution-v4/components/selects/AppSelect.vue

cp src/views/distribution-v4/components/GlobalAppSelector.vue \
   src/views/distribution-v4/components/GlobalAppSelector.vue
```

2. **创建状态管理**
```bash
# 确保 store 目录存在
mkdir -p src/store/modules

# 复制状态管理文件
cp src/store/modules/distribution.ts \
   src/store/modules/distribution.ts
```

### Step 2: 准备工作（5分钟）

1. **确认后端接口已支持 appId**
   - 所有分销相关接口都可以接收 appId 参数
   - 后端会根据 appId 过滤数据

2. **理解参数传递策略**
   - 不使用请求拦截器自动注入
   - 每个 API 调用显式传递 appId
   - 从查询参数或 store 获取 appId

### Step 3: 改造第一个页面（分销员列表）

1. **修改列表页** `src/views/distribution-v4/agent/index.vue`

```vue
<template>
  <!-- 添加全局应用选择器 -->
  <GlobalAppSelector />
  
  <!-- 现有内容... -->
  
  <!-- 在搜索表单中添加 -->
  <el-form-item label="所属应用" prop="appId">
    <AppSelect
      v-model="queryParams.appId"
      clearable
      class="!w-240px"
      placeholder="全部应用"
      :show-channel="true"
    />
  </el-form-item>
</template>

<script setup lang="ts">
// 导入新组件
import GlobalAppSelector from '../components/GlobalAppSelector.vue'
import AppSelect from '../components/selects/AppSelect.vue'
import { useDistributionStore } from '@/store/modules/distribution'

// 使用 store
const distributionStore = useDistributionStore()

// 修改查询参数
const queryParams = reactive({
  appId: undefined, // 新增
  // ... 其他参数
})

// 查询列表 - 显式传递 appId
const getList = async () => {
  loading.value = true
  try {
    // 构建参数，确保包含 appId
    const params = {
      ...queryParams,
      appId: queryParams.appId || distributionStore.currentAppId
    }
    const data = await DistAgentApi.getDistAgentList(params)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 初始化时加载应用列表
onMounted(() => {
  distributionStore.init()
  getList()
})
</script>
```

2. **修改 API 接口定义** `src/api/distribution/agent/index.ts`

```typescript
// 在 VO 接口中添加
export interface DistAgentVO {
  // ... 现有字段
  appId?: number
  appName?: string
  appLogo?: string
}

// 在查询参数中添加
export interface DistAgentPageReqVO {
  appId?: number // 新增
  // ... 其他参数
}
```

### Step 4: 验证改造效果

1. **启动项目**
```bash
pnpm dev
```

2. **访问分销员管理页面**
- 检查是否显示应用选择器
- 测试应用切换功能
- 验证数据是否按应用过滤

## 三、改造模板

### 3.1 列表页改造模板

```vue
<!-- 搜索表单添加 -->
<el-form-item label="所属应用" prop="appId">
  <AppSelect
    v-model="queryParams.appId"
    clearable
    class="!w-240px"
    placeholder="全部应用"
  />
</el-form-item>

<!-- 列表添加应用列 -->
<el-table-column label="所属应用" align="center" prop="appName" width="150">
  <template #default="scope">
    <div class="app-info-cell">
      <el-image
        v-if="scope.row.appLogo"
        :src="scope.row.appLogo"
        class="app-logo-small"
        fit="cover"
      />
      <span>{{ scope.row.appName || '-' }}</span>
    </div>
  </template>
</el-table-column>
```

### 3.2 表单页改造模板

```vue
<!-- 表单添加应用选择 -->
<el-form-item label="所属应用" prop="appId">
  <AppSelect
    v-model="formData.appId"
    :disabled="formType === 'update'"
    placeholder="请选择应用"
    :show-channel="true"
  />
  <div v-if="formType === 'update'" class="el-form-item__tips">
    创建后不可更换所属应用
  </div>
</el-form-item>

<!-- 表单验证规则 -->
const formRules = reactive({
  appId: [{ required: true, message: '请选择应用', trigger: 'change' }],
  // ... 其他规则
})
```

### 3.3 选择组件改造模板

```vue
<!-- 组件 props 添加 appId -->
const props = defineProps({
  appId: {
    type: Number,
    default: undefined
  }
})

<!-- 查询时过滤 -->
const loadData = async () => {
  const params: any = {}
  if (props.appId) {
    params.appId = props.appId
  }
  const data = await API.getList(params)
  // ...
}
```

## 四、常见问题

### Q1: 如何处理没有 appId 的旧数据？
A: 后端会将旧数据默认归属到 appId=2，前端正常显示即可。

### Q2: 用户没有选择应用怎么办？
A: 可以在 store 中设置默认应用，或提示用户必须选择应用。

### Q3: 如何实现跨应用查看？
A: 在特定场景下，可以临时清空 appId 参数实现跨应用查询。

### Q4: 批量操作如何限制？
A: 在批量操作前检查选中项的 appId 是否一致：
```typescript
const appIds = new Set(selectedRows.map(row => row.appId))
if (appIds.size > 1) {
  ElMessage.error('批量操作仅支持同一应用内的数据')
  return
}
```

## 五、调试技巧

1. **查看请求参数**
在浏览器开发者工具的 Network 面板查看请求是否包含 appId。

2. **检查 Store 状态**
```javascript
// 在控制台执行
const store = useDistributionStore()
console.log('当前应用:', store.currentAppId)
console.log('应用列表:', store.appList)
```

3. **临时禁用 appId**
如果需要查看所有数据，可以临时注释请求拦截器中的 appId 注入。

## 六、下一步

1. 按照详细 TODO 清单逐个改造其他模块
2. 完成所有模块后进行集成测试
3. 编写用户使用文档
4. 部署上线

---

**提示**: 建议先完成一个完整模块的改造，验证方案可行性后再批量改造其他模块。