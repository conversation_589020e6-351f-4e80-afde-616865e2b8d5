# 分销管理前端应用化改造实施方案 V2

## 一、核心改造策略

### 1.1 参数传递策略
- **不使用请求拦截器自动注入**
- **每个API调用显式传递 appId 参数**
- **保持代码清晰可控**

### 1.2 应用管理方式
1. **全局状态管理**：使用 Pinia store 管理当前应用
2. **组件获取**：从 store 获取当前 appId
3. **显式传递**：调用 API 时明确传入 appId

## 二、API 调用改造示例

### 2.1 分销员管理模块

#### 列表查询
```typescript
// 原来的调用方式
const getList = async () => {
  loading.value = true
  try {
    const data = await DistAgentApi.getDistAgentList(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 改造后的调用方式
const getList = async () => {
  loading.value = true
  try {
    // 确保查询参数包含 appId
    const params = {
      ...queryParams,
      appId: queryParams.appId || distributionStore.currentAppId
    }
    const data = await DistAgentApi.getDistAgentList(params)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
```

#### 创建分销员
```typescript
// 改造后的创建方法
const handleCreate = async (formData: DistAgentVO) => {
  try {
    // 确保包含 appId
    const data = {
      ...formData,
      appId: formData.appId || distributionStore.currentAppId
    }
    await DistAgentApi.createDistAgent(data)
    message.success('创建成功')
  } catch (error) {
    console.error('创建失败:', error)
  }
}
```

#### 统计查询
```typescript
// 获取统计数据时传入 appId
const getStatistics = async () => {
  statisticsLoading.value = true
  try {
    const params = {
      appId: queryParams.appId || distributionStore.currentAppId
    }
    const data = await DistAgentApi.getDistAgentOverallStatistics(params)
    statistics.value = data
  } finally {
    statisticsLoading.value = false
  }
}
```

### 2.2 佣金管理模块

```typescript
// 佣金列表查询
const getCommissionList = async () => {
  const params = {
    ...queryParams,
    appId: queryParams.appId || distributionStore.currentAppId
  }
  const data = await CommissionApi.getCommissionPage(params)
  // 处理数据...
}

// 批量结算
const handleBatchSettle = async (ids: number[]) => {
  const params = {
    ids,
    appId: distributionStore.currentAppId // 确保同应用
  }
  await CommissionApi.batchSettle(params)
}
```

### 2.3 商品配置模块

```typescript
// 获取商品配置列表
const getGoodsConfigList = async () => {
  const params = {
    ...queryParams,
    appId: queryParams.appId || distributionStore.currentAppId
  }
  const data = await GoodsConfigApi.getConfigPage(params)
  // 处理数据...
}

// 保存配置
const saveConfig = async (config: GoodsConfigVO) => {
  const data = {
    ...config,
    appId: config.appId || distributionStore.currentAppId
  }
  if (config.id) {
    await GoodsConfigApi.updateConfig(data)
  } else {
    await GoodsConfigApi.createConfig(data)
  }
}
```

## 三、组件改造示例

### 3.1 选择组件改造

#### AgentSelect 组件
```vue
<template>
  <el-select v-model="value" v-bind="$attrs">
    <el-option
      v-for="item in agentList"
      :key="item.id"
      :label="item.realName"
      :value="item.id"
    />
  </el-select>
</template>

<script setup lang="ts">
const props = defineProps({
  modelValue: [Number, String],
  appId: Number // 新增 appId 属性
})

const agentList = ref([])

// 加载分销员列表
const loadAgentList = async () => {
  try {
    // 显式传递 appId
    const params = {
      pageSize: 100,
      appId: props.appId || distributionStore.currentAppId
    }
    const data = await DistAgentApi.getDistAgentList(params)
    agentList.value = data.list
  } catch (error) {
    console.error('加载分销员列表失败:', error)
  }
}

// 监听 appId 变化
watch(() => props.appId, () => {
  if (props.appId) {
    loadAgentList()
  }
})
</script>
```

#### LevelSelect 组件
```vue
<script setup lang="ts">
const props = defineProps({
  modelValue: Number,
  appId: Number
})

const levelList = ref([])

const loadLevelList = async () => {
  try {
    const params = {
      appId: props.appId || distributionStore.currentAppId,
      status: 1
    }
    const data = await LevelApi.getLevelList(params)
    levelList.value = data
  } catch (error) {
    console.error('加载等级列表失败:', error)
  }
}
</script>
```

### 3.2 列表页改造模板

```vue
<template>
  <div>
    <!-- 全局应用选择器 -->
    <GlobalAppSelector />
    
    <ContentWrap>
      <el-form :model="queryParams" ref="queryFormRef" :inline="true">
        <!-- 应用筛选 -->
        <el-form-item label="所属应用" prop="appId">
          <AppSelect
            v-model="queryParams.appId"
            clearable
            placeholder="全部应用"
            @change="handleQuery"
          />
        </el-form-item>
        
        <!-- 其他筛选条件... -->
      </el-form>
    </ContentWrap>
    
    <!-- 列表... -->
  </div>
</template>

<script setup lang="ts">
import { useDistributionStore } from '@/store/modules/distribution'

const distributionStore = useDistributionStore()

// 查询参数
const queryParams = reactive({
  appId: undefined,
  // 其他参数...
})

// 查询列表
const getList = async () => {
  loading.value = true
  try {
    // 构建查询参数，确保包含 appId
    const params = {
      ...queryParams,
      // 如果没有选择应用，使用当前应用
      appId: queryParams.appId || distributionStore.currentAppId
    }
    
    const data = await API.getList(params)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  // 初始化 store
  distributionStore.init()
  
  // 如果有默认应用，设置到查询参数
  if (distributionStore.currentAppId) {
    queryParams.appId = distributionStore.currentAppId
  }
  
  // 查询数据
  getList()
})
</script>
```

### 3.3 表单页改造模板

```vue
<template>
  <el-form :model="formData" :rules="formRules">
    <!-- 应用选择 -->
    <el-form-item label="所属应用" prop="appId">
      <AppSelect
        v-model="formData.appId"
        :disabled="formType === 'update'"
        placeholder="请选择应用"
      />
    </el-form-item>
    
    <!-- 关联选择器传递 appId -->
    <el-form-item label="分销等级" prop="levelId">
      <LevelSelect
        v-model="formData.levelId"
        :app-id="formData.appId"
        :disabled="!formData.appId"
      />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
const formData = reactive({
  appId: distributionStore.currentAppId, // 默认当前应用
  levelId: undefined,
  // 其他字段...
})

// 提交表单
const submitForm = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) return
  
  try {
    // 确保包含 appId
    const data = {
      ...formData,
      appId: formData.appId || distributionStore.currentAppId
    }
    
    if (formType.value === 'create') {
      await API.create(data)
    } else {
      await API.update(data)
    }
    
    message.success('操作成功')
  } catch (error) {
    console.error('操作失败:', error)
  }
}
</script>
```

## 四、批量操作处理

```typescript
// 批量操作前检查应用一致性
const handleBatchOperation = async (operation: string) => {
  // 检查是否选中数据
  if (selectedRows.length === 0) {
    message.warning('请先选择数据')
    return
  }
  
  // 检查应用是否一致
  const appIds = new Set(selectedRows.map(row => row.appId))
  if (appIds.size > 1) {
    message.error('批量操作仅支持同一应用内的数据')
    return
  }
  
  // 获取应用ID
  const appId = selectedRows[0].appId
  
  // 执行批量操作
  const ids = selectedRows.map(row => row.id)
  const params = {
    ids,
    appId,
    operation
  }
  
  await API.batchOperation(params)
}
```

## 五、数据导出处理

```typescript
// 导出数据时包含 appId
const handleExport = async () => {
  try {
    await message.exportConfirm()
    exportLoading.value = true
    
    // 构建导出参数
    const params = {
      ...queryParams,
      appId: queryParams.appId || distributionStore.currentAppId
    }
    
    const data = await API.exportData(params)
    download.excel(data, '分销数据.xlsx')
  } finally {
    exportLoading.value = false
  }
}
```

## 六、关键注意事项

### 6.1 参数传递原则
1. **查询操作**：从查询表单或 store 获取 appId
2. **创建操作**：必须指定 appId
3. **更新操作**：不允许修改 appId
4. **批量操作**：验证所有数据属于同一应用

### 6.2 组件联动
```typescript
// 当应用变化时，清空关联数据
watch(() => formData.appId, (newAppId, oldAppId) => {
  if (newAppId !== oldAppId) {
    // 清空依赖应用的字段
    formData.levelId = undefined
    formData.tagIds = []
    formData.parentId = undefined
  }
})
```

### 6.3 默认值处理
```typescript
// 获取有效的 appId
const getEffectiveAppId = () => {
  return queryParams.appId || distributionStore.currentAppId || undefined
}

// 使用时
const params = {
  ...queryParams,
  appId: getEffectiveAppId()
}
```

## 七、测试要点

1. **参数验证**
   - 确保所有 API 调用都传递了 appId
   - 验证批量操作的应用一致性检查

2. **数据隔离**
   - 切换应用后数据正确过滤
   - 不同应用数据不会混淆

3. **用户体验**
   - 应用切换流畅
   - 错误提示清晰
   - 加载状态正确

## 八、实施步骤

1. **第一步**：完成基础组件（AppSelect、GlobalAppSelector、store）
2. **第二步**：改造一个完整模块（如分销员管理）作为示例
3. **第三步**：验证方案可行性
4. **第四步**：批量改造其他模块
5. **第五步**：集成测试和优化

这个方案不依赖请求拦截器，每个 API 调用都显式传递 appId，代码更加清晰可控。