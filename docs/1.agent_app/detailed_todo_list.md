# 分销管理前端应用化改造详细TODO清单

## 一、基础组件和框架（优先级：高）

### 1.1 创建应用选择组件
- [x] 创建 `src/views/distribution-v4/components/selects/AppSelect.vue`
  - [x] 实现基础下拉选择功能
  - [x] 支持显示应用Logo
  - [x] 支持显示渠道信息
  - [x] 支持搜索过滤
  - [x] 支持多选模式
  - [x] 处理空状态和加载状态
  - [x] 支持禁用状态

### 1.2 创建全局应用选择器
- [x] 创建 `src/views/distribution-v4/components/GlobalAppSelector.vue`
  - [x] 设计卡片式选择器UI
  - [x] 显示当前应用详情（名称、渠道、状态）
  - [x] 实现切换确认对话框
  - [x] 添加切换动画效果
  - [ ] 支持快捷切换（最近3个）
  - [x] 响应式设计（移动端适配）

### 1.3 创建状态管理
- [x] 创建 `src/store/modules/distribution.ts`
  - [x] 定义 state 结构（currentAppId, appList, loading等）
  - [x] 实现 loadAppList action
  - [x] 实现 setCurrentApp action
  - [x] 实现本地存储持久化
  - [ ] 实现应用权限过滤
  - [x] 添加应用信息缓存机制
  - [x] 实现 reset 和 refresh 方法

### 1.4 配置请求拦截器
- [ ] 修改 `src/utils/request/index.ts`
  - [ ] 识别分销模块URL（/distribution/）
  - [ ] 获取当前 appId
  - [ ] GET请求注入到params
  - [ ] POST/PUT请求注入到data
  - [ ] 处理特殊接口（不需要appId的）
  - [ ] 添加调试日志

## 二、分销员管理模块（优先级：高）

### 2.1 分销员列表页改造
- [x] 修改 `src/views/distribution-v4/agent/index.vue`
  - [x] 导入 AppSelect 组件
  - [x] 在搜索表单添加应用选择项
  - [x] queryParams 添加 appId 字段
  - [x] 列表添加"所属应用"列
  - [x] 修改统计接口调用（传入appId）
  - [x] 修改导出功能（包含appId）
  - [x] 批量操作添加同应用校验

### 2.2 分销员表单改造
- [x] 修改 `src/views/distribution-v4/agent/components/AgentDrawer.vue`
  - [x] 导入 AppSelect 组件
  - [x] 表单添加 appId 字段
  - [x] 添加 appId 必填验证规则
  - [x] 新增时可选择，编辑时只读
  - [x] 上级选择器添加 appId 过滤
  - [x] 等级选择器添加 appId 过滤
  - [x] 标签选择器添加 appId 过滤

### 2.3 分销员详情改造
- [x] 修改 `src/views/distribution-v4/agent/components/AgentDetailDrawer.vue`
  - [x] 添加应用信息展示区域
  - [ ] 团队列表过滤同应用数据
  - [ ] 业绩统计限定应用范围

### 2.4 相关组件改造
- [x] 修改 `src/views/distribution-v4/components/selects/AgentSelect.vue`
  - [x] 添加 appId 属性
  - [x] 根据 appId 过滤分销员列表
  - [ ] 显示分销员所属应用

### 2.5 API接口调整
- [x] 修改 `src/api/distribution/agent/index.ts`
  - [x] DistAgentVO 添加 appId、appName、appLogo 字段
  - [x] DistAgentPageReqVO 添加 appId 字段
  - [ ] 所有查询方法确保传递 appId

## 三、佣金管理模块（优先级：高）

### 3.1 佣金列表页改造
- [ ] 修改 `src/views/distribution-v4/commission/index.vue`
  - [ ] 搜索表单添加应用选择
  - [ ] queryParams 添加 appId
  - [ ] 列表添加应用信息列
  - [ ] 统计数据按应用过滤
  - [ ] 批量结算限制同应用

### 3.2 佣金详情改造
- [ ] 修改 `src/views/distribution-v4/commission/components/CommissionDetailDrawer.vue`
  - [ ] 显示佣金所属应用
  - [ ] 分销链路信息包含应用

### 3.3 API接口调整
- [ ] 修改 `src/api/distribution/commission/index.ts`
  - [ ] VO 添加应用相关字段
  - [ ] 查询参数添加 appId

## 四、商品分销配置模块（优先级：高）

### 4.1 配置列表页改造
- [ ] 修改 `src/views/distribution-v4/goods/index.vue`
  - [ ] 搜索表单添加应用选择
  - [ ] 列表显示适用应用
  - [ ] 批量配置检查应用一致性

### 4.2 配置表单改造
- [ ] 修改 `src/views/distribution-v4/goods/components/GoodsConfigDrawer.vue`
  - [ ] 添加应用选择字段
  - [ ] 支持选择多个应用
  - [ ] 不同应用不同配置的处理

### 4.3 API接口调整
- [ ] 修改 `src/api/distribution/goods-config.ts`
  - [ ] 所有接口支持 appId

## 五、分销等级管理模块（优先级：中）

### 5.1 等级列表页改造
- [ ] 修改 `src/views/distribution-v4/level/index.vue`
  - [ ] 添加适用范围筛选（全局/应用）
  - [ ] 显示等级适用的应用
  - [ ] 区分展示全局等级和应用等级

### 5.2 等级表单改造
- [ ] 修改 `src/views/distribution-v4/level/components/LevelDrawer.vue`
  - [ ] 添加适用范围选择
  - [ ] 应用等级需选择具体应用
  - [ ] 处理等级继承关系

### 5.3 等级选择组件改造
- [ ] 修改 `src/views/distribution-v4/components/selects/LevelSelect.vue`
  - [ ] 添加 appId 属性
  - [ ] 根据 appId 过滤等级列表
  - [ ] 优先显示应用等级，其次全局等级

## 六、分销标签管理模块（优先级：中）

### 6.1 标签列表页改造
- [ ] 修改 `src/views/distribution-v4/tag/index.vue`
  - [ ] 搜索添加应用筛选
  - [ ] 显示标签所属应用
  - [ ] 批量操作校验

### 6.2 标签表单改造
- [ ] 修改 `src/views/distribution-v4/tag/components/TagDrawer.vue`
  - [ ] 添加应用选择
  - [ ] 标签合并限制

### 6.3 标签选择组件改造
- [ ] 修改 `src/views/distribution-v4/components/selects/AgentTagSelect.vue`
  - [ ] 添加 appId 过滤

## 七、提现管理模块（优先级：中）

### 7.1 提现列表页改造
- [ ] 修改 `src/views/distribution-v4/withdraw/index.vue`
  - [ ] 添加应用筛选
  - [ ] 显示提现应用
  - [ ] 批量审核校验

### 7.2 API接口调整
- [ ] 相关接口添加 appId 支持

## 八、其他模块（优先级：低）

### 8.1 分销海报模块
- [ ] 海报模板关联应用
- [ ] 生成海报包含应用信息

### 8.2 统计报表模块
- [ ] 默认展示当前应用数据
- [ ] 支持多应用对比分析
- [ ] 导出包含应用信息

## 九、集成测试和优化（优先级：高）

### 9.1 功能测试
- [ ] 应用切换流程测试
- [ ] 数据隔离验证
- [ ] 权限控制测试
- [ ] 批量操作测试

### 9.2 性能优化
- [ ] 应用列表缓存优化
- [ ] 切换应用防抖处理
- [ ] 大数据量测试

### 9.3 用户体验优化
- [ ] Loading 状态优化
- [ ] 错误提示优化
- [ ] 快捷操作优化
- [ ] 移动端适配

### 9.4 文档和示例
- [ ] 编写使用文档
- [ ] 创建示例代码
- [ ] 录制操作视频

## 十、实施时间估算

| 阶段 | 内容 | 预计时间 | 优先级 |
|------|------|----------|--------|
| 第一阶段 | 基础组件和框架 | 2天 | 高 |
| 第二阶段 | 分销员管理模块 | 2天 | 高 |
| 第三阶段 | 佣金管理模块 | 1.5天 | 高 |
| 第四阶段 | 商品配置模块 | 1.5天 | 高 |
| 第五阶段 | 等级标签模块 | 2天 | 中 |
| 第六阶段 | 其他模块 | 2天 | 低 |
| 第七阶段 | 测试优化 | 2天 | 高 |
| **总计** | | **13天** | |

## 十一、风险点和注意事项

1. **数据一致性**：确保前后端 appId 传递一致
2. **权限控制**：用户只能看到有权限的应用
3. **性能影响**：避免频繁切换应用导致的性能问题
4. **用户习惯**：提供清晰的引导，帮助用户适应新功能
5. **向后兼容**：确保不影响现有功能的使用