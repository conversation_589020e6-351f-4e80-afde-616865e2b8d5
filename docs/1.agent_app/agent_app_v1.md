# 分销模块应用化改造方案 v1.0

## 一、背景与目标

### 1.1 现状分析
当前分销模块是一个全局化的模块，所有分销员、分销商品、佣金方案等都是系统级别的，没有区分不同的应用渠道。这导致：
- 无法为不同应用渠道设置不同的分销策略
- 分销商品无法区分渠道来源
- 佣金结算无法按渠道隔离
- 分销数据统计混杂在一起

### 1.2 改造目标
- 实现分销模块的应用化，支持多应用渠道分销管理
- 分销员归属于特定应用，实现应用间隔离
- 分销商品按应用渠道区分，支持不同渠道的商品分销配置
- 佣金方案支持应用级别的差异化配置
- 数据统计支持按应用维度查看

## 二、数据库改造方案

### 2.1 核心表结构调整

#### 2.1.1 分销员表（yt_dist_agent）
```sql
ALTER TABLE `yt_dist_agent` 
ADD COLUMN `app_id` bigint(20) NOT NULL COMMENT '应用ID' AFTER `member_id`,
ADD INDEX `idx_app_id` (`app_id`);
```

#### 2.1.2 分销商品配置表（yt_dist_product_config）
```sql
ALTER TABLE `yt_dist_product_config` 
ADD COLUMN `app_id` bigint(20) NOT NULL COMMENT '应用ID' AFTER `spu_id`,
ADD INDEX `idx_app_spu` (`app_id`, `spu_id`);

-- 修改唯一索引
ALTER TABLE `yt_dist_product_config` 
DROP INDEX `uk_spu_id`,
ADD UNIQUE KEY `uk_app_spu` (`app_id`, `spu_id`);
```

#### 2.1.3 奖励方案表（yt_dist_reward_scheme）
```sql
ALTER TABLE `yt_dist_reward_scheme` 
ADD COLUMN `app_id` bigint(20) DEFAULT NULL COMMENT '应用ID，NULL表示全局方案' AFTER `scheme_code`,
ADD INDEX `idx_app_id` (`app_id`);
```

#### 2.1.4 佣金账单表（yt_dist_commission_bill）
```sql
ALTER TABLE `yt_dist_commission_bill` 
ADD COLUMN `app_id` bigint(20) NOT NULL COMMENT '应用ID' AFTER `agent_id`,
ADD INDEX `idx_app_agent` (`app_id`, `agent_id`);
```

#### 2.1.5 提现记录表（yt_dist_withdraw_record）
```sql
ALTER TABLE `yt_dist_withdraw_record` 
ADD COLUMN `app_id` bigint(20) NOT NULL COMMENT '应用ID' AFTER `agent_id`,
ADD INDEX `idx_app_agent` (`app_id`, `agent_id`);
```

### 2.2 新增应用管理表

#### 2.2.1 分销应用配置表
```sql
CREATE TABLE `yt_dist_app_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` bigint(20) NOT NULL COMMENT '应用ID',
  `app_name` varchar(64) NOT NULL COMMENT '应用名称',
  `enable_distribution` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用分销',
  `auto_audit` tinyint(1) NOT NULL DEFAULT '0' COMMENT '分销员申请自动审核',
  `withdraw_min_amount` decimal(10,2) DEFAULT '100.00' COMMENT '最低提现金额',
  `withdraw_fee_rate` decimal(5,2) DEFAULT '0.00' COMMENT '提现手续费率（%）',
  `commission_freeze_days` int(11) DEFAULT '0' COMMENT '佣金冻结天数',
  `max_trace_level` int(11) DEFAULT '3' COMMENT '最大追溯层级',
  `share_config` json DEFAULT NULL COMMENT '分享配置（JSON）',
  `page_config` json DEFAULT NULL COMMENT '页面配置（JSON）',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_id` (`app_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销应用配置表';
```

## 三、业务逻辑改造

### 3.1 分销员管理
1. **申请成为分销员**
   - 需要指定所属应用ID
   - 不同应用的分销员相互独立
   - 支持同一个会员在不同应用成为分销员

2. **分销员查询**
   - 所有查询接口增加app_id过滤条件
   - 后台管理支持按应用筛选
   - 前端API只查询当前应用的分销员

3. **分销关系**
   - 上下级关系限定在同一应用内
   - 跨应用不能建立分销关系

### 3.2 商品分销配置
1. **商品分销设置**
   - 同一商品可以在不同应用设置不同的分销配置
   - 佣金比例、限制条件等可以差异化配置

2. **分销商品列表**
   - 按应用展示可分销商品
   - 支持应用级别的商品推荐

### 3.3 佣金方案
1. **方案配置**
   - 支持全局方案（app_id为NULL）
   - 支持应用专属方案
   - 应用专属方案优先级高于全局方案

2. **佣金计算**
   - 优先使用应用专属方案
   - 若无专属方案，使用全局方案
   - 确保佣金计算的应用隔离

### 3.4 数据统计
1. **统计维度**
   - 新增应用维度的统计
   - 支持查看单个应用的分销数据
   - 支持多应用对比分析

2. **报表改造**
   - 所有报表增加应用筛选条件
   - 支持导出应用维度的数据

## 四、接口改造方案

### 4.1 管理后台接口
1. **列表查询接口**
   - 请求参数增加 `appId` 字段
   - 支持按应用过滤数据

2. **创建/修改接口**
   - 必填 `appId` 字段
   - 校验应用是否启用分销功能

### 4.2 前端API接口
1. **请求头处理**
   - 从请求头或Token中获取当前应用ID
   - 自动注入到查询条件中

2. **数据隔离**
   - 确保只能操作当前应用的数据
   - 防止跨应用数据访问

## 五、实施步骤

### 5.1 第一阶段：数据库改造（2天）
1. 执行数据库表结构变更脚本
2. 历史数据迁移（设置默认app_id）
3. 创建必要的索引

### 5.2 第二阶段：核心功能改造（5天）
1. 改造分销员管理模块
2. 改造商品分销配置模块
3. 改造佣金计算逻辑
4. 改造提现管理模块

### 5.3 第三阶段：接口适配（3天）
1. 管理后台接口改造
2. 前端API接口改造
3. 接口文档更新

### 5.4 第四阶段：测试与上线（2天）
1. 功能测试
2. 性能测试
3. 数据验证
4. 灰度发布

## 六、兼容性方案

### 6.1 向后兼容
1. 历史数据统一归属到默认应用（app_id=1）
2. 旧接口保持兼容，默认查询默认应用数据
3. 逐步引导客户使用新接口

### 6.2 数据迁移
```sql
-- 示例：历史数据迁移脚本
UPDATE yt_dist_agent SET app_id = 1 WHERE app_id IS NULL;
UPDATE yt_dist_product_config SET app_id = 1 WHERE app_id IS NULL;
UPDATE yt_dist_commission_bill SET app_id = 1 WHERE app_id IS NULL;
UPDATE yt_dist_withdraw_record SET app_id = 1 WHERE app_id IS NULL;
```

## 七、风险与应对

### 7.1 数据一致性风险
- **风险**：改造过程中可能出现数据不一致
- **应对**：分批次迁移，每批次验证数据完整性

### 7.2 性能影响
- **风险**：新增字段和索引可能影响查询性能
- **应对**：优化索引设计，必要时分表处理

### 7.3 业务中断风险
- **风险**：改造期间影响正常业务
- **应对**：采用灰度发布，逐步切换

## 八、多级分销体系详细设计

### 8.0 现有结构分析与拓展方案

#### 8.0.1 现有数据库结构分析
通过分析现有的 `yitong_distribution.sql`，发现系统已经具备了部分多级分销的基础：

1. **分销关系基础**
   - `yt_dist_agent` 表已包含 `ancestor_path`（祖先路径）字段，格式如：`/1/2/3/`
   - `team_depth` 字段记录团队深度，支持层级关系
   - `parent_id` 字段记录直接上级

2. **佣金分配基础**  
   - `yt_dist_level` 表已设计了三级佣金比例字段：
     - `first_commission_rate` - 一级佣金比例
     - `second_commission_rate` - 二级佣金比例  
     - `third_commission_rate` - 三级佣金比例
   - 但目前限制为固定3级，缺乏灵活性

3. **现有不足**
   - 缺少应用维度（app_id）支持
   - ancestor_path 查询效率低，需要优化
   - 无法动态配置分销层级数
   - 缺少跨应用共享机制

#### 8.0.2 基于现有结构的拓展策略

1. **最小化改动原则**
   - 保留现有表结构和字段
   - 通过新增字段和表来扩展功能
   - 确保向后兼容

2. **渐进式改造**
   - 第一步：增加app_id支持多应用
   - 第二步：优化多级查询性能
   - 第三步：实现动态层级配置
   - 第四步：支持跨应用共享

### 8.1 核心概念

#### 8.1.1 分销层级定义
- **直销层（L0）**：商品的直接销售者，获得销售佣金
- **一级分销（L1）**：直销者的上级，获得一级分润
- **二级分销（L2）**：一级分销的上级，获得二级分润
- **N级分销（Ln）**：支持动态配置最大分销层级

#### 8.1.2 佣金分配模式
1. **固定比例模式**：每级按固定比例分配
2. **递减比例模式**：越高层级分润比例越低
3. **差额分配模式**：基于等级差额分配佣金
4. **自定义模式**：支持复杂的分配规则

### 8.2 基于现有结构的数据库拓展设计

#### 8.2.1 扩展现有等级表支持动态层级
```sql
-- 在现有 yt_dist_level 表基础上增加字段
ALTER TABLE `yt_dist_level` 
ADD COLUMN `app_id` bigint(20) DEFAULT NULL COMMENT '应用ID，NULL表示全局配置' AFTER `id`,
ADD COLUMN `commission_config` json DEFAULT NULL COMMENT '动态佣金配置（支持N级）' AFTER `third_commission_rate`,
ADD COLUMN `max_trace_level` int(11) DEFAULT '3' COMMENT '最大追溯层级' AFTER `commission_config`,
ADD INDEX `idx_app_id` (`app_id`);

-- 动态佣金配置JSON示例：
-- {
--   "mode": "fixed", // fixed-固定比例, decreasing-递减, custom-自定义
--   "levels": [
--     {"level": 1, "rate": 20.00, "minAmount": 0},
--     {"level": 2, "rate": 10.00, "minAmount": 0},
--     {"level": 3, "rate": 5.00, "minAmount": 0},
--     {"level": 4, "rate": 2.00, "minAmount": 100}
--   ],
--   "rules": {
--     "enableDynamic": true,
--     "conditions": []
--   }
-- }
```

#### 8.2.2 优化分销关系查询 - 新增关系缓存表
```sql
-- 基于现有 ancestor_path 字段，创建关系缓存表优化查询
CREATE TABLE `yt_dist_relation_cache` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` bigint(20) NOT NULL COMMENT '应用ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `ancestor_id` bigint(20) NOT NULL COMMENT '祖先分销员ID', 
  `level_diff` int(11) NOT NULL COMMENT '层级差（1表示直接上级）',
  `relation_path` varchar(500) NOT NULL COMMENT '关系路径（冗余自ancestor_path）',
  `agent_level_id` bigint(20) DEFAULT NULL COMMENT '分销员等级ID（冗余）',
  `ancestor_level_id` bigint(20) DEFAULT NULL COMMENT '祖先等级ID（冗余）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_agent_ancestor` (`app_id`, `agent_id`, `ancestor_id`),
  KEY `idx_app_ancestor` (`app_id`, `ancestor_id`),
  KEY `idx_level_diff` (`level_diff`),
  KEY `idx_update_time` (`update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销关系缓存表';

-- 创建触发器，当 yt_dist_agent 的 ancestor_path 变更时更新缓存
DELIMITER $$
CREATE TRIGGER `trg_dist_agent_path_update` 
AFTER UPDATE ON `yt_dist_agent`
FOR EACH ROW
BEGIN
  IF OLD.ancestor_path != NEW.ancestor_path OR OLD.parent_id != NEW.parent_id THEN
    -- 标记需要重建缓存
    INSERT INTO yt_dist_relation_rebuild_log (agent_id, app_id, status) 
    VALUES (NEW.id, NEW.app_id, 0);
  END IF;
END$$
DELIMITER ;
```

#### 8.2.3 扩展佣金记录表支持多级
```sql
-- 修改现有 yt_dist_commission 表
ALTER TABLE `yt_dist_commission`
ADD COLUMN `app_id` bigint(20) NOT NULL COMMENT '应用ID' AFTER `agent_id`,
ADD COLUMN `source_agent_id` bigint(20) DEFAULT NULL COMMENT '来源分销员ID（产生销售的分销员）' AFTER `member_id`,
ADD COLUMN `trace_level` int(11) DEFAULT '0' COMMENT '追溯层级（0-直接销售，1-一级，2-二级...）' AFTER `source_agent_id`,
ADD COLUMN `level_config_snapshot` json DEFAULT NULL COMMENT '等级配置快照' AFTER `commission_amount`,
ADD INDEX `idx_app_agent` (`app_id`, `agent_id`),
ADD INDEX `idx_source_agent` (`source_agent_id`),
ADD INDEX `idx_trace_level` (`trace_level`);
```

### 8.3 核心功能实现

#### 8.3.1 基于 ancestor_path 的分销关系建立
```java
// 利用现有 ancestor_path 字段构建分销关系
public void buildDistributionRelation(Long agentId, Long parentId, Long appId) {
    // 1. 获取父级分销员信息
    DistAgent parent = distAgentMapper.selectById(parentId);
    if (parent == null) {
        throw new BusinessException("上级分销员不存在");
    }
    
    // 2. 构建当前分销员的祖先路径
    String ancestorPath = parent.getAncestorPath() + "/" + agentId;
    int teamDepth = parent.getTeamDepth() + 1;
    
    // 3. 更新分销员信息
    DistAgent agent = distAgentMapper.selectById(agentId);
    agent.setParentId(parentId);
    agent.setAncestorPath(ancestorPath);
    agent.setTeamDepth(teamDepth);
    agent.setAppId(appId);
    distAgentMapper.updateById(agent);
    
    // 4. 异步构建关系缓存（优化查询性能）
    asyncBuildRelationCache(agentId, ancestorPath, appId);
}

// 异步构建关系缓存
@Async
public void asyncBuildRelationCache(Long agentId, String ancestorPath, Long appId) {
    // 解析祖先路径: "1948149045648871426/1948149045648871427/1948149045648871428"
    String[] ancestors = ancestorPath.split("/");
    
    List<DistRelationCache> cacheList = new ArrayList<>();
    for (int i = 0; i < ancestors.length - 1; i++) {
        DistRelationCache cache = new DistRelationCache();
        cache.setAppId(appId);
        cache.setAgentId(agentId);
        cache.setAncestorId(Long.parseLong(ancestors[i]));
        cache.setLevelDiff(ancestors.length - i - 1);
        cache.setRelationPath(ancestorPath);
        cacheList.add(cache);
    }
    
    // 批量插入缓存
    distRelationCacheMapper.insertBatch(cacheList);
}
```

#### 8.3.2 支持动态多级的佣金计算引擎
```java
// 基于现有结构的动态佣金计算
public List<DistCommission> calculateCommission(Order order) {
    // 1. 获取销售分销员
    Long sourceAgentId = order.getAgentId();
    DistAgent sourceAgent = distAgentMapper.selectById(sourceAgentId);
    
    // 2. 获取该分销员的等级配置
    DistLevel agentLevel = distLevelMapper.selectById(sourceAgent.getLevelId());
    
    // 3. 解析动态佣金配置
    JSONObject commissionConfig = JSON.parseObject(agentLevel.getCommissionConfig());
    int maxTraceLevel = agentLevel.getMaxTraceLevel() != null ? 
        agentLevel.getMaxTraceLevel() : 3; // 默认3级
    
    // 4. 根据关系缓存计算各级佣金
    List<DistCommission> commissions = new ArrayList<>();
    
    // 4.1 直接销售佣金
    DistCommission directCommission = createCommission(
        order, sourceAgentId, sourceAgentId, 0, 
        getCommissionRate(commissionConfig, agentLevel, 0)
    );
    commissions.add(directCommission);
    
    // 4.2 上级分润（使用关系缓存表优化查询）
    List<DistRelationCache> ancestors = distRelationCacheMapper.selectList(
        new LambdaQueryWrapper<DistRelationCache>()
            .eq(DistRelationCache::getAppId, order.getAppId())
            .eq(DistRelationCache::getAgentId, sourceAgentId)
            .le(DistRelationCache::getLevelDiff, maxTraceLevel)
            .orderByAsc(DistRelationCache::getLevelDiff)
    );
    
    for (DistRelationCache relation : ancestors) {
        // 获取祖先的等级信息
        DistLevel ancestorLevel = distLevelMapper.selectById(relation.getAncestorLevelId());
        BigDecimal rate = getCommissionRate(commissionConfig, ancestorLevel, relation.getLevelDiff());
        
        if (rate.compareTo(BigDecimal.ZERO) > 0) {
            DistCommission commission = createCommission(
                order, relation.getAncestorId(), sourceAgentId, 
                relation.getLevelDiff(), rate
            );
            commissions.add(commission);
        }
    }
    
    return commissions;
}

// 获取指定层级的佣金比例
private BigDecimal getCommissionRate(JSONObject config, DistLevel level, int traceLevel) {
    // 优先使用动态配置
    if (config != null && config.containsKey("levels")) {
        JSONArray levels = config.getJSONArray("levels");
        for (int i = 0; i < levels.size(); i++) {
            JSONObject levelConfig = levels.getJSONObject(i);
            if (levelConfig.getInteger("level") == traceLevel) {
                return levelConfig.getBigDecimal("rate");
            }
        }
    }
    
    // 降级到固定三级配置
    if (traceLevel == 0) {
        return level.getCommissionRate(); // 直接销售使用基础佣金比例
    } else if (traceLevel == 1) {
        return level.getFirstCommissionRate();
    } else if (traceLevel == 2) {
        return level.getSecondCommissionRate();
    } else if (traceLevel == 3) {
        return level.getThirdCommissionRate();
    }
    
    return BigDecimal.ZERO;
}
```

### 8.4 高级特性

#### 8.4.1 动态层级调整
- 支持根据业绩动态调整分销层级
- 支持临时活动的层级扩展
- 支持特殊商品的层级限制

#### 8.4.2 佣金保护机制
- 最低佣金保障
- 佣金封顶限制
- 异常订单佣金冻结

## 九、应用间数据共享详细设计

### 9.1 共享模式

#### 9.1.1 分销员共享
1. **完全独立模式**：各应用分销员完全隔离
2. **主账号模式**：一个主分销员账号，多个应用子账号
3. **联盟模式**：分销员可选择加入多个应用联盟

#### 9.1.2 佣金共享
1. **独立结算**：各应用独立计算和结算佣金
2. **统一钱包**：多应用佣金进入统一钱包
3. **跨应用分润**：支持跨应用的分润机制

### 9.2 基于现有结构的数据共享设计

#### 9.2.1 扩展分销员表支持多应用
```sql
-- 在现有 yt_dist_agent 表基础上增加应用支持
ALTER TABLE `yt_dist_agent`
ADD COLUMN `app_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '所属应用ID' AFTER `member_id`,
ADD COLUMN `main_agent_id` bigint(20) DEFAULT NULL COMMENT '主账号ID（跨应用共享时使用）' AFTER `app_id`,
ADD COLUMN `share_status` tinyint(4) DEFAULT '0' COMMENT '共享状态：0-独立账号，1-主账号，2-子账号' AFTER `main_agent_id`,
ADD INDEX `idx_app_member` (`app_id`, `member_id`),
ADD INDEX `idx_main_agent` (`main_agent_id`);

-- 创建分销员跨应用映射表
CREATE TABLE `yt_dist_agent_app_mapping` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `main_agent_id` bigint(20) NOT NULL COMMENT '主分销员ID',
  `app_id` bigint(20) NOT NULL COMMENT '应用ID',
  `app_agent_id` bigint(20) NOT NULL COMMENT '应用内分销员ID',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `mapping_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '映射类型：1-完全共享，2-部分共享',
  `share_config` json DEFAULT NULL COMMENT '共享配置（权限、佣金分配等）',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_agent` (`app_id`, `app_agent_id`),
  KEY `idx_main_agent` (`main_agent_id`),
  KEY `idx_member_id` (`member_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员跨应用映射表';
```

#### 9.2.2 跨应用佣金分配表
```sql
CREATE TABLE `yt_dist_cross_app_commission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `source_app_id` bigint(20) NOT NULL COMMENT '来源应用ID',
  `target_app_id` bigint(20) NOT NULL COMMENT '目标应用ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `source_agent_id` bigint(20) NOT NULL COMMENT '来源分销员ID',
  `target_agent_id` bigint(20) NOT NULL COMMENT '目标分销员ID',
  `commission_amount` decimal(10,2) NOT NULL COMMENT '分配金额',
  `commission_rate` decimal(5,2) NOT NULL COMMENT '分配比例',
  `reason` varchar(256) DEFAULT NULL COMMENT '分配原因',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待结算，1-已结算',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_order` (`order_id`),
  KEY `idx_source` (`source_app_id`, `source_agent_id`),
  KEY `idx_target` (`target_app_id`, `target_agent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='跨应用佣金分配表';
```

#### 9.2.3 统一钱包表
```sql
CREATE TABLE `yt_dist_unified_wallet` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `agent_id` bigint(20) NOT NULL COMMENT '分销员主账号ID',
  `total_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '总余额',
  `available_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '可用余额',
  `frozen_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '冻结余额',
  `app_balances` json DEFAULT NULL COMMENT '各应用余额明细',
  `last_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `version` bigint(20) NOT NULL DEFAULT '0' COMMENT '版本号（乐观锁）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agent_id` (`agent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='统一钱包表';
```

### 9.3 共享机制实现

#### 9.3.1 分销员共享流程
1. **申请共享**
   - 分销员在A应用申请加入B应用
   - B应用审核是否接受共享
   - 设置共享参数（佣金分配比例等）

2. **数据同步**
   - 基础信息同步（姓名、联系方式等）
   - 等级映射（不同应用等级体系映射）
   - 标签转换（跨应用标签映射）

3. **权限控制**
   - 主应用拥有完整权限
   - 共享应用权限受限
   - 支持自定义权限配置

#### 9.3.2 佣金共享实现
```java
// 跨应用佣金分配示例
public void distributeCrossAppCommission(Order order) {
    // 1. 检查是否有跨应用分销关系
    CrossAppRelation relation = getCrossAppRelation(order);
    if (relation == null) return;
    
    // 2. 计算跨应用分配金额
    BigDecimal crossAppAmount = calculateCrossAppAmount(
        order.getCommission(), 
        relation.getShareRate()
    );
    
    // 3. 创建跨应用佣金记录
    CrossAppCommission commission = new CrossAppCommission();
    commission.setSourceAppId(order.getAppId());
    commission.setTargetAppId(relation.getTargetAppId());
    commission.setCommissionAmount(crossAppAmount);
    save(commission);
    
    // 4. 更新统一钱包
    updateUnifiedWallet(relation.getTargetAgentId(), crossAppAmount);
}
```

### 9.4 安全与合规

#### 9.4.1 数据安全
- 敏感数据加密存储
- 跨应用数据访问审计
- 定期安全评估

#### 9.4.2 合规要求
- 用户授权机制
- 数据使用协议
- 隐私保护措施

## 十、实施路线图

### 10.1 第一期：基础多级分销（1个月）
- 实现基础的多级分销体系
- 支持3级分销配置
- 固定比例佣金分配

### 10.2 第二期：高级分销功能（1.5个月）
- 动态层级配置
- 多种佣金分配模式
- 分销关系链优化

### 10.3 第三期：应用间共享（2个月）
- 分销员跨应用共享
- 统一钱包功能
- 跨应用佣金结算

### 10.4 第四期：智能化升级（1.5个月）
- 智能佣金计算
- 风险控制系统
- 数据分析平台

## 十一、基于现有结构的改造总结

### 11.1 改造优势

1. **最小化数据库变更**
   - 充分利用现有的 `ancestor_path` 和 `team_depth` 字段
   - 通过增加字段而非修改表结构来扩展功能
   - 保持向后兼容性，降低升级风险

2. **渐进式升级路径**
   - 第一阶段：仅增加 app_id 实现应用隔离
   - 第二阶段：通过缓存表优化查询性能
   - 第三阶段：通过 JSON 配置实现动态多级
   - 第四阶段：实现跨应用共享

3. **灵活的扩展性**
   - 利用 JSON 字段存储动态配置，无需频繁改表
   - 通过缓存表提升性能，不影响核心业务表
   - 支持新旧模式并存，平滑过渡

### 11.2 关键技术点

1. **ancestor_path 的巧妙利用**
   ```sql
   -- 现有格式：1948149045648871426/1948149045648871427/1948149045648871428
   -- 可直接用于：
   -- 1. 查找所有上级：WHERE FIND_IN_SET(agent_id, REPLACE(ancestor_path, '/', ','))
   -- 2. 查找所有下级：WHERE ancestor_path LIKE CONCAT('%/', agent_id, '/%')
   -- 3. 计算层级差：LENGTH(ancestor_path) - LENGTH(REPLACE(ancestor_path, '/', ''))
   ```

2. **JSON 配置的向后兼容**
   ```json
   {
     "mode": "compatible",  // 兼容模式
     "useLegacy": true,     // 使用旧字段
     "levels": [            // 新的动态配置
       {"level": 1, "rate": "use_first_commission_rate"},
       {"level": 2, "rate": "use_second_commission_rate"},
       {"level": 3, "rate": "use_third_commission_rate"},
       {"level": 4, "rate": 2.00}
     ]
   }
   ```

3. **关系缓存的异步构建**
   - 不影响主流程性能
   - 支持增量更新
   - 可随时重建

### 11.3 数据迁移方案

```sql
-- 1. 为历史数据设置默认 app_id
UPDATE yt_dist_agent SET app_id = 1 WHERE app_id IS NULL;
UPDATE yt_dist_commission SET app_id = 1 WHERE app_id IS NULL;

-- 2. 初始化关系缓存（可分批执行）
INSERT INTO yt_dist_relation_cache (app_id, agent_id, ancestor_id, level_diff, relation_path)
SELECT 
    1 as app_id,
    a1.id as agent_id,
    a2.id as ancestor_id,
    (LENGTH(a1.ancestor_path) - LENGTH(REPLACE(a1.ancestor_path, '/', ''))) - 
    (LENGTH(SUBSTRING_INDEX(a1.ancestor_path, CONCAT('/', a2.id, '/'), 1)) - 
     LENGTH(REPLACE(SUBSTRING_INDEX(a1.ancestor_path, CONCAT('/', a2.id, '/'), 1), '/', ''))) as level_diff,
    a1.ancestor_path as relation_path
FROM yt_dist_agent a1
JOIN yt_dist_agent a2 ON FIND_IN_SET(a2.id, REPLACE(a1.ancestor_path, '/', ','))
WHERE a1.id != a2.id;

-- 3. 初始化等级的动态配置
UPDATE yt_dist_level 
SET commission_config = JSON_OBJECT(
    'mode', 'legacy',
    'levels', JSON_ARRAY(
        JSON_OBJECT('level', 1, 'field', 'first_commission_rate'),
        JSON_OBJECT('level', 2, 'field', 'second_commission_rate'),
        JSON_OBJECT('level', 3, 'field', 'third_commission_rate')
    )
)
WHERE commission_config IS NULL;
```

### 11.4 性能优化建议

1. **索引优化**
   ```sql
   -- 为 ancestor_path 创建前缀索引
   ALTER TABLE yt_dist_agent ADD INDEX idx_ancestor_path_prefix (ancestor_path(100));
   
   -- 复合索引优化查询
   ALTER TABLE yt_dist_relation_cache 
   ADD INDEX idx_query_optimize (app_id, agent_id, level_diff, ancestor_id);
   ```

2. **缓存策略**
   - 使用 Redis 缓存热点分销关系
   - 定期预热常用查询
   - 设置合理的过期时间

3. **分表考虑**
   - 当数据量超过千万级时，考虑按 app_id 分表
   - 历史数据归档策略
   - 读写分离优化

### 11.5 监控与运维

1. **关键指标监控**
   - ancestor_path 更新频率
   - 关系缓存命中率
   - 佣金计算耗时
   - 跨应用查询性能

2. **数据一致性检查**
   ```sql
   -- 定期检查 ancestor_path 完整性
   SELECT id, ancestor_path, parent_id 
   FROM yt_dist_agent 
   WHERE parent_id > 0 
   AND ancestor_path NOT LIKE CONCAT('%/', parent_id, '/%');
   
   -- 检查缓存表同步状态
   SELECT COUNT(*) as missing_count
   FROM yt_dist_agent a
   WHERE NOT EXISTS (
       SELECT 1 FROM yt_dist_relation_cache c 
       WHERE c.agent_id = a.id AND c.app_id = a.app_id
   );
   ```

通过这种基于现有结构的拓展方案，我们可以在保持系统稳定性的同时，逐步实现多级分销和应用间数据共享的高级功能。