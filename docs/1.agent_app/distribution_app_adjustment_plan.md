# 分销管理后台应用化调整方案

## 一、总体设计思路

根据 `agent_app_only_v1.0.md` 文档要求，为分销管理后台增加应用维度（app_id）支持，实现按应用隔离的分销管理。

### 1.1 核心改造点

1. **全局应用选择器**：在分销管理顶部增加应用选择器，默认选中第一个应用
2. **列表页改造**：所有列表页增加应用筛选条件
3. **表单页改造**：新增/编辑页面增加应用选择字段
4. **API 接口调整**：所有接口增加 appId 参数
5. **数据隔离**：确保不同应用的数据完全隔离

### 1.2 应用选择组件设计

已创建通用的应用选择组件 `AppSelect.vue`，特点：
- 支持单选/多选模式
- 显示应用Logo和名称
- 支持按渠道过滤
- 懒加载应用列表
- 可配置是否显示渠道信息

## 二、具体页面调整方案

### 2.1 分销员管理 (`/distribution-v4/agent`)

#### 2.1.1 列表页调整

**文件**: `src/views/distribution-v4/agent/index.vue`

```vue
<!-- 在搜索表单中增加应用选择 -->
<el-form-item label="所属应用" prop="appId">
  <AppSelect
    v-model="queryParams.appId"
    clearable
    class="!w-240px"
    placeholder="请选择应用"
    :show-channel="true"
  />
</el-form-item>
```

**修改 queryParams**:
```typescript
const queryParams = reactive({
  appId: undefined, // 新增
  keyword: '',
  levelId: undefined,
  tagIds: [],
  status: undefined,
  dateRange: [],
  pageNo: 1,
  pageSize: 10
})
```

#### 2.1.2 新增/编辑表单调整

**文件**: `src/views/distribution-v4/agent/components/AgentForm.vue`

```vue
<!-- 在表单中增加应用选择 -->
<el-form-item label="所属应用" prop="appId">
  <AppSelect
    v-model="formData.appId"
    :disabled="formType === 'update'"
    placeholder="请选择应用"
    :show-channel="true"
  />
</el-form-item>
```

**表单验证规则**:
```typescript
const formRules = reactive({
  appId: [{ required: true, message: '请选择应用', trigger: 'change' }],
  // ... 其他规则
})
```

### 2.2 佣金管理 (`/distribution-v4/commission`)

#### 2.2.1 列表页调整

**文件**: `src/views/distribution-v4/commission/index.vue`

```vue
<!-- 搜索表单增加应用选择 -->
<el-form-item label="所属应用" prop="appId">
  <AppSelect
    v-model="queryParams.appId"
    clearable
    class="!w-240px"
    placeholder="请选择应用"
  />
</el-form-item>
```

#### 2.2.2 统计页面调整

**文件**: `src/views/distribution-v4/statistics/index.vue`

- 增加应用维度的统计数据展示
- 支持按应用筛选统计数据
- 增加应用对比分析功能

### 2.3 商品分销配置 (`/distribution-v4/goods`)

#### 2.3.1 列表页调整

**文件**: `src/views/distribution-v4/goods/index.vue`

```vue
<!-- 搜索表单增加应用选择 -->
<el-form-item label="所属应用" prop="appId">
  <AppSelect
    v-model="queryParams.appId"
    clearable
    class="!w-240px"
    placeholder="请选择应用"
  />
</el-form-item>
```

#### 2.3.2 配置表单调整

**文件**: `src/views/distribution-v4/goods/components/GoodsConfigDrawer.vue`

```vue
<!-- 配置表单中增加应用选择 -->
<el-form-item label="适用应用" prop="appId">
  <AppSelect
    v-model="formData.appId"
    placeholder="请选择应用"
    :disabled="isEdit && formData.id"
  />
</el-form-item>
```

### 2.4 分销等级管理 (`/distribution-v4/level`)

#### 2.4.1 列表页调整

**文件**: `src/views/distribution-v4/level/index.vue`

```vue
<!-- 搜索表单增加应用选择 -->
<el-form-item label="适用应用" prop="appId">
  <AppSelect
    v-model="queryParams.appId"
    clearable
    class="!w-240px"
    placeholder="全部应用"
    :show-channel="true"
  />
</el-form-item>
```

#### 2.4.2 等级表单调整

**文件**: `src/views/distribution-v4/level/components/LevelDrawer.vue`

```vue
<!-- 表单中增加应用选择 -->
<el-form-item label="适用范围" prop="appId">
  <el-radio-group v-model="scopeType" @change="handleScopeChange">
    <el-radio :label="1">全局等级</el-radio>
    <el-radio :label="2">应用专属</el-radio>
  </el-radio-group>
</el-form-item>

<el-form-item v-if="scopeType === 2" label="选择应用" prop="appId">
  <AppSelect
    v-model="formData.appId"
    placeholder="请选择应用"
    :show-channel="true"
  />
</el-form-item>
```

### 2.5 分销标签管理 (`/distribution-v4/tag`)

#### 2.5.1 列表页调整

**文件**: `src/views/distribution-v4/tag/index.vue`

```vue
<!-- 搜索表单增加应用选择 -->
<el-form-item label="所属应用" prop="appId">
  <AppSelect
    v-model="queryParams.appId"
    clearable
    class="!w-240px"
    placeholder="请选择应用"
  />
</el-form-item>
```

## 三、API 接口调整方案

### 3.1 分销员接口调整

**文件**: `src/api/distribution/agent/index.ts`

```typescript
// 修改查询参数接口
export interface DistAgentPageReqVO {
  appId?: number      // 新增
  keyword?: string
  levelId?: number
  status?: number
  // ... 其他参数
}

// 修改 VO 接口
export interface DistAgentVO {
  id?: number
  appId: number       // 新增
  memberId: number
  // ... 其他字段
}
```

### 3.2 佣金接口调整

**文件**: `src/api/distribution/commission/index.ts`

```typescript
export interface DistCommissionPageReqVO {
  appId?: number      // 新增
  agentId?: number
  // ... 其他参数
}

export interface DistCommissionVO {
  id?: number
  appId: number       // 新增
  agentId: number
  // ... 其他字段
}
```

### 3.3 商品配置接口调整

**文件**: `src/api/distribution/goods-config.ts`

```typescript
export interface DistGoodsConfigPageReqVO {
  appId?: number      // 新增
  goodsId?: number
  // ... 其他参数
}

export interface DistGoodsConfigVO {
  id?: number
  appId: number       // 新增
  goodsId: number
  // ... 其他字段
}
```

## 四、全局应用切换方案

### 4.1 创建全局应用状态管理

**文件**: `src/store/modules/distribution.ts`

```typescript
import { defineStore } from 'pinia'
import * as AppInfoApi from '@/api/app/appinfo'

export const useDistributionStore = defineStore('distribution', {
  state: () => ({
    currentAppId: null as number | null,
    appList: [] as any[],
    loading: false
  }),
  
  getters: {
    currentApp: (state) => {
      return state.appList.find(app => app.id === state.currentAppId)
    }
  },
  
  actions: {
    // 加载应用列表
    async loadAppList() {
      this.loading = true
      try {
        const data = await AppInfoApi.getInfoList({ status: 1 })
        this.appList = data || []
        
        // 如果没有选中应用，默认选中第一个
        if (!this.currentAppId && this.appList.length > 0) {
          this.currentAppId = this.appList[0].id
        }
      } finally {
        this.loading = false
      }
    },
    
    // 切换应用
    setCurrentApp(appId: number) {
      this.currentAppId = appId
      // 保存到本地存储
      localStorage.setItem('distribution_current_app_id', String(appId))
    },
    
    // 初始化
    async init() {
      // 从本地存储恢复
      const savedAppId = localStorage.getItem('distribution_current_app_id')
      if (savedAppId) {
        this.currentAppId = Number(savedAppId)
      }
      
      await this.loadAppList()
    }
  }
})
```

### 4.2 创建全局应用选择器组件

**文件**: `src/views/distribution-v4/components/GlobalAppSelector.vue`

```vue
<template>
  <div class="global-app-selector">
    <el-card class="selector-card">
      <div class="selector-header">
        <Icon icon="ep:office-building" class="selector-icon" />
        <span class="selector-label">当前应用：</span>
        <AppSelect
          v-model="currentAppId"
          :clearable="false"
          :show-logo="true"
          :show-channel="true"
          placeholder="请选择应用"
          @change="handleAppChange"
          class="selector-input"
        />
        <el-tooltip content="切换应用将重新加载数据" placement="bottom">
          <Icon icon="ep:info-filled" class="info-icon" />
        </el-tooltip>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useDistributionStore } from '@/store/modules/distribution'
import AppSelect from './selects/AppSelect.vue'
import { ElMessage } from 'element-plus'

const distributionStore = useDistributionStore()

const currentAppId = computed({
  get: () => distributionStore.currentAppId,
  set: (val) => distributionStore.setCurrentApp(val)
})

const handleAppChange = (appId: number) => {
  ElMessage.success('已切换应用，正在刷新数据...')
  // 触发页面刷新
  window.location.reload()
}

onMounted(() => {
  distributionStore.init()
})
</script>

<style lang="scss" scoped>
.global-app-selector {
  margin-bottom: 16px;
  
  .selector-card {
    :deep(.el-card__body) {
      padding: 12px 20px;
    }
  }
  
  .selector-header {
    display: flex;
    align-items: center;
    
    .selector-icon {
      font-size: 20px;
      color: var(--el-color-primary);
      margin-right: 8px;
    }
    
    .selector-label {
      font-size: 14px;
      color: #606266;
      margin-right: 12px;
    }
    
    .selector-input {
      width: 300px;
    }
    
    .info-icon {
      margin-left: 8px;
      color: #909399;
      cursor: help;
    }
  }
}
</style>
```

### 4.3 在分销模块入口使用全局选择器

**修改分销模块布局**，在每个分销页面顶部引入全局应用选择器：

```vue
<template>
  <div>
    <!-- 全局应用选择器 -->
    <GlobalAppSelector />
    
    <!-- 原有内容 -->
    <ContentWrap>
      <!-- ... -->
    </ContentWrap>
  </div>
</template>
```

## 五、数据权限控制方案

### 5.1 前端权限控制

1. **自动注入 appId**：在所有 API 请求中自动注入当前选中的 appId
2. **列表过滤**：所有列表查询自动添加 appId 过滤条件
3. **表单默认值**：新增表单自动填充当前 appId

### 5.2 请求拦截器改造

**文件**: `src/utils/request/index.ts`

```typescript
// 在请求拦截器中注入 appId
service.interceptors.request.use(
  (config) => {
    // 分销模块请求自动注入 appId
    if (config.url?.includes('/distribution/')) {
      const distributionStore = useDistributionStore()
      const currentAppId = distributionStore.currentAppId
      
      if (currentAppId) {
        // GET 请求
        if (config.method === 'get') {
          config.params = config.params || {}
          if (!config.params.appId) {
            config.params.appId = currentAppId
          }
        }
        // POST/PUT 请求
        else if (['post', 'put'].includes(config.method || '')) {
          config.data = config.data || {}
          if (!config.data.appId) {
            config.data.appId = currentAppId
          }
        }
      }
    }
    
    return config
  }
)
```

## 六、实施步骤

### 6.1 第一阶段：基础组件开发（1天）
- [x] 开发 AppSelect 应用选择组件
- [ ] 开发 GlobalAppSelector 全局应用选择器
- [ ] 创建分销模块状态管理

### 6.2 第二阶段：核心页面改造（3天）
- [ ] 分销员管理页面改造
- [ ] 佣金管理页面改造
- [ ] 商品配置页面改造
- [ ] 等级管理页面改造
- [ ] 标签管理页面改造

### 6.3 第三阶段：API 接口调整（2天）
- [ ] 修改所有 API 接口定义
- [ ] 添加请求拦截器
- [ ] 测试接口兼容性

### 6.4 第四阶段：功能测试（1天）
- [ ] 单应用数据隔离测试
- [ ] 多应用切换测试
- [ ] 权限控制测试
- [ ] 兼容性测试

## 七、注意事项

1. **向后兼容**：确保未选择应用时，系统能够正常工作
2. **数据迁移**：历史数据默认归属到应用 ID=2
3. **性能优化**：应用列表缓存，避免频繁请求
4. **用户体验**：切换应用时给予明确提示
5. **权限控制**：确保用户只能看到有权限的应用

## 八、扩展考虑

1. **跨应用功能**：预留跨应用查看数据的入口
2. **批量操作**：支持批量选择应用进行操作
3. **默认应用**：支持设置用户的默认应用
4. **应用权限**：后续可以增加应用级别的权限控制