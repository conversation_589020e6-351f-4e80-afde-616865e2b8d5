# 分销管理前端应用化改造实施清单

## 改造原则
- ✅ 后端已完成 app_id 支持
- ❌ 不使用请求拦截器自动注入
- ✅ 每个 API 调用显式传递 appId
- ✅ 保持代码清晰可控

## 一、基础组件（已完成）

### ✅ AppSelect 应用选择组件
- 文件：`src/views/distribution-v4/components/selects/AppSelect.vue`
- 功能：下拉选择应用，支持显示Logo和渠道

### ✅ GlobalAppSelector 全局选择器
- 文件：`src/views/distribution-v4/components/GlobalAppSelector.vue`
- 功能：全局应用切换，状态展示

### ✅ Distribution Store
- 文件：`src/store/modules/distribution.ts`
- 功能：管理当前应用状态，缓存应用列表

## 二、分销员管理模块

### [ ] 分销员列表页 - `src/views/distribution-v4/agent/index.vue`
```typescript
// 1. 导入组件
import AppSelect from '../components/selects/AppSelect.vue'
import GlobalAppSelector from '../components/GlobalAppSelector.vue'

// 2. 添加应用筛选
<el-form-item label="所属应用" prop="appId">
  <AppSelect v-model="queryParams.appId" clearable />
</el-form-item>

// 3. 查询时传递 appId
const getList = async () => {
  const params = {
    ...queryParams,
    appId: queryParams.appId || distributionStore.currentAppId
  }
  const data = await DistAgentApi.getDistAgentList(params)
}

// 4. 统计查询传递 appId
const getStatistics = async () => {
  const data = await DistAgentApi.getDistAgentOverallStatistics({
    appId: queryParams.appId || distributionStore.currentAppId
  })
}

// 5. 列表添加应用列
<el-table-column label="所属应用" prop="appName" width="150" />
```

### [ ] 分销员表单 - `src/views/distribution-v4/agent/components/AgentDrawer.vue`
```typescript
// 1. 添加应用选择
<el-form-item label="所属应用" prop="appId">
  <AppSelect 
    v-model="formData.appId"
    :disabled="formType === 'update'"
  />
</el-form-item>

// 2. 提交时确保有 appId
const submitForm = async () => {
  const data = {
    ...formData,
    appId: formData.appId || distributionStore.currentAppId
  }
  await DistAgentApi.createDistAgent(data)
}

// 3. 关联组件传递 appId
<LevelSelect :app-id="formData.appId" />
<AgentTagSelect :app-id="formData.appId" />
<AgentSelect :app-id="formData.appId" />
```

### [ ] API 接口调整 - `src/api/distribution/agent/index.ts`
```typescript
// 1. VO 添加字段
export interface DistAgentVO {
  appId?: number
  appName?: string
  appLogo?: string
  // ... 其他字段
}

// 2. 查询参数添加 appId
export interface DistAgentPageReqVO {
  appId?: number
  // ... 其他参数
}
```

### [ ] 选择组件改造
- [ ] `AgentSelect.vue` - 添加 appId prop，查询时过滤
- [ ] `LevelSelect.vue` - 添加 appId prop，显示应用等级
- [ ] `AgentTagSelect.vue` - 添加 appId prop，过滤标签

## 三、佣金管理模块

### [ ] 佣金列表页 - `src/views/distribution-v4/commission/index.vue`
```typescript
// 1. 添加应用筛选
// 2. 查询传递 appId
// 3. 批量操作检查应用一致性
// 4. 统计查询传递 appId
```

### [ ] 佣金详情 - `src/views/distribution-v4/commission/components/CommissionDetailDrawer.vue`
```typescript
// 1. 显示所属应用信息
// 2. 关联查询限制同应用
```

### [ ] API 接口调整 - `src/api/distribution/commission/index.ts`
```typescript
// 1. VO 添加 appId 相关字段
// 2. 查询参数添加 appId
```

## 四、商品配置模块

### [ ] 配置列表页 - `src/views/distribution-v4/goods/index.vue`
```typescript
// 1. 添加应用筛选
// 2. 查询传递 appId
// 3. 批量配置限制同应用
```

### [ ] 配置表单 - `src/views/distribution-v4/goods/components/GoodsConfigDrawer.vue`
```typescript
// 1. 添加应用选择
// 2. 保存时传递 appId
```

## 五、分销等级模块

### [ ] 等级列表页 - `src/views/distribution-v4/level/index.vue`
```typescript
// 1. 区分全局/应用等级显示
// 2. 查询支持 appId 过滤
```

### [ ] 等级表单 - `src/views/distribution-v4/level/components/LevelDrawer.vue`
```typescript
// 1. 选择适用范围（全局/应用）
// 2. 应用等级需选择具体应用
```

## 六、其他模块

### [ ] 分销标签模块
- 列表页添加应用筛选
- 表单添加应用选择
- API 调用传递 appId

### [ ] 提现管理模块
- 列表页添加应用筛选
- 详情显示应用信息
- API 调用传递 appId

### [ ] 分销海报模块
- 模板关联应用
- 生成海报传递 appId

### [ ] 统计报表模块
- 默认显示当前应用数据
- 支持多应用对比

## 七、通用处理模式

### 批量操作检查
```typescript
const checkAppConsistency = (rows: any[]) => {
  const appIds = new Set(rows.map(row => row.appId))
  if (appIds.size > 1) {
    message.error('批量操作仅支持同一应用内的数据')
    return false
  }
  return true
}
```

### 参数构建辅助
```typescript
const buildParams = (params: any) => {
  return {
    ...params,
    appId: params.appId || distributionStore.currentAppId
  }
}
```

### 组件联动处理
```typescript
watch(() => formData.appId, (newVal) => {
  if (newVal) {
    // 清空关联字段
    formData.levelId = undefined
    formData.tagIds = []
    // 重新加载选项
    loadLevels(newVal)
    loadTags(newVal)
  }
})
```

## 八、测试要点

1. **功能测试**
   - [ ] 应用切换数据正确刷新
   - [ ] 不同应用数据隔离
   - [ ] 批量操作应用一致性验证

2. **接口测试**
   - [ ] 所有查询接口传递 appId
   - [ ] 创建/更新包含 appId
   - [ ] 导出数据包含应用信息

3. **用户体验**
   - [ ] 应用选择器使用流畅
   - [ ] 错误提示清晰
   - [ ] 加载状态正确

## 九、实施顺序建议

1. **第一批**（核心功能）
   - 分销员管理
   - 佣金管理
   - 商品配置

2. **第二批**（配置功能）
   - 等级管理
   - 标签管理
   - 提现管理

3. **第三批**（辅助功能）
   - 海报管理
   - 统计报表

每完成一个模块，进行充分测试后再继续下一个模块。