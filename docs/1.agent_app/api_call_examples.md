# 分销管理 API 调用改造示例

## 一、分销员管理模块

### 1.1 查询列表
```typescript
// src/views/distribution-v4/agent/index.vue
const getList = async () => {
  loading.value = true
  try {
    const params: DistAgentPageReqVO = {
      pageNo: queryParams.pageNo,
      pageSize: queryParams.pageSize,
      keyword: queryParams.keyword,
      levelId: queryParams.levelId,
      tagIds: queryParams.tagIds,
      status: queryParams.status,
      appId: queryParams.appId || distributionStore.currentAppId, // 显式传递 appId
    }
    
    const data = await DistAgentApi.getDistAgentList(params)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
```

### 1.2 获取统计数据
```typescript
const getStatistics = async () => {
  statisticsLoading.value = true
  try {
    // 传递 appId 参数
    const data = await DistAgentApi.getDistAgentOverallStatistics({
      appId: queryParams.appId || distributionStore.currentAppId
    })
    statistics.value = data
  } finally {
    statisticsLoading.value = false
  }
}
```

### 1.3 创建分销员
```typescript
// src/views/distribution-v4/agent/components/AgentDrawer.vue
const submitForm = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) return
  
  formLoading.value = true
  try {
    const data: DistAgentVO = {
      ...formData,
      appId: formData.appId || distributionStore.currentAppId // 确保有 appId
    }
    
    if (formType.value === 'create') {
      await DistAgentApi.createDistAgent(data)
      message.success('创建成功')
    } else {
      await DistAgentApi.updateDistAgent(data)
      message.success('更新成功')
    }
    
    dialogVisible.value = false
    emit('success')
  } finally {
    formLoading.value = false
  }
}
```

### 1.4 批量审核
```typescript
const handleBatchAudit = async (status: number, remark: string) => {
  // 检查应用一致性
  const appIds = new Set(selectedRows.map(row => row.appId))
  if (appIds.size > 1) {
    message.error('批量审核仅支持同一应用内的分销员')
    return
  }
  
  const ids = selectedRows.map(row => row.id)
  await DistAgentApi.batchAuditDistAgent(ids, status, remark)
  message.success('批量审核成功')
  getList()
}
```

### 1.5 查询团队成员
```typescript
const getTeamList = async (parentId: number) => {
  const params = {
    parentId,
    appId: distributionStore.currentAppId, // 限制在同一应用内
    pageNo: 1,
    pageSize: 20
  }
  const data = await DistAgentApi.getDistAgentTeamList(parentId, params)
  return data
}
```

## 二、佣金管理模块

### 2.1 查询佣金列表
```typescript
// src/views/distribution-v4/commission/index.vue
const getCommissionList = async () => {
  loading.value = true
  try {
    const params = {
      ...queryParams,
      appId: queryParams.appId || distributionStore.currentAppId
    }
    
    const data = await CommissionApi.getCommissionPage(params)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
```

### 2.2 批量结算佣金
```typescript
const handleBatchSettle = async () => {
  // 验证选中数据
  if (selectedRows.length === 0) {
    message.warning('请选择要结算的佣金')
    return
  }
  
  // 检查应用一致性
  const appIds = new Set(selectedRows.map(row => row.appId))
  if (appIds.size > 1) {
    message.error('批量结算仅支持同一应用内的佣金')
    return
  }
  
  const ids = selectedRows.map(row => row.id)
  const params = {
    ids,
    appId: selectedRows[0].appId // 使用第一条数据的 appId
  }
  
  await CommissionApi.batchSettle(params)
  message.success('批量结算成功')
  getList()
}
```

### 2.3 佣金统计
```typescript
const getCommissionStatistics = async () => {
  const params = {
    agentId: queryParams.agentId,
    appId: queryParams.appId || distributionStore.currentAppId,
    dateRange: queryParams.dateRange
  }
  
  const data = await CommissionApi.getStatistics(params)
  return data
}
```

## 三、商品分销配置模块

### 3.1 查询配置列表
```typescript
// src/views/distribution-v4/goods/index.vue
const getConfigList = async () => {
  loading.value = true
  try {
    const params = {
      ...queryParams,
      appId: queryParams.appId || distributionStore.currentAppId
    }
    
    const data = await GoodsConfigApi.getConfigPage(params)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
```

### 3.2 保存配置
```typescript
// src/views/distribution-v4/goods/components/GoodsConfigDrawer.vue
const saveConfig = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) return
  
  try {
    const data = {
      ...formData,
      appId: formData.appId || distributionStore.currentAppId
    }
    
    if (formData.id) {
      await GoodsConfigApi.updateConfig(data)
    } else {
      await GoodsConfigApi.createConfig(data)
    }
    
    message.success('保存成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('保存失败:', error)
  }
}
```

### 3.3 批量配置
```typescript
const handleBatchConfig = async (goodsIds: number[], config: any) => {
  const params = {
    goodsIds,
    config,
    appId: distributionStore.currentAppId // 当前应用
  }
  
  await GoodsConfigApi.batchConfig(params)
  message.success('批量配置成功')
  getList()
}
```

## 四、分销等级管理模块

### 4.1 查询等级列表
```typescript
// src/views/distribution-v4/level/index.vue
const getLevelList = async () => {
  loading.value = true
  try {
    const params = {
      ...queryParams,
      appId: queryParams.appId // 可选，不传则查询全部（包括全局等级）
    }
    
    const data = await LevelApi.getLevelPage(params)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
```

### 4.2 创建等级
```typescript
const createLevel = async (formData: DistLevelVO) => {
  const data = {
    ...formData,
    // 如果是应用等级，需要 appId
    appId: formData.scopeType === 'app' ? formData.appId : null
  }
  
  await LevelApi.createLevel(data)
  message.success('创建成功')
}
```

### 4.3 获取可用等级（供选择器使用）
```typescript
// src/views/distribution-v4/components/selects/LevelSelect.vue
const loadLevelList = async () => {
  try {
    const params = {
      status: 1, // 只查询启用的
      appId: props.appId // 如果提供了 appId，则过滤
    }
    
    const data = await LevelApi.getSimpleLevelList(params)
    
    // 如果指定了应用，优先显示应用等级，其次显示全局等级
    if (props.appId) {
      levelList.value = data.sort((a, b) => {
        if (a.appId === props.appId && b.appId !== props.appId) return -1
        if (a.appId !== props.appId && b.appId === props.appId) return 1
        return 0
      })
    } else {
      levelList.value = data
    }
  } catch (error) {
    console.error('加载等级列表失败:', error)
  }
}
```

## 五、选择组件通用模式

### 5.1 AgentSelect 组件
```typescript
const loadAgentList = async () => {
  if (!props.appId && !distributionStore.currentAppId) {
    console.warn('AgentSelect: 未指定 appId')
    return
  }
  
  try {
    const params = {
      pageSize: 100,
      status: 1,
      appId: props.appId || distributionStore.currentAppId
    }
    
    const data = await DistAgentApi.getDistAgentList(params)
    agentList.value = data.list
  } catch (error) {
    console.error('加载分销员列表失败:', error)
  }
}
```

### 5.2 TagSelect 组件
```typescript
const loadTagList = async () => {
  try {
    const params = {
      appId: props.appId || distributionStore.currentAppId
    }
    
    const data = await TagApi.getSimpleTagList(params)
    tagList.value = data
  } catch (error) {
    console.error('加载标签列表失败:', error)
  }
}
```

## 六、导出功能

```typescript
const handleExport = async () => {
  try {
    await message.exportConfirm()
    exportLoading.value = true
    
    // 构建导出参数，包含所有查询条件
    const params = {
      ...queryParams,
      appId: queryParams.appId || distributionStore.currentAppId
    }
    
    const data = await DistAgentApi.exportDistAgent(params)
    download.excel(data, `分销员数据_${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
  } finally {
    exportLoading.value = false
  }
}
```

## 七、最佳实践

### 7.1 参数构建辅助函数
```typescript
// utils/distribution.ts
export const buildDistributionParams = (params: any, appId?: number) => {
  const distributionStore = useDistributionStore()
  return {
    ...params,
    appId: params.appId || appId || distributionStore.currentAppId
  }
}

// 使用
const getList = async () => {
  const params = buildDistributionParams(queryParams)
  const data = await API.getList(params)
}
```

### 7.2 应用一致性检查
```typescript
// utils/distribution.ts
export const checkAppConsistency = (rows: any[]) => {
  if (!rows || rows.length === 0) return true
  
  const appIds = new Set(rows.map(row => row.appId))
  if (appIds.size > 1) {
    message.error('操作仅支持同一应用内的数据')
    return false
  }
  
  return true
}

// 使用
if (!checkAppConsistency(selectedRows)) return
```

### 7.3 默认应用处理
```typescript
// 在组件 setup 中
const initAppId = () => {
  // 优先级：URL参数 > Store当前应用 > 第一个可用应用
  const urlAppId = route.query.appId
  if (urlAppId) {
    queryParams.appId = Number(urlAppId)
  } else if (distributionStore.currentAppId) {
    queryParams.appId = distributionStore.currentAppId
  }
}

onMounted(() => {
  initAppId()
  getList()
})
```