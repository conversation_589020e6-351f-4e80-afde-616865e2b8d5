# 分销管理前端应用化改造实施方案

## 一、改造背景与目标

后端已完成 app_id 改造，前端需要配合实现：
1. 在所有分销相关接口调用时传递 appId 参数
2. 在界面上展示应用维度的数据
3. 提供便捷的应用切换功能
4. 确保用户体验流畅

## 二、技术方案

### 2.1 应用管理策略

1. **全局应用状态管理**
   - 使用 Pinia store 管理当前选中的应用
   - 应用信息缓存，减少重复请求
   - 本地存储持久化，刷新页面不丢失

2. **应用切换方式**
   - 方案一：全局顶部选择器（推荐）
   - 方案二：每个页面独立选择
   - 方案三：路由参数传递

3. **数据请求策略**
   - 自动注入当前 appId 到所有分销模块请求
   - 支持临时覆盖（如跨应用查看）
   - 错误处理和降级方案

### 2.2 界面设计原则

1. **最小化干扰**：应用选择不影响原有操作流程
2. **状态可见**：始终显示当前所在应用
3. **便捷切换**：一键切换应用，数据自动刷新
4. **权限控制**：只显示用户有权限的应用

## 三、详细改造清单

### 3.1 基础设施建设

#### 3.1.1 创建 AppSelect 组件
**文件**: `src/views/distribution-v4/components/selects/AppSelect.vue`
- [x] 基础选择功能
- [ ] 支持搜索过滤
- [ ] 显示应用状态（启用/禁用/沙盒）
- [ ] 支持分组显示（按渠道）
- [ ] 空状态处理

#### 3.1.2 创建全局应用选择器
**文件**: `src/views/distribution-v4/components/GlobalAppSelector.vue`
- [x] 基础切换功能
- [ ] 切换确认提示
- [ ] 切换后数据刷新
- [ ] 快捷切换（最近使用）
- [ ] 应用信息展示

#### 3.1.3 创建状态管理
**文件**: `src/store/modules/distribution.ts`
- [x] 基础状态定义
- [ ] 应用列表管理
- [ ] 当前应用管理
- [ ] 权限过滤
- [ ] 缓存策略

#### 3.1.4 请求拦截器改造
**文件**: `src/utils/request/index.ts`
- [ ] 识别分销模块请求
- [ ] 自动注入 appId
- [ ] 支持覆盖机制
- [ ] 错误处理

### 3.2 分销员管理模块改造

#### 3.2.1 分销员列表页
**文件**: `src/views/distribution-v4/agent/index.vue`
- [ ] 搜索栏增加应用筛选
- [ ] 列表显示应用信息列
- [ ] 统计数据按应用过滤
- [ ] 批量操作限制同应用
- [ ] 导出数据包含应用信息

#### 3.2.2 分销员表单
**文件**: `src/views/distribution-v4/agent/components/AgentDrawer.vue`
- [ ] 新增时选择应用（必填）
- [ ] 编辑时显示应用（只读）
- [ ] 上级选择限制同应用
- [ ] 等级选择过滤应用
- [ ] 标签选择过滤应用

#### 3.2.3 分销员详情
**文件**: `src/views/distribution-v4/agent/components/AgentDetailDrawer.vue`
- [ ] 显示所属应用信息
- [ ] 团队成员限制同应用
- [ ] 业绩数据按应用统计

#### 3.2.4 API 接口调整
**文件**: `src/api/distribution/agent/index.ts`
- [ ] 所有查询接口增加 appId 参数
- [ ] VO 接口增加 appId 和 appName 字段
- [ ] 统计接口支持应用维度

### 3.3 佣金管理模块改造

#### 3.3.1 佣金列表页
**文件**: `src/views/distribution-v4/commission/index.vue`
- [ ] 搜索栏增加应用筛选
- [ ] 显示佣金所属应用
- [ ] 统计数据按应用汇总
- [ ] 批量结算限制同应用

#### 3.3.2 佣金详情
**文件**: `src/views/distribution-v4/commission/components/CommissionDetailDrawer.vue`
- [ ] 显示应用信息
- [ ] 关联订单的应用
- [ ] 分销链路限制同应用

#### 3.3.3 API 接口调整
**文件**: `src/api/distribution/commission/index.ts`
- [ ] 查询接口增加 appId 参数
- [ ] VO 增加应用相关字段

### 3.4 商品分销配置模块改造

#### 3.4.1 配置列表页
**文件**: `src/views/distribution-v4/goods/index.vue`
- [ ] 搜索栏增加应用筛选
- [ ] 显示配置适用应用
- [ ] 批量配置限制同应用
- [ ] 配置复制支持跨应用

#### 3.4.2 配置表单
**文件**: `src/views/distribution-v4/goods/components/GoodsConfigDrawer.vue`
- [ ] 选择适用应用
- [ ] 支持批量选择应用
- [ ] 等级配置关联应用
- [ ] 预览不同应用效果

#### 3.4.3 API 接口调整
**文件**: `src/api/distribution/goods-config.ts`
- [ ] 所有接口增加 appId 参数
- [ ] 支持跨应用复制配置

### 3.5 分销等级管理模块改造

#### 3.5.1 等级列表页
**文件**: `src/views/distribution-v4/level/index.vue`
- [ ] 区分全局等级和应用等级
- [ ] 搜索支持按应用过滤
- [ ] 显示适用范围
- [ ] 等级升降级规则关联应用

#### 3.5.2 等级表单
**文件**: `src/views/distribution-v4/level/components/LevelDrawer.vue`
- [ ] 选择适用范围（全局/应用）
- [ ] 应用等级选择具体应用
- [ ] 权益配置支持应用差异化
- [ ] 升级条件关联应用

#### 3.5.3 等级选择组件
**文件**: `src/views/distribution-v4/components/selects/LevelSelect.vue`
- [ ] 根据 appId 过滤等级
- [ ] 显示等级适用范围
- [ ] 支持显示等级权益

### 3.6 分销标签管理模块改造

#### 3.6.1 标签列表页
**文件**: `src/views/distribution-v4/tag/index.vue`
- [ ] 搜索栏增加应用筛选
- [ ] 显示标签所属应用
- [ ] 批量操作限制同应用

#### 3.6.2 标签表单
**文件**: `src/views/distribution-v4/tag/components/TagDrawer.vue`
- [ ] 选择所属应用
- [ ] 标签合并限制同应用

#### 3.6.3 标签选择组件
**文件**: `src/views/distribution-v4/components/selects/AgentTagSelect.vue`
- [ ] 根据 appId 过滤标签
- [ ] 支持创建应用专属标签

### 3.7 提现管理模块改造

#### 3.7.1 提现列表页
**文件**: `src/views/distribution-v4/withdraw/index.vue`
- [ ] 搜索栏增加应用筛选
- [ ] 显示提现所属应用
- [ ] 批量审核限制同应用

#### 3.7.2 提现详情
- [ ] 显示应用信息
- [ ] 关联佣金的应用

### 3.8 分销海报模块改造

#### 3.8.1 海报模板管理
**文件**: `src/views/distribution-v4/poster/index.vue`
- [ ] 模板关联应用
- [ ] 支持应用专属模板
- [ ] 模板复制跨应用

#### 3.8.2 海报生成
- [ ] 根据应用选择模板
- [ ] 海报内容包含应用信息

### 3.9 统计报表模块改造

#### 3.9.1 统计首页
**文件**: `src/views/distribution-v4/statistics/index.vue`
- [ ] 默认显示当前应用数据
- [ ] 支持多应用对比
- [ ] 应用维度的趋势分析

#### 3.9.2 数据导出
- [ ] 导出包含应用信息
- [ ] 支持按应用批量导出

## 四、实施步骤

### 第一阶段：基础设施（2天）
1. 创建 AppSelect 组件
2. 创建全局应用选择器
3. 创建 distribution store
4. 配置请求拦截器

### 第二阶段：核心模块改造（5天）
1. 分销员管理模块（2天）
2. 佣金管理模块（1.5天）
3. 商品配置模块（1.5天）

### 第三阶段：配置模块改造（3天）
1. 等级管理模块（1天）
2. 标签管理模块（1天）
3. 提现管理模块（1天）

### 第四阶段：辅助模块改造（2天）
1. 海报模块（1天）
2. 统计报表模块（1天）

### 第五阶段：集成测试（2天）
1. 功能测试
2. 性能优化
3. 用户体验优化

## 五、关键技术点

### 5.1 应用切换机制

```typescript
// 切换应用时的处理流程
async function handleAppChange(newAppId: number) {
  // 1. 确认提示
  await confirm('切换应用将重新加载数据，是否继续？')
  
  // 2. 保存到 store
  distributionStore.setCurrentApp(newAppId)
  
  // 3. 刷新当前页面数据
  await refreshCurrentPage()
  
  // 4. 更新相关组件
  eventBus.emit('app-changed', newAppId)
}
```

### 5.2 请求自动注入

```typescript
// 请求拦截器
service.interceptors.request.use((config) => {
  // 分销模块请求自动注入 appId
  if (config.url?.includes('/distribution/')) {
    const appId = distributionStore.currentAppId
    
    if (appId) {
      // GET 请求
      if (config.method === 'get') {
        config.params = { ...config.params, appId }
      }
      // POST/PUT 请求
      else if (['post', 'put'].includes(config.method || '')) {
        config.data = { ...config.data, appId }
      }
    }
  }
  
  return config
})
```

### 5.3 组件联动更新

```typescript
// 监听应用变化，更新关联数据
watch(() => formData.appId, (newAppId) => {
  if (newAppId) {
    // 清空关联字段
    formData.levelId = undefined
    formData.tagIds = []
    formData.parentId = undefined
    
    // 重新加载选项
    loadLevels(newAppId)
    loadTags(newAppId)
  }
})
```

## 六、注意事项

### 6.1 用户体验
1. **加载状态**：切换应用时显示 loading
2. **错误处理**：应用无权限时的友好提示
3. **快捷操作**：记住最近使用的应用
4. **视觉提示**：当前应用始终可见

### 6.2 性能优化
1. **缓存策略**：应用列表缓存 5 分钟
2. **懒加载**：按需加载应用相关数据
3. **防抖处理**：避免频繁切换
4. **预加载**：预加载常用应用数据

### 6.3 兼容性
1. **默认应用**：未选择时使用默认应用
2. **旧数据**：兼容没有 appId 的数据
3. **降级方案**：支持关闭应用选择功能

### 6.4 安全性
1. **权限验证**：前后端双重验证
2. **数据隔离**：严格按应用过滤
3. **操作审计**：记录应用切换日志

## 七、测试要点

### 7.1 功能测试
- [ ] 应用切换功能正常
- [ ] 数据按应用正确过滤
- [ ] 新增/编辑正确保存 appId
- [ ] 批量操作限制验证

### 7.2 界面测试
- [ ] 应用选择器显示正常
- [ ] 列表页应用列显示
- [ ] 表单页应用选择交互
- [ ] 错误提示友好

### 7.3 性能测试
- [ ] 应用列表加载速度
- [ ] 切换应用响应时间
- [ ] 大数据量下的表现

### 7.4 兼容性测试
- [ ] 不同浏览器兼容
- [ ] 移动端适配
- [ ] 旧数据兼容