# 分销模块应用化改造方案（仅支持app_id） v1.0

## 一、改造目标

本期改造仅聚焦于为分销模块增加应用维度（app_id）支持，实现：
- 分销员按应用隔离
- 分销数据按应用区分
- 支持多应用独立运营

**本期不包括**：多级分销优化、跨应用共享、动态配置等复杂功能。

## 二、现状分析

### 2.1 需要改造的核心表

通过分析现有数据库结构，以下表需要增加app_id：

| 表名 | 说明 | 改造原因 |
|------|------|----------|
| yt_dist_agent | 分销员表 | 分销员需要归属特定应用 |
| yt_dist_commission | 佣金记录表 | 佣金按应用隔离结算 |
| yt_dist_withdraw | 提现记录表 | 提现按应用独立管理 |
| yt_dist_goods_config | 商品分销配置表 | 同一商品在不同应用可有不同配置 |
| yt_dist_level | 分销等级表 | 支持应用级别的等级体系 |
| yt_dist_agent_tag | 分销员标签表 | 标签按应用管理 |

### 2.2 关联影响分析

1. **分销员邀请码**：需要确保在应用内唯一
2. **会员与分销员关系**：同一会员可在不同应用成为分销员
3. **上下级关系**：限定在同一应用内
4. **数据统计**：需要按应用维度统计

## 三、数据库改造方案

### 3.1 分销员表（yt_dist_agent）

```sql
-- 添加app_id字段
ALTER TABLE `yt_dist_agent` 
ADD COLUMN `app_id` bigint(20) NOT NULL DEFAULT '2' COMMENT '应用ID' AFTER `member_id`,
ADD INDEX `idx_app_id` (`app_id`),
ADD INDEX `idx_app_member` (`app_id`, `member_id`);

-- 修改唯一索引，支持同一会员在不同应用
ALTER TABLE `yt_dist_agent` 
DROP INDEX `uk_member_id`,
ADD UNIQUE KEY `uk_app_member` (`app_id`, `member_id`, `deleted`);

-- 修改邀请码唯一索引，限定在应用内唯一
ALTER TABLE `yt_dist_agent` 
DROP INDEX `uk_invite_code`,
ADD UNIQUE KEY `uk_app_invite_code` (`app_id`, `invite_code`, `deleted`);
```

### 3.2 佣金记录表（yt_dist_commission）

```sql
-- 添加app_id字段
ALTER TABLE `yt_dist_commission`
ADD COLUMN `app_id` bigint(20) NOT NULL DEFAULT '2' COMMENT '应用ID' AFTER `agent_id`,
ADD INDEX `idx_app_id` (`app_id`),
ADD INDEX `idx_app_agent` (`app_id`, `agent_id`);
```

### 3.3 提现记录表（yt_dist_withdraw）

```sql
-- 添加app_id字段
ALTER TABLE `yt_dist_withdraw`
ADD COLUMN `app_id` bigint(20) NOT NULL DEFAULT '2' COMMENT '应用ID' AFTER `agent_id`,
ADD INDEX `idx_app_id` (`app_id`),
ADD INDEX `idx_app_agent` (`app_id`, `agent_id`);
```

### 3.4 商品分销配置表（yt_dist_goods_config）

```sql
-- 添加app_id字段
ALTER TABLE `yt_dist_goods_config`
ADD COLUMN `app_id` bigint(20) NOT NULL DEFAULT '2' COMMENT '应用ID' AFTER `goods_id`,
ADD INDEX `idx_app_id` (`app_id`);

-- 修改唯一索引，支持同一商品在不同应用有不同配置
ALTER TABLE `yt_dist_goods_config`
DROP INDEX `uk_goods_id`,
ADD UNIQUE KEY `uk_app_goods` (`app_id`, `goods_id`, `deleted`);
```

### 3.5 分销等级表（yt_dist_level）

```sql
-- 添加app_id字段，NULL表示全局等级
ALTER TABLE `yt_dist_level`
ADD COLUMN `app_id` bigint(20) DEFAULT NULL COMMENT '应用ID，NULL表示全局等级' AFTER `id`,
ADD INDEX `idx_app_id` (`app_id`);

-- 修改唯一索引
ALTER TABLE `yt_dist_level`
DROP INDEX `uk_level_code`,
DROP INDEX `uk_level_grade`,
ADD UNIQUE KEY `uk_app_level_code` (`app_id`, `level_code`, `deleted`),
ADD UNIQUE KEY `uk_app_level_grade` (`app_id`, `level_grade`, `deleted`);
```

### 3.6 分销员标签表（yt_dist_agent_tag）

```sql
-- 添加app_id字段
ALTER TABLE `yt_dist_agent_tag`
ADD COLUMN `app_id` bigint(20) NOT NULL DEFAULT '2' COMMENT '应用ID' AFTER `id`,
ADD INDEX `idx_app_id` (`app_id`);

-- 标签名称在应用内唯一
ALTER TABLE `yt_dist_agent_tag`
ADD UNIQUE KEY `uk_app_tag_name` (`app_id`, `name`, `deleted`);
```

### 3.7 其他关联表

```sql
-- 分销员标签关联表
ALTER TABLE `yt_dist_agent_tag_relation`
ADD COLUMN `app_id` bigint(20) NOT NULL DEFAULT '2' COMMENT '应用ID' AFTER `tag_id`,
ADD INDEX `idx_app_id` (`app_id`);

-- 海报相关表
ALTER TABLE `yt_dist_poster_template`
ADD COLUMN `app_id` bigint(20) DEFAULT NULL COMMENT '应用ID，NULL表示通用模板' AFTER `id`,
ADD COLUMN `level_id` bigint(20) DEFAULT NULL COMMENT '等级ID，NULL表示不限等级' AFTER `app_id`,
ADD COLUMN `platform_type` varchar(32) DEFAULT NULL COMMENT '平台类型：XHS-小红书，H5-网页，DOUYIN-抖音，WECHAT-微信' AFTER `type`,
ADD COLUMN `qrcode_config` json DEFAULT NULL COMMENT '二维码配置（位置、大小、样式等）' AFTER `config`,
ADD COLUMN `invite_path_template` varchar(500) DEFAULT NULL COMMENT '邀请链接模板' AFTER `qrcode_config`,
ADD INDEX `idx_app_id` (`app_id`),
ADD INDEX `idx_level_id` (`level_id`),
ADD INDEX `idx_platform_type` (`platform_type`);

ALTER TABLE `yt_dist_agent_poster`
ADD COLUMN `app_id` bigint(20) NOT NULL DEFAULT '2' COMMENT '应用ID' AFTER `agent_id`,
ADD COLUMN `platform_type` varchar(32) NOT NULL COMMENT '平台类型' AFTER `template_id`,
ADD COLUMN `invite_path` varchar(500) DEFAULT NULL COMMENT '邀请链接' AFTER `qrcode_url`,
ADD COLUMN `qrcode_scene` varchar(128) DEFAULT NULL COMMENT '二维码场景值（小程序专用）' AFTER `invite_path`,
ADD COLUMN `poster_config` json DEFAULT NULL COMMENT '海报个性化配置' AFTER `qrcode_scene`,
ADD INDEX `idx_app_id` (`app_id`),
ADD INDEX `idx_platform_type` (`platform_type`);

-- 奖励方案相关表
ALTER TABLE `yt_dist_reward_scheme`
ADD COLUMN `app_id` bigint(20) DEFAULT NULL COMMENT '应用ID，NULL表示全局方案' AFTER `id`,
ADD INDEX `idx_app_id` (`app_id`);
```

## 四、业务逻辑调整

### 4.1 分销员管理

#### 4.1.1 分销员注册/申请
```java
// 修改前
public void applyDistAgent(Long memberId) {
    // 检查会员是否已是分销员
    DistAgent existAgent = distAgentMapper.selectOne(
        new LambdaQueryWrapper<DistAgent>()
            .eq(DistAgent::getMemberId, memberId)
    );
    if (existAgent != null) {
        throw new BusinessException("您已经是分销员");
    }
    // ... 创建分销员
}

// 修改后
public void applyDistAgent(Long memberId, Long appId) {
    // 检查会员在该应用是否已是分销员
    DistAgent existAgent = distAgentMapper.selectOne(
        new LambdaQueryWrapper<DistAgent>()
            .eq(DistAgent::getAppId, appId)
            .eq(DistAgent::getMemberId, memberId)
    );
    if (existAgent != null) {
        throw new BusinessException("您在该应用已经是分销员");
    }
    // ... 创建分销员
    agent.setAppId(appId);
}
```

#### 4.1.2 邀请码生成
```java
// 确保邀请码在应用内唯一
private String generateInviteCode(Long appId) {
    String code;
    do {
        code = RandomUtil.randomString(INVITE_CODE_CHARS, 6);
    } while (existsInviteCode(appId, code));
    return code;
}

private boolean existsInviteCode(Long appId, String code) {
    return distAgentMapper.selectCount(
        new LambdaQueryWrapper<DistAgent>()
            .eq(DistAgent::getAppId, appId)
            .eq(DistAgent::getInviteCode, code)
    ) > 0;
}
```

#### 4.1.3 上下级绑定
```java
// 确保上下级在同一应用
public void bindParent(Long agentId, String parentInviteCode, Long appId) {
    // 查找上级分销员
    DistAgent parent = distAgentMapper.selectOne(
        new LambdaQueryWrapper<DistAgent>()
            .eq(DistAgent::getAppId, appId)
            .eq(DistAgent::getInviteCode, parentInviteCode)
    );
    if (parent == null) {
        throw new BusinessException("邀请码无效");
    }
    
    // 更新下级信息
    DistAgent agent = distAgentMapper.selectById(agentId);
    if (!agent.getAppId().equals(appId)) {
        throw new BusinessException("不能跨应用绑定上级");
    }
    // ... 执行绑定
}
```

### 4.2 佣金计算

```java
// 佣金计算时需要考虑应用维度
public void calculateCommission(Order order) {
    // 获取订单所属应用
    Long appId = order.getAppId();
    
    // 获取该应用的商品分销配置
    DistGoodsConfig config = distGoodsConfigMapper.selectOne(
        new LambdaQueryWrapper<DistGoodsConfig>()
            .eq(DistGoodsConfig::getAppId, appId)
            .eq(DistGoodsConfig::getGoodsId, order.getGoodsId())
    );
    
    // 创建佣金记录
    DistCommission commission = new DistCommission();
    commission.setAppId(appId);
    // ... 其他佣金计算逻辑
}
```

### 4.3 分销海报生成

#### 4.3.1 海报模板选择策略
```java
public DistPosterTemplate selectPosterTemplate(Long agentId, Long appId, String platformType) {
    // 1. 获取分销员信息
    DistAgent agent = distAgentMapper.selectById(agentId);
    
    // 2. 查询海报模板（优先级：用户专属 > 等级专属 > 应用默认 > 通用模板）
    DistPosterTemplate template = distPosterTemplateMapper.selectOne(
        new LambdaQueryWrapper<DistPosterTemplate>()
            .eq(DistPosterTemplate::getAppId, appId)
            .eq(DistPosterTemplate::getLevelId, agent.getLevelId())
            .eq(DistPosterTemplate::getPlatformType, platformType)
            .eq(DistPosterTemplate::getStatus, 1)
            .orderByDesc(DistPosterTemplate::getId)
            .last("LIMIT 1")
    );
    
    // 3. 降级查询逻辑
    if (template == null) {
        // 尝试查询应用级别模板（不限等级）
        template = distPosterTemplateMapper.selectOne(
            new LambdaQueryWrapper<DistPosterTemplate>()
                .eq(DistPosterTemplate::getAppId, appId)
                .isNull(DistPosterTemplate::getLevelId)
                .eq(DistPosterTemplate::getPlatformType, platformType)
                .eq(DistPosterTemplate::getStatus, 1)
        );
    }
    
    if (template == null) {
        // 最后尝试通用模板
        template = distPosterTemplateMapper.selectOne(
            new LambdaQueryWrapper<DistPosterTemplate>()
                .isNull(DistPosterTemplate::getAppId)
                .isNull(DistPosterTemplate::getLevelId)
                .eq(DistPosterTemplate::getPlatformType, platformType)
                .eq(DistPosterTemplate::getStatus, 1)
        );
    }
    
    return template;
}
```

#### 4.3.2 不同平台的邀请链接和二维码生成
```java
@Component
public class PlatformPosterGenerator {
    
    private static final Map<String, PlatformConfig> PLATFORM_CONFIGS = new HashMap<>();
    
    static {
        // 小红书配置
        PLATFORM_CONFIGS.put("XHS", PlatformConfig.builder()
            .invitePathPattern("https://www.xiaohongshu.com/user/profile/{userId}?inviteCode={inviteCode}")
            .qrcodeType("URL")
            .qrcodeSize(200)
            .build());
            
        // H5配置
        PLATFORM_CONFIGS.put("H5", PlatformConfig.builder()
            .invitePathPattern("https://h5.example.com/invite?code={inviteCode}&app={appId}")
            .qrcodeType("URL")
            .qrcodeSize(250)
            .build());
            
        // 抖音配置
        PLATFORM_CONFIGS.put("DOUYIN", PlatformConfig.builder()
            .invitePathPattern("https://www.douyin.com/user/{userId}?share_code={inviteCode}")
            .qrcodeType("URL")
            .qrcodeSize(200)
            .build());
            
        // 微信小程序配置
        PLATFORM_CONFIGS.put("WECHAT", PlatformConfig.builder()
            .invitePathPattern("pages/invite/index?code={inviteCode}")
            .qrcodeType("MINIAPP")
            .qrcodeSize(280)
            .build());
    }
    
    public DistAgentPoster generatePoster(Long agentId, Long appId, String platformType) {
        // 1. 选择合适的海报模板
        DistPosterTemplate template = selectPosterTemplate(agentId, appId, platformType);
        if (template == null) {
            throw new BusinessException("未找到合适的海报模板");
        }
        
        // 2. 获取平台配置
        PlatformConfig config = PLATFORM_CONFIGS.get(platformType);
        
        // 3. 生成邀请链接
        DistAgent agent = distAgentMapper.selectById(agentId);
        String invitePath = generateInvitePath(agent, appId, config);
        
        // 4. 生成二维码
        String qrcodeUrl = generateQRCode(invitePath, platformType, config);
        
        // 5. 生成个性化海报
        String posterUrl = generatePersonalizedPoster(agent, template, qrcodeUrl);
        
        // 6. 保存海报记录
        DistAgentPoster poster = new DistAgentPoster();
        poster.setAgentId(agentId);
        poster.setAppId(appId);
        poster.setTemplateId(template.getId());
        poster.setPlatformType(platformType);
        poster.setPosterUrl(posterUrl);
        poster.setQrcodeUrl(qrcodeUrl);
        poster.setInvitePath(invitePath);
        
        // 小程序特殊处理
        if ("WECHAT".equals(platformType)) {
            poster.setQrcodeScene(generateMiniappScene(agent.getInviteCode()));
        }
        
        distAgentPosterMapper.insert(poster);
        return poster;
    }
    
    private String generateInvitePath(DistAgent agent, Long appId, PlatformConfig config) {
        return config.getInvitePathPattern()
            .replace("{userId}", agent.getMemberId().toString())
            .replace("{inviteCode}", agent.getInviteCode())
            .replace("{appId}", appId.toString());
    }
    
    private String generateQRCode(String content, String platformType, PlatformConfig config) {
        if ("MINIAPP".equals(config.getQrcodeType())) {
            // 调用微信API生成小程序码
            return wechatService.generateMiniappQRCode(content, config.getQrcodeSize());
        } else {
            // 生成普通二维码
            return qrcodeService.generateQRCode(content, config.getQrcodeSize());
        }
    }
    
    private String generatePersonalizedPoster(DistAgent agent, DistPosterTemplate template, String qrcodeUrl) {
        // 解析模板配置
        JSONObject templateConfig = JSON.parseObject(template.getConfig());
        JSONObject qrcodeConfig = JSON.parseObject(template.getQrcodeConfig());
        
        // 个性化参数
        Map<String, Object> params = new HashMap<>();
        params.put("agentName", agent.getRealName());
        params.put("inviteCode", agent.getInviteCode());
        params.put("qrcodeUrl", qrcodeUrl);
        params.put("qrcodePosition", qrcodeConfig);
        
        // 根据等级添加特殊元素
        DistLevel level = distLevelMapper.selectById(agent.getLevelId());
        if (level != null) {
            params.put("levelName", level.getLevelName());
            params.put("levelIcon", level.getIconUrl());
            params.put("levelColor", level.getBgColor());
        }
        
        // 调用图片处理服务生成海报
        return imageService.generatePoster(template.getBackgroundUrl(), params);
    }
}
```

#### 4.3.3 海报配置JSON示例
```json
// 海报模板配置 (yt_dist_poster_template.config)
{
    "elements": [
        {
            "type": "text",
            "content": "{agentName}邀请您加入",
            "position": {"x": 100, "y": 50},
            "style": {
                "fontSize": 24,
                "color": "#333333",
                "fontWeight": "bold"
            }
        },
        {
            "type": "text",
            "content": "邀请码：{inviteCode}",
            "position": {"x": 100, "y": 100},
            "style": {
                "fontSize": 18,
                "color": "#666666"
            }
        },
        {
            "type": "image",
            "content": "{levelIcon}",
            "position": {"x": 50, "y": 200},
            "size": {"width": 60, "height": 60}
        }
    ]
}

// 二维码配置 (yt_dist_poster_template.qrcode_config)
{
    "position": {"x": 200, "y": 400},
    "size": {"width": 200, "height": 200},
    "style": {
        "borderWidth": 10,
        "borderColor": "#FFFFFF",
        "borderRadius": 10
    },
    "logo": {
        "enabled": true,
        "url": "https://example.com/logo.png",
        "size": 40
    }
}
```

### 4.4 数据查询

所有查询都需要增加app_id条件：

```java
// 分销员列表查询
public PageResult<DistAgent> getAgentPage(DistAgentPageReqVO reqVO, Long appId) {
    return distAgentMapper.selectPage(reqVO, 
        new LambdaQueryWrapper<DistAgent>()
            .eq(DistAgent::getAppId, appId)
            // ... 其他查询条件
    );
}

// 佣金统计
public BigDecimal getTotalCommission(Long agentId, Long appId) {
    return distCommissionMapper.sumCommission(
        new LambdaQueryWrapper<DistCommission>()
            .eq(DistCommission::getAppId, appId)
            .eq(DistCommission::getAgentId, agentId)
            .eq(DistCommission::getStatus, CommissionStatus.SETTLED)
    );
}
```

## 五、接口改造

### 5.1 管理后台接口

#### 5.1.1 请求参数增加appId

```java
// 分销员分页查询
@Data
public class DistAgentPageReqVO extends PageParam {
    @ApiModelProperty("应用ID")
    @NotNull(message = "应用ID不能为空")
    private Long appId;
    
    // ... 其他查询参数
}

// Controller层获取并传递appId
@GetMapping("/page")
public CommonResult<PageResult<DistAgentRespVO>> getAgentPage(
    @Valid DistAgentPageReqVO pageVO) {
    // 从请求头或参数获取appId
    Long appId = pageVO.getAppId();
    PageResult<DistAgent> pageResult = distAgentService.getAgentPage(pageVO, appId);
    return success(DistAgentConvert.INSTANCE.convertPage(pageResult));
}
```

### 5.2 前端API接口

#### 5.2.1 从请求头获取appId

```java
import static com.yitong.octopus.framework.common.constant.Constants.APPID;

@RestController
@RequestMapping("/app/distribution")
public class AppDistributionController {
    
    @GetMapping("/agent/info")
    public CommonResult<AppDistAgentRespVO> getAgentInfo(
            @RequestHeader(APPID) Long appId) {
        // 从登录信息获取会员ID
        Long memberId = SecurityUtils.getLoginUserId();
        
        DistAgent agent = distAgentService.getByMemberAndApp(memberId, appId);
        return success(DistAgentConvert.INSTANCE.convert(agent));
    }
    
    @PostMapping("/agent/apply")
    public CommonResult<Boolean> applyAgent(
            @RequestHeader(APPID) Long appId,
            @Valid @RequestBody AppDistAgentApplyReqVO reqVO) {
        Long memberId = SecurityUtils.getLoginUserId();
        distAgentService.applyAgent(memberId, appId, reqVO);
        return success(true);
    }
    
    @GetMapping("/commission/list")
    public CommonResult<PageResult<AppDistCommissionRespVO>> getCommissionPage(
            @RequestHeader(APPID) Long appId,
            @Valid AppDistCommissionPageReqVO pageVO) {
        Long memberId = SecurityUtils.getLoginUserId();
        DistAgent agent = distAgentService.getByMemberAndApp(memberId, appId);
        
        PageResult<DistCommission> pageResult = distCommissionService.getCommissionPage(
            agent.getId(), appId, pageVO);
        return success(DistCommissionConvert.INSTANCE.convertPage(pageResult));
    }
    
    @GetMapping("/poster/templates")
    public CommonResult<List<AppDistPosterTemplateRespVO>> getPosterTemplates(
            @RequestHeader(APPID) Long appId,
            @RequestParam String platformType) {
        Long memberId = SecurityUtils.getLoginUserId();
        DistAgent agent = distAgentService.getByMemberAndApp(memberId, appId);
        
        // 获取该分销员可用的海报模板
        List<DistPosterTemplate> templates = distPosterTemplateService.getAvailableTemplates(
            agent.getId(), agent.getLevelId(), appId, platformType);
        return success(DistPosterTemplateConvert.INSTANCE.convertList(templates));
    }
    
    @PostMapping("/poster/generate")
    public CommonResult<AppDistAgentPosterRespVO> generatePoster(
            @RequestHeader(APPID) Long appId,
            @Valid @RequestBody AppDistPosterGenerateReqVO reqVO) {
        Long memberId = SecurityUtils.getLoginUserId();
        DistAgent agent = distAgentService.getByMemberAndApp(memberId, appId);
        
        // 生成分销海报
        DistAgentPoster poster = platformPosterGenerator.generatePoster(
            agent.getId(), appId, reqVO.getPlatformType());
        return success(DistAgentPosterConvert.INSTANCE.convert(poster));
    }
    
    @GetMapping("/poster/list")
    public CommonResult<PageResult<AppDistAgentPosterRespVO>> getPosterList(
            @RequestHeader(APPID) Long appId,
            @Valid AppDistPosterPageReqVO pageVO) {
        Long memberId = SecurityUtils.getLoginUserId();
        DistAgent agent = distAgentService.getByMemberAndApp(memberId, appId);
        
        PageResult<DistAgentPoster> pageResult = distAgentPosterService.getPosterPage(
            agent.getId(), appId, pageVO);
        return success(DistAgentPosterConvert.INSTANCE.convertPage(pageResult));
    }
}
```

## 六、数据迁移方案

### 6.1 历史数据处理

```sql
-- 1. 为所有历史数据设置默认app_id = 2
UPDATE yt_dist_agent SET app_id = 2 WHERE app_id IS NULL OR app_id = 0;
UPDATE yt_dist_commission SET app_id = 2 WHERE app_id IS NULL OR app_id = 0;
UPDATE yt_dist_withdraw SET app_id = 2 WHERE app_id IS NULL OR app_id = 0;
UPDATE yt_dist_goods_config SET app_id = 2 WHERE app_id IS NULL OR app_id = 0;
UPDATE yt_dist_agent_tag SET app_id = 2 WHERE app_id IS NULL OR app_id = 0;
UPDATE yt_dist_agent_tag_relation SET app_id = 2 WHERE app_id IS NULL OR app_id = 0;
UPDATE yt_dist_agent_poster SET app_id = 2 WHERE app_id IS NULL OR app_id = 0;

-- 2. 海报相关表默认平台类型处理
UPDATE yt_dist_poster_template SET platform_type = 'H5' WHERE platform_type IS NULL;
UPDATE yt_dist_agent_poster SET platform_type = 'H5' WHERE platform_type IS NULL;

-- 2. 等级表处理（全局等级app_id保持为NULL）
UPDATE yt_dist_level SET app_id = NULL WHERE is_default = 2;

-- 3. 数据一致性检查
-- 检查是否有孤立的佣金记录
SELECT c.* FROM yt_dist_commission c
LEFT JOIN yt_dist_agent a ON c.agent_id = a.id AND c.app_id = a.app_id
WHERE a.id IS NULL;

-- 检查上下级关系是否在同一应用
SELECT a1.id, a1.app_id, a2.id as parent_id, a2.app_id as parent_app_id
FROM yt_dist_agent a1
JOIN yt_dist_agent a2 ON a1.parent_id = a2.id
WHERE a1.app_id != a2.app_id;
```

## 七、实施步骤

### 7.1 第一阶段：数据库改造（1天）

1. **备份数据库**
   ```bash
   mysqldump -h host -u user -p yitong_dev > yitong_dev_backup_$(date +%Y%m%d).sql
   ```

2. **执行DDL脚本**
   - 按照第三章的顺序执行ALTER TABLE语句
   - 每个表改造后验证结构

3. **执行数据迁移**
   - 设置历史数据的默认app_id
   - 执行数据一致性检查

### 7.2 第二阶段：代码改造（3天）

1. **Entity层改造**
   - 所有DO类增加appId字段
   - 更新MyBatis映射文件

2. **Service层改造**
   - 所有方法增加appId参数
   - 查询条件增加appId过滤

3. **Controller层改造**  
   - 请求VO增加appId字段
   - 从请求上下文获取appId

### 7.3 第三阶段：测试验证（2天）

1. **功能测试**
   - 分销员注册、绑定上级
   - 佣金计算、提现
   - 数据隔离验证

2. **性能测试**
   - 查询性能对比
   - 索引效果验证

3. **兼容性测试**
   - 历史数据访问
   - 旧接口兼容

### 7.4 第四阶段：上线部署（1天）

1. **灰度发布**
   - 先更新一台服务器
   - 观察日志和监控

2. **全量发布**
   - 逐步更新所有服务器
   - 实时监控系统状态

3. **回滚预案**
   - 准备回滚脚本
   - 设置回滚触发条件

## 八、注意事项

### 8.1 关键风险点

1. **数据一致性**
   - 确保上下级在同一应用
   - 佣金记录与分销员应用一致

2. **唯一性约束**
   - 邀请码在应用内唯一
   - 会员在应用内只能有一个分销员身份

3. **性能影响**
   - 新增索引可能影响写入性能
   - 需要评估大表ALTER TABLE的执行时间

### 8.2 监控指标

1. **业务指标**
   - 各应用分销员数量
   - 跨应用数据隔离情况

2. **技术指标**
   - 查询响应时间
   - 数据库连接数
   - 错误日志监控

### 8.3 降级方案

如果出现严重问题，可以通过配置开关降级到单应用模式：

```java
@Value("${distribution.multi-app.enabled:true}")
private boolean multiAppEnabled;

private static final Long DEFAULT_APP_ID = 2L;

public Long getEffectiveAppId(Long requestAppId) {
    return multiAppEnabled ? requestAppId : DEFAULT_APP_ID;
}
```

## 九、总结

本方案通过最小化改动实现分销模块的应用化：
- 仅增加app_id字段，不改变原有业务逻辑
- 保持向后兼容，历史数据正常运行
- 实施风险可控，可随时回滚

### 9.1 核心功能覆盖

1. **应用隔离**
   - 分销员按应用独立管理
   - 佣金和提现按应用分别结算
   - 商品配置支持应用差异化

2. **海报生成增强**
   - 支持多平台（小红书、H5、抖音、微信）
   - 基于等级的差异化海报模板
   - 个性化海报生成能力
   - 平台特定的二维码和邀请链接

3. **数据完整性**
   - 上下级关系限定在同一应用内
   - 邀请码在应用内唯一
   - 历史数据平滑迁移

### 9.2 后续扩展方向

1. **多级分销优化**：基于现有ancestor_path字段优化
2. **跨应用共享**：实现分销员跨应用协作
3. **智能推荐**：基于应用特征的商品推荐
4. **数据分析**：应用维度的分销数据报表

## 十、实施进度（2025-07-28）

### 10.1 已完成工作 ✅

1. **Phase 1: 数据库改造**
   - ✅ 创建完整的SQL升级脚本：`/sql/mysql/distribution_app_id_upgrade.sql`
   - ✅ 所有核心表添加app_id字段
   - ✅ 更新唯一索引支持应用隔离
   - ✅ 历史数据迁移脚本（默认app_id=2）

2. **Phase 2: 代码改造**
   - ✅ Entity层：所有DO类添加appId字段
   - ✅ Service层：接口方法添加appId参数
   - ✅ Controller层：
     - 管理后台：请求VO添加appId字段
     - APP端：从请求头获取app-id
   - ✅ 多平台海报生成功能实现

3. **核心文件清单**
   ```
   /sql/mysql/distribution_app_id_upgrade.sql
   /docs/1.agent_app/implementation-summary.md
   .../service/poster/PlatformPosterGenerator.java
   .../service/poster/QRCodeService.java
   .../service/poster/ImageService.java
   .../service/poster/WechatService.java
   ```

### 10.2 待完成工作 ⏳

1. **ServiceImpl实现类改造** ✅ (2025-07-28 完成)
   - ✅ 修改所有ServiceImpl以适配新接口
     - DistAgentServiceImpl: 支持appId参数的分销员管理
     - DistCommissionServiceImpl: 支持appId参数的佣金计算
     - DistWithdrawServiceImpl: 支持appId参数的提现管理
     - DistGoodsConfigServiceImpl: 支持appId参数的商品配置
     - DistLevelServiceImpl: 支持appId参数的等级管理
   - ✅ 在所有查询中添加app_id过滤条件

2. **测试验证**
   - 单元测试编写
   - 应用隔离功能测试
   - 性能测试

3. **部署上线**
   - 数据库脚本执行
   - 代码部署
   - 监控观察

### 10.3 风险提示 ⚠️

1. **数据库执行**：建议在低峰期执行，预估30-60分钟
2. **前端适配**：需要确保前端在请求头传递app-id
3. **性能影响**：新增索引可能影响写入性能，需要监控