# 分销员标签合并功能实现文档

## 一、功能概述

实现了分销员标签合并功能，允许将多个源标签下的分销员合并到一个目标标签，支持去重处理和日志记录。

## 二、实现文件清单

### 1. VO类（视图对象）

#### DistAgentTagMergeReqVO.java
- 路径：`controller/admin/agent/tag/vo/DistAgentTagMergeReqVO.java`
- 描述：标签合并请求参数
- 字段：
  - `sourceTagIds` - 源标签ID数组
  - `targetTagId` - 目标标签ID
  - `deleteSourceTags` - 是否删除源标签
  - `remark` - 备注说明

#### DistAgentTagMergeRespVO.java
- 路径：`controller/admin/agent/tag/vo/DistAgentTagMergeRespVO.java`
- 描述：标签合并响应结果
- 字段：
  - `mergedCount` - 合并的分销员数量
  - `duplicateCount` - 重复的分销员数量
  - `deletedTagCount` - 删除的标签数量

### 2. Controller层

#### DistAgentTagController.java（修改）
- 新增接口：`POST /distribution/agent-tag/merge`
- 权限：`distribution:agent:tag:merge`
- 功能：接收合并请求，调用Service层执行合并

### 3. Service层

#### DistAgentTagService.java（修改）
- 新增方法：`mergeDistAgentTags(DistAgentTagMergeReqVO mergeReqVO)`
- 返回类型：`DistAgentTagMergeRespVO`

#### DistAgentTagServiceImpl.java（修改）
- 实现合并逻辑：
  1. 参数校验（源标签不能包含目标标签）
  2. 验证标签存在性和状态
  3. 获取源标签下的所有分销员
  4. 去重处理，计算需要新增的关联
  5. 批量添加新的标签关联
  6. 删除源标签关联
  7. 可选删除源标签（软删除）
  8. 更新目标标签使用人数
  9. 记录合并日志

### 4. Mapper层

#### DistAgentTagRelationMapper.java（修改）
- 新增方法：
  - `selectAgentIdsByTagIds(List<Long> tagIds)` - 根据多个标签ID获取分销员ID列表
  - `selectAgentIdsByTagId(Long tagId)` - 根据单个标签ID获取分销员ID列表
  - `deleteByTagIds(List<Long> tagIds)` - 根据标签ID列表删除关联

### 5. DO类（数据对象）

#### DistAgentTagMergeLogDO.java
- 路径：`dal/dataobject/agent/tag/DistAgentTagMergeLogDO.java`
- 描述：标签合并日志实体
- 字段：
  - `id` - 主键ID
  - `sourceTagIds` - 源标签ID列表（JSON格式）
  - `targetTagId` - 目标标签ID
  - `mergedCount` - 合并的分销员数量
  - `duplicateCount` - 重复的分销员数量
  - `deletedTagCount` - 删除的标签数量
  - `operatorId` - 操作人ID
  - `remark` - 备注说明
  - `createTime` - 创建时间

#### DistAgentTagMergeLogMapper.java
- 路径：`dal/mysql/agent/tag/DistAgentTagMergeLogMapper.java`
- 描述：合并日志Mapper接口

### 6. 错误码

#### ErrorCodeConstants.java（修改）
- 新增错误码：
  - `DIST_AGENT_TAG_MERGE_SOURCE_CONTAINS_TARGET` - 源标签不能包含目标标签
  - `DIST_AGENT_TAG_STATUS_ERROR` - 标签状态异常，无法合并

## 三、数据库脚本

### 1. 创建合并日志表
```sql
-- 文件：dist_agent_tag_merge_log.sql
CREATE TABLE IF NOT EXISTS `yt_dist_agent_tag_merge_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `source_tag_ids` varchar(500) NOT NULL COMMENT '源标签ID列表（JSON格式）',
  `target_tag_id` bigint(20) NOT NULL COMMENT '目标标签ID',
  `merged_count` int(11) NOT NULL DEFAULT '0' COMMENT '合并的分销员数量',
  `duplicate_count` int(11) NOT NULL DEFAULT '0' COMMENT '重复的分销员数量',
  `deleted_tag_count` int(11) NOT NULL DEFAULT '0' COMMENT '删除的标签数量',
  `operator_id` bigint(20) DEFAULT NULL COMMENT '操作人ID',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_target_tag_id` (`target_tag_id`) USING BTREE,
  KEY `idx_operator_id` (`operator_id`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员标签合并日志表';
```

### 2. 添加唯一索引
```sql
-- 防止同一个分销员重复拥有同一个标签
ALTER TABLE `yt_dist_agent_tag_relation` 
ADD UNIQUE INDEX `uk_agent_tag` (`agent_id`, `tag_id`) USING BTREE;
```

### 3. 权限配置
```sql
-- 文件：dist_agent_tag_merge_permission.sql
-- 添加合并权限菜单
INSERT INTO `system_menu` (...) VALUES ('分销员标签合并', 'distribution:agent:tag:merge', ...);
```

## 四、API使用示例

### 请求示例
```bash
POST /distribution/agent-tag/merge
Content-Type: application/json

{
  "sourceTagIds": [1, 2, 3],
  "targetTagId": 4,
  "deleteSourceTags": true,
  "remark": "合并重复标签"
}
```

### 响应示例
```json
{
  "code": 200,
  "data": {
    "mergedCount": 150,
    "duplicateCount": 20,
    "deletedTagCount": 3
  },
  "msg": "success"
}
```

## 五、核心逻辑说明

### 1. 去重机制
- 先获取目标标签已有的分销员列表
- 遍历源标签的分销员，过滤掉已存在的
- 统计重复数量用于返回

### 2. 事务处理
- 整个合并操作在一个事务中执行
- 确保数据一致性，要么全部成功，要么全部回滚

### 3. 批量操作优化
- 使用MyBatis Plus的`insertBatch`方法批量插入关联
- 减少数据库交互次数，提高性能

### 4. 软删除机制
- 标签删除采用软删除（设置deleted=1）
- 保留历史数据，便于追溯

### 5. 日志记录
- 记录详细的合并信息
- 包括操作人、时间、影响数据等
- 便于审计和问题排查

## 六、注意事项

1. **权限控制**：需要`distribution:agent:tag:merge`权限才能执行合并
2. **数据验证**：严格验证标签状态，避免异常数据合并
3. **性能考虑**：大量数据合并时采用批量操作
4. **唯一索引**：确保分销员与标签的关联唯一性
5. **操作日志**：完整记录合并操作，便于追溯