# 分销管理后台 V4 设计文档

## 概述

基于API v1文档和现有管理后台样式，设计完整的分销系统管理后台。采用现有的技术架构和组件库，确保与现有系统的一致性和可维护性。

## 技术架构

### 前端技术栈

- **框架**: Vue 3 + TypeScript (现有版本)
- **UI组件库**: Element Plus (现有版本)
- **状态管理**: Pinia (现有架构)
- **路由管理**: Vue Router (现有架构)
- **构建工具**: Vite (现有配置)
- **HTTP客户端**: Axios (现有配置)
- **样式方案**: SCSS (现有样式系统)
- **图表库**: ECharts (现有配置)

### API接口规范

基于API v1文档的接口规范：

```typescript
// 基础配置
const API_BASE = '/api/v1'
const AUTH_TYPE = 'Bearer Token'

// 统一响应格式
interface CommonResult<T = any> {
  code: number
  data: T
  msg: string
}

// 分页响应格式
interface PageResult<T> {
  list: T[]
  total: number
}

// 权限格式
const PERMISSION_FORMAT = 'distribution:{module}:{action}'
```

## 项目结构设计

### 目录结构

```
src/views/distribution-v4/              # 分销V4模块
├── agent/                             # 分销员管理
│   ├── index.vue                      # 分销员列表页
│   └── components/                    # 分销员组件
│       ├── AgentDrawer.vue            # 分销员抽屉（新增/编辑/详情）
│       ├── AgentAuditDrawer.vue       # 审核抽屉
│       ├── AgentTagDrawer.vue         # 标签管理抽屉
│       ├── AgentLevelDrawer.vue       # 等级调整抽屉
│       ├── AgentTeamDrawer.vue        # 团队信息抽屉
│       └── AgentStatsCards.vue        # 统计卡片
├── agent-tag/                         # 分销员标签管理
│   ├── index.vue                      # 标签列表页
│   └── components/                    # 标签组件
│       └── TagDrawer.vue              # 标签抽屉
├── commission/                        # 佣金管理
│   ├── index.vue                      # 佣金列表页
│   └── components/                    # 佣金组件
│       ├── CommissionDrawer.vue       # 佣金详情抽屉
│       ├── SettlementDrawer.vue       # 结算抽屉
│       └── CommissionStatsCards.vue   # 佣金统计卡片
├── goods/                             # 商品配置管理
│   ├── index.vue                      # 商品配置列表页
│   └── components/                    # 商品配置组件
│       └── GoodsConfigDrawer.vue      # 商品配置抽屉
├── level/                             # 等级管理
│   ├── index.vue                      # 等级列表页
│   └── components/                    # 等级组件
│       └── LevelDrawer.vue            # 等级抽屉
├── poster/                            # 海报管理
│   ├── template/                      # 海报模板
│   │   ├── index.vue                  # 模板列表页
│   │   └── components/                # 模板组件
│   │       └── TemplateDrawer.vue     # 模板抽屉
│   └── record/                        # 海报记录
│       ├── index.vue                  # 记录列表页
│       └── components/                # 记录组件
│           └── RecordDrawer.vue       # 记录抽屉
├── reward/                            # 奖励方案管理
│   ├── scheme/                        # 奖励方案
│   │   ├── index.vue                  # 方案列表页
│   │   └── components/                # 方案组件
│   │       └── SchemeDrawer.vue       # 方案抽屉
│   ├── level-config/                  # 等级配置
│   │   ├── index.vue                  # 等级配置页
│   │   └── components/                # 等级配置组件
│   └── tag-config/                    # 标签配置
│       ├── index.vue                  # 标签配置页
│       └── components/                # 标签配置组件
├── withdraw/                          # 提现管理
│   ├── index.vue                      # 提现列表页
│   └── components/                    # 提现组件
│       ├── WithdrawDrawer.vue         # 提现详情抽屉
│       └── WithdrawAuditDrawer.vue    # 提现审核抽屉
├── statistics/                        # 统计分析
│   ├── index.vue                      # 统计概览页
│   └── components/                    # 统计组件
│       ├── OverviewStats.vue          # 概览统计
│       ├── AgentAnalysis.vue          # 分销员分析
│       ├── CommissionAnalysis.vue     # 佣金分析
│       └── ProductAnalysis.vue        # 商品分析
└── components/                        # 公共组件
    ├── BaseDrawer.vue                 # 基础抽屉组件
    ├── SearchForm.vue                 # 搜索表单组件
    └── StatsGrid.vue                  # 统计网格组件
```

### API结构设计

```
src/api/distribution-v4/               # 分销V4 API
├── agent.ts                          # 分销员管理API
├── agent-tag.ts                      # 分销员标签API
├── commission.ts                     # 佣金管理API
├── goods-config.ts                   # 商品配置API
├── level.ts                          # 等级管理API
├── poster.ts                         # 海报模板API
├── agent-poster.ts                   # 分销员海报API
├── reward-scheme.ts                  # 奖励方案API
├── reward-level-config.ts            # 等级配置API
├── reward-tag-config.ts              # 标签配置API
├── withdraw.ts                       # 提现管理API
├── statistics.ts                     # 统计分析API
├── types.ts                          # 类型定义
└── index.ts                          # 统一导出
```

## 核心组件设计

### 1. 基础抽屉组件 (BaseDrawer)

基于现有的抽屉设计模式，创建统一的基础抽屉组件：

```vue
<template>
  <el-drawer
    v-model="visible"
    :title="title"
    :size="size"
    :before-close="handleBeforeClose"
    :close-on-click-modal="false"
    class="distribution-drawer"
  >
    <!-- 头部 -->
    <template #header>
      <div class="drawer-header">
        <h3 class="drawer-title">{{ title }}</h3>
        <div class="drawer-actions">
          <slot name="header-actions" />
        </div>
      </div>
    </template>
    
    <!-- 内容 -->
    <div class="drawer-content">
      <slot />
    </div>
    
    <!-- 底部 -->
    <template #footer v-if="showFooter">
      <div class="drawer-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm"
          :loading="loading"
        >
          {{ confirmText }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
interface Props {
  modelValue: boolean
  title: string
  size?: string | number
  loading?: boolean
  showFooter?: boolean
  confirmText?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 600,
  loading: false,
  showFooter: true,
  confirmText: '确定'
})

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const handleConfirm = () => emit('confirm')
const handleCancel = () => emit('cancel')
const handleBeforeClose = (done: () => void) => {
  // 可以在这里添加确认逻辑
  done()
}
</script>
```

### 2. 统计卡片组件增强

基于现有的StatisticsCard组件进行增强：

```vue
<template>
  <el-card class="stats-card" :class="{ clickable }" @click="handleClick">
    <div class="stats-content">
      <!-- 图标区域 -->
      <div class="stats-icon" :style="iconStyle">
        <Icon :icon="icon" :size="iconSize" />
      </div>
      
      <!-- 信息区域 -->
      <div class="stats-info">
        <div class="stats-value">{{ formattedValue }}</div>
        <div class="stats-label">{{ label }}</div>
        
        <!-- 趋势指示器 -->
        <div v-if="trend !== undefined" class="stats-trend" :class="trendClass">
          <Icon :icon="trendIcon" />
          <span>{{ Math.abs(trend) }}%</span>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
interface Props {
  label: string
  value: number | string
  icon: string
  iconColor?: string
  iconSize?: number
  trend?: number
  format?: 'number' | 'currency' | 'percentage'
  clickable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  iconColor: '#409EFF',
  iconSize: 24,
  format: 'number',
  clickable: false
})

const emit = defineEmits(['click'])

const formattedValue = computed(() => {
  const val = props.value
  switch (props.format) {
    case 'currency':
      return `¥${Number(val).toLocaleString()}`
    case 'percentage':
      return `${val}%`
    default:
      return Number(val).toLocaleString()
  }
})

const iconStyle = computed(() => ({
  backgroundColor: `${props.iconColor}20`,
  color: props.iconColor
}))

const trendClass = computed(() => {
  if (props.trend === undefined) return ''
  return props.trend > 0 ? 'trend-up' : 'trend-down'
})

const trendIcon = computed(() => {
  if (props.trend === undefined) return ''
  return props.trend > 0 ? 'ep:arrow-up' : 'ep:arrow-down'
})

const handleClick = () => {
  if (props.clickable) {
    emit('click')
  }
}
</script>
```

### 3. 数据表格组件优化

基于现有的DataTable组件进行优化：

```vue
<template>
  <div class="data-table-container">
    <!-- 表格工具栏 -->
    <div class="table-toolbar" v-if="showToolbar">
      <div class="toolbar-left">
        <slot name="toolbar" />
      </div>
      <div class="toolbar-right">
        <el-button @click="handleRefresh" :loading="loading">
          <Icon icon="ep:refresh" />
          刷新
        </el-button>
        <el-button @click="handleExport" v-if="exportable">
          <Icon icon="ep:download" />
          导出
        </el-button>
      </div>
    </div>
    
    <!-- 数据表格 -->
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="data"
      :stripe="true"
      :show-overflow-tooltip="true"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      class="distribution-table"
    >
      <!-- 选择列 -->
      <el-table-column 
        v-if="selectable"
        type="selection" 
        width="55" 
      />
      
      <!-- 序号列 -->
      <el-table-column
        v-if="showIndex"
        type="index"
        label="序号"
        width="60"
        :index="indexMethod"
      />
      
      <!-- 数据列 -->
      <slot />
    </el-table>
    
    <!-- 分页 -->
    <div class="table-pagination" v-if="showPagination">
      <Pagination
        :total="total"
        v-model:page="currentPage"
        v-model:limit="pageSize"
        @pagination="handlePaginationChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  data: any[]
  loading?: boolean
  total?: number
  page?: number
  limit?: number
  selectable?: boolean
  showIndex?: boolean
  showToolbar?: boolean
  showPagination?: boolean
  exportable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  total: 0,
  page: 1,
  limit: 20,
  selectable: false,
  showIndex: false,
  showToolbar: true,
  showPagination: true,
  exportable: false
})

const emit = defineEmits([
  'refresh',
  'export',
  'selection-change',
  'sort-change',
  'pagination'
])

const tableRef = ref()
const currentPage = ref(props.page)
const pageSize = ref(props.limit)

const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize.value + index + 1
}

const handleRefresh = () => emit('refresh')
const handleExport = () => emit('export')
const handleSelectionChange = (selection: any[]) => emit('selection-change', selection)
const handleSortChange = (sort: any) => emit('sort-change', sort)
const handlePaginationChange = ({ page, limit }: any) => {
  currentPage.value = page
  pageSize.value = limit
  emit('pagination', { page, limit })
}

// 暴露表格方法
defineExpose({
  clearSelection: () => tableRef.value?.clearSelection(),
  toggleRowSelection: (row: any, selected?: boolean) => 
    tableRef.value?.toggleRowSelection(row, selected)
})
</script>
```

## 页面设计规范

### 1. 列表页面设计

所有列表页面采用统一的设计模式，参考现有的activity/coupon/index.vue：

```vue
<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="分销员姓名" prop="agentName">
        <el-input
          v-model="queryParams.agentName"
          placeholder="请输入分销员姓名"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="分销等级" prop="levelId">
        <el-select
          v-model="queryParams.levelId"
          placeholder="请选择分销等级"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="level in levelList"
            :key="level.id"
            :label="level.levelName"
            :value="level.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请状态" prop="applyStatus">
        <el-select
          v-model="queryParams.applyStatus"
          placeholder="请选择申请状态"
          clearable
          class="!w-240px"
        >
          <el-option label="待审核" :value="0" />
          <el-option label="审核通过" :value="1" />
          <el-option label="审核拒绝" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> 重置
        </el-button>
        <el-button
          type="primary"
          @click="handleCreate"
          v-hasPermi="['distribution:agent:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          @click="handleBatchAudit"
          :disabled="!selectedRows.length"
          v-hasPermi="['distribution:agent:audit']"
        >
          <Icon icon="ep:check" class="mr-5px" /> 批量审核
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['distribution:agent:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      
      <el-table-column label="分销员信息" align="left" min-width="200">
        <template #default="scope">
          <div class="flex items-center">
            <el-avatar :size="40" class="mr-10px">
              {{ scope.row.agentName.substring(0, 1) }}
            </el-avatar>
            <div>
              <div class="font-bold">{{ scope.row.agentName }}</div>
              <div class="text-xs text-gray-500">{{ scope.row.agentCode }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="手机号" align="center" prop="agentMobile" />
      <el-table-column label="分销等级" align="center" prop="levelName" />
      <el-table-column label="申请状态" align="center" prop="applyStatus" />
      <el-table-column label="创建时间" align="center" prop="createTime" :formatter="dateFormatter" />
      
      <el-table-column label="操作" align="center" fixed="right" width="200">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleDetail(scope.row)"
            v-hasPermi="['distribution:agent:query']"
          >
            详情
          </el-button>
          <el-button
            v-if="scope.row.applyStatus === 0"
            link
            type="success"
            @click="handleAudit(scope.row)"
            v-hasPermi="['distribution:agent:audit']"
          >
            审核
          </el-button>
          <el-button
            link
            type="primary"
            @click="handleEdit(scope.row)"
            v-hasPermi="['distribution:agent:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['distribution:agent:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 抽屉组件 -->
  <AgentDrawer ref="agentDrawerRef" @success="getList" />
  <AuditDrawer ref="auditDrawerRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import { checkPermi } from '@/utils/permission'
import { DISTRIBUTION_PERMISSIONS } from '@/utils/distribution/permissions'
import * as AgentApi from '@/api/distribution-v4/agent'

defineOptions({ name: 'DistributionV4Agent' })

const message = useMessage()
const { t } = useI18n()

// 响应式数据
const loading = ref(true)
const total = ref(0)
const list = ref([])
const selectedRows = ref([])
const exportLoading = ref(false)
const levelList = ref([])

const queryParams = reactive({
  pageNo: 1,
  pageSize: 20,
  agentName: '',
  levelId: undefined,
  applyStatus: undefined,
  createTime: []
})

const queryFormRef = ref()

// 权限检查
const canCreate = computed(() => checkPermi([DISTRIBUTION_PERMISSIONS.AGENT_CREATE]))
const canAudit = computed(() => checkPermi([DISTRIBUTION_PERMISSIONS.AGENT_AUDIT]))
const canExport = computed(() => checkPermi([DISTRIBUTION_PERMISSIONS.AGENT_EXPORT]))

// 方法实现
const getList = async () => {
  loading.value = true
  try {
    const data = await AgentApi.getAgentPage(queryParams)
    list.value = data.data.list
    total.value = data.data.total
  } finally {
    loading.value = false
  }
}

const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 其他方法...

onMounted(() => {
  getList()
})
</script>
```

### 2. 抽屉页面设计

所有抽屉组件采用统一的设计模式：

```vue
<template>
  <BaseDrawer
    v-model="visible"
    :title="drawerTitle"
    :size="drawerSize"
    :loading="loading"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <!-- 根据模式显示不同内容 -->
    <div v-if="mode === 'detail'" class="detail-content">
      <!-- 详情展示 -->
      <el-descriptions :column="2" border>
        <el-descriptions-item 
          v-for="field in detailFields" 
          :key="field.key"
          :label="field.label"
          :span="field.span"
        >
          <component 
            :is="field.component || 'span'" 
            v-bind="field.props"
          >
            {{ formatFieldValue(data[field.key], field) }}
          </component>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    
    <div v-else class="form-content">
      <!-- 表单编辑 -->
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item
          v-for="field in formFields"
          :key="field.prop"
          :label="field.label"
          :prop="field.prop"
          :required="field.required"
        >
          <component
            :is="field.component"
            v-model="formData[field.prop]"
            v-bind="field.props"
          />
        </el-form-item>
      </el-form>
    </div>
  </BaseDrawer>
</template>
```

## API接口设计

### 1. 统一API封装

```typescript
// api/distribution-v4/base.ts
import request from '@/config/axios'

export class BaseApi {
  protected baseUrl: string

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl
  }

  // 分页查询
  async getPage<T>(params: any): Promise<CommonResult<PageResult<T>>> {
    return request.get({
      url: `${this.baseUrl}/page`,
      params
    })
  }

  // 获取详情
  async getDetail<T>(id: number): Promise<CommonResult<T>> {
    return request.get({
      url: `${this.baseUrl}/get`,
      params: { id }
    })
  }

  // 创建
  async create<T>(data: any): Promise<CommonResult<T>> {
    return request.post({
      url: `${this.baseUrl}/create`,
      data
    })
  }

  // 更新
  async update<T>(data: any): Promise<CommonResult<T>> {
    return request.put({
      url: `${this.baseUrl}/update`,
      data
    })
  }

  // 删除
  async delete(id: number): Promise<CommonResult<void>> {
    return request.delete({
      url: `${this.baseUrl}/delete`,
      params: { id }
    })
  }

  // 导出
  async export(params: any): Promise<Blob> {
    return request.get({
      url: `${this.baseUrl}/export`,
      params,
      responseType: 'blob'
    })
  }
}
```

### 2. 具体API实现

```typescript
// api/distribution-v4/agent.ts
import { BaseApi } from './base'

class AgentApi extends BaseApi {
  constructor() {
    super('/distribution/agent')
  }

  // 获取申请列表
  async getApplyList(params: any): Promise<CommonResult<PageResult<any>>> {
    return request.get({
      url: `${this.baseUrl}/apply-list`,
      params
    })
  }

  // 审核申请
  async audit(data: any): Promise<CommonResult<void>> {
    return request.post({
      url: `${this.baseUrl}/audit`,
      data
    })
  }

  // 批量审核
  async batchAudit(data: any): Promise<CommonResult<void>> {
    return request.post({
      url: `${this.baseUrl}/batch-audit`,
      data
    })
  }

  // 更新标签
  async updateTags(data: any): Promise<CommonResult<void>> {
    return request.put({
      url: `${this.baseUrl}/update-tags`,
      data
    })
  }

  // 更新等级
  async updateLevel(data: any): Promise<CommonResult<void>> {
    return request.put({
      url: `${this.baseUrl}/update-level`,
      data
    })
  }

  // 调整等级
  async adjustLevel(data: any): Promise<CommonResult<void>> {
    return request.put({
      url: `${this.baseUrl}/adjust-level`,
      data
    })
  }

  // 更新状态
  async updateStatus(data: any): Promise<CommonResult<void>> {
    return request.put({
      url: `${this.baseUrl}/update-status`,
      data
    })
  }

  // 批量更新状态
  async batchUpdateStatus(data: any): Promise<CommonResult<void>> {
    return request.post({
      url: `${this.baseUrl}/batch-update-status`,
      data
    })
  }

  // 调整上级
  async adjustParent(data: any): Promise<CommonResult<void>> {
    return request.put({
      url: `${this.baseUrl}/adjust-parent`,
      data
    })
  }

  // 查询关系链
  async getRelationship(params: any): Promise<CommonResult<any>> {
    return request.get({
      url: `${this.baseUrl}/relationship`,
      params
    })
  }

  // 获取团队成员
  async getTeamMembers(params: any): Promise<CommonResult<PageResult<any>>> {
    return request.get({
      url: `${this.baseUrl}/team-members`,
      params
    })
  }

  // 查询团队列表
  async getTeamList(params: any): Promise<CommonResult<PageResult<any>>> {
    return request.get({
      url: `${this.baseUrl}/team-list`,
      params
    })
  }

  // 查询下级树
  async getChildrenTree(params: any): Promise<CommonResult<any[]>> {
    return request.get({
      url: `${this.baseUrl}/children-tree`,
      params
    })
  }

  // 获取统计信息
  async getStatistics(): Promise<CommonResult<any>> {
    return request.get({
      url: `${this.baseUrl}/statistics`
    })
  }

  // 查询团队订单
  async getTeamOrders(params: any): Promise<CommonResult<PageResult<any>>> {
    return request.get({
      url: `${this.baseUrl}/team-orders`,
      params
    })
  }

  // 查询个人订单
  async getPersonalOrders(params: any): Promise<CommonResult<PageResult<any>>> {
    return request.get({
      url: `${this.baseUrl}/personal-orders`,
      params
    })
  }
}

export const agentApi = new AgentApi()
```

## 权限控制设计

### 1. 权限常量定义

基于API文档的权限注解，定义完整的权限常量：

```typescript
// utils/distribution/permissions.ts
export const DISTRIBUTION_PERMISSIONS = {
  // 分销员管理权限
  AGENT_QUERY: 'distribution:agent:query',
  AGENT_CREATE: 'distribution:agent:create',
  AGENT_UPDATE: 'distribution:agent:update',
  AGENT_DELETE: 'distribution:agent:delete',
  AGENT_AUDIT: 'distribution:agent:audit',
  AGENT_EXPORT: 'distribution:agent:export',

  // 分销员标签权限
  AGENT_TAG_QUERY: 'distribution:agent:tag:query',
  AGENT_TAG_CREATE: 'distribution:agent:tag:create',
  AGENT_TAG_UPDATE: 'distribution:agent:tag:update',
  AGENT_TAG_DELETE: 'distribution:agent:tag:delete',
  AGENT_TAG_EXPORT: 'distribution:agent:tag:export',

  // 佣金管理权限
  COMMISSION_QUERY: 'distribution:commission:query',
  COMMISSION_SETTLE: 'distribution:commission:settle',
  COMMISSION_MANAGE: 'distribution:commission:manage',
  COMMISSION_EXPORT: 'distribution:commission:export',

  // 商品配置权限
  PRODUCT_QUERY: 'distribution:product:query',
  PRODUCT_CONFIG: 'distribution:product:config',
  PRODUCT_UPDATE: 'distribution:product:update',
  PRODUCT_EXPORT: 'distribution:product:export',

  // 等级管理权限
  LEVEL_QUERY: 'distribution:level:query',
  LEVEL_CREATE: 'distribution:level:create',
  LEVEL_UPDATE: 'distribution:level:update',
  LEVEL_DELETE: 'distribution:level:delete',
  LEVEL_EXPORT: 'distribution:level:export',

  // 海报管理权限
  POSTER_QUERY: 'distribution:poster:query',
  POSTER_CREATE: 'distribution:poster:create',
  POSTER_UPDATE: 'distribution:poster:update',
  POSTER_DELETE: 'distribution:poster:delete',
  AGENT_POSTER_QUERY: 'distribution:agent-poster:query',
  AGENT_POSTER_CREATE: 'distribution:agent-poster:create',
  AGENT_POSTER_UPDATE: 'distribution:agent-poster:update',
  AGENT_POSTER_DELETE: 'distribution:agent-poster:delete',

  // 奖励方案权限
  REWARD_SCHEME_QUERY: 'distribution:reward-scheme:query',
  REWARD_SCHEME_CREATE: 'distribution:reward-scheme:create',
  REWARD_SCHEME_UPDATE: 'distribution:reward-scheme:update',
  REWARD_SCHEME_DELETE: 'distribution:reward-scheme:delete',
  REWARD_LEVEL_CONFIG_QUERY: 'distribution:reward-level-config:query',
  REWARD_LEVEL_CONFIG_CREATE: 'distribution:reward-level-config:create',
  REWARD_LEVEL_CONFIG_UPDATE: 'distribution:reward-level-config:update',
  REWARD_LEVEL_CONFIG_DELETE: 'distribution:reward-level-config:delete',
  REWARD_TAG_CONFIG_QUERY: 'distribution:reward-tag-config:query',
  REWARD_TAG_CONFIG_CREATE: 'distribution:reward-tag-config:create',
  REWARD_TAG_CONFIG_UPDATE: 'distribution:reward-tag-config:update',
  REWARD_TAG_CONFIG_DELETE: 'distribution:reward-tag-config:delete',

  // 统计分析权限
  STATISTICS_QUERY: 'distribution:statistics:query',

  // 提现管理权限
  WITHDRAW_QUERY: 'distribution:withdraw:query',
  WITHDRAW_AUDIT: 'distribution:withdraw:audit',
  WITHDRAW_TRANSFER: 'distribution:withdraw:transfer',
  WITHDRAW_CANCEL: 'distribution:withdraw:cancel',
  WITHDRAW_MANAGE: 'distribution:withdraw:manage',
  WITHDRAW_PAYMENT: 'distribution:withdraw:payment',
  WITHDRAW_EXPORT: 'distribution:withdraw:export'
} as const
```

### 2. 权限控制使用方式

基于现有的权限控制系统，使用 `v-hasPermi` 指令进行权限控制：

#### 2.1 按钮权限控制

```vue
<template>
  <!-- 新增按钮 -->
  <el-button 
    type="primary" 
    @click="handleCreate"
    v-hasPermi="['distribution:agent:create']"
  >
    <Icon icon="ep:plus" class="mr-5px" />
    新增分销员
  </el-button>

  <!-- 批量审核按钮 -->
  <el-button 
    type="success" 
    @click="handleBatchAudit"
    :disabled="!selectedRows.length"
    v-hasPermi="['distribution:agent:audit']"
  >
    <Icon icon="ep:check" class="mr-5px" />
    批量审核
  </el-button>

  <!-- 导出按钮 -->
  <el-button 
    type="success" 
    @click="handleExport"
    :loading="exportLoading"
    v-hasPermi="['distribution:agent:export']"
  >
    <Icon icon="ep:download" class="mr-5px" />
    导出
  </el-button>
</template>
```

#### 2.2 表格操作列权限控制

```vue
<template>
  <el-table-column label="操作" align="center" fixed="right" width="200">
    <template #default="scope">
      <!-- 详情按钮 -->
      <el-button
        link
        type="primary"
        @click="handleDetail(scope.row)"
        v-hasPermi="['distribution:agent:query']"
      >
        详情
      </el-button>
      
      <!-- 审核按钮 -->
      <el-button
        v-if="scope.row.applyStatus === 0"
        link
        type="success"
        @click="handleAudit(scope.row)"
        v-hasPermi="['distribution:agent:audit']"
      >
        审核
      </el-button>
      
      <!-- 编辑按钮 -->
      <el-button
        link
        type="primary"
        @click="handleEdit(scope.row)"
        v-hasPermi="['distribution:agent:update']"
      >
        编辑
      </el-button>
      
      <!-- 删除按钮 -->
      <el-button
        link
        type="danger"
        @click="handleDelete(scope.row.id)"
        v-hasPermi="['distribution:agent:delete']"
      >
        删除
      </el-button>
    </template>
  </el-table-column>
</template>
```

#### 2.3 下拉菜单权限控制

```vue
<template>
  <el-dropdown @command="(command) => handleCommand(command, scope.row)">
    <el-button link type="primary">
      更多
      <Icon icon="ep:arrow-down" class="ml-5px" />
    </el-button>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item 
          command="updateStatus" 
          v-if="scope.row.status === 0"
          v-hasPermi="['distribution:agent:update']"
        >
          启用
        </el-dropdown-item>
        <el-dropdown-item 
          command="updateStatus" 
          v-if="scope.row.status === 1"
          v-hasPermi="['distribution:agent:update']"
        >
          禁用
        </el-dropdown-item>
        <el-dropdown-item 
          command="adjustLevel"
          v-hasPermi="['distribution:agent:update']"
        >
          调整等级
        </el-dropdown-item>
        <el-dropdown-item 
          command="viewTeam"
          v-hasPermi="['distribution:agent:query']"
        >
          查看团队
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>
```

### 3. 编程式权限检查

在某些复杂场景下，可以使用编程式权限检查：

```vue
<script setup lang="ts">
import { checkPermi } from '@/utils/permission'
import { DISTRIBUTION_PERMISSIONS } from '@/utils/distribution/permissions'

// 检查单个权限
const canCreateAgent = computed(() => {
  return checkPermi([DISTRIBUTION_PERMISSIONS.AGENT_CREATE])
})

// 检查多个权限（OR关系）
const canManageAgent = computed(() => {
  return checkPermi([
    DISTRIBUTION_PERMISSIONS.AGENT_UPDATE,
    DISTRIBUTION_PERMISSIONS.AGENT_DELETE
  ])
})

// 在方法中使用权限检查
const handleBatchOperation = () => {
  if (!checkPermi([DISTRIBUTION_PERMISSIONS.AGENT_UPDATE])) {
    ElMessage.error('权限不足')
    return
  }
  // 执行批量操作
}
</script>
```

### 4. 路由权限控制

在路由配置中添加权限要求：

```typescript
// router/modules/distribution-v4.ts
export default {
  path: '/distribution-v4',
  component: Layout,
  name: 'DistributionV4',
  meta: {
    title: '分销管理V4',
    icon: 'ep:share',
    alwaysShow: true
  },
  children: [
    {
      path: 'agent',
      component: () => import('@/views/distribution-v4/agent/index.vue'),
      name: 'DistributionV4Agent',
      meta: { 
        title: '分销员管理', 
        keepAlive: true,
        permissions: ['distribution:agent:query'] // 页面访问权限
      }
    },
    {
      path: 'agent-tag',
      component: () => import('@/views/distribution-v4/agent-tag/index.vue'),
      name: 'DistributionV4AgentTag',
      meta: { 
        title: '分销员标签', 
        keepAlive: true,
        permissions: ['distribution:agent:tag:query']
      }
    }
    // 其他路由...
  ]
}
```

### 5. 权限控制最佳实践

#### 5.1 权限粒度设计

```typescript
// 按照API文档的权限注解设计，确保前后端权限一致
const PERMISSION_MAPPING = {
  // 页面访问权限
  PAGE_ACCESS: {
    AGENT_LIST: ['distribution:agent:query'],
    COMMISSION_LIST: ['distribution:commission:query'],
    // ...
  },
  
  // 功能操作权限
  OPERATIONS: {
    CREATE: ['distribution:agent:create'],
    UPDATE: ['distribution:agent:update'],
    DELETE: ['distribution:agent:delete'],
    AUDIT: ['distribution:agent:audit'],
    EXPORT: ['distribution:agent:export'],
    // ...
  }
}
```

#### 5.2 权限错误处理

```vue
<script setup lang="ts">
// 统一的权限错误处理
const handlePermissionError = (action: string) => {
  ElMessage.error(`权限不足，无法执行${action}操作`)
  console.warn(`Permission denied for action: ${action}`)
}

// 在操作前检查权限
const handleCreate = () => {
  if (!checkPermi([DISTRIBUTION_PERMISSIONS.AGENT_CREATE])) {
    handlePermissionError('新增分销员')
    return
  }
  // 执行创建操作
}
</script>
```

#### 5.3 权限状态管理

```typescript
// stores/permission.ts
export const usePermissionStore = defineStore('permission', () => {
  const permissions = ref<string[]>([])
  
  // 检查权限的便捷方法
  const hasPermission = (permission: string | string[]) => {
    const perms = Array.isArray(permission) ? permission : [permission]
    return checkPermi(perms)
  }
  
  // 检查分销模块权限
  const hasDistributionPermission = (action: keyof typeof DISTRIBUTION_PERMISSIONS) => {
    return hasPermission(DISTRIBUTION_PERMISSIONS[action])
  }
  
  return {
    permissions,
    hasPermission,
    hasDistributionPermission
  }
})
```

## 路由配置

```typescript
// router/modules/distribution-v4.ts
export default {
  path: '/distribution-v4',
  component: Layout,
  name: 'DistributionV4',
  meta: {
    title: '分销管理V4',
    icon: 'ep:share',
    alwaysShow: true
  },
  children: [
    {
      path: 'agent',
      component: () => import('@/views/distribution-v4/agent/index.vue'),
      name: 'DistributionV4Agent',
      meta: { 
        title: '分销员管理', 
        keepAlive: true,
        permissions: ['distribution:agent:query']
      }
    },
    {
      path: 'agent-tag',
      component: () => import('@/views/distribution-v4/agent-tag/index.vue'),
      name: 'DistributionV4AgentTag',
      meta: { 
        title: '分销员标签', 
        keepAlive: true,
        permissions: ['distribution:agent:tag:query']
      }
    },
    {
      path: 'commission',
      component: () => import('@/views/distribution-v4/commission/index.vue'),
      name: 'DistributionV4Commission',
      meta: { 
        title: '佣金管理', 
        keepAlive: true,
        permissions: ['distribution:commission:query']
      }
    },
    // 其他路由配置...
  ]
}
```

## 样式设计规范

### 1. 统一样式变量

```scss
// styles/distribution-v4.scss
:root {
  // 分销模块专用颜色
  --dist-primary-color: #409eff;
  --dist-success-color: #67c23a;
  --dist-warning-color: #e6a23c;
  --dist-danger-color: #f56c6c;
  --dist-info-color: #909399;
  
  // 间距
  --dist-spacing-xs: 4px;
  --dist-spacing-sm: 8px;
  --dist-spacing-md: 16px;
  --dist-spacing-lg: 24px;
  --dist-spacing-xl: 32px;
  
  // 圆角
  --dist-border-radius: 8px;
  --dist-border-radius-sm: 4px;
  --dist-border-radius-lg: 12px;
}

// 分销页面通用样式
.distribution-page {
  .stats-section {
    margin-bottom: var(--dist-spacing-lg);
  }
  
  .search-section {
    margin-bottom: var(--dist-spacing-md);
  }
  
  .table-section {
    .el-card {
      border-radius: var(--dist-border-radius);
    }
  }
}

// 分销抽屉通用样式
.distribution-drawer {
  .drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--dist-spacing-md);
    border-bottom: 1px solid var(--el-border-color-light);
    
    .drawer-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }
  }
  
  .drawer-content {
    padding: var(--dist-spacing-lg);
    height: calc(100% - 120px);
    overflow-y: auto;
  }
  
  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--dist-spacing-sm);
    padding: var(--dist-spacing-md);
    border-top: 1px solid var(--el-border-color-light);
  }
}

// 统计卡片样式
.stats-card {
  border-radius: var(--dist-border-radius);
  transition: all 0.3s ease;
  
  &.clickable {
    cursor: pointer;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
  
  .stats-content {
    display: flex;
    align-items: center;
    gap: var(--dist-spacing-md);
  }
  
  .stats-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: var(--dist-border-radius);
  }
  
  .stats-info {
    flex: 1;
    
    .stats-value {
      font-size: 24px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 4px;
    }
    
    .stats-label {
      font-size: 14px;
      color: var(--el-text-color-regular);
      margin-bottom: 4px;
    }
    
    .stats-trend {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      
      &.trend-up {
        color: var(--dist-success-color);
      }
      
      &.trend-down {
        color: var(--dist-danger-color);
      }
    }
  }
}
```

## 性能优化设计

### 1. 虚拟滚动实现

```vue
<!-- components/VirtualTable.vue -->
<template>
  <div class="virtual-table-container" :style="{ height: `${height}px` }">
    <div class="virtual-table-header">
      <!-- 表头 -->
      <div class="table-header-row">
        <div 
          v-for="column in columns" 
          :key="column.key"
          class="table-header-cell"
          :style="{ width: column.width }"
        >
          {{ column.title }}
        </div>
      </div>
    </div>
    
    <div 
      class="virtual-table-body"
      :style="{ height: `${bodyHeight}px` }"
      @scroll="handleScroll"
      ref="scrollContainer"
    >
      <div :style="{ height: `${totalHeight}px`, position: 'relative' }">
        <div 
          v-for="(item, index) in visibleData" 
          :key="getRowKey(item, startIndex + index)"
          class="table-row"
          :style="{ 
            position: 'absolute',
            top: `${(startIndex + index) * rowHeight}px`,
            width: '100%',
            height: `${rowHeight}px`
          }"
        >
          <div 
            v-for="column in columns" 
            :key="column.key"
            class="table-cell"
            :style="{ width: column.width }"
          >
            <slot 
              :name="column.key" 
              :row="item" 
              :index="startIndex + index"
              :column="column"
            >
              {{ item[column.key] }}
            </slot>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Column {
  key: string
  title: string
  width: string
}

interface Props {
  data: any[]
  columns: Column[]
  height?: number
  rowHeight?: number
  rowKey?: string | ((row: any, index: number) => string)
}

const props = withDefaults(defineProps<Props>(), {
  height: 400,
  rowHeight: 48,
  rowKey: 'id'
})

const scrollContainer = ref<HTMLElement>()
const startIndex = ref(0)
const endIndex = ref(0)

const bodyHeight = computed(() => props.height - 48) // 减去表头高度
const totalHeight = computed(() => props.data.length * props.rowHeight)
const visibleCount = computed(() => Math.ceil(bodyHeight.value / props.rowHeight) + 2)

const visibleData = computed(() => {
  return props.data.slice(startIndex.value, endIndex.value)
})

const getRowKey = (row: any, index: number) => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(row, index)
  }
  return row[props.rowKey] || index
}

const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement
  const scrollTop = target.scrollTop
  
  startIndex.value = Math.floor(scrollTop / props.rowHeight)
  endIndex.value = Math.min(
    startIndex.value + visibleCount.value,
    props.data.length
  )
}

// 初始化可见数据
onMounted(() => {
  endIndex.value = Math.min(visibleCount.value, props.data.length)
})
</script>
```

### 2. 数据缓存策略

```typescript
// utils/cache.ts
class DataCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()

  set(key: string, data: any, ttl: number = 5 * 60 * 1000) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  get(key: string) {
    const item = this.cache.get(key)
    if (!item) return null

    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  clear() {
    this.cache.clear()
  }

  delete(key: string) {
    this.cache.delete(key)
  }
}

export const dataCache = new DataCache()

// 使用示例
export const useCachedApi = <T>(
  key: string,
  apiCall: () => Promise<T>,
  ttl?: number
) => {
  return async (): Promise<T> => {
    const cached = dataCache.get(key)
    if (cached) {
      return cached
    }

    const result = await apiCall()
    dataCache.set(key, result, ttl)
    return result
  }
}
```

这个重写的设计文档完全基于API v1文档和现有管理后台样式，提供了完整的技术架构、组件设计、API接口、权限控制、路由配置、样式规范和性能优化方案。
