# 分销系统管理后台 API 文档 v1

本文档详细列出了分销系统管理后台的所有 API 接口信息。

## 基础信息

- **基础路径**: `/api/v1`
- **认证方式**: JWT Token
- **权限控制**: Spring Security 权限注解

## 1. 分销员管理 (DistAgentController)

**基础路径**: `/distribution/agent`

### 1.1 申请管理

| HTTP方法 | 路径 | 功能说明 | 权限要求 |
|---------|------|---------|---------|
| GET | `/apply-list` | 获取分销员申请列表 | `distribution:agent:query` |
| POST | `/audit` | 审核分销员申请 | `distribution:agent:audit` |
| POST | `/batch-audit` | 批量审核分销员申请 | `distribution:agent:audit` |

### 1.2 分销员信息管理

| HTTP方法 | 路径 | 功能说明 | 权限要求 |
|---------|------|---------|---------|
| POST | `/create` | 创建分销员 | `distribution:agent:create` |
| PUT | `/update` | 更新分销员信息 | `distribution:agent:update` |
| PUT | `/update-tags` | 更新分销员标签 | `distribution:agent:update` |
| GET | `/page` | 获取分销员分页 | `distribution:agent:query` |
| GET | `/get` | 获取分销员详情 | `distribution:agent:query` |
| DELETE | `/delete` | 删除分销员 | `distribution:agent:delete` |
| GET | `/export` | 导出分销员数据 | `distribution:agent:export` |

### 1.3 等级与状态管理

| HTTP方法 | 路径 | 功能说明 | 权限要求 |
|---------|------|---------|---------|
| PUT | `/update-level` | 更新分销员等级 | `distribution:agent:update` |
| PUT | `/adjust-level` | 调整分销员等级（含原因） | `distribution:agent:update` |
| PUT | `/update-status` | 更新分销员状态 | `distribution:agent:update` |
| POST | `/batch-update-status` | 批量更新分销员状态 | `distribution:agent:update` |

### 1.4 团队与关系管理

| HTTP方法 | 路径 | 功能说明 | 权限要求 |
|---------|------|---------|---------|
| PUT | `/adjust-parent` | 调整分销员上级 | `distribution:agent:update` |
| GET | `/relationship` | 查询分销员关系链 | `distribution:agent:query` |
| GET | `/team-members` | 获取团队成员列表 | `distribution:agent:query` |
| GET | `/team-list` | 查询团队成员列表 | `distribution:agent:query` |
| GET | `/children-tree` | 查询下级树 | `distribution:agent:query` |

### 1.5 统计与订单查询

| HTTP方法 | 路径 | 功能说明 | 权限要求 |
|---------|------|---------|---------|
| GET | `/statistics` | 获取分销员统计信息 | `distribution:agent:query` |
| GET | `/team-orders` | 查询团队订单 | `distribution:agent:query` |
| GET | `/personal-orders` | 查询个人订单 | `distribution:agent:query` |

## 2. 分销员标签管理 (DistAgentTagController)

**基础路径**: `/distribution/agent-tag`

| HTTP方法 | 路径 | 功能说明 | 权限要求 |
|---------|------|---------|---------|
| POST | `/create` | 创建分销员标签 | `distribution:agent:tag:create` |
| PUT | `/update` | 更新分销员标签 | `distribution:agent:tag:update` |
| DELETE | `/delete` | 删除分销员标签 | `distribution:agent:tag:delete` |
| GET | `/get` | 获得分销员标签 | `distribution:agent:tag:query` |
| GET | `/page` | 获得分销员标签分页 | `distribution:agent:tag:query` |
| GET | `/list` | 获得分销员标签列表 | `distribution:agent:tag:query` |
| GET | `/simple-list` | 获得启用状态的分销员标签列表 | `distribution:agent:tag:query` |
| GET | `/export-excel` | 导出分销员标签 Excel | `distribution:agent:tag:export` |
| GET | `/by-agent` | 根据分销员ID获取标签列表 | `distribution:agent:tag:query` |
| PUT | `/update-agent-tags` | 更新分销员的标签 | `distribution:agent:tag:update` |

## 3. 佣金管理 (DistCommissionController)

**基础路径**: `/distribution/commission`

### 3.1 账单查询

| HTTP方法 | 路径 | 功能说明 | 权限要求 |
|---------|------|---------|---------|
| GET | `/bill/list` | 查询佣金账单列表 | `distribution:commission:query` |
| GET | `/bill/get` | 查询佣金账单详情 | `distribution:commission:query` |
| GET | `/bill/export` | 导出佣金账单 | `distribution:commission:export` |

### 3.2 结算与管理

| HTTP方法 | 路径 | 功能说明 | 权限要求 |
|---------|------|---------|---------|
| GET | `/pending` | 获取待结算佣金分页 | `distribution:commission:settle` |
| POST | `/batch-settle` | 批量结算佣金 | `distribution:commission:settle` |
| PUT | `/unfreeze` | 解冻佣金 | `distribution:commission:manage` |
| PUT | `/batch-unfreeze` | 批量解冻佣金 | `distribution:commission:manage` |
| PUT | `/cancel` | 取消佣金账单 | `distribution:commission:manage` |
| POST | `/refund` | 退款佣金 | `distribution:commission:manage` |

### 3.3 统计与分析

| HTTP方法 | 路径 | 功能说明 | 权限要求 |
|---------|------|---------|---------|
| GET | `/statistics` | 获取佣金统计 | `distribution:commission:query` |
| GET | `/ranking` | 获取佣金排行榜 | `distribution:commission:query` |
| GET | `/summary` | 获取佣金汇总信息 | `distribution:commission:query` |

## 4. 商品配置管理 (DistGoodsConfigController)

**基础路径**: `/distribution/product`

| HTTP方法 | 路径 | 功能说明 | 权限要求 |
|---------|------|---------|---------|
| POST | `/batch-config` | 批量配置分销商品 | `distribution:product:config` |
| PUT | `/update-config` | 更新单个商品分销配置 | `distribution:product:update` |
| GET | `/config` | 查询商品分销配置详情 | `distribution:product:query` |
| GET | `/list` | 查询分销商品列表 | `distribution:product:query` |
| GET | `/export` | 导出分销商品数据 | `distribution:product:export` |
| PUT | `/update-status` | 启用/禁用商品分销 | `distribution:product:update` |

## 5. 等级管理 (DistLevelController)

**基础路径**: `/distribution/level`

| HTTP方法 | 路径 | 功能说明 | 权限要求 |
|---------|------|---------|---------|
| POST | `/create` | 创建分销等级 | `distribution:level:create` |
| PUT | `/update` | 更新分销等级 | `distribution:level:update` |
| DELETE | `/delete` | 删除分销等级 | `distribution:level:delete` |
| GET | `/get` | 获得分销等级 | `distribution:level:query` |
| GET | `/list` | 获得分销等级分页 | `distribution:level:query` |
| GET | `/export-excel` | 导出分销等级 Excel | `distribution:level:export` |
| GET | `/list-simple` | 获取分销等级精简信息列表 | 无需权限 |

## 6. 海报管理

### 6.1 分销员海报记录 (DistAgentPosterController)

**基础路径**: `/distribution/agent-poster`

| HTTP方法 | 路径 | 功能说明 | 权限要求 |
|---------|------|---------|---------|
| POST | `/generate` | 生成分销员海报 | `distribution:agent-poster:create` |
| GET | `/get` | 获得分销员海报记录 | `distribution:agent-poster:query` |
| GET | `/page` | 获得分销员海报记录分页 | `distribution:agent-poster:query` |
| GET | `/list-by-agent` | 获得分销员的海报记录列表 | `distribution:agent-poster:query` |
| PUT | `/increment-share` | 增加分享次数 | `distribution:agent-poster:update` |
| PUT | `/increment-view` | 增加浏览次数 | `distribution:agent-poster:update` |
| GET | `/statistics` | 获取海报统计信息 | `distribution:agent-poster:query` |
| DELETE | `/delete` | 删除分销员海报记录 | `distribution:agent-poster:delete` |

### 6.2 海报模板管理 (DistPosterController)

**基础路径**: `/distribution/poster`

| HTTP方法 | 路径 | 功能说明 | 权限要求 |
|---------|------|---------|---------|
| POST | `/create` | 创建分销海报模板 | `distribution:poster:create` |
| PUT | `/update` | 更新分销海报模板 | `distribution:poster:update` |
| DELETE | `/delete` | 删除分销海报模板 | `distribution:poster:delete` |
| GET | `/get` | 获得分销海报模板 | `distribution:poster:query` |
| GET | `/page` | 获得分销海报模板分页 | `distribution:poster:query` |
| GET | `/list` | 获得分销海报模板列表 | `distribution:poster:query` |
| PUT | `/update-status` | 更新分销海报模板状态 | `distribution:poster:update` |

## 7. 奖励方案管理

### 7.1 等级配置 (DistRewardLevelConfigController)

**基础路径**: `/distribution/reward-level-config`

| HTTP方法 | 路径 | 功能说明 | 权限要求 |
|---------|------|---------|---------|
| POST | `/create` | 创建分销等级配置 | `distribution:reward-level-config:create` |
| PUT | `/update` | 更新分销等级配置 | `distribution:reward-level-config:update` |
| DELETE | `/delete` | 删除分销等级配置 | `distribution:reward-level-config:delete` |
| GET | `/get` | 获得分销等级配置详情 | `distribution:reward-level-config:query` |
| GET | `/page` | 获得分销等级配置分页 | `distribution:reward-level-config:query` |
| POST | `/batch-save` | 批量保存分销等级配置 | `distribution:reward-level-config:update` |
| GET | `/list-by-scheme` | 获取方案的所有等级配置 | `distribution:reward-level-config:query` |

### 7.2 奖励方案 (DistRewardSchemeController)

**基础路径**: `/distribution/reward-scheme`

| HTTP方法 | 路径 | 功能说明 | 权限要求 |
|---------|------|---------|---------|
| GET | `/page` | 获取奖励方案分页 | `distribution:reward-scheme:query` |
| GET | `/get` | 获取奖励方案详情 | `distribution:reward-scheme:query` |
| POST | `/create` | 创建奖励方案 | `distribution:reward-scheme:create` |
| PUT | `/update` | 更新奖励方案 | `distribution:reward-scheme:update` |
| DELETE | `/delete` | 删除奖励方案 | `distribution:reward-scheme:delete` |
| PUT | `/update-status` | 更新奖励方案状态 | `distribution:reward-scheme:update` |
| GET | `/list` | 获取奖励方案简单列表 | `distribution:reward-scheme:query` |

### 7.3 标签配置 (DistRewardTagConfigController)

**基础路径**: `/distribution/reward-tag-config`

| HTTP方法 | 路径 | 功能说明 | 权限要求 |
|---------|------|---------|---------|
| POST | `/create` | 创建分销标签配置 | `distribution:reward-tag-config:create` |
| PUT | `/update` | 更新分销标签配置 | `distribution:reward-tag-config:update` |
| DELETE | `/delete` | 删除分销标签配置 | `distribution:reward-tag-config:delete` |
| GET | `/get` | 获得分销标签配置详情 | `distribution:reward-tag-config:query` |
| GET | `/page` | 获得分销标签配置分页 | `distribution:reward-tag-config:query` |
| POST | `/batch-save` | 批量保存分销标签配置 | `distribution:reward-tag-config:update` |
| GET | `/list-by-scheme` | 获取方案的所有标签配置 | `distribution:reward-tag-config:query` |

## 8. 统计分析 (DistStatisticsController)

**基础路径**: `/distribution/statistics`

| HTTP方法 | 路径 | 功能说明 | 权限要求 |
|---------|------|---------|---------|
| GET | `/overview` | 获取分销概览统计 | `distribution:statistics:query` |
| GET | `/ranking` | 获取分销排行榜 | `distribution:statistics:query` |
| GET | `/trend` | 获取分销趋势统计 | `distribution:statistics:query` |
| GET | `/commission-analysis` | 获取佣金分析 | `distribution:statistics:query` |
| GET | `/agent-analysis` | 获取分销员分析 | `distribution:statistics:query` |
| GET | `/product-analysis` | 获取商品分销分析 | `distribution:statistics:query` |

## 9. 提现管理 (DistWithdrawController)

**基础路径**: `/distribution/withdraw`

| HTTP方法 | 路径 | 功能说明 | 权限要求 |
|---------|------|---------|---------|
| GET | `/list` | 查询提现记录列表 | `distribution:withdraw:query` |
| GET | `/get` | 查询提现记录详情 | `distribution:withdraw:query` |
| POST | `/{withdrawId}/audit` | 审核提现 | `distribution:withdraw:audit` |
| POST | `/batch-audit` | 批量审核提现申请 | `distribution:withdraw:audit` |
| PUT | `/mark-transferred` | 标记已转账 | `distribution:withdraw:transfer` |
| PUT | `/cancel` | 取消提现申请 | `distribution:withdraw:cancel` |
| GET | `/statistics` | 获取提现统计信息 | `distribution:withdraw:query` |
| GET | `/export` | 导出提现记录 | `distribution:withdraw:export` |
| GET | `/check-timeout` | 检查超时提现 | `distribution:withdraw:manage` |
| POST | `/{withdrawId}/retry-payment` | 重试提现支付 | `distribution:withdraw:payment` |

## 权限说明

所有接口都需要通过 Spring Security 进行权限验证，使用 `@PreAuthorize` 注解进行权限控制。权限标识符格式为：`distribution:{模块}:{操作}`

常见操作权限：
- `query`: 查询权限
- `create`: 创建权限
- `update`: 更新权限
- `delete`: 删除权限
- `export`: 导出权限
- `audit`: 审核权限
- `manage`: 管理权限

## 注意事项

1. 所有接口返回格式统一使用 `CommonResult<T>` 包装
2. 分页查询使用 `PageResult<T>` 返回结果
3. 所有接口都包含 Swagger/OpenAPI 文档注解
4. 部分接口（如订单查询）标记为 TODO，需要与其他模块集成后完善