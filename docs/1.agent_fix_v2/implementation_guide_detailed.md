# 分销模块DO对齐详细实施指南

## 一、核心变更对比表

### 1.1 DistAgentDO 字段变更
| 原字段名 | 新字段名 | 数据库字段 | 变更说明 |
|---------|---------|-----------|----------|
| agentCode | inviteCode | invite_code | 字段含义调整：分销员自己的邀请码 |
| agentName | realName | real_name | 字段名对齐数据库 |
| path | ancestorPath | ancestor_path | 字段名对齐数据库 |
| level | teamDepth | team_depth | 字段名对齐数据库 |
| withdrawnAmount | withdrawnCommission | withdrawn_commission | 字段名对齐数据库 |
| directTeamCount | directMemberCount | direct_member_count | 字段名对齐数据库 |
| totalTeamCount | teamMemberCount | team_member_count | 字段名对齐数据库 |
| - | totalSales | total_sales | 新增字段 |
| - | lastActiveTime | last_active_time | 新增字段 |
| - | extraInfo | extra_info | 新增字段 |
| auditorId | - | - | 删除字段（数据库无此字段） |
| remark | - | - | 删除字段（数据库无此字段） |

### 1.2 DistCommissionDO 字段变更
| 原字段名 | 新字段名 | 数据库字段 | 变更说明 |
|---------|---------|-----------|----------|
| - | commissionNo | commission_no | 新增字段 |
| - | memberId | member_id | 新增字段 |
| - | orderItemId | order_item_id | 新增字段 |
| - | goodsPrice | goods_price | 新增字段 |
| - | quantity | quantity | 新增字段 |
| - | commissionMode | commission_mode | 新增字段 |
| - | extraRate | extra_rate | 新增字段 |
| - | extraAmount | extra_amount | 新增字段 |
| - | freezeTime | freeze_time | 新增字段 |
| - | withdrawId | withdraw_id | 新增字段 |
| goodsAmount | - | - | 删除字段 |
| commissionBase | - | - | 删除字段 |
| commissionType | - | - | 删除字段 |
| commissionLevel | - | - | 删除字段 |
| buyerUserId | - | - | 删除字段 |
| buyerUserName | - | - | 删除字段 |
| sourceAgentId | - | - | 删除字段 |
| settleBatchNo | - | - | 删除字段 |

## 二、邀请码生成器实现

### 2.1 创建邀请码生成器
```java
package com.yitong.octopus.module.distribution.util;

import java.security.SecureRandom;
import java.util.function.Function;

/**
 * 邀请码生成器
 * 生成6位不含混淆字符的邀请码
 */
public class InviteCodeGenerator {
    
    // 排除容易混淆的字符：0, O, 1, I, l
    private static final String CHARS = "23456789ABCDEFGHJKMNPQRSTUVWXYZ";
    private static final int CODE_LENGTH = 6;
    private static final SecureRandom RANDOM = new SecureRandom();
    
    /**
     * 生成邀请码
     */
    public static String generate() {
        StringBuilder code = new StringBuilder(CODE_LENGTH);
        for (int i = 0; i < CODE_LENGTH; i++) {
            code.append(CHARS.charAt(RANDOM.nextInt(CHARS.length())));
        }
        return code.toString();
    }
    
    /**
     * 生成唯一邀请码
     * @param existsChecker 检查邀请码是否已存在的函数
     * @return 唯一的邀请码
     */
    public static String generateUnique(Function<String, Boolean> existsChecker) {
        String code;
        int attempts = 0;
        do {
            code = generate();
            attempts++;
            if (attempts > 100) {
                throw new RuntimeException("无法生成唯一邀请码，请稍后重试");
            }
        } while (existsChecker.apply(code));
        
        return code;
    }
    
    /**
     * 验证邀请码格式
     */
    public static boolean isValid(String code) {
        if (code == null || code.length() != CODE_LENGTH) {
            return false;
        }
        
        for (char c : code.toCharArray()) {
            if (CHARS.indexOf(c) == -1) {
                return false;
            }
        }
        
        return true;
    }
}
```

### 2.2 创建邀请码生成器测试
```java
package com.yitong.octopus.module.distribution.util;

import org.junit.jupiter.api.Test;
import static org.assertj.core.api.Assertions.*;

class InviteCodeGeneratorTest {
    
    @Test
    void testGenerate() {
        String code = InviteCodeGenerator.generate();
        
        // 验证长度
        assertThat(code).hasSize(6);
        
        // 验证字符集
        assertThat(code).matches("^[23456789ABCDEFGHJKMNPQRSTUVWXYZ]{6}$");
        
        // 验证不包含混淆字符
        assertThat(code).doesNotContain("0", "O", "1", "I", "l");
    }
    
    @Test
    void testGenerateUnique() {
        // 模拟已存在的邀请码
        Set<String> existingCodes = Set.of("ABC123", "DEF456");
        
        String code = InviteCodeGenerator.generateUnique(existingCodes::contains);
        
        assertThat(code).hasSize(6);
        assertThat(existingCodes).doesNotContain(code);
    }
    
    @Test
    void testIsValid() {
        // 有效邀请码
        assertThat(InviteCodeGenerator.isValid("ABC123")).isTrue();
        assertThat(InviteCodeGenerator.isValid("XYZ789")).isTrue();
        
        // 无效邀请码
        assertThat(InviteCodeGenerator.isValid("ABC12")).isFalse(); // 长度不够
        assertThat(InviteCodeGenerator.isValid("ABC1234")).isFalse(); // 长度过长
        assertThat(InviteCodeGenerator.isValid("ABC12O")).isFalse(); // 包含混淆字符O
        assertThat(InviteCodeGenerator.isValid("ABC120")).isFalse(); // 包含混淆字符0
        assertThat(InviteCodeGenerator.isValid(null)).isFalse(); // null值
    }
}
```

## 三、状态枚举实现

### 3.1 分销员状态枚举
```java
package com.yitong.octopus.module.distribution.enums;

/**
 * 分销员状态枚举
 */
public enum AgentStatus {
    
    PENDING(0, "待审核"),
    ACTIVE(1, "正常"),
    FROZEN(2, "冻结"),
    CANCELLED(3, "已注销");
    
    private final Integer code;
    private final String desc;
    
    AgentStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public static AgentStatus fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (AgentStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("未知的分销员状态码: " + code);
    }
    
    public boolean isPending() {
        return this == PENDING;
    }
    
    public boolean isActive() {
        return this == ACTIVE;
    }
    
    public boolean isFrozen() {
        return this == FROZEN;
    }
    
    public boolean isCancelled() {
        return this == CANCELLED;
    }
}
```

### 3.2 佣金状态枚举
```java
package com.yitong.octopus.module.distribution.enums;

/**
 * 佣金状态枚举
 */
public enum CommissionStatus {
    
    PENDING(0, "待结算"),
    FROZEN(1, "已冻结"),
    SETTLED(2, "已结算"),
    WITHDRAWN(3, "已提现"),
    CANCELLED(4, "已取消");
    
    private final Integer code;
    private final String desc;
    
    CommissionStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public static CommissionStatus fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (CommissionStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("未知的佣金状态码: " + code);
    }
    
    public boolean isPending() {
        return this == PENDING;
    }
    
    public boolean isFrozen() {
        return this == FROZEN;
    }
    
    public boolean isSettled() {
        return this == SETTLED;
    }
    
    public boolean isWithdrawn() {
        return this == WITHDRAWN;
    }
    
    public boolean isCancelled() {
        return this == CANCELLED;
    }
}
```

## 四、实施步骤详解

### 4.1 第一步：创建工具类和枚举（预计0.5天）

1. **创建邀请码生成器**
   - 在 `util` 包下创建 `InviteCodeGenerator` 类
   - 编写对应的单元测试

2. **创建状态枚举**
   - 在 `enums` 包下创建各种状态枚举
   - 编写枚举的单元测试

3. **验证编译**
   ```bash
   mvn clean compile
   mvn test -Dtest=InviteCodeGeneratorTest
   ```

### 4.2 第二步：重构DO类（预计1天）

1. **备份现有DO类**
   ```bash
   cp -r src/main/java/com/yitong/octopus/module/distribution/dal/dataobject backup/
   ```

2. **逐个重构DO类**
   - 先重构 `DistAgentDO`
   - 再重构 `DistCommissionDO`
   - 最后重构其他DO类

3. **编译验证**
   ```bash
   mvn clean compile
   ```

### 4.3 第三步：更新Mapper（预计0.5天）

1. **更新Mapper XML文件**
   - 更新字段映射
   - 调整ResultMap
   - 修改SQL语句

2. **更新Mapper接口**
   - 调整方法参数
   - 添加新的查询方法

### 4.4 第四步：更新Service层（预计1天）

1. **更新Service实现类**
   - 调整字段赋值逻辑
   - 更新状态处理逻辑
   - 集成邀请码生成器

2. **更新业务逻辑**
   - 佣金计算逻辑
   - 统计数据计算
   - 状态流转逻辑

## 五、关键代码示例

### 5.1 Service层邀请码生成示例
```java
@Service
public class DistAgentServiceImpl implements DistAgentService {
    
    @Autowired
    private DistAgentMapper distAgentMapper;
    
    @Override
    public DistAgentDO createAgent(CreateAgentRequest request) {
        // 生成唯一邀请码
        String inviteCode = InviteCodeGenerator.generateUnique(
            code -> distAgentMapper.existsByInviteCode(code)
        );
        
        DistAgentDO agent = DistAgentDO.builder()
            .memberId(request.getMemberId())
            .inviteCode(inviteCode)
            .realName(request.getRealName())
            .mobile(request.getMobile())
            .levelId(request.getLevelId())
            .parentId(request.getParentId())
            .status(AgentStatus.PENDING.getCode())
            .applyTime(LocalDateTime.now())
            .totalSales(BigDecimal.ZERO)
            .totalCommission(BigDecimal.ZERO)
            .availableCommission(BigDecimal.ZERO)
            .frozenCommission(BigDecimal.ZERO)
            .withdrawnCommission(BigDecimal.ZERO)
            .directMemberCount(0)
            .teamMemberCount(0)
            .teamDepth(0)
            .build();
            
        distAgentMapper.insert(agent);
        return agent;
    }
}
```

### 5.2 Mapper XML更新示例
```xml
<!-- DistAgentMapper.xml -->
<resultMap id="BaseResultMap" type="com.yitong.octopus.module.distribution.dal.dataobject.agent.DistAgentDO">
    <id column="id" property="id" />
    <result column="member_id" property="memberId" />
    <result column="invite_code" property="inviteCode" />
    <result column="real_name" property="realName" />
    <result column="mobile" property="mobile" />
    <result column="level_id" property="levelId" />
    <result column="parent_id" property="parentId" />
    <result column="ancestor_path" property="ancestorPath" />
    <result column="team_depth" property="teamDepth" />
    <result column="status" property="status" />
    <result column="apply_time" property="applyTime" />
    <result column="audit_time" property="auditTime" />
    <result column="audit_remark" property="auditRemark" />
    <result column="total_sales" property="totalSales" />
    <result column="total_commission" property="totalCommission" />
    <result column="available_commission" property="availableCommission" />
    <result column="frozen_commission" property="frozenCommission" />
    <result column="withdrawn_commission" property="withdrawnCommission" />
    <result column="direct_member_count" property="directMemberCount" />
    <result column="team_member_count" property="teamMemberCount" />
    <result column="last_active_time" property="lastActiveTime" />
    <result column="extra_info" property="extraInfo" />
</resultMap>

<select id="selectByInviteCode" resultMap="BaseResultMap">
    SELECT * FROM yt_dist_agent 
    WHERE invite_code = #{inviteCode} AND deleted = 0
</select>

<select id="existsByInviteCode" resultType="boolean">
    SELECT COUNT(1) > 0 FROM yt_dist_agent 
    WHERE invite_code = #{inviteCode} AND deleted = 0
</select>
```

## 六、测试验证方案

### 6.1 单元测试清单
- [ ] InviteCodeGenerator 测试
- [ ] AgentStatus 枚举测试
- [ ] CommissionStatus 枚举测试
- [ ] DistAgentDO 字段验证测试
- [ ] DistCommissionDO 字段验证测试

### 6.2 集成测试清单
- [ ] 分销员创建流程测试
- [ ] 邀请码唯一性测试
- [ ] 佣金计算准确性测试
- [ ] 状态流转测试
- [ ] 数据统计一致性测试

### 6.3 性能测试清单
- [ ] 团队查询性能测试
- [ ] 佣金统计查询性能测试
- [ ] 邀请码生成性能测试
- [ ] 大数据量下的响应时间测试

## 七、上线检查清单

### 7.1 代码检查
- [ ] 所有DO类编译通过
- [ ] 所有单元测试通过
- [ ] 所有集成测试通过
- [ ] 代码审查完成
- [ ] 静态代码分析通过

### 7.2 数据库检查
- [ ] 数据库索引创建完成
- [ ] 数据库连接测试通过
- [ ] 数据一致性检查通过
- [ ] 性能测试达标

### 7.3 部署检查
- [ ] 测试环境部署成功
- [ ] 功能验证通过
- [ ] 性能验证通过
- [ ] 回滚方案准备完成
- [ ] 监控告警配置完成

---

**文档版本**: V1.0  
**最后更新**: 2025-07-22