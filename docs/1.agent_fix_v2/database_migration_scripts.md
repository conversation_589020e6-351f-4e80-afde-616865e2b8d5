# 分销模块数据库直接调整方案

## 一、调整策略

### 1.1 调整原则

- **直接修改**: 项目未发布，可以直接修改数据库结构
- **数据安全**: 执行前进行数据备份
- **一次到位**: 直接调整到目标结构，无需分阶段

### 1.2 调整步骤

1. **数据备份**（可选，开发环境可跳过）
2. **直接执行结构调整脚本**
3. **创建必要的索引**
4. **验证调整结果**

## 二、数据备份（可选）

```bash
#!/bin/bash
# 仅在需要备份时执行
mysqldump -u root -p yitong_dev > backup_distribution_$(date +%Y%m%d_%H%M%S).sql
```

## 三、数据库结构直接调整脚本

### 3.1 分销员表调整

```sql
-- 由于项目未发布，直接重建表结构更简单
-- 如果有测试数据需要保留，先备份数据
CREATE TABLE yt_dist_agent_temp AS SELECT * FROM yt_dist_agent;

-- 删除原表
DROP TABLE yt_dist_agent;

-- 重新创建表（使用最新的表结构）
CREATE TABLE `yt_dist_agent` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分销员ID',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `invite_code` varchar(32) NOT NULL COMMENT '邀请码',
  `real_name` varchar(32) DEFAULT NULL COMMENT '真实姓名',
  `mobile` varchar(11) DEFAULT NULL COMMENT '手机号',
  `level_id` bigint(20) NOT NULL COMMENT '等级ID',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '上级分销员ID',
  `ancestor_path` varchar(500) DEFAULT NULL COMMENT '祖先路径',
  `team_depth` int(11) DEFAULT '0' COMMENT '团队深度',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待审核 1-正常 2-冻结 3-已注销',
  `apply_time` datetime NOT NULL COMMENT '申请时间',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(200) DEFAULT NULL COMMENT '审核备注',
  `total_sales` decimal(10,2) DEFAULT '0.00' COMMENT '累计销售额',
  `total_commission` decimal(10,2) DEFAULT '0.00' COMMENT '累计佣金',
  `available_commission` decimal(10,2) DEFAULT '0.00' COMMENT '可用佣金',
  `frozen_commission` decimal(10,2) DEFAULT '0.00' COMMENT '冻结佣金',
  `withdrawn_commission` decimal(10,2) DEFAULT '0.00' COMMENT '已提现佣金',
  `direct_member_count` int(11) DEFAULT '0' COMMENT '直推人数',
  `team_member_count` int(11) DEFAULT '0' COMMENT '团队人数',
  `last_active_time` datetime DEFAULT NULL COMMENT '最后活跃时间',
  `extra_info` text COMMENT '扩展信息',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_member_id` (`member_id`,`deleted`) USING BTREE,
  UNIQUE KEY `uk_invite_code` (`invite_code`,`deleted`) USING BTREE,
  KEY `idx_parent_id` (`parent_id`) USING BTREE,
  KEY `idx_level_id` (`level_id`) USING BTREE,
  KEY `idx_mobile` (`mobile`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_ancestor_path` (`ancestor_path`(191)) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='分销员表';

-- 如果需要恢复测试数据，执行以下语句（根据实际情况调整字段映射）
/*
INSERT INTO yt_dist_agent (
  member_id, invite_code, real_name, mobile, level_id, parent_id, 
  ancestor_path, team_depth, status, apply_time, audit_time, audit_remark,
  total_sales, total_commission, available_commission, frozen_commission, 
  withdrawn_commission, direct_member_count, team_member_count, 
  last_active_time, extra_info, creator, create_time, updater, update_time, deleted, tenant_id
)
SELECT 
  member_id, 
  CASE 
    WHEN agent_code IS NOT NULL AND LENGTH(agent_code) = 6 THEN agent_code
    ELSE CONCAT('A', LPAD(id, 5, '0'))  -- 生成临时邀请码
  END as invite_code,
  agent_name as real_name,
  mobile, level_id, parent_id,
  path as ancestor_path,
  level as team_depth,
  status, apply_time, audit_time, audit_remark,
  0.00 as total_sales,
  total_commission, available_commission, frozen_commission,
  withdrawn_amount as withdrawn_commission,
  direct_team_count as direct_member_count,
  total_team_count as team_member_count,
  update_time as last_active_time,
  JSON_OBJECT('migrated', true) as extra_info,
  creator, create_time, updater, update_time, deleted, tenant_id
FROM yt_dist_agent_temp;
*/

-- 删除临时表
DROP TABLE IF EXISTS yt_dist_agent_temp;
```

### 3.2 佣金记录表调整

```sql
-- 备份数据
CREATE TABLE yt_dist_commission_temp AS SELECT * FROM yt_dist_commission;

-- 删除原表
DROP TABLE yt_dist_commission;

-- 重新创建表
CREATE TABLE `yt_dist_commission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '佣金ID',
  `commission_no` varchar(64) NOT NULL COMMENT '佣金单号',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_no` varchar(64) NOT NULL COMMENT '订单编号',
  `order_item_id` bigint(20) DEFAULT NULL COMMENT '订单项ID',
  `goods_id` bigint(20) NOT NULL COMMENT '商品ID',
  `goods_name` varchar(100) NOT NULL COMMENT '商品名称',
  `goods_price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `order_amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `commission_mode` tinyint(4) NOT NULL COMMENT '佣金模式：1-固定金额 2-百分比 3-阶梯式',
  `commission_rate` decimal(5,2) DEFAULT NULL COMMENT '佣金比例(%)',
  `commission_amount` decimal(10,2) NOT NULL COMMENT '佣金金额',
  `extra_rate` decimal(5,2) DEFAULT '0.00' COMMENT '额外奖励比例(%)',
  `extra_amount` decimal(10,2) DEFAULT '0.00' COMMENT '额外奖励金额',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待结算 1-已冻结 2-已结算 3-已提现 4-已取消',
  `freeze_time` datetime DEFAULT NULL COMMENT '冻结时间',
  `settle_time` datetime DEFAULT NULL COMMENT '结算时间',
  `withdraw_id` bigint(20) DEFAULT NULL COMMENT '提现ID',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_commission_no` (`commission_no`,`deleted`) USING BTREE,
  KEY `idx_agent_id` (`agent_id`) USING BTREE,
  KEY `idx_member_id` (`member_id`) USING BTREE,
  KEY `idx_order_id` (`order_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_agent_status_time` (`agent_id`, `status`, `create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='佣金记录表';

-- 如果需要恢复测试数据
/*
INSERT INTO yt_dist_commission (
  commission_no, agent_id, member_id, order_id, order_no, order_item_id,
  goods_id, goods_name, goods_price, quantity, order_amount,
  commission_mode, commission_rate, commission_amount, extra_rate, extra_amount,
  status, freeze_time, settle_time, withdraw_id, remark,
  creator, create_time, updater, update_time, deleted, tenant_id
)
SELECT 
  CONCAT('COMM', DATE_FORMAT(create_time, '%Y%m%d'), LPAD(id, 8, '0')) as commission_no,
  agent_id,
  (SELECT member_id FROM yt_dist_agent WHERE id = t.agent_id) as member_id,
  order_id, order_no, NULL as order_item_id,
  goods_id, goods_name,
  goods_amount as goods_price,
  1 as quantity,
  order_amount,
  2 as commission_mode,  -- 默认按比例
  commission_rate, commission_amount,
  0.00 as extra_rate, 0.00 as extra_amount,
  CASE 
    WHEN status = 1 THEN 2  -- 已结算 -> 已结算
    WHEN status = 2 THEN 4  -- 已取消 -> 已取消
    WHEN status = 3 THEN 4  -- 已退款 -> 已取消
    ELSE status
  END as status,
  create_time as freeze_time,
  settle_time, NULL as withdraw_id, remark,
  creator, create_time, updater, update_time, deleted, tenant_id
FROM yt_dist_commission_temp t;
*/

-- 删除临时表
DROP TABLE IF EXISTS yt_dist_commission_temp;
```

### 3.3 提现记录表调整

```sql
-- 备份数据
CREATE TABLE yt_dist_withdraw_temp AS SELECT * FROM yt_dist_withdraw;

-- 删除原表
DROP TABLE yt_dist_withdraw;

-- 重新创建表
CREATE TABLE `yt_dist_withdraw` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '提现ID',
  `withdraw_no` varchar(64) NOT NULL COMMENT '提现单号',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `fee` decimal(10,2) DEFAULT '0.00' COMMENT '手续费',
  `real_amount` decimal(10,2) NOT NULL COMMENT '实际到账金额',
  `account_type` tinyint(4) NOT NULL COMMENT '账户类型：1-银行卡 2-支付宝 3-微信',
  `account_name` varchar(32) NOT NULL COMMENT '账户名',
  `account_no` varchar(64) NOT NULL COMMENT '账户号',
  `bank_name` varchar(50) DEFAULT NULL COMMENT '银行名称',
  `bank_branch` varchar(100) DEFAULT NULL COMMENT '开户支行',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待审核 1-审核通过 2-审核拒绝 3-打款中 4-打款成功 5-打款失败',
  `apply_time` datetime NOT NULL COMMENT '申请时间',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_user` varchar(64) DEFAULT NULL COMMENT '审核人',
  `audit_remark` varchar(200) DEFAULT NULL COMMENT '审核备注',
  `pay_time` datetime DEFAULT NULL COMMENT '打款时间',
  `pay_no` varchar(64) DEFAULT NULL COMMENT '三方支付单号',
  `fail_reason` varchar(200) DEFAULT NULL COMMENT '失败原因',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_withdraw_no` (`withdraw_no`,`deleted`) USING BTREE,
  KEY `idx_agent_id` (`agent_id`) USING BTREE,
  KEY `idx_member_id` (`member_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_apply_time` (`apply_time`) USING BTREE,
  KEY `idx_agent_status_time` (`agent_id`, `status`, `apply_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='提现记录表';

-- 如果需要恢复测试数据
/*
INSERT INTO yt_dist_withdraw (
  withdraw_no, agent_id, member_id, amount, fee, real_amount,
  account_type, account_name, account_no, bank_name, bank_branch,
  status, apply_time, audit_time, audit_user, audit_remark,
  pay_time, pay_no, fail_reason,
  creator, create_time, updater, update_time, deleted, tenant_id
)
SELECT 
  withdraw_no, agent_id,
  (SELECT member_id FROM yt_dist_agent WHERE id = t.agent_id) as member_id,
  amount, fee,
  actual_amount as real_amount,
  withdraw_type as account_type,
  account_name, account_no, bank_name, bank_branch,
  status, apply_time, audit_time,
  CONCAT('USER_', auditor_id) as audit_user,
  audit_remark, pay_time,
  pay_transaction_no as pay_no,
  pay_fail_reason as fail_reason,
  creator, create_time, updater, update_time, deleted, tenant_id
FROM yt_dist_withdraw_temp t;
*/

-- 删除临时表
DROP TABLE IF EXISTS yt_dist_withdraw_temp;
```

## 四、索引创建脚本

### 4.1 额外性能优化索引（表创建时已包含基础索引）

### 4.1 额外性能优化索引（基础索引已在表创建时添加）

```sql
-- 佣金记录表额外索引
ALTER TABLE yt_dist_commission ADD INDEX idx_order_goods (order_id, goods_id);
ALTER TABLE yt_dist_commission ADD INDEX idx_settle_time (settle_time);

-- 提现记录表额外索引
ALTER TABLE yt_dist_withdraw ADD INDEX idx_audit_time (audit_time);

-- 商品配置表索引（如果该表需要调整）
ALTER TABLE yt_dist_goods_config ADD INDEX idx_goods_enable (goods_id, enable_dist);
ALTER TABLE yt_dist_goods_config ADD INDEX idx_status_priority (status, priority);
ALTER TABLE yt_dist_goods_config ADD INDEX idx_time_range (start_time, end_time);

-- 等级表索引（如果该表需要调整）
ALTER TABLE yt_dist_level ADD INDEX idx_level_code (level_code);
ALTER TABLE yt_dist_level ADD INDEX idx_grade_status (level_grade, status);
ALTER TABLE yt_dist_level ADD INDEX idx_default_status (is_default, status);
```

## 五、数据验证脚本

### 5.1 数据一致性检查

```sql
-- 检查分销员数据一致性
SELECT 
  '分销员数据检查' as check_type,
  COUNT(*) as total_count,
  COUNT(CASE WHEN invite_code IS NOT NULL AND LENGTH(invite_code) = 6 THEN 1 END) as valid_invite_code,
  COUNT(CASE WHEN real_name IS NOT NULL THEN 1 END) as has_real_name,
  COUNT(CASE WHEN status IN (0,1,2,3) THEN 1 END) as valid_status
FROM yt_dist_agent;

-- 检查佣金数据一致性
SELECT 
  '佣金数据检查' as check_type,
  COUNT(*) as total_count,
  COUNT(CASE WHEN commission_no IS NOT NULL THEN 1 END) as has_commission_no,
  COUNT(CASE WHEN member_id IS NOT NULL THEN 1 END) as has_member_id,
  COUNT(CASE WHEN status IN (0,1,2,3,4) THEN 1 END) as valid_status
FROM yt_dist_commission;

-- 检查提现数据一致性
SELECT 
  '提现数据检查' as check_type,
  COUNT(*) as total_count,
  COUNT(CASE WHEN member_id IS NOT NULL THEN 1 END) as has_member_id,
  COUNT(CASE WHEN real_amount > 0 THEN 1 END) as valid_amount,
  COUNT(CASE WHEN status IN (0,1,2,3,4,5) THEN 1 END) as valid_status
FROM yt_dist_withdraw;
```

### 5.2 业务数据验证

```sql
-- 验证分销员统计数据
SELECT 
  a.id,
  a.invite_code,
  a.total_commission,
  COALESCE(SUM(c.commission_amount), 0) as calculated_commission,
  ABS(a.total_commission - COALESCE(SUM(c.commission_amount), 0)) as diff
FROM yt_dist_agent a
LEFT JOIN yt_dist_commission c ON a.id = c.agent_id AND c.status IN (2,3)
GROUP BY a.id
HAVING diff > 0.01
LIMIT 10;

-- 验证团队人数统计
SELECT 
  a.id,
  a.invite_code,
  a.team_member_count,
  (SELECT COUNT(*) FROM yt_dist_agent sub WHERE sub.ancestor_path LIKE CONCAT('%/', a.id, '/%') OR sub.parent_id = a.id) as calculated_team_count
FROM yt_dist_agent a
WHERE a.team_member_count != (SELECT COUNT(*) FROM yt_dist_agent sub WHERE sub.ancestor_path LIKE CONCAT('%/', a.id, '/%') OR sub.parent_id = a.id)
LIMIT 10;
```

## 六、回滚方案

### 6.1 回滚脚本（如果有备份）

```bash
#!/bin/bash
# 如果执行前有备份，可以直接恢复
mysql -u root -p yitong_dev < backup_distribution_$(date +%Y%m%d_%H%M%S).sql
```

### 6.2 重新执行原始建表脚本

```sql
-- 如果没有备份，可以重新执行原始的 distribution.sql 脚本
-- 然后重新导入测试数据
```

## 七、执行计划

### 7.1 简化执行步骤

| 步骤 | 操作 | 预计时间 | 说明 |
|-----|------|---------|------|
| 1 | 数据备份（可选） | 5分钟 | 开发环境可跳过 |
| 2 | 执行结构调整脚本 | 10分钟 | 直接重建表结构 |
| 3 | 创建额外索引 | 5分钟 | 性能优化 |
| 4 | 验证表结构 | 5分钟 | 确认调整成功 |
| 5 | 更新应用代码 | - | 同步更新DO类 |

### 7.2 执行检查清单

- [ ] 备份完成（如需要）
- [ ] 表结构调整成功
- [ ] 索引创建成功
- [ ] 表结构验证通过
- [ ] DO类代码更新完成
- [ ] 编译测试通过

---

**文档版本**: V1.0  
**最后更新**: 2025-07-22
