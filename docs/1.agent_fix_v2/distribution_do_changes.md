# 分销模块 DO 类与数据库表结构对齐修改记录

## 修改概述

根据 `sql/mysql/distribution.sql` 中的数据库表结构，对 `dal/dataobject` 目录下的 DO 类进行了全面对齐，确保 Java 实体类与数据库表字段完全一致。

## 修改详情

### 1. DistAgentDO (对应 yt_dist_agent 表)

#### 字段名称修改
- `agentCode` → `inviteCode` (邀请码)
- `agentName` → `realName` (真实姓名)
- `path` → `ancestorPath` (祖先路径)
- `level` → `teamDepth` (团队深度)
- `withdrawnAmount` → `withdrawnCommission` (已提现佣金)
- `directTeamCount` → `directMemberCount` (直推人数)
- `totalTeamCount` → `teamMemberCount` (团队人数)

#### 新增字段
- `totalSales` - 累计销售额
- `lastActiveTime` - 最后活跃时间
- `extraInfo` - 扩展信息

#### 删除字段
- `auditorId` - 审核人ID (数据库中无此字段)
- `remark` - 备注 (数据库中无此字段)

#### 状态值调整
- 状态 3 从 "审核拒绝" 改为 "已注销"

### 2. DistLevelDO (对应 yt_dist_level 表)

#### 字段修改
- 删除 `firstCommissionRate`, `secondCommissionRate`, `thirdCommissionRate`
- 新增 `commissionRate`, `extraCommissionRate`, `teamCommissionRate`
- 删除独立的升级条件字段，改为 JSON 格式的 `upgradeCondition`
- `privileges` → `levelBenefits` (等级权益说明)
- `icon` → `iconUrl` (等级图标)

#### 新增字段
- `bgColor` - 背景颜色

### 3. DistCommissionDO (对应 yt_dist_commission 表)

#### 新增字段
- `commissionNo` - 佣金单号
- `memberId` - 会员ID
- `orderItemId` - 订单项ID
- `goodsPrice` - 商品价格
- `quantity` - 数量
- `commissionMode` - 佣金模式
- `extraRate` - 额外奖励比例
- `extraAmount` - 额外奖励金额
- `freezeTime` - 冻结时间
- `withdrawId` - 提现ID

#### 删除字段
- `goodsAmount` - 商品金额
- `commissionBase` - 佣金基数
- `commissionType` - 佣金类型
- `commissionLevel` - 佣金层级
- `buyerUserId`, `buyerUserName` - 买家信息
- `sourceAgentId` - 来源分销员ID
- `settleBatchNo` - 结算批次号
- `unfreezeTime`, `refundTime`, `refundAmount` - 退款相关字段

#### 状态值调整
- 状态重新定义：0-待结算 1-已冻结 2-已结算 3-已提现 4-已取消

### 4. DistGoodsConfigDO (对应 yt_dist_goods_config 表)

#### 新增字段
- `goodsType` - 商品类型
- `commissionRate` - 佣金比例
- `commissionAmount` - 固定佣金金额
- `tieredConfig` - 阶梯配置(JSON)
- `levelConfig` - 等级差异配置(JSON)
- `distLimit` - 分销限制
- `limitConfig` - 限制配置(JSON)
- `totalCommission` - 累计佣金

#### 字段名称修改
- `distStartTime` → `startTime`
- `distEndTime` → `endTime`
- `distSoldCount` → `soldCount`

#### 删除字段
- `spuCode` - 商品SPU编码
- `firstCommissionRate/Amount`, `secondCommissionRate/Amount`, `thirdCommissionRate/Amount`
- `commissionBaseType` - 佣金基数类型
- `minDistPrice` - 最低分销价
- `limitBuyCount` - 限购数量
- `minLevelId` - 分销员等级限制

### 5. DistWithdrawDO (对应 yt_dist_withdraw 表)

#### 新增字段
- `memberId` - 会员ID

#### 字段名称修改
- `actualAmount` → `realAmount` (实际到账金额)
- `withdrawType` → `accountType` (账户类型)
- `auditorId` → `auditUser` (从ID改为用户名字符串)
- `payTransactionNo` → `payNo` (支付单号)
- `payFailReason` → `failReason` (失败原因)

#### 删除字段
- `payOperatorId` - 打款操作人ID
- `beforeBalance`, `afterBalance` - 余额变动
- `ipAddress` - IP地址

## 注意事项

1. **数据迁移**: 如果系统已有数据，需要编写数据迁移脚本处理字段名称变更
2. **业务逻辑**: 相关的 Service、Mapper 等业务逻辑代码需要同步更新
3. **API 影响**: 部分字段变更可能影响前端 API，需要同步更新 VO 类
4. **MyBatis XML**: 需要更新对应的 Mapper XML 文件中的字段映射

## 建议后续操作

1. 更新对应的 Mapper XML 文件
2. 修改 Service 层相关业务代码
3. 更新 VO 类确保 API 兼容性
4. 编写数据迁移脚本（如果需要）
5. 进行完整的单元测试和集成测试

## 更新时间

2025-07-22