# 分销商品配置佣金类型更新说明

## 更新日期：2025-08-06
## 作者：一筒科技

## 更新概要

本次更新主要对分销商品配置中的佣金类型（commissionType）功能进行了完善和优化，明确了两种佣金类型的处理逻辑：

1. **统一佣金（commissionType = 1）**：使用多级佣金字段（first/second/third）
2. **按等级差异化佣金（commissionType = 2）**：使用等级配置表（DistGoodsLevelConfig）

## 主要改动

### 1. 移除冗余字段
- 删除了 `enableLevelConfig` 字段，因其功能与 `commissionType` 重复

### 2. 统一佣金模式增强
当 `commissionType = 1` 时，系统支持多级佣金结构：
- **一级佣金**：`firstCommissionRate` 或 `firstCommissionAmount`（二选一）
- **二级佣金**：`secondCommissionRate` 或 `secondCommissionAmount`（二选一）
- **三级佣金**：`thirdCommissionRate` 或 `thirdCommissionAmount`（二选一）

### 3. 佣金类型切换处理
- **从统一佣金切换到按等级差异化**：自动为所有启用的等级创建默认配置
- **从按等级差异化切换到统一佣金**：删除所有等级配置记录

### 4. 验证规则
- 统一佣金模式下，每级佣金的比例和金额只能设置其中一个
- 按比例计算时，总佣金比例不能超过100%
- 统一佣金模式下必须至少设置一级佣金
- 统一佣金模式下不允许设置等级配置

## 数据库变更

### SQL迁移脚本
```sql
-- 删除 enable_level_config 列
ALTER TABLE `yt_dist_goods_config` 
DROP COLUMN `enable_level_config`;

-- 更新 commission_type 字段注释
ALTER TABLE `yt_dist_goods_config` 
MODIFY COLUMN `commission_type` INT NOT NULL DEFAULT 1 
COMMENT '佣金类型：1-统一佣金（所有等级相同），2-按等级差异化佣金';
```

## 错误码定义

新增了以下错误码：
- `1_035_004_018`：一级佣金比例和金额只能设置其中一个
- `1_035_004_019`：二级佣金比例和金额只能设置其中一个
- `1_035_004_020`：三级佣金比例和金额只能设置其中一个
- `1_035_004_021`：佣金比例总和不能超过100%
- `1_035_004_022`：统一佣金模式下必须设置一级佣金

## 使用示例

### 统一佣金配置示例
```json
{
  "commissionType": 1,
  "commissionMode": 1,  // 1-按比例, 2-固定金额
  "firstCommissionRate": 10.0,   // 一级佣金10%
  "secondCommissionRate": 5.0,   // 二级佣金5%
  "thirdCommissionRate": 2.5     // 三级佣金2.5%
}
```

### 按等级差异化佣金配置示例
```json
{
  "commissionType": 2,
  "commissionMode": 1,
  "levelConfigs": [
    {
      "levelId": "1",
      "levelName": "青铜",
      "commissionValue": 5.0
    },
    {
      "levelId": "2", 
      "levelName": "白银",
      "commissionValue": 10.0
    }
  ]
}
```

## 注意事项

1. 切换佣金类型时会影响现有的配置数据，请谨慎操作
2. 统一佣金模式下，commissionValue字段已废弃，但为了兼容性仍会设置为一级佣金值
3. 批量配置时同样支持多级佣金设置