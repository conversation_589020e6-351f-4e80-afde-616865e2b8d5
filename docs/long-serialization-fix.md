# Long 类型统一序列化为字符串的修改说明

## 问题描述

在接口 `/app/info/list-simple?status=1` 的响应中，Long 类型的字段序列化不一致：
- 小数字（如 1、2、3）以数字形式返回
- 大数字以字符串形式返回

这种不一致性会给前端处理带来困扰。

## 原因分析

项目使用了自定义的 `NumberSerializer`，其原始逻辑是：
- 当 Long 值在 JavaScript 安全整数范围内（-2^53+1 到 2^53-1）时，保持数字格式
- 超出范围时才转换为字符串

## 解决方案

修改 `NumberSerializer` 的序列化逻辑，将所有 Long 类型统一序列化为字符串。

### 修改文件
`yitong-framework/yitong-spring-boot-starter-web/src/main/java/com/yitong/octopus/framework/jackson/core/databind/NumberSerializer.java`

### 修改内容
```java
@Override
public void serialize(Number value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
    // 将所有 Long 类型统一序列化为字符串，确保前端处理的一致性
    gen.writeString(value.toString());
}
```

## 影响范围

此修改会影响整个项目中所有使用 Jackson 序列化的 Long 类型字段：
1. 所有 REST API 响应中的 Long 类型字段都会以字符串形式返回
2. 包括但不限于：ID、数量、金额等字段

## 前端适配建议

前端在处理接口响应时：
1. 统一将 ID 类字段作为字符串处理
2. 如果需要进行数值计算，使用 `parseInt()` 或 `parseFloat()` 转换
3. 注意处理可能的 null 值

## 示例

修改前的响应：
```json
{
  "id": 1,
  "name": "应用名称",
  "spId": [2, 3, 9007199254740993]
}
```

修改后的响应：
```json
{
  "id": "1",
  "name": "应用名称",
  "spId": ["2", "3", "9007199254740993"]
}
```

## 注意事项

1. 此修改是全局性的，会影响所有模块
2. 需要通知前端团队进行相应的适配
3. 建议在测试环境充分测试后再上线