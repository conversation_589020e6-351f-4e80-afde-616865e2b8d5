# 分销管理后台模块详细设计

## 一、分销商管理模块

### 1.1 分销商列表页面

#### 页面路径
`/distribution/agent/list`

#### 功能描述
展示所有分销商信息，支持查询、筛选、导出等操作。

#### 页面元素
```vue
<template>
  <div class="agent-list">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" inline>
      <el-form-item label="分销商名称">
        <el-input v-model="queryParams.name" />
      </el-form-item>
      <el-form-item label="手机号">
        <el-input v-model="queryParams.mobile" />
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="queryParams.status">
          <el-option label="全部" value="" />
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="等级">
        <el-select v-model="queryParams.levelId">
          <!-- 动态加载等级选项 -->
        </el-select>
      </el-form-item>
      <el-form-item label="标签">
        <el-select v-model="queryParams.tagIds" multiple>
          <!-- 动态加载标签选项 -->
        </el-select>
      </el-form-item>
      <el-form-item label="加入时间">
        <el-date-picker 
          v-model="queryParams.createTime"
          type="daterange"
          range-separator="至"
        />
      </el-form-item>
    </el-form>
    
    <!-- 操作按钮 -->
    <div class="table-operator">
      <el-button type="primary" @click="handleCreate">新增分销商</el-button>
      <el-button @click="handleExport">导出</el-button>
      <el-button @click="handleBatchStatus">批量启用/禁用</el-button>
      <el-button @click="handleBatchTag">批量设置标签</el-button>
    </div>
    
    <!-- 数据表格 -->
    <el-table :data="agentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="分销商名称" />
      <el-table-column prop="mobile" label="手机号" />
      <el-table-column prop="levelName" label="等级" />
      <el-table-column prop="tags" label="标签">
        <template #default="{ row }">
          <el-tag v-for="tag in row.tags" :key="tag.id">{{ tag.name }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="parentName" label="上级分销商" />
      <el-table-column prop="teamMemberCount" label="团队人数" />
      <el-table-column prop="totalCommission" label="累计佣金" />
      <el-table-column prop="status" label="状态">
        <template #default="{ row }">
          <el-switch v-model="row.status" @change="handleStatusChange(row)" />
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="加入时间" />
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button link @click="handleView(row)">查看</el-button>
          <el-button link @click="handleEdit(row)">编辑</el-button>
          <el-button link @click="handleAdjustLevel(row)">调整等级</el-button>
          <el-button link @click="handleAdjustParent(row)">调整上级</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <el-pagination 
      v-model:current-page="queryParams.pageNo"
      v-model:page-size="queryParams.pageSize"
      :total="total"
    />
  </div>
</template>
```

#### API调用
- `GET /distribution/agent/page` - 分页查询
- `PUT /distribution/agent/update-status` - 更新状态
- `PUT /distribution/agent/batch-update-status` - 批量更新状态
- `GET /distribution/agent/export-excel` - 导出Excel

### 1.2 分销商详情页面

#### 页面路径
`/distribution/agent/detail/:id`

#### 功能描述
展示分销商详细信息，包括基本信息、统计数据、团队成员、佣金记录等。

#### 页面布局
```vue
<template>
  <div class="agent-detail">
    <!-- 基本信息卡片 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>基本信息</span>
          <el-button type="primary" size="small" @click="handleEdit">编辑</el-button>
        </div>
      </template>
      <el-descriptions :column="3">
        <el-descriptions-item label="分销商ID">{{ agent.id }}</el-descriptions-item>
        <el-descriptions-item label="分销商名称">{{ agent.name }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ agent.mobile }}</el-descriptions-item>
        <el-descriptions-item label="等级">{{ agent.levelName }}</el-descriptions-item>
        <el-descriptions-item label="标签">
          <el-tag v-for="tag in agent.tags" :key="tag.id">{{ tag.name }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="agent.status ? 'success' : 'danger'">
            {{ agent.status ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="上级分销商">{{ agent.parentName }}</el-descriptions-item>
        <el-descriptions-item label="加入时间">{{ agent.createTime }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
    
    <!-- 统计数据 -->
    <el-row :gutter="20" class="statistics">
      <el-col :span="6">
        <el-statistic title="累计佣金" :value="statistics.totalCommission" />
      </el-col>
      <el-col :span="6">
        <el-statistic title="待结算佣金" :value="statistics.pendingCommission" />
      </el-col>
      <el-col :span="6">
        <el-statistic title="团队人数" :value="statistics.teamMemberCount" />
      </el-col>
      <el-col :span="6">
        <el-statistic title="累计订单" :value="statistics.totalOrders" />
      </el-col>
    </el-row>
    
    <!-- 标签页 -->
    <el-tabs v-model="activeTab">
      <el-tab-pane label="团队成员" name="team">
        <TeamMemberList :agentId="agentId" />
      </el-tab-pane>
      <el-tab-pane label="佣金记录" name="commission">
        <CommissionList :agentId="agentId" />
      </el-tab-pane>
      <el-tab-pane label="推广订单" name="orders">
        <TeamOrderList :agentId="agentId" />
      </el-tab-pane>
      <el-tab-pane label="关系链" name="relationship">
        <RelationshipTree :agentId="agentId" />
      </el-tab-pane>
      <el-tab-pane label="操作日志" name="logs">
        <OperationLogs :agentId="agentId" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
```

### 1.3 分销商审核页面

#### 页面路径
`/distribution/agent/apply`

#### 功能描述
审核分销商申请，支持单个审核和批量审核。

#### 页面设计
```vue
<template>
  <div class="agent-apply">
    <!-- 筛选条件 -->
    <el-form :model="queryParams" inline>
      <el-form-item label="申请状态">
        <el-select v-model="queryParams.auditStatus">
          <el-option label="待审核" value="0" />
          <el-option label="已通过" value="1" />
          <el-option label="已拒绝" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间">
        <el-date-picker v-model="queryParams.applyTime" type="daterange" />
      </el-form-item>
    </el-form>
    
    <!-- 批量操作 -->
    <div class="batch-actions">
      <el-button type="success" @click="handleBatchApprove">批量通过</el-button>
      <el-button type="danger" @click="handleBatchReject">批量拒绝</el-button>
    </div>
    
    <!-- 申请列表 -->
    <el-table :data="applyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="applyUserName" label="申请人" />
      <el-table-column prop="mobile" label="手机号" />
      <el-table-column prop="applyReason" label="申请理由" show-overflow-tooltip />
      <el-table-column prop="recommendName" label="推荐人" />
      <el-table-column prop="applyTime" label="申请时间" />
      <el-table-column prop="auditStatus" label="审核状态">
        <template #default="{ row }">
          <el-tag v-if="row.auditStatus === 0">待审核</el-tag>
          <el-tag v-else-if="row.auditStatus === 1" type="success">已通过</el-tag>
          <el-tag v-else type="danger">已拒绝</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="{ row }">
          <template v-if="row.auditStatus === 0">
            <el-button link type="success" @click="handleApprove(row)">通过</el-button>
            <el-button link type="danger" @click="handleReject(row)">拒绝</el-button>
          </template>
          <el-button link @click="handleViewDetail(row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
```

## 二、佣金管理模块

### 2.1 佣金记录页面

#### 页面路径
`/distribution/commission/list`

#### 功能描述
展示所有佣金记录，支持查询、筛选、结算等操作。

#### 主要功能
1. **佣金列表**
   - 分页展示佣金记录
   - 按状态筛选（待结算、已结算、已取消等）
   - 按时间范围查询
   - 按分销商查询

2. **佣金结算**
   - 单个结算
   - 批量结算
   - 结算确认

3. **佣金统计**
   - 佣金汇总数据
   - 佣金趋势图表

### 2.2 佣金统计页面

#### 页面路径
`/distribution/commission/statistics`

#### 功能描述
展示佣金相关的统计数据和图表。

#### 统计维度
1. **时间维度**
   - 日、周、月、年统计
   - 趋势分析

2. **分销商维度**
   - TOP分销商排行
   - 分销商佣金分布

3. **商品维度**
   - 高佣金商品排行
   - 商品贡献度分析

## 三、商品配置模块

### 3.1 分销商品管理页面

#### 页面路径
`/distribution/goods/config`

#### 功能描述
配置哪些商品可以参与分销，设置分销规则。

#### 主要功能
1. **商品列表**
   - 展示所有商品
   - 标记可分销商品
   - 批量启用/禁用分销

2. **分销配置**
   - 设置佣金比例
   - 设置分销层级
   - 特殊规则配置

## 四、等级管理模块

### 4.1 等级配置页面

#### 页面路径
`/distribution/level/config`

#### 功能描述
管理分销商等级体系，配置各等级权益。

#### 配置项
1. **等级基本信息**
   - 等级名称
   - 等级图标
   - 等级描述

2. **晋升条件**
   - 销售额要求
   - 团队人数要求
   - 其他条件

3. **等级权益**
   - 佣金比例
   - 特殊权限
   - 奖励配置

## 五、奖励方案模块

### 5.1 奖励方案管理页面

#### 页面路径
`/distribution/reward/scheme`

#### 功能描述
创建和管理各种奖励方案。

#### 方案类型
1. **固定奖励**
   - 按等级配置
   - 按标签配置

2. **阶梯奖励**
   - 销售额阶梯
   - 团队规模阶梯

3. **活动奖励**
   - 限时活动
   - 特殊任务

## 六、海报管理模块

### 6.1 海报模板管理页面

#### 页面路径
`/distribution/poster/template`

#### 功能描述
管理分销推广海报模板。

#### 主要功能
1. **模板管理**
   - 模板上传
   - 模板编辑
   - 模板预览

2. **海报生成**
   - 批量生成
   - 个性化配置

## 七、提现管理模块

### 7.1 提现申请管理页面

#### 页面路径
`/distribution/withdraw/apply`

#### 功能描述
处理分销商的提现申请。

#### 审核流程
1. **申请列表**
   - 待审核申请
   - 历史记录

2. **审核操作**
   - 审核通过
   - 审核拒绝
   - 转账确认

## 八、统计分析模块

### 8.1 数据总览页面

#### 页面路径
`/distribution/statistics/overview`

#### 功能描述
展示分销系统的整体运营数据。

#### 数据指标
1. **核心指标**
   - 分销商总数
   - 活跃分销商数
   - 累计佣金
   - 累计订单

2. **趋势分析**
   - 增长趋势
   - 转化分析

3. **实时监控**
   - 今日数据
   - 实时订单

### 8.2 数据分析页面

#### 页面路径
`/distribution/statistics/analysis`

#### 功能描述
深度分析分销业务数据。

#### 分析维度
1. **分销商分析**
   - 活跃度分析
   - 贡献度分析
   - 留存分析

2. **商品分析**
   - 热销商品
   - 转化率分析

3. **渠道分析**
   - 渠道效果
   - ROI分析

## 九、公共组件设计

### 9.1 分销商选择器
```vue
<DistAgentSelector 
  v-model="selectedAgentId"
  :multiple="false"
  :show-tree="true"
/>
```

### 9.2 等级选择器
```vue
<DistLevelSelect 
  v-model="levelId"
  :clearable="true"
/>
```

### 9.3 标签选择器
```vue
<DistTagSelect 
  v-model="tagIds"
  :multiple="true"
/>
```

### 9.4 佣金状态标签
```vue
<CommissionStatusTag :status="commission.status" />
```

### 9.5 分销商关系树
```vue
<AgentRelationshipTree 
  :agentId="agentId"
  :showStats="true"
/>
```

## 十、权限控制

### 权限点定义
```javascript
const permissions = {
  // 分销商管理
  'distribution:agent:query': '查询分销商',
  'distribution:agent:create': '创建分销商',
  'distribution:agent:update': '更新分销商',
  'distribution:agent:delete': '删除分销商',
  'distribution:agent:export': '导出分销商',
  'distribution:agent:audit': '审核分销商',
  
  // 佣金管理
  'distribution:commission:query': '查询佣金',
  'distribution:commission:settle': '结算佣金',
  'distribution:commission:cancel': '取消佣金',
  
  // 提现管理
  'distribution:withdraw:query': '查询提现',
  'distribution:withdraw:audit': '审核提现',
  
  // 配置管理
  'distribution:config:query': '查询配置',
  'distribution:config:update': '更新配置',
}
```

### 菜单权限配置
```javascript
const menus = [
  {
    path: '/distribution',
    name: '分销管理',
    icon: 'Share',
    children: [
      {
        path: '/distribution/agent',
        name: '分销商管理',
        permission: 'distribution:agent:query'
      },
      {
        path: '/distribution/commission',
        name: '佣金管理',
        permission: 'distribution:commission:query'
      },
      // 更多菜单...
    ]
  }
]
```