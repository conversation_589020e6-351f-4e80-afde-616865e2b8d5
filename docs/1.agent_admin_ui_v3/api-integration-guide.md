# 分销管理后台API集成指南

## 一、API基础配置

### 1.1 Axios配置
```typescript
// src/utils/request.ts
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/modules/user'

const service = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  timeout: 30000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers['Authorization'] = `Bearer ${userStore.token}`
    }
    // 添加租户ID
    if (userStore.tenantId) {
      config.headers['tenant-id'] = userStore.tenantId
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    if (res.code !== 0) {
      ElMessage({
        message: res.msg || 'Error',
        type: 'error',
        duration: 5 * 1000
      })
      return Promise.reject(new Error(res.msg || 'Error'))
    }
    return res
  },
  error => {
    ElMessage({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
```

### 1.2 API响应类型定义
```typescript
// src/types/common.ts
export interface CommonResult<T = any> {
  code: number
  data: T
  msg: string
}

export interface PageResult<T> {
  list: T[]
  total: number
}

export interface PageParam {
  pageNo: number
  pageSize: number
}
```

## 二、分销商管理API

### 2.1 API接口定义
```typescript
// src/api/distribution/agent.ts
import request from '@/utils/request'
import type { 
  DistAgentPageReqVO,
  DistAgentRespVO,
  DistAgentCreateReqVO,
  DistAgentUpdateReqVO,
  DistAgentDetailRespVO,
  DistAgentStatisticsRespVO
} from '@/types/distribution/agent'

export const agentApi = {
  // 分页查询分销商
  getAgentPage(params: DistAgentPageReqVO) {
    return request.get<PageResult<DistAgentRespVO>>('/distribution/agent/page', { params })
  },

  // 获取分销商详情
  getAgentDetail(id: number) {
    return request.get<DistAgentDetailRespVO>(`/distribution/agent/get?id=${id}`)
  },

  // 创建分销商
  createAgent(data: DistAgentCreateReqVO) {
    return request.post<number>('/distribution/agent/create', data)
  },

  // 更新分销商
  updateAgent(data: DistAgentUpdateReqVO) {
    return request.put('/distribution/agent/update', data)
  },

  // 删除分销商
  deleteAgent(id: number) {
    return request.delete(`/distribution/agent/delete?id=${id}`)
  },

  // 更新分销商状态
  updateAgentStatus(data: { id: number; status: number }) {
    return request.put('/distribution/agent/update-status', data)
  },

  // 批量更新状态
  batchUpdateStatus(data: { ids: number[]; status: number }) {
    return request.put('/distribution/agent/batch-update-status', data)
  },

  // 调整分销商等级
  adjustAgentLevel(data: { id: number; levelId: number }) {
    return request.put('/distribution/agent/adjust-level', data)
  },

  // 调整上级分销商
  adjustAgentParent(data: { id: number; parentId: number }) {
    return request.put('/distribution/agent/adjust-parent', data)
  },

  // 更新分销商标签
  updateAgentTags(data: { id: number; tagIds: number[] }) {
    return request.put('/distribution/agent/update-tags', data)
  },

  // 获取分销商统计数据
  getAgentStatistics(id: number) {
    return request.get<DistAgentStatisticsRespVO>(`/distribution/agent/statistics?id=${id}`)
  },

  // 获取团队成员
  getTeamMembers(params: { agentId: number } & PageParam) {
    return request.get<PageResult<DistAgentTeamMemberRespVO>>('/distribution/agent/team-members', { params })
  },

  // 获取团队订单
  getTeamOrders(params: { agentId: number } & PageParam) {
    return request.get<PageResult<any>>('/distribution/agent/team-orders', { params })
  },

  // 获取分销商关系树
  getAgentRelationship(id: number) {
    return request.get<DistAgentRelationshipRespVO>(`/distribution/agent/relationship?id=${id}`)
  },

  // 获取下级分销商树
  getChildrenTree(id: number) {
    return request.get<DistAgentChildrenTreeRespVO[]>(`/distribution/agent/children-tree?id=${id}`)
  },

  // 导出分销商
  exportAgents(params: DistAgentExportReqVO) {
    return request.download('/distribution/agent/export-excel', params)
  }
}
```

### 2.2 类型定义
```typescript
// src/types/distribution/agent.ts
export interface DistAgentPageReqVO extends PageParam {
  name?: string
  mobile?: string
  status?: number
  levelId?: number
  tagIds?: number[]
  parentId?: number
  createTime?: [string, string]
}

export interface DistAgentRespVO {
  id: number
  userId: number
  name: string
  mobile: string
  avatar: string
  levelId: number
  levelName: string
  status: number
  parentId: number
  parentName: string
  tags: DistAgentTagSimpleRespVO[]
  teamMemberCount: number
  totalCommission: number
  createTime: string
}

export interface DistAgentCreateReqVO {
  userId: number
  levelId: number
  parentId?: number
  tagIds?: number[]
}

export interface DistAgentUpdateReqVO {
  id: number
  levelId?: number
  parentId?: number
  tagIds?: number[]
  status?: number
}

export interface DistAgentDetailRespVO extends DistAgentRespVO {
  totalOrders: number
  monthOrders: number
  monthCommission: number
  pendingCommission: number
  withdrawnAmount: number
  path: string
  depth: number
}
```

### 2.3 分销商申请API
```typescript
// src/api/distribution/agent-apply.ts
export const agentApplyApi = {
  // 获取申请列表
  getApplyPage(params: DistAgentApplyPageReqVO) {
    return request.get<PageResult<DistAgentApplyRespVO>>('/distribution/agent/apply-page', { params })
  },

  // 审核申请
  auditApply(data: DistAgentAuditReqVO) {
    return request.put('/distribution/agent/audit', data)
  },

  // 批量审核
  batchAudit(data: DistAgentBatchAuditReqVO) {
    return request.put('/distribution/agent/batch-audit', data)
  }
}
```

## 三、佣金管理API

### 3.1 佣金记录API
```typescript
// src/api/distribution/commission.ts
export const commissionApi = {
  // 分页查询佣金记录
  getCommissionPage(params: DistCommissionPageReqVO) {
    return request.get<PageResult<DistCommissionRespVO>>('/distribution/commission/page', { params })
  },

  // 待结算佣金列表
  getPendingCommissions(params: DistCommissionPendingPageReqVO) {
    return request.get<PageResult<DistCommissionRespVO>>('/distribution/commission/pending-page', { params })
  },

  // 结算佣金
  settleCommission(data: DistCommissionSettleReqVO) {
    return request.put('/distribution/commission/settle', data)
  },

  // 取消佣金
  cancelCommission(data: DistCommissionCancelReqVO) {
    return request.put('/distribution/commission/cancel', data)
  },

  // 解冻佣金
  unfreezeCommission(data: DistCommissionUnfreezeReqVO) {
    return request.put('/distribution/commission/unfreeze', data)
  },

  // 批量解冻
  batchUnfreeze(data: DistCommissionBatchUnfreezeReqVO) {
    return request.put('/distribution/commission/batch-unfreeze', data)
  },

  // 退款处理
  refundCommission(data: DistCommissionRefundReqVO) {
    return request.put('/distribution/commission/refund', data)
  },

  // 佣金统计
  getCommissionStatistics(params: DistCommissionStatisticsReqVO) {
    return request.get<DistCommissionStatisticsRespVO>('/distribution/commission/statistics', { params })
  },

  // 佣金汇总
  getCommissionSummary(params: DistCommissionSummaryReqVO) {
    return request.get<DistCommissionSummaryVO>('/distribution/commission/summary', { params })
  },

  // 佣金排行榜
  getCommissionRanking(params: { period: string; limit: number }) {
    return request.get<DistCommissionRankingVO[]>('/distribution/commission/ranking', { params })
  },

  // 导出佣金记录
  exportCommissions(params: DistCommissionExportReqVO) {
    return request.download('/distribution/commission/export-excel', params)
  }
}
```

## 四、商品配置API

### 4.1 商品分销配置API
```typescript
// src/api/distribution/goods-config.ts
export const goodsConfigApi = {
  // 分页查询商品配置
  getGoodsConfigPage(params: DistGoodsConfigPageReqVO) {
    return request.get<PageResult<DistGoodsConfigRespVO>>('/distribution/goods-config/page', { params })
  },

  // 获取可分销商品
  getDistributableGoods(params: DistGoodsConfigDistributablePageReqVO) {
    return request.get<PageResult<any>>('/distribution/goods-config/distributable-page', { params })
  },

  // 保存商品配置
  saveGoodsConfig(data: DistGoodsConfigSaveReqVO) {
    return request.post('/distribution/goods-config/save', data)
  },

  // 批量保存配置
  batchSaveConfig(data: DistGoodsConfigBatchSaveReqVO) {
    return request.post('/distribution/goods-config/batch-save', data)
  },

  // 批量更新状态
  batchUpdateStatus(data: DistGoodsConfigBatchUpdateStatusReqVO) {
    return request.put('/distribution/goods-config/batch-update-status', data)
  }
}
```

## 五、等级管理API

### 5.1 等级配置API
```typescript
// src/api/distribution/level.ts
export const levelApi = {
  // 分页查询等级
  getLevelPage(params: DistLevelPageReqVO) {
    return request.get<PageResult<DistLevelRespVO>>('/distribution/level/page', { params })
  },

  // 获取等级详情
  getLevelDetail(id: number) {
    return request.get<DistLevelRespVO>(`/distribution/level/get?id=${id}`)
  },

  // 创建等级
  createLevel(data: DistLevelSaveReqVO) {
    return request.post<number>('/distribution/level/create', data)
  },

  // 更新等级
  updateLevel(data: DistLevelSaveReqVO) {
    return request.put('/distribution/level/update', data)
  },

  // 删除等级
  deleteLevel(id: number) {
    return request.delete(`/distribution/level/delete?id=${id}`)
  },

  // 获取等级简单列表
  getLevelSimpleList() {
    return request.get<DistLevelSimpleRespVO[]>('/distribution/level/simple-list')
  }
}
```

## 六、奖励方案API

### 6.1 奖励方案API
```typescript
// src/api/distribution/reward.ts
export const rewardSchemeApi = {
  // 分页查询奖励方案
  getSchemePage(params: DistRewardSchemePageReqVO) {
    return request.get<PageResult<DistRewardSchemeRespVO>>('/distribution/reward-scheme/page', { params })
  },

  // 创建奖励方案
  createScheme(data: DistRewardSchemeCreateReqVO) {
    return request.post<number>('/distribution/reward-scheme/create', data)
  },

  // 更新奖励方案
  updateScheme(data: DistRewardSchemeUpdateReqVO) {
    return request.put('/distribution/reward-scheme/update', data)
  },

  // 更新方案状态
  updateSchemeStatus(data: DistRewardSchemeStatusReqVO) {
    return request.put('/distribution/reward-scheme/update-status', data)
  },

  // 删除方案
  deleteScheme(id: number) {
    return request.delete(`/distribution/reward-scheme/delete?id=${id}`)
  },

  // 获取简单列表
  getSchemeSimpleList() {
    return request.get<DistRewardSchemeSimpleRespVO[]>('/distribution/reward-scheme/simple-list')
  }
}

// 等级奖励配置
export const rewardLevelConfigApi = {
  // 分页查询
  getLevelConfigPage(params: DistRewardLevelConfigPageReqVO) {
    return request.get<PageResult<DistRewardLevelConfigRespVO>>('/distribution/reward-level-config/page', { params })
  },

  // 保存配置
  saveLevelConfig(data: DistRewardLevelConfigSaveReqVO) {
    return request.post('/distribution/reward-level-config/save', data)
  },

  // 批量保存
  batchSaveLevelConfig(data: DistRewardLevelConfigBatchSaveReqVO) {
    return request.post('/distribution/reward-level-config/batch-save', data)
  }
}

// 标签奖励配置
export const rewardTagConfigApi = {
  // 分页查询
  getTagConfigPage(params: DistRewardTagConfigPageReqVO) {
    return request.get<PageResult<DistRewardTagConfigRespVO>>('/distribution/reward-tag-config/page', { params })
  },

  // 保存配置
  saveTagConfig(data: DistRewardTagConfigSaveReqVO) {
    return request.post('/distribution/reward-tag-config/save', data)
  },

  // 批量保存
  batchSaveTagConfig(data: DistRewardTagConfigBatchSaveReqVO) {
    return request.post('/distribution/reward-tag-config/batch-save', data)
  }
}
```

## 七、海报管理API

### 7.1 海报模板API
```typescript
// src/api/distribution/poster.ts
export const posterApi = {
  // 分页查询海报模板
  getPosterTemplatePage(params: DistPosterTemplatePageReqVO) {
    return request.get<PageResult<DistPosterTemplateRespVO>>('/distribution/poster-template/page', { params })
  },

  // 创建模板
  createTemplate(data: DistPosterTemplateSaveReqVO) {
    return request.post<number>('/distribution/poster-template/create', data)
  },

  // 更新模板
  updateTemplate(data: DistPosterTemplateSaveReqVO) {
    return request.put('/distribution/poster-template/update', data)
  },

  // 删除模板
  deleteTemplate(id: number) {
    return request.delete(`/distribution/poster-template/delete?id=${id}`)
  }
}

// 分销商海报
export const agentPosterApi = {
  // 生成海报
  generatePoster(data: DistAgentPosterGenerateReqVO) {
    return request.post<string>('/distribution/agent-poster/generate', data)
  },

  // 分页查询海报记录
  getPosterPage(params: DistAgentPosterPageReqVO) {
    return request.get<PageResult<DistAgentPosterRespVO>>('/distribution/agent-poster/page', { params })
  },

  // 获取海报统计
  getPosterStatistics(agentId: number) {
    return request.get<DistAgentPosterStatisticsRespVO>(`/distribution/agent-poster/statistics?agentId=${agentId}`)
  }
}
```

## 八、提现管理API

### 8.1 提现申请API
```typescript
// src/api/distribution/withdraw.ts
export const withdrawApi = {
  // 分页查询提现记录
  getWithdrawPage(params: DistWithdrawPageReqVO) {
    return request.get<PageResult<DistWithdrawRespVO>>('/distribution/withdraw/page', { params })
  },

  // 审核提现
  auditWithdraw(data: DistWithdrawAuditReqVO) {
    return request.put('/distribution/withdraw/audit', data)
  },

  // 批量审核
  batchAudit(data: DistWithdrawBatchAuditReqVO) {
    return request.put('/distribution/withdraw/batch-audit', data)
  },

  // 确认转账
  confirmTransfer(data: DistWithdrawTransferReqVO) {
    return request.put('/distribution/withdraw/transfer', data)
  },

  // 取消提现
  cancelWithdraw(data: DistWithdrawCancelReqVO) {
    return request.put('/distribution/withdraw/cancel', data)
  },

  // 获取提现统计
  getWithdrawStats(params: DistWithdrawStatsReqVO) {
    return request.get<DistWithdrawStatsRespVO>('/distribution/withdraw/stats', { params })
  }
}
```

## 九、统计分析API

### 9.1 统计数据API
```typescript
// src/api/distribution/statistics.ts
export const statisticsApi = {
  // 获取总览统计
  getOverviewStats() {
    return request.get<DistOverviewStatisticsRespVO>('/distribution/statistics/overview')
  },

  // 获取趋势统计
  getTrendStats(params: DistTrendStatisticsReqVO) {
    return request.get<DistTrendStatisticsRespVO>('/distribution/statistics/trend', { params })
  },

  // 获取排行统计
  getRankingStats(params: DistRankingStatisticsReqVO) {
    return request.get<DistRankingStatisticsRespVO[]>('/distribution/statistics/ranking', { params })
  },

  // 分销商分析
  getAgentAnalysis(params: DistAgentAnalysisReqVO) {
    return request.get<DistAgentAnalysisRespVO>('/distribution/statistics/agent-analysis', { params })
  },

  // 佣金分析
  getCommissionAnalysis(params: DistCommissionAnalysisReqVO) {
    return request.get<DistCommissionAnalysisRespVO>('/distribution/statistics/commission-analysis', { params })
  },

  // 商品分析
  getProductAnalysis(params: DistProductAnalysisReqVO) {
    return request.get<DistProductAnalysisRespVO>('/distribution/statistics/product-analysis', { params })
  },

  // 等级统计
  getLevelStats() {
    return request.get<DistLevelStatisticsRespVO[]>('/distribution/statistics/level')
  }
}
```

## 十、API调用示例

### 10.1 在组件中使用API
```vue
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { agentApi } from '@/api/distribution/agent'
import type { DistAgentPageReqVO, DistAgentRespVO } from '@/types/distribution/agent'

// 查询参数
const queryParams = ref<DistAgentPageReqVO>({
  pageNo: 1,
  pageSize: 10,
  status: undefined,
  levelId: undefined
})

// 列表数据
const agentList = ref<DistAgentRespVO[]>([])
const total = ref(0)
const loading = ref(false)

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const { data } = await agentApi.getAgentPage(queryParams.value)
    agentList.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 更新状态
const handleStatusChange = async (row: DistAgentRespVO) => {
  try {
    await agentApi.updateAgentStatus({
      id: row.id,
      status: row.status
    })
    ElMessage.success('状态更新成功')
  } catch (error) {
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1
  }
}

onMounted(() => {
  getList()
})
</script>
```

### 10.2 Pinia Store中使用
```typescript
// src/store/modules/distribution.ts
import { defineStore } from 'pinia'
import { levelApi } from '@/api/distribution/level'
import type { DistLevelSimpleRespVO } from '@/types/distribution/level'

export const useDistributionStore = defineStore('distribution', {
  state: () => ({
    levels: [] as DistLevelSimpleRespVO[],
    tags: [] as any[]
  }),

  actions: {
    // 获取等级列表
    async fetchLevels() {
      const { data } = await levelApi.getLevelSimpleList()
      this.levels = data
    },

    // 获取标签列表
    async fetchTags() {
      // 调用标签API
    }
  }
})
```

### 10.3 错误处理
```typescript
// 统一错误处理
const handleApiError = (error: any) => {
  if (error.response) {
    switch (error.response.status) {
      case 401:
        ElMessage.error('登录已过期，请重新登录')
        // 跳转登录页
        break
      case 403:
        ElMessage.error('没有权限执行此操作')
        break
      case 404:
        ElMessage.error('请求的资源不存在')
        break
      case 500:
        ElMessage.error('服务器错误，请稍后重试')
        break
      default:
        ElMessage.error(error.response.data.msg || '请求失败')
    }
  } else {
    ElMessage.error('网络错误，请检查网络连接')
  }
}
```

## 十一、文件上传处理

### 11.1 上传组件封装
```vue
<!-- src/components/UploadFile.vue -->
<template>
  <el-upload
    :action="uploadUrl"
    :headers="uploadHeaders"
    :before-upload="beforeUpload"
    :on-success="handleSuccess"
    :on-error="handleError"
  >
    <el-button type="primary">点击上传</el-button>
  </el-upload>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useUserStore } from '@/store/modules/user'

const userStore = useUserStore()

const uploadUrl = computed(() => `${import.meta.env.VITE_API_URL}/infra/file/upload`)
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${userStore.token}`
}))

const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB')
    return false
  }
  return true
}

const handleSuccess = (response: any) => {
  if (response.code === 0) {
    emit('success', response.data)
  } else {
    ElMessage.error(response.msg)
  }
}

const handleError = () => {
  ElMessage.error('上传失败')
}
</script>
```

## 十二、WebSocket集成

### 12.1 实时消息推送
```typescript
// src/utils/websocket.ts
import { useUserStore } from '@/store/modules/user'

class WebSocketClient {
  private ws: WebSocket | null = null
  private reconnectTimer: number | null = null
  private heartbeatTimer: number | null = null

  connect() {
    const userStore = useUserStore()
    const wsUrl = `${import.meta.env.VITE_WS_URL}?token=${userStore.token}`

    this.ws = new WebSocket(wsUrl)

    this.ws.onopen = () => {
      console.log('WebSocket connected')
      this.startHeartbeat()
    }

    this.ws.onmessage = (event) => {
      const message = JSON.parse(event.data)
      this.handleMessage(message)
    }

    this.ws.onclose = () => {
      this.reconnect()
    }
  }

  private handleMessage(message: any) {
    switch (message.type) {
      case 'commission_update':
        // 处理佣金更新通知
        break
      case 'new_agent_apply':
        // 处理新的分销商申请
        break
      // 更多消息类型...
    }
  }

  private startHeartbeat() {
    this.heartbeatTimer = window.setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({ type: 'heartbeat' }))
      }
    }, 30000)
  }

  private reconnect() {
    if (this.reconnectTimer) return
    
    this.reconnectTimer = window.setTimeout(() => {
      this.connect()
      this.reconnectTimer = null
    }, 5000)
  }

  disconnect() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
    }
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }
    if (this.ws) {
      this.ws.close()
    }
  }
}

export const wsClient = new WebSocketClient()
```

## 十三、最佳实践

### 13.1 API模块化组织
```
src/api/
├── distribution/
│   ├── index.ts          # 统一导出
│   ├── agent.ts          # 分销商相关
│   ├── commission.ts     # 佣金相关
│   ├── goods-config.ts   # 商品配置
│   ├── level.ts          # 等级管理
│   ├── reward.ts         # 奖励方案
│   ├── poster.ts         # 海报管理
│   ├── withdraw.ts       # 提现管理
│   └── statistics.ts     # 统计分析
```

### 13.2 类型安全
- 为所有API请求和响应定义TypeScript类型
- 使用泛型确保类型安全
- 避免使用any类型

### 13.3 错误处理
- 在API层统一处理错误
- 提供友好的错误提示
- 记录错误日志

### 13.4 性能优化
- 使用防抖/节流处理频繁的API调用
- 实现请求缓存机制
- 合理使用分页加载

### 13.5 安全考虑
- 所有请求携带认证token
- 敏感操作需要二次确认
- 防止CSRF攻击