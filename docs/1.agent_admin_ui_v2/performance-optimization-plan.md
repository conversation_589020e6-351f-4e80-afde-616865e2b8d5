# 分销模块性能优化方案

## 一、性能瓶颈分析

### 1.1 高频查询场景
1. **分销员信息查询**
   - 场景：每次请求都需要查询分销员基本信息
   - 频率：极高
   - 优化点：使用Redis缓存

2. **团队成员查询**
   - 场景：查询下级分销员列表、团队统计
   - 问题：递归查询、路径匹配性能差
   - 优化点：路径索引、分页优化

3. **佣金统计查询**
   - 场景：实时统计、排行榜
   - 问题：聚合查询耗时
   - 优化点：定时统计、缓存结果

4. **等级/标签配置查询**
   - 场景：计算佣金时需要查询配置
   - 频率：高
   - 优化点：本地缓存+Redis缓存

### 1.2 写入性能问题
1. **团队人数更新**
   - 问题：级联更新多条记录
   - 优化点：异步更新、批量更新

2. **佣金账单生成**
   - 问题：大量订单同时结算
   - 优化点：批量插入、异步处理

## 二、缓存策略设计

### 2.1 缓存层级
```
浏览器缓存(HTTP Cache)
    ↓
CDN缓存(静态资源)
    ↓
本地缓存(Caffeine)
    ↓
分布式缓存(Redis)
    ↓
数据库(MySQL)
```

### 2.2 缓存方案

#### 1. 分销员信息缓存
- **缓存key**: `dist:agent:{agentId}`
- **缓存时间**: 2小时
- **更新策略**: 更新时删除缓存
- **数据结构**: Hash

#### 2. 等级配置缓存
- **缓存key**: `dist:level:all`
- **缓存时间**: 24小时
- **更新策略**: 更新时刷新全量缓存
- **数据结构**: String(JSON)

#### 3. 奖励方案缓存
- **缓存key**: `dist:scheme:active:{type}`
- **缓存时间**: 1小时
- **更新策略**: 更新时删除相关缓存
- **数据结构**: String(JSON)

#### 4. 统计数据缓存
- **缓存key**: `dist:stats:{type}:{date}`
- **缓存时间**: 5分钟(今日数据)，24小时(历史数据)
- **更新策略**: 定时刷新
- **数据结构**: String(JSON)

## 三、数据库优化

### 3.1 索引优化
```sql
-- 分销员表索引
ALTER TABLE dist_agent ADD INDEX idx_member_id (member_id);
ALTER TABLE dist_agent ADD INDEX idx_parent_id (parent_id);
ALTER TABLE dist_agent ADD INDEX idx_invite_code (invite_code);
ALTER TABLE dist_agent ADD INDEX idx_status_level (status, level_id);
ALTER TABLE dist_agent ADD INDEX idx_ancestor_path (ancestor_path);

-- 佣金表索引
ALTER TABLE dist_commission ADD INDEX idx_agent_status_time (agent_id, status, create_time);
ALTER TABLE dist_commission ADD INDEX idx_order_no (order_no);

-- 提现表索引
ALTER TABLE dist_withdraw ADD INDEX idx_agent_status_time (agent_id, status, apply_time);

-- 商品配置表索引
ALTER TABLE dist_goods_config ADD INDEX idx_goods_id (goods_id);
ALTER TABLE dist_goods_config ADD INDEX idx_enable_status (enable_dist, status);
```

### 3.2 SQL优化示例

#### 优化前：团队成员查询
```sql
-- 效率低下的LIKE查询
SELECT * FROM dist_agent 
WHERE ancestor_path LIKE '1/2/3%'
```

#### 优化后：使用路径字段
```sql
-- 添加路径深度和ID数组字段
ALTER TABLE dist_agent 
ADD COLUMN path_ids JSON COMMENT '路径ID数组',
ADD COLUMN path_depth INT COMMENT '路径深度';

-- 使用JSON函数查询
SELECT * FROM dist_agent 
WHERE JSON_CONTAINS(path_ids, '3')
AND path_depth > 3;
```

## 四、代码层面优化

### 4.1 批量操作优化
```java
// 优化前：循环单条插入
for (CommissionDO commission : commissions) {
    commissionMapper.insert(commission);
}

// 优化后：批量插入
commissionService.saveBatch(commissions, 1000);
```

### 4.2 异步处理
```java
// 团队人数更新改为异步
@Async
public void updateTeamCountAsync(Long agentId) {
    // 异步更新逻辑
}
```

### 4.3 查询优化
```java
// 使用投影查询减少数据传输
@Query("SELECT new DistAgentStatVO(a.id, a.name, a.totalCommission) FROM DistAgent a")
List<DistAgentStatVO> getAgentStats();
```

## 五、实施计划

### 第一阶段：缓存实施（1-2天）
1. 集成Redis配置
2. 实现分销员信息缓存
3. 实现配置数据缓存
4. 实现统计数据缓存

### 第二阶段：数据库优化（1天）
1. 添加必要索引
2. 优化慢查询SQL
3. 实施读写分离（如需要）

### 第三阶段：代码优化（1-2天）
1. 实现批量操作
2. 异步处理实现
3. 查询优化

### 第四阶段：监控和调优（持续）
1. 添加性能监控
2. 慢查询分析
3. 缓存命中率监控

## 六、预期效果

1. **查询性能提升**
   - 分销员信息查询：从50ms降至5ms（90%缓存命中）
   - 配置查询：从30ms降至2ms（95%缓存命中）
   - 统计查询：从500ms降至50ms（90%缓存命中）

2. **写入性能提升**
   - 批量佣金生成：从10s降至2s（1000条）
   - 团队人数更新：异步处理，不阻塞主流程

3. **系统容量提升**
   - QPS从1000提升至5000
   - 并发用户数从500提升至2000

## 七、注意事项

1. **缓存一致性**
   - 使用Cache Aside模式
   - 关键数据采用主动更新策略

2. **缓存雪崩防护**
   - 设置随机过期时间
   - 使用缓存预热

3. **缓存穿透防护**
   - 布隆过滤器
   - 空值缓存

4. **监控告警**
   - Redis连接池监控
   - 缓存命中率告警
   - 慢查询告警