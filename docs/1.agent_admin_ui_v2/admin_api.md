# 分销模块 (Distribution Module) API 接口文档

本文档列出分销模块所有的 API 接口，包含接口名称、HTTP 方法、URL 路径，方便后端开发人员排查和对照实现。

## 1. 分销员管理 (Agent Management)

### 分销等级管理
| 接口名称 | HTTP方法 | URL | 说明 |
|---------|----------|-----|------|
| `getDistLevelList` | GET | `/distribution/level/list` | 查询分销等级列表 |
| `getDistLevel` | GET | `/distribution/level/get?id=${id}` | 查询分销等级详情 |
| `createDistLevel` | POST | `/distribution/level/create` | 创建分销等级 |
| `updateDistLevel` | PUT | `/distribution/level/update` | 更新分销等级 |
| `deleteDistLevel` | DELETE | `/distribution/level/delete?id=${id}` | 删除分销等级 |
| `getSimpleDistLevelList` | GET | `/distribution/level/list-simple` | 查询简单分销等级列表 |

### 分销员标签管理
| 接口名称 | HTTP方法 | URL | 说明 |
|---------|----------|-----|------|
| `getDistAgentTagList` | GET | `/distribution/agent-tag/list` | 查询分销员标签列表 |
| `getDistAgentTag` | GET | `/distribution/agent-tag/get?id=${id}` | 查询分销员标签详情 |
| `createDistAgentTag` | POST | `/distribution/agent-tag/create` | 创建分销员标签 |
| `updateDistAgentTag` | PUT | `/distribution/agent-tag/update` | 更新分销员标签 |
| `deleteDistAgentTag` | DELETE | `/distribution/agent-tag/delete?id=${id}` | 删除分销员标签 |
| `getSimpleDistAgentTagList` | GET | `/distribution/agent-tag/list-simple` | 查询简单分销员标签列表 |

### 分销员管理
| 接口名称 | HTTP方法 | URL | 说明 |
|---------|----------|-----|------|
| `getDistAgentList` | GET | `/distribution/agent/page` | 分页查询分销员列表 |
| `getDistAgent` | GET | `/distribution/agent/get?id=${id}` | 查询分销员详情 |
| `createDistAgent` | POST | `/distribution/agent/create` | 创建分销员 |
| `updateDistAgent` | PUT | `/distribution/agent/update` | 更新分销员信息 |
| `deleteDistAgent` | DELETE | `/distribution/agent/delete?id=${id}` | 删除分销员 |
| `updateDistAgentStatus` | PUT | `/distribution/agent/update-status` | 更新分销员状态 |
| `auditDistAgent` | PUT | `/distribution/agent/audit` | 审核分销员申请 |
| `updateDistAgentTags` | PUT | `/distribution/agent/update-tags` | 更新分销员标签 |
| `getDistAgentStatistics` | GET | `/distribution/agent/statistics?id=${id}` | 查询分销员统计数据 |
| `getDistAgentTeamList` | GET | `/distribution/agent/team-list?parentId=${parentId}` | 查询团队成员列表 |
| `exportDistAgent` | DOWNLOAD | `/distribution/agent/export` | 导出分销员数据 |
| `batchAuditDistAgent` | PUT | `/distribution/agent/batch-audit` | 批量审核分销员申请 |
| `adjustDistAgentLevel` | PUT | `/distribution/agent/adjust-level` | 调整分销员等级 |
| `adjustDistAgentParent` | PUT | `/distribution/agent/adjust-parent` | 调整分销员上级 |
| `getDistAgentRelationship` | GET | `/distribution/agent/relationship?id=${id}` | 查询分销员关系链 |
| `getDistAgentTeamOrders` | GET | `/distribution/agent/team-orders?agentId=${id}` | 查询分销员团队订单 |
| `getDistAgentPersonalOrders` | GET | `/distribution/agent/personal-orders?agentId=${id}` | 查询分销员个人订单 |
| `getDistAgentChildrenTree` | GET | `/distribution/agent/children-tree?id=${id}` | 查询分销员下级树 |

## 2. 佣金管理 (Commission Management)

### 佣金账单管理
| 接口名称 | HTTP方法 | URL | 说明 |
|---------|----------|-----|------|
| `getCommissionBillList` | GET | `/distribution/commission/bill/list` | 查询佣金账单列表 |
| `getCommissionBill` | GET | `/distribution/commission/bill/get?id=${id}` | 查询佣金账单详情 |
| `batchSettleCommission` | POST | `/distribution/commission/batch-settle` | 批量结算佣金 |
| `cancelCommissionBill` | PUT | `/distribution/commission/cancel` | 取消佣金账单 |
| `exportCommissionBill` | DOWNLOAD | `/distribution/commission/bill/export` | 导出佣金账单 |
| `unfreezeCommission` | PUT | `/distribution/commission/unfreeze` | 解冻佣金 |
| `batchUnfreezeCommission` | PUT | `/distribution/commission/batch-unfreeze` | 批量解冻佣金 |

### 提现管理
| 接口名称 | HTTP方法 | URL | 说明 |
|---------|----------|-----|------|
| `getWithdrawList` | GET | `/distribution/withdraw/list` | 查询提现记录列表 |
| `getWithdrawRecord` | GET | `/distribution/withdraw/get?id=${id}` | 查询提现记录详情 |
| `auditWithdraw` | POST | `/distribution/withdraw/${withdrawId}/audit` | 审核提现申请 |
| `batchAuditWithdraw` | POST | `/distribution/withdraw/batch-audit` | 批量审核提现 |
| `retryWithdrawPayment` | POST | `/distribution/withdraw/${withdrawId}/retry-payment` | 重试提现支付 |
| `exportWithdraw` | DOWNLOAD | `/distribution/withdraw/export` | 导出提现记录 |
| `markWithdrawTransferred` | PUT | `/distribution/withdraw/mark-transferred` | 标记已转账 |
| `cancelWithdraw` | PUT | `/distribution/withdraw/cancel` | 取消提现申请 |
| `getWithdrawStatistics` | GET | `/distribution/withdraw/statistics` | 获取提现统计 |
| `checkWithdrawTimeout` | GET | `/distribution/withdraw/check-timeout` | 检查超时提现 |

### 统计分析
| 接口名称 | HTTP方法 | URL | 说明 |
|---------|----------|-----|------|
| `getDistributionOverview` | GET | `/distribution/statistics/overview` | 获取分销概览统计 |
| `getDistributionRanking` | GET | `/distribution/statistics/ranking` | 获取分销排行榜 |
| `getCommissionStatistics` | GET | `/distribution/commission/statistics` | 获取佣金统计 |

## 3. 分销配置 (Distribution Configuration)

### 奖励方案管理
| 接口名称 | HTTP方法 | URL | 说明 |
|---------|----------|-----|------|
| `getRewardSchemeList` | GET | `/distribution/reward-scheme/list` | 查询奖励方案列表 |
| `getRewardScheme` | GET | `/distribution/reward-scheme/get?id=${id}` | 查询奖励方案详情 |
| `createRewardScheme` | POST | `/distribution/reward-scheme/create` | 创建奖励方案 |
| `updateRewardScheme` | PUT | `/distribution/reward-scheme/update` | 更新奖励方案 |
| `deleteRewardScheme` | DELETE | `/distribution/reward-scheme/delete?id=${id}` | 删除奖励方案 |
| `updateRewardSchemeStatus` | PUT | `/distribution/reward-scheme/update-status` | 更新奖励方案状态 |
| `getSimpleRewardSchemeList` | GET | `/distribution/reward-scheme/list-simple` | 查询简单奖励方案列表（仅启用） |

### 等级配置
| 接口名称 | HTTP方法 | URL | 说明 |
|---------|----------|-----|------|
| `getLevelConfigList` | GET | `/distribution/reward-level-config/list?schemeId=${schemeId}` | 查询等级配置列表 |
| `getLevelConfig` | GET | `/distribution/reward-level-config/get?id=${id}` | 查询等级配置详情 |
| `createLevelConfig` | POST | `/distribution/reward-level-config/create` | 创建等级配置 |
| `updateLevelConfig` | PUT | `/distribution/reward-level-config/update` | 更新等级配置 |
| `deleteLevelConfig` | DELETE | `/distribution/reward-level-config/delete?id=${id}` | 删除等级配置 |
| `saveLevelConfigBatch` | POST | `/distribution/reward-level-config/save-batch` | 批量保存等级配置 |

### 标签配置
| 接口名称 | HTTP方法 | URL | 说明 |
|---------|----------|-----|------|
| `getTagConfigList` | GET | `/distribution/reward-tag-config/list?schemeId=${schemeId}` | 查询标签配置列表 |
| `getTagConfig` | GET | `/distribution/reward-tag-config/get?id=${id}` | 查询标签配置详情 |
| `createTagConfig` | POST | `/distribution/reward-tag-config/create` | 创建标签配置 |
| `updateTagConfig` | PUT | `/distribution/reward-tag-config/update` | 更新标签配置 |
| `deleteTagConfig` | DELETE | `/distribution/reward-tag-config/delete?id=${id}` | 删除标签配置 |
| `saveTagConfigBatch` | POST | `/distribution/reward-tag-config/save-batch` | 批量保存标签配置 |

## 4. 商品分销配置 (Product Distribution)

| 接口名称 | HTTP方法 | URL | 说明 |
|---------|----------|-----|------|
| `getDistProductList` | GET | `/distribution/product/list` | 查询分销商品列表 |
| `getDistProductConfig` | GET | `/distribution/product/config?spuId=${spuId}` | 查询商品分销配置详情 |
| `batchConfigDistProduct` | POST | `/distribution/product/batch-config` | 批量配置分销商品 |
| `updateDistProductConfig` | PUT | `/distribution/product/update-config` | 更新单个商品分销配置 |
| `updateDistProductStatus` | PUT | `/distribution/product/update-status` | 启用/禁用商品分销 |
| `exportDistProduct` | DOWNLOAD | `/distribution/product/export` | 导出分销商品数据 |

### 商品选择（从主商品系统）
| 接口名称 | HTTP方法 | URL | 说明 |
|---------|----------|-----|------|
| `getProductList` | GET | `/product/spu/page` | 查询商品列表（用于选择） |
| `getSimpleProductList` | GET | `/product/spu/list-simple` | 查询简单商品列表 |

## 5. 海报管理 (Poster Management)

### 邀请海报模板管理
| 接口名称 | HTTP方法 | URL | 说明 |
|---------|----------|-----|------|
| `getInvitePosterList` | GET | `/distribution/poster/list` | 查询邀请海报列表 |
| `getInvitePoster` | GET | `/distribution/poster/get?id=${id}` | 查询邀请海报详情 |
| `createInvitePoster` | POST | `/distribution/poster/create` | 创建邀请海报 |
| `updateInvitePoster` | PUT | `/distribution/poster/update` | 更新邀请海报 |
| `deleteInvitePoster` | DELETE | `/distribution/poster/delete?id=${id}` | 删除邀请海报 |
| `updateInvitePosterStatus` | PUT | `/distribution/poster/update-status` | 更新邀请海报状态 |
| `exportInvitePoster` | DOWNLOAD | `/distribution/poster/export` | 导出邀请海报 |
| `getSimpleInvitePosterList` | GET | `/distribution/poster/list-all-simple` | 获取简化海报列表（用于选择器） |

### 分销员海报记录管理
| 接口名称 | HTTP方法 | URL | 说明 |
|---------|----------|-----|------|
| `getAgentPosterList` | GET | `/distribution/agent-poster/list` | 查询分销员海报记录列表 |
| `getAgentPoster` | GET | `/distribution/agent-poster/get?id=${id}` | 查询分销员海报记录详情 |
| `generateAgentPoster` | POST | `/distribution/agent-poster/generate` | 生成分销员专属海报 |
| `getAvailablePosters` | GET | `/distribution/agent-poster/available?agentId=${agentId}` | 获取分销员可用海报 |
| `getAgentPosterStatistics` | GET | `/distribution/agent-poster/statistics` | 获取分销员海报统计 |
| `refreshAgentPoster` | POST | `/distribution/agent-poster/${id}/refresh` | 刷新海报（重新生成） |
| `deleteAgentPoster` | DELETE | `/distribution/agent-poster/delete?id=${id}` | 删除分销员海报记录 |
| `exportAgentPoster` | DOWNLOAD | `/distribution/agent-poster/export` | 导出分销员海报记录 |

### 海报效果统计
| 接口名称 | HTTP方法 | URL | 说明 |
|---------|----------|-----|------|
| `getPosterEffectStatistics` | GET | `/distribution/poster/effect-statistics` | 获取海报效果统计 |
| `getPosterTrendAnalysis` | GET | `/distribution/poster/trend-analysis` | 获取海报趋势分析 |

## API 基础信息

### 基础 URL
- 开发环境: `http://localhost:48080/api`
- 生产环境: `https://octopus-api.5ihuish.com/api`

### 请求头
- `Authorization`: Bearer {token} (必需)
- `tenant-id`: {tenantId} (多租户场景必需)
- `Content-Type`: application/json (POST/PUT 请求)

### 响应格式
```json
{
  "code": 0,      // 0 表示成功，其他为错误码
  "data": {},     // 响应数据
  "msg": "成功"   // 响应消息
}
```

### 分页参数
分页接口通常接受以下查询参数：
- `pageNo`: 页码，从 1 开始
- `pageSize`: 每页大小，默认 10

### 导出接口说明
所有 DOWNLOAD 类型的接口返回文件流，通常是 Excel 格式。

## 注意事项

1. 所有 `${id}` 或 `${xxx}` 形式的参数需要在实际请求时替换为具体值
2. DELETE 方法的 id 参数通常通过 URL 查询参数传递
3. 批量操作接口通常接受 id 数组作为请求体
4. 部分查询接口支持复杂的过滤条件，具体参数需查看前端代码中的请求参数构造