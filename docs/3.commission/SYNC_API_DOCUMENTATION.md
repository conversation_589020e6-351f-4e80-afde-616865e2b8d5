# 分销佣金手动同步API文档

## 接口概述

手动同步分销订单佣金接口，用于从 `t_order` 表中根据条件查询订单，检查是否为分销订单（有分销码）并且商品已配置分销，生成对应的佣金记录。

## 接口信息

- **URL**: `/distribution/commission/sync`
- **Method**: `POST`
- **权限**: `distribution:commission:sync`

## 请求参数

### DistCommissionSyncReqVO

| 参数名 | 类型 | 必填 | 说明 |
| --- | --- | --- | --- |
| appId | Long | 是 | 应用ID |
| orderIds | List<Long> | 否 | 订单ID列表 |
| thirdOrderNos | List<String> | 否 | 三方订单号列表 |
| startTime | LocalDateTime | 否 | 开始时间 |
| endTime | LocalDateTime | 否 | 结束时间 |
| skipProcessed | Boolean | 否 | 是否跳过已处理订单，默认true |
| forceSync | Boolean | 否 | 是否强制同步（即使商品未配置分销也尝试），默认false |

**注意**：
- 订单号（orderIds或thirdOrderNos）和时间范围（startTime和endTime）至少需要提供一个
- 如果提供时间范围，结束时间必须大于开始时间

## 响应参数

### DistCommissionSyncRespVO

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| totalOrders | Integer | 同步总订单数 |
| successOrders | Integer | 成功同步订单数 |
| skippedOrders | Integer | 跳过的订单数（已处理） |
| failedOrders | Integer | 失败的订单数 |
| noConfigOrders | Integer | 无分销配置的订单数 |
| generatedCommissions | Integer | 生成的佣金记录数 |
| failedOrderDetails | List<FailedOrder> | 同步失败的订单详情 |

### FailedOrder

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| orderId | Long | 订单ID |
| orderNo | String | 订单号 |
| reason | String | 失败原因 |

## 业务逻辑

### 1. 订单筛选条件
- 必须有分销码（invite_code不为空）
- 订单状态必须是已支付（status >= 2）
- 根据请求参数筛选：订单ID、三方订单号、时间范围

### 2. 处理流程
对于每个符合条件的订单：

1. **检查分销码**：如果没有分销码，跳过
2. **检查订单状态**：只处理已支付的订单
3. **检查重复处理**：如果skipProcessed=true且订单已存在佣金记录，跳过
4. **查找分销员**：根据分销码和appId查找分销员，验证状态
5. **检查商品配置**：
   - 验证商品是否配置了分销
   - 如果未配置且forceSync=false，记录为无配置订单
6. **计算佣金**：调用现有的calculateCommission方法生成佣金记录

### 3. 特殊说明
- **避免重复处理**：通过检查订单是否已存在佣金记录来避免重复
- **商品配置验证**：即使订单有分销码，如果商品未配置分销，默认不会生成佣金
- **金额转换**：订单金额从分转换为元（除以100）

## 请求示例

```json
{
  "appId": 1,
  "orderIds": [1001, 1002, 1003],
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-01-31 23:59:59",
  "skipProcessed": true,
  "forceSync": false
}
```

## 响应示例

```json
{
  "code": 0,
  "data": {
    "totalOrders": 10,
    "successOrders": 7,
    "skippedOrders": 1,
    "failedOrders": 1,
    "noConfigOrders": 1,
    "generatedCommissions": 14,
    "failedOrderDetails": [
      {
        "orderId": 1004,
        "orderNo": "ORDER001",
        "reason": "商品未配置分销"
      }
    ]
  },
  "msg": "success"
}
```

## 错误处理

| 错误码 | 说明 |
| --- | --- |
| 400 | 参数验证失败（如未提供订单号和时间范围） |
| 403 | 权限不足 |
| 500 | 服务器内部错误 |

## 注意事项

1. **性能考虑**：如果同步大量订单，建议分批处理
2. **事务处理**：整个同步过程在一个事务中，失败会回滚
3. **日志记录**：所有同步操作都会记录详细日志
4. **权限控制**：需要 `distribution:commission:sync` 权限