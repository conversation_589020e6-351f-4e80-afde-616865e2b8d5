# 分销订单自动同步定时任务

## 功能说明

定时从 `t_order` 表中获取未处理的分销订单并自动生成佣金记录。

## 任务类

- **类名**: `DistOrderSyncJob`
- **位置**: `com.yitong.octopus.module.distribution.job.DistOrderSyncJob`
- **类型**: Quartz定时任务

## 执行逻辑

1. 查询指定时间范围内的所有已支付的分销订单（不限定appId）
2. 从订单中提取appId信息
3. 根据订单的appId查找对应的分销员和商品配置
4. 生成佣金记录
5. 自动排除已处理的订单（避免重复）

## 参数配置

任务支持通过参数配置查询时间范围：
- **参数格式**: 数字，表示查询过去多少分钟内的订单
- **默认值**: 30（查询过去30分钟内的订单）
- **示例**: 
  - 传入 `60` 表示查询过去60分钟内的订单
  - 传入 `10` 表示查询过去10分钟内的订单
  - 不传参数默认查询过去30分钟内的订单

## 定时任务配置示例

在系统管理 -> 定时任务管理中添加：

```
任务名称: 分销订单自动同步
任务处理器: distOrderSyncJob
执行参数: 30
CRON表达式: 0 */5 * * * ?  （每5分钟执行一次）
```

## 查询条件

- **订单状态**: status >= 2（已支付）
- **分销标识**: invite_code 不为空
- **时间范围**: pay_time 在指定范围内
- **排除条件**: 排除已存在佣金记录的订单

## 处理结果

任务执行后返回以下统计信息：
- **总订单数**: 查询到的订单总数
- **成功订单数**: 成功生成佣金的订单数
- **跳过订单数**: 被跳过的订单数（如无分销码）
- **失败订单数**: 处理失败的订单数
- **无配置订单数**: 商品未配置分销的订单数
- **生成佣金数**: 实际生成的佣金记录数

## 日志输出

任务执行过程中会输出详细日志：
- INFO级别：任务开始、结束、统计信息
- WARN级别：分销员不存在、参数无效等警告
- ERROR级别：处理异常信息
- DEBUG级别：单个订单处理细节

## 注意事项

1. **幂等性**: 任务自动排除已处理的订单，支持重复执行
2. **事务性**: 整个同步过程在事务中执行，失败自动回滚
3. **性能考虑**: 建议根据业务量调整查询时间范围和执行频率
4. **监控建议**: 定期检查失败订单详情，及时处理异常情况