# 分销商品核销后结算功能设计方案 v1.0

## 1. 需求背景

### 1.1 业务场景
在分销业务中，为了防止恶意刷单和退款，需要在订单核销后的一定时间内才进行佣金结算。这样可以：
- 降低退款造成的佣金损失风险
- 提高佣金结算的准确性
- 给予足够的冲账和对账时间
- 支持不同商品设置不同的结算周期

### 1.2 当前现状
- 目前系统在订单支付完成后即计算佣金
- 缺少核销后延迟结算机制
- 无法灵活配置不同商品的结算周期
- 缺少佣金冻结和解冻机制

## 2. 功能设计

### 2.1 核心功能
1. **结算周期配置**：支持按商品配置核销后N天结算
2. **佣金状态管理**：冻结期、可结算、已结算、已失效
3. **自动结算任务**：定时扫描并结算到期佣金
4. **手动结算支持**：支持运营人员手动触发结算
5. **结算明细查询**：提供详细的结算记录和查询功能

### 2.2 业务流程

```mermaid
graph TD
    A[订单支付完成] --> B[生成佣金记录]
    B --> C{是否需要核销}
    C -->|是| D[等待核销]
    C -->|否| E[进入冻结期]
    D --> F[订单核销]
    F --> E
    E --> G[佣金冻结中]
    G --> H{达到结算周期}
    H -->|是| I[佣金可结算]
    H -->|否| G
    I --> J[执行结算]
    J --> K[佣金已结算]
    
    L[订单退款] --> M[佣金失效]
```

## 3. 数据库设计

### 3.1 表结构修改

#### 3.1.1 yt_dist_goods_config 表新增字段

```sql
-- Phase 1: 添加核销后结算相关字段
ALTER TABLE `yt_dist_goods_config` 
ADD COLUMN `settlement_type` TINYINT NOT NULL DEFAULT 1 
    COMMENT '结算类型：1-实时结算，2-核销后结算，3-订单完成后结算' AFTER `commission_base_type`,
ADD COLUMN `settlement_days` INT NOT NULL DEFAULT 0 
    COMMENT '结算周期（天）：0-立即结算，N-核销/完成后N天结算' AFTER `settlement_type`,
ADD COLUMN `require_verification` BIT(1) NOT NULL DEFAULT b'0' 
    COMMENT '是否需要核销：0-不需要，1-需要' AFTER `settlement_days`,
ADD COLUMN `settlement_rule` VARCHAR(500) DEFAULT NULL 
    COMMENT '结算规则描述' AFTER `require_verification`;

-- 添加索引优化查询性能
ALTER TABLE `yt_dist_goods_config` 
ADD INDEX `idx_settlement` (`settlement_type`, `settlement_days`, `status`);
```

#### 3.1.2 创建佣金结算配置历史表

```sql
-- 记录结算配置变更历史
CREATE TABLE `yt_dist_settlement_config_history` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_id` BIGINT NOT NULL COMMENT '配置ID',
  `spu_id` BIGINT NOT NULL COMMENT '商品SPU ID',
  `settlement_type` TINYINT NOT NULL COMMENT '结算类型',
  `settlement_days` INT NOT NULL COMMENT '结算周期（天）',
  `require_verification` BIT(1) NOT NULL COMMENT '是否需要核销',
  `change_reason` VARCHAR(500) COMMENT '变更原因',
  `operator` VARCHAR(64) COMMENT '操作人',
  `operation_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `creator` VARCHAR(64) DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_spu_id` (`spu_id`),
  KEY `idx_operation_time` (`operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销结算配置历史表';
```

### 3.2 佣金记录表改造

#### 3.2.1 yt_dist_commission 表新增字段

```sql
-- 添加结算状态管理字段
ALTER TABLE `yt_dist_commission` 
ADD COLUMN `settlement_status` TINYINT NOT NULL DEFAULT 0 
    COMMENT '结算状态：0-待核销，1-冻结中，2-可结算，3-已结算，4-已失效' AFTER `status`,
ADD COLUMN `freeze_time` DATETIME DEFAULT NULL 
    COMMENT '冻结开始时间' AFTER `settlement_status`,
ADD COLUMN `unfreeze_time` DATETIME DEFAULT NULL 
    COMMENT '解冻时间（预计结算时间）' AFTER `freeze_time`,
ADD COLUMN `settlement_time` DATETIME DEFAULT NULL 
    COMMENT '实际结算时间' AFTER `unfreeze_time`,
ADD COLUMN `verification_time` DATETIME DEFAULT NULL 
    COMMENT '核销时间' AFTER `order_time`,
ADD COLUMN `settlement_batch_no` VARCHAR(32) DEFAULT NULL 
    COMMENT '结算批次号' AFTER `settlement_time`,
ADD COLUMN `settlement_type` TINYINT DEFAULT NULL 
    COMMENT '结算类型：1-自动结算，2-手动结算' AFTER `settlement_batch_no`,
ADD COLUMN `settlement_remark` VARCHAR(500) DEFAULT NULL 
    COMMENT '结算备注' AFTER `settlement_type`;

-- 添加索引优化查询
ALTER TABLE `yt_dist_commission` 
ADD INDEX `idx_settlement_status` (`settlement_status`, `unfreeze_time`),
ADD INDEX `idx_settlement_batch` (`settlement_batch_no`),
ADD INDEX `idx_verification` (`verification_time`);
```

#### 3.2.2 创建结算批次表

```sql
-- 佣金结算批次表
CREATE TABLE `yt_dist_settlement_batch` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '批次ID',
  `batch_no` VARCHAR(32) NOT NULL COMMENT '批次号',
  `settlement_type` TINYINT NOT NULL COMMENT '结算类型：1-自动结算，2-手动结算',
  `settlement_count` INT NOT NULL DEFAULT 0 COMMENT '结算笔数',
  `settlement_amount` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '结算总金额',
  `start_time` DATETIME NOT NULL COMMENT '结算开始时间',
  `end_time` DATETIME DEFAULT NULL COMMENT '结算结束时间',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '批次状态：0-处理中，1-成功，2-部分成功，3-失败',
  `success_count` INT DEFAULT 0 COMMENT '成功笔数',
  `fail_count` INT DEFAULT 0 COMMENT '失败笔数',
  `fail_reason` TEXT COMMENT '失败原因',
  `operator` VARCHAR(64) COMMENT '操作人（手动结算时）',
  `creator` VARCHAR(64) DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) DEFAULT '' COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_batch_no` (`batch_no`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销结算批次表';
```

#### 3.2.3 创建结算明细表

```sql
-- 佣金结算明细表
CREATE TABLE `yt_dist_settlement_detail` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `batch_no` VARCHAR(32) NOT NULL COMMENT '批次号',
  `commission_id` BIGINT NOT NULL COMMENT '佣金记录ID',
  `agent_id` BIGINT NOT NULL COMMENT '分销员ID',
  `order_no` VARCHAR(64) NOT NULL COMMENT '订单号',
  `spu_id` BIGINT NOT NULL COMMENT '商品SPU ID',
  `commission_amount` DECIMAL(10,2) NOT NULL COMMENT '佣金金额',
  `settlement_status` TINYINT NOT NULL COMMENT '结算状态：0-待处理，1-成功，2-失败',
  `fail_reason` VARCHAR(500) COMMENT '失败原因',
  `process_time` DATETIME COMMENT '处理时间',
  `creator` VARCHAR(64) DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_commission_id` (`commission_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_order_no` (`order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销结算明细表';
```

## 4. 业务实现设计

### 4.1 核心服务类设计

#### 4.1.1 结算配置服务

```java
public interface DistSettlementConfigService {
    
    /**
     * 获取商品的结算配置
     */
    DistSettlementConfigVO getSettlementConfig(Long spuId);
    
    /**
     * 更新商品结算配置
     */
    void updateSettlementConfig(DistSettlementConfigUpdateReqVO reqVO);
    
    /**
     * 批量更新结算配置
     */
    void batchUpdateSettlementConfig(List<Long> spuIds, DistSettlementConfigUpdateReqVO reqVO);
    
    /**
     * 获取结算配置历史
     */
    PageResult<DistSettlementConfigHistoryVO> getConfigHistory(Long spuId, PageParam pageParam);
}
```

#### 4.1.2 佣金结算服务

```java
public interface DistCommissionSettlementService {
    
    /**
     * 订单支付完成后创建佣金记录
     */
    void createCommissionRecord(OrderPaymentEvent event);
    
    /**
     * 订单核销后更新佣金状态
     */
    void onOrderVerification(OrderVerificationEvent event);
    
    /**
     * 订单退款后处理佣金
     */
    void onOrderRefund(OrderRefundEvent event);
    
    /**
     * 自动结算到期佣金（定时任务调用）
     */
    DistSettlementBatchVO autoSettleCommissions();
    
    /**
     * 手动结算指定佣金
     */
    DistSettlementBatchVO manualSettleCommissions(List<Long> commissionIds);
    
    /**
     * 查询待结算佣金列表
     */
    PageResult<DistCommissionVO> getPendingSettlements(DistCommissionPageReqVO reqVO);
    
    /**
     * 获取结算批次详情
     */
    DistSettlementBatchVO getSettlementBatch(String batchNo);
}
```

#### 4.1.3 结算任务调度服务

```java
@Component
@Slf4j
public class DistSettlementScheduleTask {
    
    @Resource
    private DistCommissionSettlementService settlementService;
    
    /**
     * 自动结算任务 - 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    @SchedulerLock(name = "distCommissionAutoSettle", lockAtMostFor = "1h", lockAtLeastFor = "5m")
    public void autoSettleCommissions() {
        log.info("[autoSettleCommissions][开始执行自动结算任务]");
        try {
            DistSettlementBatchVO batch = settlementService.autoSettleCommissions();
            log.info("[autoSettleCommissions][自动结算完成] 批次号: {}, 结算笔数: {}, 结算金额: {}", 
                batch.getBatchNo(), batch.getSettlementCount(), batch.getSettlementAmount());
        } catch (Exception e) {
            log.error("[autoSettleCommissions][自动结算失败]", e);
        }
    }
    
    /**
     * 检查冻结佣金任务 - 每小时执行一次
     */
    @Scheduled(cron = "0 0 * * * ?")
    @SchedulerLock(name = "distCommissionFreezeCheck", lockAtMostFor = "30m", lockAtLeastFor = "5m")
    public void checkFrozenCommissions() {
        log.info("[checkFrozenCommissions][开始检查冻结佣金]");
        // 将到期的冻结佣金更新为可结算状态
        settlementService.updateExpiredFrozenCommissions();
    }
}
```

### 4.2 结算核心逻辑

#### 4.2.1 佣金状态流转逻辑

```java
@Service
@Slf4j
public class DistCommissionSettlementServiceImpl implements DistCommissionSettlementService {
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createCommissionRecord(OrderPaymentEvent event) {
        // 1. 获取商品结算配置
        DistGoodsConfigDO config = getGoodsConfig(event.getSpuId());
        
        // 2. 创建佣金记录
        DistCommissionDO commission = new DistCommissionDO();
        commission.setOrderNo(event.getOrderNo());
        commission.setAgentId(event.getAgentId());
        commission.setCommissionAmount(calculateCommission(event));
        
        // 3. 根据结算配置设置状态
        if (config.getRequireVerification()) {
            // 需要核销的商品，设置为待核销
            commission.setSettlementStatus(SettlementStatusEnum.PENDING_VERIFICATION.getValue());
        } else {
            // 不需要核销，直接进入冻结期
            commission.setSettlementStatus(SettlementStatusEnum.FROZEN.getValue());
            commission.setFreezeTime(LocalDateTime.now());
            
            // 计算解冻时间
            LocalDateTime unfreezeTime = calculateUnfreezeTime(config);
            commission.setUnfreezeTime(unfreezeTime);
        }
        
        commissionMapper.insert(commission);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onOrderVerification(OrderVerificationEvent event) {
        // 1. 查询佣金记录
        DistCommissionDO commission = commissionMapper.selectByOrderNo(event.getOrderNo());
        if (commission == null) {
            log.warn("[onOrderVerification][佣金记录不存在] orderNo: {}", event.getOrderNo());
            return;
        }
        
        // 2. 更新核销时间和状态
        commission.setVerificationTime(event.getVerificationTime());
        commission.setSettlementStatus(SettlementStatusEnum.FROZEN.getValue());
        commission.setFreezeTime(event.getVerificationTime());
        
        // 3. 计算解冻时间
        DistGoodsConfigDO config = getGoodsConfig(commission.getSpuId());
        LocalDateTime unfreezeTime = event.getVerificationTime()
            .plusDays(config.getSettlementDays());
        commission.setUnfreezeTime(unfreezeTime);
        
        commissionMapper.updateById(commission);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DistSettlementBatchVO autoSettleCommissions() {
        // 1. 创建结算批次
        String batchNo = generateBatchNo();
        DistSettlementBatchDO batch = createSettlementBatch(batchNo, SettlementTypeEnum.AUTO);
        
        try {
            // 2. 查询可结算的佣金记录
            List<DistCommissionDO> commissions = commissionMapper.selectList(
                new LambdaQueryWrapper<DistCommissionDO>()
                    .eq(DistCommissionDO::getSettlementStatus, SettlementStatusEnum.FROZEN.getValue())
                    .le(DistCommissionDO::getUnfreezeTime, LocalDateTime.now())
                    .last("LIMIT 1000") // 每批最多处理1000条
            );
            
            if (CollUtil.isEmpty(commissions)) {
                log.info("[autoSettleCommissions][没有需要结算的佣金]");
                batch.setStatus(BatchStatusEnum.SUCCESS.getValue());
                settlementBatchMapper.updateById(batch);
                return null;
            }
            
            // 3. 执行结算
            int successCount = 0;
            int failCount = 0;
            BigDecimal totalAmount = BigDecimal.ZERO;
            
            for (DistCommissionDO commission : commissions) {
                try {
                    // 结算单条佣金
                    settleCommission(commission, batchNo);
                    successCount++;
                    totalAmount = totalAmount.add(commission.getCommissionAmount());
                } catch (Exception e) {
                    log.error("[autoSettleCommissions][结算失败] commissionId: {}", 
                        commission.getId(), e);
                    failCount++;
                    // 记录失败明细
                    saveSettlementDetail(commission, batchNo, false, e.getMessage());
                }
            }
            
            // 4. 更新批次信息
            batch.setSuccessCount(successCount);
            batch.setFailCount(failCount);
            batch.setSettlementCount(successCount + failCount);
            batch.setSettlementAmount(totalAmount);
            batch.setEndTime(LocalDateTime.now());
            batch.setStatus(failCount == 0 ? 
                BatchStatusEnum.SUCCESS.getValue() : 
                BatchStatusEnum.PARTIAL_SUCCESS.getValue());
            settlementBatchMapper.updateById(batch);
            
            return BeanUtils.toBean(batch, DistSettlementBatchVO.class);
            
        } catch (Exception e) {
            log.error("[autoSettleCommissions][批次处理失败] batchNo: {}", batchNo, e);
            batch.setStatus(BatchStatusEnum.FAILED.getValue());
            batch.setFailReason(e.getMessage());
            batch.setEndTime(LocalDateTime.now());
            settlementBatchMapper.updateById(batch);
            throw new ServiceException(SETTLEMENT_BATCH_FAILED);
        }
    }
    
    private void settleCommission(DistCommissionDO commission, String batchNo) {
        // 1. 更新佣金状态
        commission.setSettlementStatus(SettlementStatusEnum.SETTLED.getValue());
        commission.setSettlementTime(LocalDateTime.now());
        commission.setSettlementBatchNo(batchNo);
        commission.setSettlementType(SettlementTypeEnum.AUTO.getValue());
        commissionMapper.updateById(commission);
        
        // 2. 更新分销员余额
        agentService.addBalance(commission.getAgentId(), commission.getCommissionAmount());
        
        // 3. 记录结算明细
        saveSettlementDetail(commission, batchNo, true, null);
        
        // 4. 发送结算通知
        sendSettlementNotification(commission);
    }
}
```

### 4.3 API接口设计

#### 4.3.1 管理端接口

```java
@RestController
@RequestMapping("/distribution/settlement")
@Api(tags = "管理后台 - 分销结算管理")
public class DistSettlementController {
    
    @GetMapping("/config/{spuId}")
    @ApiOperation("获取商品结算配置")
    public CommonResult<DistSettlementConfigVO> getSettlementConfig(@PathVariable Long spuId) {
        return success(settlementConfigService.getSettlementConfig(spuId));
    }
    
    @PutMapping("/config")
    @ApiOperation("更新商品结算配置")
    @PreAuthorize("@ss.hasPermission('distribution:settlement:update')")
    public CommonResult<Boolean> updateSettlementConfig(@Valid @RequestBody DistSettlementConfigUpdateReqVO reqVO) {
        settlementConfigService.updateSettlementConfig(reqVO);
        return success(true);
    }
    
    @GetMapping("/pending")
    @ApiOperation("查询待结算佣金列表")
    @PreAuthorize("@ss.hasPermission('distribution:settlement:query')")
    public CommonResult<PageResult<DistCommissionVO>> getPendingSettlements(@Valid DistCommissionPageReqVO reqVO) {
        return success(settlementService.getPendingSettlements(reqVO));
    }
    
    @PostMapping("/manual")
    @ApiOperation("手动结算佣金")
    @PreAuthorize("@ss.hasPermission('distribution:settlement:manual')")
    public CommonResult<DistSettlementBatchVO> manualSettle(@RequestBody List<Long> commissionIds) {
        return success(settlementService.manualSettleCommissions(commissionIds));
    }
    
    @GetMapping("/batch/{batchNo}")
    @ApiOperation("获取结算批次详情")
    public CommonResult<DistSettlementBatchVO> getSettlementBatch(@PathVariable String batchNo) {
        return success(settlementService.getSettlementBatch(batchNo));
    }
    
    @GetMapping("/batch/list")
    @ApiOperation("查询结算批次列表")
    public CommonResult<PageResult<DistSettlementBatchVO>> getSettlementBatchList(@Valid PageParam pageParam) {
        return success(settlementService.getSettlementBatchPage(pageParam));
    }
}
```

## 5. 前端界面设计

### 5.1 管理端页面架构

基于 `/src/views/distribution-v4` 的架构规范，采用抽屉式详情/编辑设计。

#### 5.1.1 目录结构

```
src/views/distribution-v4/
├── settlement/                       # 结算管理模块
│   ├── index.vue                   # 主列表页面
│   └── components/
│       ├── SettlementConfigDrawer.vue    # 商品结算配置抽屉
│       ├── SettlementDetailDrawer.vue    # 结算详情抽屉
│       ├── BatchSettleDialog.vue         # 批量结算对话框
│       ├── ManualSettleDialog.vue        # 手动结算对话框
│       ├── SettlementHistoryDrawer.vue   # 结算历史抽屉
│       └── SettlementStatsCard.vue       # 结算统计卡片
```

### 5.2 商品结算配置抽屉组件

```vue
<!-- SettlementConfigDrawer.vue -->
<template>
  <el-drawer
    v-model="drawerVisible"
    :title="drawerTitle"
    :direction="'rtl'"
    :size="'60%'"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="distribution-settlement-drawer"
  >
    <div v-loading="formLoading" class="drawer-content">
      <!-- 商品信息卡片 -->
      <el-card v-if="formData.spuId" class="mb-20px" shadow="never">
        <template #header>
          <div class="card-header">
            <span>商品信息</span>
            <el-button 
              v-if="formType === 'update'" 
              type="primary" 
              link 
              @click="openHistory">
              <Icon icon="ep:time" class="mr-5px" />
              配置历史
            </el-button>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="商品名称">
            {{ formData.spuName }}
          </el-descriptions-item>
          <el-descriptions-item label="商品编码">
            {{ formData.channelSpuId }}
          </el-descriptions-item>
          <el-descriptions-item label="商品分类">
            <GoodsCategoryName :category-id="formData.categoryId" />
          </el-descriptions-item>
          <el-descriptions-item label="商品价格">
            ￥{{ fenToYuan(formData.price || 0) }}
          </el-descriptions-item>
          <el-descriptions-item label="当前状态">
            <el-tag :type="formData.enableDist ? 'success' : 'info'">
              {{ formData.enableDist ? '分销中' : '未启用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="累计佣金">
            ￥{{ fenToYuan(formData.totalCommission || 0) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 佣金配置表单 -->
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        v-if="formType !== 'detail'"
      >
        <!-- 基础配置 -->
        <el-card class="mb-20px" shadow="never">
          <template #header>
            <span>基础配置</span>
          </template>
          
          <el-form-item label="启用分销" prop="enableDist">
            <el-switch 
              v-model="formData.enableDist"
              active-text="启用"
              inactive-text="禁用" />
          </el-form-item>

          <el-form-item label="佣金模式" prop="commissionMode">
            <el-radio-group v-model="formData.commissionMode">
              <el-radio :label="1">按比例</el-radio>
              <el-radio :label="2">固定金额</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="佣金值" prop="commissionValue">
            <el-input-number
              v-model="formData.commissionValue"
              :min="0"
              :max="formData.commissionMode === 1 ? 100 : 999999"
              :precision="2"
              :step="formData.commissionMode === 1 ? 0.1 : 1"
              class="!w-200px" />
            <span class="ml-10px">
              {{ formData.commissionMode === 1 ? '%' : '元' }}
            </span>
          </el-form-item>
        </el-card>

        <!-- 结算配置 -->
        <el-card class="mb-20px" shadow="never">
          <template #header>
            <div class="card-header">
              <span>结算配置</span>
              <el-tooltip content="设置佣金结算的时机和周期" placement="top">
                <Icon icon="ep:question-filled" class="ml-5px" />
              </el-tooltip>
            </div>
          </template>
          
          <el-form-item label="结算类型" prop="settlementType">
            <el-radio-group 
              v-model="formData.settlementType" 
              @change="onSettlementTypeChange">
              <el-radio-button :label="1">
                <Icon icon="ep:lightning-filled" class="mr-5px" />
                实时结算
              </el-radio-button>
              <el-radio-button :label="2">
                <Icon icon="ep:check" class="mr-5px" />
                核销后结算
              </el-radio-button>
              <el-radio-button :label="3">
                <Icon icon="ep:finished" class="mr-5px" />
                订单完成后结算
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          
          <!-- 核销配置 -->
          <el-form-item 
            v-if="formData.settlementType === 2" 
            label="需要核销" 
            prop="requireVerification">
            <el-switch 
              v-model="formData.requireVerification"
              active-text="需要"
              inactive-text="不需要" />
            <el-tooltip 
              content="开启后需要订单核销才开始计算结算周期" 
              placement="top"
              class="ml-10px">
              <Icon icon="ep:info-filled" />
            </el-tooltip>
          </el-form-item>
          
          <!-- 结算周期 -->
          <el-form-item 
            v-if="formData.settlementType !== 1" 
            label="结算周期" 
            prop="settlementDays">
            <el-input-number 
              v-model="formData.settlementDays" 
              :min="0" 
              :max="365" 
              :step="1"
              class="!w-200px" />
            <span class="ml-10px">天</span>
          </el-form-item>

          <!-- 动态提示 -->
          <el-form-item v-if="formData.settlementType !== 1">
            <el-alert 
              :title="getSettlementDesc()" 
              type="info" 
              :closable="false"
              show-icon />
          </el-form-item>
          
          <!-- 结算规则说明 -->
          <el-form-item label="结算说明" prop="settlementRule">
            <el-input 
              v-model="formData.settlementRule" 
              type="textarea" 
              :rows="3" 
              :maxlength="500"
              show-word-limit
              placeholder="请输入结算规则说明，方便运营人员理解" />
          </el-form-item>
        </el-card>

        <!-- 高级配置 -->
        <el-card class="mb-20px" shadow="never">
          <template #header>
            <span>高级配置</span>
          </template>
          
          <el-form-item label="最低分销价" prop="minDistPrice">
            <el-input-number
              v-model="formData.minDistPrice"
              :min="0"
              :precision="2"
              class="!w-200px" />
            <span class="ml-10px">元</span>
          </el-form-item>

          <el-form-item label="分销有效期">
            <el-date-picker
              v-model="distTimeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="!w-400px" />
          </el-form-item>

          <el-form-item label="分销库存" prop="distStock">
            <el-input-number
              v-model="formData.distStock"
              :min="-1"
              class="!w-200px" />
            <span class="ml-10px text-gray-500">
              -1 表示不限制
            </span>
          </el-form-item>
        </el-card>
      </el-form>

      <!-- 详情展示模式 -->
      <div v-else class="detail-view">
        <el-card class="mb-20px" shadow="never">
          <template #header>
            <span>结算配置详情</span>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="结算类型">
              <el-tag :type="getSettlementTypeTag(formData.settlementType)">
                {{ getSettlementTypeText(formData.settlementType) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="结算周期">
              {{ formData.settlementDays }} 天
            </el-descriptions-item>
            <el-descriptions-item label="需要核销">
              <el-tag :type="formData.requireVerification ? 'success' : 'info'">
                {{ formData.requireVerification ? '是' : '否' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="佣金模式">
              {{ formData.commissionMode === 1 ? '按比例' : '固定金额' }}
            </el-descriptions-item>
            <el-descriptions-item label="佣金值">
              {{ formData.commissionValue }}
              {{ formData.commissionMode === 1 ? '%' : '元' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDate(formData.createTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="结算说明" :span="2">
              {{ formData.settlementRule || '无' }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>
    </div>

    <!-- 底部操作区 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">
          {{ formType === 'detail' ? '关闭' : '取消' }}
        </el-button>
        <el-button 
          v-if="formType !== 'detail'"
          type="primary" 
          @click="submitForm"
          :loading="submitLoading">
          {{ formType === 'create' ? '创建' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-drawer>

  <!-- 配置历史抽屉 -->
  <SettlementHistoryDrawer
    v-model="historyDrawerVisible"
    :spu-id="formData.spuId" />
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { fenToYuan, yuanToFen } from '@/utils'
import { formatDate } from '@/utils/formatTime'
import * as DistGoodsApi from '@/api/distribution/goods'
import SettlementHistoryDrawer from './SettlementHistoryDrawer.vue'

// Props定义
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'create'
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

// Emits定义
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const drawerVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const formType = computed(() => props.type)
const drawerTitle = computed(() => {
  const titles = {
    create: '新增结算配置',
    update: '编辑结算配置',
    detail: '结算配置详情'
  }
  return titles[formType.value] || ''
})

const formLoading = ref(false)
const submitLoading = ref(false)
const formRef = ref()
const historyDrawerVisible = ref(false)

// 表单数据
const formData = reactive({
  id: undefined,
  spuId: undefined,
  spuName: '',
  channelSpuId: '',
  categoryId: undefined,
  price: 0,
  enableDist: true,
  commissionMode: 1,
  commissionValue: 0,
  settlementType: 1,
  settlementDays: 0,
  requireVerification: false,
  settlementRule: '',
  minDistPrice: 0,
  distStartTime: undefined,
  distEndTime: undefined,
  distStock: -1,
  totalCommission: 0,
  createTime: undefined
})

// 时间范围
const distTimeRange = ref([])

// 表单规则
const formRules = {
  enableDist: [
    { required: true, message: '请选择是否启用分销', trigger: 'change' }
  ],
  commissionMode: [
    { required: true, message: '请选择佣金模式', trigger: 'change' }
  ],
  commissionValue: [
    { required: true, message: '请输入佣金值', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (formData.commissionMode === 1 && value > 100) {
          callback(new Error('佣金比例不能超过100%'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  settlementType: [
    { required: true, message: '请选择结算类型', trigger: 'change' }
  ],
  settlementDays: [
    { 
      required: true, 
      message: '请输入结算周期', 
      trigger: 'blur',
      type: 'number'
    }
  ]
}

// 方法
const getSettlementDesc = () => {
  if (formData.settlementType === 2) {
    if (formData.requireVerification) {
      return `订单核销后${formData.settlementDays}天自动结算佣金`
    } else {
      return `订单支付后${formData.settlementDays}天自动结算佣金`
    }
  } else if (formData.settlementType === 3) {
    return `订单完成后${formData.settlementDays}天自动结算佣金`
  } else {
    return '订单支付成功后立即结算佣金'
  }
}

const onSettlementTypeChange = (val: number) => {
  if (val === 1) {
    formData.settlementDays = 0
    formData.requireVerification = false
  }
}

const getSettlementTypeText = (type: number) => {
  const types = {
    1: '实时结算',
    2: '核销后结算',
    3: '订单完成后结算'
  }
  return types[type] || ''
}

const getSettlementTypeTag = (type: number) => {
  const tags = {
    1: 'success',
    2: 'warning',
    3: 'info'
  }
  return tags[type] || 'info'
}

const openHistory = () => {
  historyDrawerVisible.value = true
}

const handleClose = () => {
  if (formType.value !== 'detail' && !submitLoading.value) {
    ElMessageBox.confirm('确定要关闭吗？未保存的数据将会丢失', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      drawerVisible.value = false
    }).catch(() => {})
  } else {
    drawerVisible.value = false
  }
}

const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate((valid) => {
    if (valid) {
      submitLoading.value = true
      
      // 处理时间范围
      if (distTimeRange.value && distTimeRange.value.length === 2) {
        formData.distStartTime = distTimeRange.value[0]
        formData.distEndTime = distTimeRange.value[1]
      }
      
      // 提交API
      const api = formType.value === 'create' 
        ? DistGoodsApi.createGoodsConfig
        : DistGoodsApi.updateGoodsConfig
      
      api(formData).then(() => {
        ElMessage.success(formType.value === 'create' ? '创建成功' : '更新成功')
        emit('success')
        drawerVisible.value = false
      }).finally(() => {
        submitLoading.value = false
      })
    }
  })
}

// 监听数据变化
watch(() => props.data, (val) => {
  if (val) {
    Object.assign(formData, val)
    if (val.distStartTime && val.distEndTime) {
      distTimeRange.value = [val.distStartTime, val.distEndTime]
    }
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.distribution-settlement-drawer {
  :deep(.el-drawer__body) {
    padding: 0;
  }

  .drawer-content {
    padding: 20px;
    height: calc(100vh - 140px);
    overflow-y: auto;
  }

  .drawer-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    background: #fff;
    border-top: 1px solid #e8e8e8;
    text-align: right;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .detail-view {
    :deep(.el-descriptions__label) {
      font-weight: bold;
    }
  }
}
</style>
```

### 5.3 佣金结算管理主页面

```vue
<!-- settlement/index.vue -->
<template>
  <div class="distribution-settlement-container">
    <!-- 统计卡片 -->
    <ContentWrap>
      <SettlementStatsCard :loading="statsLoading" :data="statsData" />
    </ContentWrap>

    <!-- 搜索表单 -->
    <ContentWrap>
      <el-form
        ref="queryFormRef"
        :inline="true"
        :model="queryParams"
        class="-mb-15px"
        label-width="100px"
      >
        <el-form-item label="结算状态" prop="settlementStatus">
          <el-select
            v-model="queryParams.settlementStatus"
            placeholder="请选择"
            clearable
            class="!w-200px"
          >
            <el-option label="待核销" :value="0" />
            <el-option label="冻结中" :value="1" />
            <el-option label="可结算" :value="2" />
            <el-option label="已结算" :value="3" />
            <el-option label="已失效" :value="4" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="分销员" prop="agentId">
          <AgentSelect 
            v-model="queryParams.agentId"
            placeholder="请选择分销员"
            clearable
            class="!w-200px" />
        </el-form-item>
        
        <el-form-item label="订单号" prop="orderNo">
          <el-input
            v-model="queryParams.orderNo"
            placeholder="请输入订单号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-200px" />
        </el-form-item>
        
        <el-form-item label="商品名称" prop="spuName">
          <el-input
            v-model="queryParams.spuName"
            placeholder="请输入商品名称"
            clearable
            @keyup.enter="handleQuery"
            class="!w-200px" />
        </el-form-item>
        
        <el-form-item label="时间范围" prop="dateRange">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="!w-340px" />
        </el-form-item>
        
        <el-form-item>
          <el-button @click="handleQuery">
            <Icon icon="ep:search" class="mr-5px" /> 搜索
          </el-button>
          <el-button @click="resetQuery">
            <Icon icon="ep:refresh" class="mr-5px" /> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>

    <!-- 操作栏 -->
    <ContentWrap>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            @click="handleBatchSettle"
            :disabled="!selectedIds.length"
            v-hasPermi="['distribution:settlement:manual']"
          >
            <Icon icon="ep:coin" class="mr-5px" />
            批量结算
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            @click="handleAutoSettle"
            v-hasPermi="['distribution:settlement:auto']"
          >
            <Icon icon="ep:timer" class="mr-5px" />
            立即执行自动结算
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            @click="handleExport"
            v-hasPermi="['distribution:settlement:export']"
          >
            <Icon icon="ep:download" class="mr-5px" />
            导出
          </el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
      </el-row>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="list"
        @selection-change="handleSelectionChange"
        row-key="id"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="佣金ID" align="center" prop="id" width="100" />
        <el-table-column label="订单号" align="center" prop="orderNo" min-width="150">
          <template #default="scope">
            <el-link type="primary" @click="handleOrderDetail(scope.row)">
              {{ scope.row.orderNo }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column label="商品信息" align="left" min-width="200">
          <template #default="scope">
            <div class="flex items-center">
              <el-image
                :src="scope.row.spuImage"
                class="w-40px h-40px mr-10px"
                fit="cover">
                <template #error>
                  <div class="image-slot">
                    <Icon icon="ep:picture" />
                  </div>
                </template>
              </el-image>
              <div class="flex-1">
                <div class="text-14px font-medium">{{ scope.row.spuName }}</div>
                <div class="text-12px text-gray-500 mt-5px">
                  SKU: {{ scope.row.skuId }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="分销员" align="center" prop="agentName" width="120">
          <template #default="scope">
            <el-link type="primary" @click="handleAgentDetail(scope.row)">
              {{ scope.row.agentName }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column label="佣金金额" align="center" prop="commissionAmount" width="120">
          <template #default="scope">
            <span class="text-16px font-bold text-red-500">
              ￥{{ fenToYuan(scope.row.commissionAmount) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column label="结算状态" align="center" prop="settlementStatus" width="100">
          <template #default="scope">
            <el-tag 
              :type="getSettlementStatusType(scope.row.settlementStatus)"
              effect="plain">
              {{ getSettlementStatusText(scope.row.settlementStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="核销时间" align="center" prop="verificationTime" width="160">
          <template #default="scope">
            <span v-if="scope.row.verificationTime">
              {{ formatDate(scope.row.verificationTime) }}
            </span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="预计结算" align="center" prop="unfreezeTime" width="160">
          <template #default="scope">
            <div v-if="scope.row.unfreezeTime">
              <div>{{ formatDate(scope.row.unfreezeTime) }}</div>
              <div 
                v-if="scope.row.settlementStatus === 1"
                class="text-12px mt-5px"
                :class="getDaysRemainClass(scope.row.unfreezeTime)">
                {{ getDaysRemainText(scope.row.unfreezeTime) }}
              </div>
            </div>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="实际结算" align="center" prop="settlementTime" width="160">
          <template #default="scope">
            <span v-if="scope.row.settlementTime">
              {{ formatDate(scope.row.settlementTime) }}
            </span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" align="center" fixed="right" width="150">
          <template #default="scope">
            <el-button
              link
              type="primary"
              @click="handleDetail(scope.row)"
              v-hasPermi="['distribution:settlement:query']"
            >
              详情
            </el-button>
            <el-button
              v-if="scope.row.settlementStatus === 2"
              link
              type="success"
              @click="handleManualSettle(scope.row)"
              v-hasPermi="['distribution:settlement:manual']"
            >
              立即结算
            </el-button>
            <el-button
              v-if="scope.row.settlementStatus === 1"
              link
              type="warning"
              @click="handleForceSettle(scope.row)"
              v-hasPermi="['distribution:settlement:force']"
            >
              强制结算
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </ContentWrap>

    <!-- 详情抽屉 -->
    <SettlementDetailDrawer
      v-model="detailDrawerVisible"
      :data="currentRow" />

    <!-- 批量结算对话框 -->
    <BatchSettleDialog
      v-model="batchSettleVisible"
      :ids="selectedIds"
      @success="handleBatchSettleSuccess" />

    <!-- 手动结算对话框 -->
    <ManualSettleDialog
      v-model="manualSettleVisible"
      :data="currentRow"
      @success="handleManualSettleSuccess" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { fenToYuan } from '@/utils'
import { formatDate } from '@/utils/formatTime'
import * as SettlementApi from '@/api/distribution/settlement'
import { AgentSelect } from '../components/selects'
import SettlementStatsCard from './components/SettlementStatsCard.vue'
import SettlementDetailDrawer from './components/SettlementDetailDrawer.vue'
import BatchSettleDialog from './components/BatchSettleDialog.vue'
import ManualSettleDialog from './components/ManualSettleDialog.vue'

// 数据定义
const loading = ref(false)
const showSearch = ref(true)
const list = ref([])
const total = ref(0)
const selectedIds = ref([])
const currentRow = ref({})

// 统计数据
const statsLoading = ref(false)
const statsData = ref({
  totalAmount: 0,
  settledAmount: 0,
  pendingAmount: 0,
  frozenAmount: 0,
  totalCount: 0,
  settledCount: 0,
  pendingCount: 0,
  frozenCount: 0
})

// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  settlementStatus: undefined,
  agentId: undefined,
  orderNo: '',
  spuName: '',
  dateRange: []
})

// 抽屉和对话框
const detailDrawerVisible = ref(false)
const batchSettleVisible = ref(false)
const manualSettleVisible = ref(false)

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const data = await SettlementApi.getCommissionPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const getStats = async () => {
  statsLoading.value = true
  try {
    statsData.value = await SettlementApi.getSettlementStats()
  } finally {
    statsLoading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

// 重置
const resetQuery = () => {
  queryParams.settlementStatus = undefined
  queryParams.agentId = undefined
  queryParams.orderNo = ''
  queryParams.spuName = ''
  queryParams.dateRange = []
  handleQuery()
}

// 多选
const handleSelectionChange = (selection) => {
  selectedIds.value = selection.map(item => item.id)
}

// 批量结算
const handleBatchSettle = () => {
  if (selectedIds.value.length === 0) {
    ElMessage.warning('请选择要结算的记录')
    return
  }
  batchSettleVisible.value = true
}

// 手动结算
const handleManualSettle = (row) => {
  currentRow.value = row
  manualSettleVisible.value = true
}

// 强制结算
const handleForceSettle = (row) => {
  ElMessageBox.confirm(
    `该佣金还需等待${getDaysRemain(row.unfreezeTime)}天才能正常结算，确定要强制结算吗？`,
    '强制结算确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    await SettlementApi.forceSettle(row.id)
    ElMessage.success('强制结算成功')
    getList()
    getStats()
  })
}

// 自动结算
const handleAutoSettle = () => {
  ElMessageBox.confirm(
    '确定要立即执行自动结算任务吗？这将结算所有到期的佣金。',
    '执行自动结算',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    const loading = ElLoading.service({
      lock: true,
      text: '正在执行自动结算...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    try {
      const result = await SettlementApi.executeAutoSettle()
      ElMessage.success(`自动结算完成，成功${result.successCount}笔，失败${result.failCount}笔`)
      getList()
      getStats()
    } finally {
      loading.close()
    }
  })
}

// 查看详情
const handleDetail = (row) => {
  currentRow.value = row
  detailDrawerVisible.value = true
}

// 查看订单详情
const handleOrderDetail = (row) => {
  // 跳转到订单详情页
  window.open(`/order/detail/${row.orderId}`)
}

// 查看分销员详情
const handleAgentDetail = (row) => {
  // 跳转到分销员详情页
  window.open(`/distribution/agent/detail/${row.agentId}`)
}

// 导出
const handleExport = async () => {
  await ElMessageBox.confirm('确定要导出佣金结算数据吗？', '系统提示')
  await SettlementApi.exportCommission(queryParams)
  ElMessage.success('导出成功')
}

// 获取结算状态类型
const getSettlementStatusType = (status) => {
  const types = {
    0: 'info',
    1: 'warning',
    2: 'success',
    3: 'primary',
    4: 'danger'
  }
  return types[status] || 'info'
}

// 获取结算状态文本
const getSettlementStatusText = (status) => {
  const texts = {
    0: '待核销',
    1: '冻结中',
    2: '可结算',
    3: '已结算',
    4: '已失效'
  }
  return texts[status] || ''
}

// 计算剩余天数
const getDaysRemain = (unfreezeTime) => {
  if (!unfreezeTime) return 0
  const now = new Date()
  const unfreeze = new Date(unfreezeTime)
  const days = Math.ceil((unfreeze - now) / (1000 * 60 * 60 * 24))
  return days > 0 ? days : 0
}

// 获取剩余天数文本
const getDaysRemainText = (unfreezeTime) => {
  const days = getDaysRemain(unfreezeTime)
  if (days === 0) return '今天到期'
  if (days < 0) return '已到期'
  return `还需${days}天`
}

// 获取剩余天数样式
const getDaysRemainClass = (unfreezeTime) => {
  const days = getDaysRemain(unfreezeTime)
  if (days <= 0) return 'text-green-500'
  if (days <= 3) return 'text-orange-500'
  return 'text-gray-500'
}

// 批量结算成功回调
const handleBatchSettleSuccess = () => {
  batchSettleVisible.value = false
  getList()
  getStats()
}

// 手动结算成功回调
const handleManualSettleSuccess = () => {
  manualSettleVisible.value = false
  getList()
  getStats()
}

// 初始化
onMounted(() => {
  getList()
  getStats()
})
</script>

<style lang="scss" scoped>
.distribution-settlement-container {
  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    font-size: 14px;
  }
}
</style>
```

## 6. 实施计划

### 6.1 Phase 1: 基础功能开发（第1-2周）

#### 任务清单
- [ ] 数据库表结构修改和创建
- [ ] 实体类和Mapper开发
- [ ] 核心服务类开发
- [ ] 单元测试编写

#### 交付物
- 数据库变更脚本
- 后端核心代码
- 单元测试报告

### 6.2 Phase 2: 业务功能完善（第3-4周）

#### 任务清单
- [ ] 定时任务开发
- [ ] API接口开发
- [ ] 前端界面开发
- [ ] 集成测试

#### 交付物
- 完整的API文档
- 前端界面
- 集成测试报告

### 6.3 Phase 3: 测试与优化（第5周）

#### 任务清单
- [ ] 性能测试和优化
- [ ] 安全性测试
- [ ] 用户验收测试
- [ ] 文档完善

#### 交付物
- 性能测试报告
- 安全测试报告
- 用户手册

### 6.4 Phase 4: 上线部署（第6周）

#### 任务清单
- [ ] 生产环境部署准备
- [ ] 数据迁移方案执行
- [ ] 灰度发布
- [ ] 监控告警配置

#### 交付物
- 部署文档
- 运维手册
- 监控大盘

## 7. 风险评估与应对

### 7.1 技术风险

| 风险项 | 影响程度 | 发生概率 | 应对措施 |
|--------|----------|----------|----------|
| 定时任务执行失败 | 高 | 中 | 1. 使用分布式锁防止重复执行<br>2. 增加任务执行监控和告警<br>3. 支持手动触发补偿机制 |
| 大量数据处理性能问题 | 高 | 中 | 1. 分批处理，每批限制数量<br>2. 使用异步处理机制<br>3. 优化数据库索引 |
| 结算金额计算错误 | 高 | 低 | 1. 完善单元测试覆盖<br>2. 增加对账机制<br>3. 保留详细的计算日志 |

### 7.2 业务风险

| 风险项 | 影响程度 | 发生概率 | 应对措施 |
|--------|----------|----------|----------|
| 结算延迟导致投诉 | 中 | 中 | 1. 明确告知结算规则<br>2. 提供结算进度查询<br>3. 支持特殊情况人工处理 |
| 配置错误影响结算 | 高 | 低 | 1. 配置变更需要审批<br>2. 保留配置历史记录<br>3. 支持配置回滚 |

## 8. 监控指标

### 8.1 业务指标
- 每日结算笔数
- 每日结算金额
- 平均结算周期
- 结算成功率
- 投诉率

### 8.2 技术指标
- 定时任务执行时长
- 结算接口响应时间
- 数据库查询性能
- 系统错误率
- 资源使用率

## 9. 总结

本方案通过在 `yt_dist_goods_config` 表中增加结算相关字段，实现了灵活的核销后N天结算功能。主要特点：

1. **灵活配置**：支持按商品个性化配置结算规则
2. **状态管理**：完整的佣金状态流转机制
3. **自动化处理**：定时任务自动处理到期结算
4. **可追溯性**：详细的结算记录和日志
5. **高可用性**：分布式锁和失败重试机制
6. **用户友好**：直观的管理界面和查询功能

通过分阶段实施，可以确保功能的稳定性和可靠性，最终提升分销业务的管理水平和用户体验。

## 附录

### A. 数据字典

#### 结算类型（settlement_type）
- 1: 实时结算
- 2: 核销后结算  
- 3: 订单完成后结算

#### 结算状态（settlement_status）
- 0: 待核销
- 1: 冻结中
- 2: 可结算
- 3: 已结算
- 4: 已失效

#### 批次状态（batch_status）
- 0: 处理中
- 1: 成功
- 2: 部分成功
- 3: 失败

### B. 数据字典Java实现

#### B.1 枚举类定义

```java
package com.yitong.octopus.module.distribution.enums.settlement;

import cn.jianwoo.octopus.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 分销结算类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DistSettlementTypeEnum implements IntArrayValuable {

    REALTIME(1, "实时结算", "订单支付成功后立即结算佣金"),
    AFTER_VERIFICATION(2, "核销后结算", "订单核销后按配置天数结算佣金"),
    AFTER_COMPLETION(3, "订单完成后结算", "订单完成后按配置天数结算佣金");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(DistSettlementTypeEnum::getType).toArray();

    /**
     * 类型值
     */
    private final Integer type;
    
    /**
     * 类型名称
     */
    private final String name;
    
    /**
     * 类型描述
     */
    private final String description;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static DistSettlementTypeEnum valueOf(Integer type) {
        return Arrays.stream(values())
            .filter(item -> item.getType().equals(type))
            .findFirst()
            .orElse(null);
    }

    public static boolean isValid(Integer type) {
        return valueOf(type) != null;
    }
}
```

```java
package com.yitong.octopus.module.distribution.enums.settlement;

import cn.jianwoo.octopus.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 分销佣金结算状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DistSettlementStatusEnum implements IntArrayValuable {

    PENDING_VERIFICATION(0, "待核销", "等待订单核销", "info"),
    FROZEN(1, "冻结中", "佣金冻结中，等待结算周期", "warning"),
    CAN_SETTLE(2, "可结算", "已到结算时间，可以结算", "success"),
    SETTLED(3, "已结算", "佣金已结算到账", "primary"),
    INVALID(4, "已失效", "订单退款或取消，佣金失效", "danger");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(DistSettlementStatusEnum::getStatus).toArray();

    /**
     * 状态值
     */
    private final Integer status;
    
    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 状态描述
     */
    private final String description;
    
    /**
     * 前端标签类型
     */
    private final String tagType;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static DistSettlementStatusEnum valueOf(Integer status) {
        return Arrays.stream(values())
            .filter(item -> item.getStatus().equals(status))
            .findFirst()
            .orElse(null);
    }

    public static boolean isValid(Integer status) {
        return valueOf(status) != null;
    }

    /**
     * 是否可以执行结算
     */
    public boolean canSettle() {
        return this == CAN_SETTLE;
    }

    /**
     * 是否已完成（已结算或已失效）
     */
    public boolean isFinished() {
        return this == SETTLED || this == INVALID;
    }
}
```

```java
package com.yitong.octopus.module.distribution.enums.settlement;

import cn.jianwoo.octopus.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 分销结算批次状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DistBatchStatusEnum implements IntArrayValuable {

    PROCESSING(0, "处理中", "结算批次正在处理"),
    SUCCESS(1, "成功", "结算批次全部成功"),
    PARTIAL_SUCCESS(2, "部分成功", "结算批次部分成功"),
    FAILED(3, "失败", "结算批次处理失败");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(DistBatchStatusEnum::getStatus).toArray();

    /**
     * 状态值
     */
    private final Integer status;
    
    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 状态描述
     */
    private final String description;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static DistBatchStatusEnum valueOf(Integer status) {
        return Arrays.stream(values())
            .filter(item -> item.getStatus().equals(status))
            .findFirst()
            .orElse(null);
    }
}
```

```java
package com.yitong.octopus.module.distribution.enums.settlement;

import cn.jianwoo.octopus.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 分销结算方式枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DistSettlementMethodEnum implements IntArrayValuable {

    AUTO(1, "自动结算", "系统定时任务自动结算"),
    MANUAL(2, "手动结算", "运营人员手动触发结算");

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(DistSettlementMethodEnum::getMethod).toArray();

    /**
     * 方式值
     */
    private final Integer method;
    
    /**
     * 方式名称
     */
    private final String name;
    
    /**
     * 方式描述
     */
    private final String description;

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static DistSettlementMethodEnum valueOf(Integer method) {
        return Arrays.stream(values())
            .filter(item -> item.getMethod().equals(method))
            .findFirst()
            .orElse(null);
    }
}
```

#### B.2 字典类型常量定义

```java
package com.yitong.octopus.module.distribution.enums;

/**
 * 分销模块字典类型常量
 *
 * <AUTHOR>
 */
public interface DistDictTypeConstants {

    // ========== 结算相关字典 ==========
    
    /**
     * 结算类型
     */
    String SETTLEMENT_TYPE = "dist_settlement_type";
    
    /**
     * 结算状态
     */
    String SETTLEMENT_STATUS = "dist_settlement_status";
    
    /**
     * 结算批次状态
     */
    String BATCH_STATUS = "dist_batch_status";
    
    /**
     * 结算方式
     */
    String SETTLEMENT_METHOD = "dist_settlement_method";
    
    // ========== 已有字典补充 ==========
    
    /**
     * 佣金模式
     */
    String COMMISSION_MODE = "dist_commission_mode";
    
    /**
     * 佣金类型
     */
    String COMMISSION_TYPE = "dist_commission_type";
    
    /**
     * 佣金基数类型
     */
    String COMMISSION_BASE_TYPE = "dist_commission_base_type";
}
```

### C. 系统字典数据初始化

#### C.1 SQL脚本

```sql
-- =====================================================
-- 分销结算功能 - 系统字典数据初始化
-- 执行时间: 2025-08-06
-- 功能说明: 初始化结算相关的系统字典数据
-- =====================================================

-- 1. 插入字典类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES 
('分销结算类型', 'dist_settlement_type', 0, '分销佣金结算类型', 'admin', NOW(), 'admin', NOW(), b'0'),
('分销结算状态', 'dist_settlement_status', 0, '分销佣金结算状态', 'admin', NOW(), 'admin', NOW(), b'0'),
('分销批次状态', 'dist_batch_status', 0, '分销结算批次状态', 'admin', NOW(), 'admin', NOW(), b'0'),
('分销结算方式', 'dist_settlement_method', 0, '分销结算方式', 'admin', NOW(), 'admin', NOW(), b'0');

-- 2. 插入字典数据 - 结算类型
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES 
(1, '实时结算', '1', 'dist_settlement_type', 0, 'success', '', '订单支付成功后立即结算佣金', 'admin', NOW(), 'admin', NOW(), b'0'),
(2, '核销后结算', '2', 'dist_settlement_type', 0, 'warning', '', '订单核销后按配置天数结算佣金', 'admin', NOW(), 'admin', NOW(), b'0'),
(3, '订单完成后结算', '3', 'dist_settlement_type', 0, 'info', '', '订单完成后按配置天数结算佣金', 'admin', NOW(), 'admin', NOW(), b'0');

-- 3. 插入字典数据 - 结算状态
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES 
(1, '待核销', '0', 'dist_settlement_status', 0, 'info', '', '等待订单核销', 'admin', NOW(), 'admin', NOW(), b'0'),
(2, '冻结中', '1', 'dist_settlement_status', 0, 'warning', '', '佣金冻结中，等待结算周期', 'admin', NOW(), 'admin', NOW(), b'0'),
(3, '可结算', '2', 'dist_settlement_status', 0, 'success', '', '已到结算时间，可以结算', 'admin', NOW(), 'admin', NOW(), b'0'),
(4, '已结算', '3', 'dist_settlement_status', 0, 'primary', '', '佣金已结算到账', 'admin', NOW(), 'admin', NOW(), b'0'),
(5, '已失效', '4', 'dist_settlement_status', 0, 'danger', '', '订单退款或取消，佣金失效', 'admin', NOW(), 'admin', NOW(), b'0');

-- 4. 插入字典数据 - 批次状态
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES 
(1, '处理中', '0', 'dist_batch_status', 0, 'warning', '', '结算批次正在处理', 'admin', NOW(), 'admin', NOW(), b'0'),
(2, '成功', '1', 'dist_batch_status', 0, 'success', '', '结算批次全部成功', 'admin', NOW(), 'admin', NOW(), b'0'),
(3, '部分成功', '2', 'dist_batch_status', 0, 'warning', '', '结算批次部分成功', 'admin', NOW(), 'admin', NOW(), b'0'),
(4, '失败', '3', 'dist_batch_status', 0, 'danger', '', '结算批次处理失败', 'admin', NOW(), 'admin', NOW(), b'0');

-- 5. 插入字典数据 - 结算方式
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES 
(1, '自动结算', '1', 'dist_settlement_method', 0, 'primary', '', '系统定时任务自动结算', 'admin', NOW(), 'admin', NOW(), b'0'),
(2, '手动结算', '2', 'dist_settlement_method', 0, 'success', '', '运营人员手动触发结算', 'admin', NOW(), 'admin', NOW(), b'0');

-- 6. 补充已有字典（如果不存在）
-- 佣金模式
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
SELECT '分销佣金模式', 'dist_commission_mode', 0, '分销佣金计算模式', 'admin', NOW(), 'admin', NOW(), b'0'
FROM DUAL WHERE NOT EXISTS (SELECT 1 FROM `system_dict_type` WHERE `type` = 'dist_commission_mode');

INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
SELECT * FROM (
    SELECT 1 as sort, '按比例' as label, '1' as value, 'dist_commission_mode' as dict_type, 0 as status, 'primary' as color_type, '' as css_class, '按商品价格百分比计算' as remark, 'admin' as creator, NOW() as create_time, 'admin' as updater, NOW() as update_time, b'0' as deleted
    UNION ALL
    SELECT 2, '固定金额', '2', 'dist_commission_mode', 0, 'success', '', '固定佣金金额', 'admin', NOW(), 'admin', NOW(), b'0'
) t WHERE NOT EXISTS (SELECT 1 FROM `system_dict_data` WHERE `dict_type` = 'dist_commission_mode' AND `value` = t.value);

-- 7. 验证字典数据
SELECT 
    dt.name AS dict_name,
    dt.type AS dict_type,
    dd.label,
    dd.value,
    dd.color_type,
    dd.remark
FROM `system_dict_type` dt
LEFT JOIN `system_dict_data` dd ON dt.type = dd.dict_type
WHERE dt.type IN (
    'dist_settlement_type',
    'dist_settlement_status',
    'dist_batch_status',
    'dist_settlement_method',
    'dist_commission_mode'
)
ORDER BY dt.type, dd.sort;
```

### D. 前端页面字典集成

#### D.1 字典工具类

```typescript
// src/utils/dict.ts
export const DICT_TYPE = {
  // ... 已有字典类型
  
  // ========== 分销结算相关 ==========
  DIST_SETTLEMENT_TYPE: 'dist_settlement_type', // 结算类型
  DIST_SETTLEMENT_STATUS: 'dist_settlement_status', // 结算状态
  DIST_BATCH_STATUS: 'dist_batch_status', // 批次状态
  DIST_SETTLEMENT_METHOD: 'dist_settlement_method', // 结算方式
  DIST_COMMISSION_MODE: 'dist_commission_mode', // 佣金模式
  DIST_COMMISSION_TYPE: 'dist_commission_type', // 佣金类型
  DIST_COMMISSION_BASE_TYPE: 'dist_commission_base_type', // 佣金基数类型
}
```

#### D.2 更新后的前端组件

```vue
<!-- SettlementConfigDrawer.vue 更新部分 -->
<script setup lang="ts">
import { DICT_TYPE, getDictOptions } from '@/utils/dict'

// ... 其他代码

// 获取字典数据
const settlementTypeOptions = ref([])
const commissionModeOptions = ref([])

onMounted(async () => {
  // 加载字典数据
  settlementTypeOptions.value = await getDictOptions(DICT_TYPE.DIST_SETTLEMENT_TYPE)
  commissionModeOptions.value = await getDictOptions(DICT_TYPE.DIST_COMMISSION_MODE)
})
</script>

<template>
  <!-- 佣金模式使用字典 -->
  <el-form-item label="佣金模式" prop="commissionMode">
    <el-radio-group v-model="formData.commissionMode">
      <el-radio 
        v-for="dict in commissionModeOptions"
        :key="dict.value"
        :label="parseInt(dict.value)">
        {{ dict.label }}
      </el-radio>
    </el-radio-group>
  </el-form-item>

  <!-- 结算类型使用字典 -->
  <el-form-item label="结算类型" prop="settlementType">
    <el-radio-group 
      v-model="formData.settlementType" 
      @change="onSettlementTypeChange">
      <el-radio-button 
        v-for="dict in settlementTypeOptions"
        :key="dict.value"
        :label="parseInt(dict.value)">
        <Icon :icon="getSettlementIcon(dict.value)" class="mr-5px" />
        {{ dict.label }}
      </el-radio-button>
    </el-radio-group>
  </el-form-item>

  <!-- 结算状态展示使用字典 -->
  <el-descriptions-item label="结算类型">
    <dict-tag :type="DICT_TYPE.DIST_SETTLEMENT_TYPE" :value="formData.settlementType" />
  </el-descriptions-item>
</template>
```

```vue
<!-- settlement/index.vue 更新部分 -->
<script setup lang="ts">
import { DICT_TYPE, getDictOptions, getDictLabel } from '@/utils/dict'
import DictTag from '@/components/DictTag/index.vue'

// 获取字典数据
const settlementStatusOptions = ref([])

onMounted(async () => {
  settlementStatusOptions.value = await getDictOptions(DICT_TYPE.DIST_SETTLEMENT_STATUS)
  // ... 其他初始化
})

// 使用字典获取文本
const getSettlementStatusText = (status: number) => {
  return getDictLabel(DICT_TYPE.DIST_SETTLEMENT_STATUS, status)
}
</script>

<template>
  <!-- 搜索表单使用字典 -->
  <el-form-item label="结算状态" prop="settlementStatus">
    <el-select
      v-model="queryParams.settlementStatus"
      placeholder="请选择"
      clearable
      class="!w-200px">
      <el-option 
        v-for="dict in settlementStatusOptions"
        :key="dict.value"
        :label="dict.label"
        :value="parseInt(dict.value)" />
    </el-select>
  </el-form-item>

  <!-- 表格中使用字典标签 -->
  <el-table-column label="结算状态" align="center" prop="settlementStatus" width="100">
    <template #default="scope">
      <dict-tag 
        :type="DICT_TYPE.DIST_SETTLEMENT_STATUS" 
        :value="scope.row.settlementStatus" />
    </template>
  </el-table-column>
</template>
```

### E. SQL脚本汇总

所有数据库变更脚本已整理在 `/sql/mysql/settlement/` 目录下：
- `01_add_settlement_fields.sql` - 添加结算字段
- `02_create_settlement_tables.sql` - 创建结算相关表
- `03_init_settlement_data.sql` - 初始化测试数据
- `04_rollback_settlement.sql` - 回滚脚本
- `05_init_dict_data.sql` - 初始化系统字典数据