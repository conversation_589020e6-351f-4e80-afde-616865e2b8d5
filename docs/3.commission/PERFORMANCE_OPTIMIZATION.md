# 分销佣金统计性能优化方案

## 优化概述
通过使用 SQL 聚合查询替代 Java 代码中的 for 循环，大幅提升佣金统计方法的性能。

## 优化的方法

### 1. getCommissionStatistics 方法
- **原实现**：使用 for 循环遍历所有佣金记录进行统计
- **优化后**：使用 SQL 聚合函数（SUM、COUNT、CASE WHEN）一次查询获取所有统计数据
- **性能提升**：从 O(n) 降低到 O(1)，数据库层面优化

### 2. getMyCommissionStatistics 方法  
- **原实现**：使用 for 循环遍历佣金记录，按状态、时间进行统计
- **优化后**：使用 SQL 聚合查询，通过 CASE WHEN 和日期函数实现条件统计
- **性能提升**：减少数据传输量，利用数据库索引加速

### 3. getCommissionRanking 方法
- **原实现**：使用 for 循环 + Map 进行分组统计
- **优化后**：使用 SQL GROUP BY + JOIN 查询，一次获取排行榜数据
- **性能提升**：避免多次查询和内存计算，利用数据库优化器

## 新增的 SQL 查询

### 1. selectMyCommissionStatistics
```sql
-- 统计个人佣金数据，包括总额、待结算、今日、本月等
SELECT 
    COALESCE(SUM(commission_amount), 0) AS totalCommission,
    COALESCE(SUM(CASE WHEN status = 0 THEN commission_amount ELSE 0 END), 0) AS pendingCommission,
    COALESCE(SUM(CASE WHEN DATE(create_time) = CURDATE() THEN commission_amount ELSE 0 END), 0) AS todayCommission,
    COUNT(DISTINCT CASE WHEN DATE(create_time) = CURDATE() THEN order_id END) AS todayOrderCount,
    -- 更多统计...
FROM yt_dist_commission
WHERE deleted = 0 AND agent_id = #{agentId}
```

### 2. selectCommissionRankingData
```sql
-- 佣金排行榜数据，使用 GROUP BY 聚合
SELECT 
    dc.agent_id AS agentId,
    da.invite_code AS agentCode,
    COALESCE(SUM(dc.commission_amount), 0) AS commissionAmount,
    COUNT(DISTINCT dc.order_id) AS orderCount,
    COUNT(DISTINCT dc.member_id) AS customerCount
FROM yt_dist_commission dc
INNER JOIN yt_dist_agent da ON dc.agent_id = da.id
LEFT JOIN yt_dist_level dl ON da.level_id = dl.id
WHERE dc.deleted = 0
GROUP BY dc.agent_id, da.invite_code, da.mobile, da.level_id
ORDER BY commissionAmount DESC
LIMIT #{limit}
```

### 3. selectCommissionTrendData
```sql
-- 佣金趋势数据，按日期分组
SELECT 
    DATE(create_time) AS date,
    COALESCE(SUM(commission_amount), 0) AS amount,
    COUNT(DISTINCT order_id) AS orderCount,
    COUNT(DISTINCT agent_id) AS agentCount
FROM yt_dist_commission
WHERE deleted = 0
GROUP BY DATE(create_time)
ORDER BY date ASC
```

### 4. selectCommissionTypeStatistics
```sql
-- 佣金类型分布统计
SELECT 
    1 AS type,
    '直推佣金' AS typeName,
    COALESCE(SUM(commission_amount), 0) AS amount,
    COUNT(*) AS count,
    -- 计算百分比
    CASE 
        WHEN SUM(commission_amount) > 0 
        THEN ROUND(SUM(commission_amount) * 100.0 / (子查询获取总额), 2)
        ELSE 0 
    END AS percentage
FROM yt_dist_commission
WHERE deleted = 0
```

## 性能优化效果

### 优化前
- **问题**：
  1. 查询所有数据到内存，数据传输量大
  2. Java 层面循环处理，CPU 密集
  3. 多次查询数据库，网络开销大
  4. 内存占用高，可能导致 OOM

### 优化后
- **优势**：
  1. 数据库层面聚合，减少网络传输
  2. 利用数据库索引和优化器
  3. 单次查询获取所有统计数据
  4. 内存占用极小，仅传输聚合结果

### 性能测试预期
- 数据量 1万条：性能提升 10-20 倍
- 数据量 10万条：性能提升 50-100 倍
- 数据量 100万条：性能提升 100-500 倍

## 建议的索引优化

为了进一步提升查询性能，建议添加以下索引：

```sql
-- 佣金统计常用索引
CREATE INDEX idx_commission_agent_status ON yt_dist_commission(agent_id, status, create_time);
CREATE INDEX idx_commission_app_create ON yt_dist_commission(app_id, create_time);
CREATE INDEX idx_commission_create_date ON yt_dist_commission(DATE(create_time));

-- 如果经常按订单查询
CREATE INDEX idx_commission_order ON yt_dist_commission(order_id);
```

## 注意事项

1. **数据一致性**：确保 SQL 统计逻辑与原 Java 逻辑完全一致
2. **空值处理**：使用 COALESCE 处理可能的 NULL 值
3. **类型转换**：在 Service 层添加了安全的类型转换方法
4. **时区问题**：注意 MySQL 的时区设置与应用保持一致

## 后续优化建议

1. **缓存优化**：对频繁查询的统计数据添加 Redis 缓存
2. **异步统计**：使用定时任务预先计算统计数据
3. **分区表**：对大数据量的佣金表进行分区
4. **读写分离**：统计查询走从库，减轻主库压力