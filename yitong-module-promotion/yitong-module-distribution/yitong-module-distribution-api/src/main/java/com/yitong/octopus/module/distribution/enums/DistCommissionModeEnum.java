package com.yitong.octopus.module.distribution.enums;

import com.yitong.octopus.framework.common.core.EnumKeyArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 分销佣金模式枚举
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Getter
public enum DistCommissionModeEnum implements EnumKeyArrayValuable {

    FIXED_AMOUNT(1, "固定金额"),
    PERCENTAGE(2, "百分比"),
    TIERED(3, "阶梯式");

    public static final Object[] ARRAYS = Arrays.stream(values()).map(DistCommissionModeEnum::getMode).toArray();

    /**
     * 模式值
     */
    private final Integer mode;
    /**
     * 模式名
     */
    private final String name;

    @Override
    public Object[] array() {
        return ARRAYS;
    }
}