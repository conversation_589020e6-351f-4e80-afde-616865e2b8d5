package com.yitong.octopus.module.distribution.enums;

import com.yitong.octopus.framework.common.core.EnumKeyArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 分销佣金类型枚举
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Getter
public enum DistCommissionTypeEnum implements EnumKeyArrayValuable {

    /**
     * 统一佣金
     * 所有等级使用相同的佣金配置，支持多级佣金（一级、二级、三级）
     */
    UNIFIED(1, "统一佣金"),
    
    /**
     * 按等级差异化佣金
     * 不同等级可以设置不同的佣金配置
     */
    LEVEL_BASED(2, "按等级差异化佣金");

    public static final Object[] ARRAYS = Arrays.stream(values()).map(DistCommissionTypeEnum::getType).toArray();

    /**
     * 类型值
     */
    private final Integer type;
    
    /**
     * 类型名称
     */
    private final String name;

    @Override
    public Object[] array() {
        return ARRAYS;
    }

    /**
     * 根据类型值获取枚举
     *
     * @param type 类型值
     * @return 枚举对象
     */
    public static DistCommissionTypeEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (DistCommissionTypeEnum item : values()) {
            if (item.getType().equals(type)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 判断是否为统一佣金类型
     *
     * @param type 类型值
     * @return 是否为统一佣金
     */
    public static boolean isUnified(Integer type) {
        return UNIFIED.getType().equals(type);
    }

    /**
     * 判断是否为按等级差异化佣金类型
     *
     * @param type 类型值
     * @return 是否为按等级差异化佣金
     */
    public static boolean isLevelBased(Integer type) {
        return LEVEL_BASED.getType().equals(type);
    }
}