package com.yitong.octopus.module.distribution.enums;

import com.yitong.octopus.framework.common.core.EnumKeyArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 分销员状态枚举
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Getter
public enum DistAgentStatusEnum implements EnumKeyArrayValuable {

    PENDING(0, "待审核"),
    ACTIVE(1, "正常"),
    FROZEN(2, "冻结"),
    CANCELLED(3, "已注销"),
    REJECTED(4, "已拒绝");

    public static final Object[] ARRAYS = Arrays.stream(values()).map(DistAgentStatusEnum::getStatus).toArray();

    /**
     * 状态值
     */
    private final Integer status;
    /**
     * 状态名
     */
    private final String name;

    @Override
    public Object[] array() {
        return ARRAYS;
    }
    
    /**
     * 根据状态获取枚举
     * 
     * @param status 状态值
     * @return 枚举实例
     */
    public static DistAgentStatusEnum getByStatus(Integer status) {
        if (status == null) {
            return null;
        }
        return Arrays.stream(values()).filter(e -> e.getStatus().equals(status)).findFirst().orElse(null);
    }
    
    /**
     * 判断状态是否有效
     * 
     * @param status 状态值
     * @return 是否有效
     */
    public static boolean isValid(Integer status) {
        if (status == null) {
            return false;
        }
        return Arrays.stream(values()).anyMatch(e -> e.getStatus().equals(status));
    }
}