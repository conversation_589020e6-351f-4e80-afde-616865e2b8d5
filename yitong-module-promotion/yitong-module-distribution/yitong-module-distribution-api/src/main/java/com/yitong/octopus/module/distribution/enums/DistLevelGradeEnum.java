package com.yitong.octopus.module.distribution.enums;

import com.yitong.octopus.framework.common.core.EnumKeyArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 分销等级级别枚举
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Getter
public enum DistLevelGradeEnum implements EnumKeyArrayValuable {

    BRONZE(1, "青铜"),
    SILVER(2, "白银"),
    GOLD(3, "黄金"),
    PLATINUM(4, "铂金"),
    DIAMOND(5, "钻石");

    public static final Object[] ARRAYS = Arrays.stream(values()).map(DistLevelGradeEnum::getGrade).toArray();

    /**
     * 等级值
     */
    private final Integer grade;
    /**
     * 等级名
     */
    private final String name;

    @Override
    public Object[] array() {
        return ARRAYS;
    }
}