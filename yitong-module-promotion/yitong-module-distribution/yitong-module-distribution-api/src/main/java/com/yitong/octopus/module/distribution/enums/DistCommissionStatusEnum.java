package com.yitong.octopus.module.distribution.enums;

import com.yitong.octopus.framework.common.core.EnumKeyArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 分销佣金状态枚举
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Getter
public enum DistCommissionStatusEnum implements EnumKeyArrayValuable {

    PENDING(0, "待结算"),
    FROZEN(1, "已冻结"),
    SETTLED(2, "已结算"),
    WITHDRAWN(3, "已提现"),
    CANCELLED(4, "已取消");

    public static final Object[] ARRAYS = Arrays.stream(values()).map(DistCommissionStatusEnum::getStatus).toArray();

    /**
     * 状态值
     */
    private final Integer status;
    /**
     * 状态名
     */
    private final String name;

    @Override
    public Object[] array() {
        return ARRAYS;
    }
    
    /**
     * 根据状态获取枚举
     * 
     * @param status 状态值
     * @return 枚举实例
     */
    public static DistCommissionStatusEnum getByStatus(Integer status) {
        if (status == null) {
            return null;
        }
        return Arrays.stream(values()).filter(e -> e.getStatus().equals(status)).findFirst().orElse(null);
    }
    
    /**
     * 判断状态是否有效
     * 
     * @param status 状态值
     * @return 是否有效
     */
    public static boolean isValid(Integer status) {
        if (status == null) {
            return false;
        }
        return Arrays.stream(values()).anyMatch(e -> e.getStatus().equals(status));
    }
    
    /**
     * 是否待结算
     */
    public boolean isPending() {
        return this == PENDING;
    }
    
    /**
     * 是否已冻结
     */
    public boolean isFrozen() {
        return this == FROZEN;
    }
    
    /**
     * 是否已结算
     */
    public boolean isSettled() {
        return this == SETTLED;
    }
    
    /**
     * 是否已提现
     */
    public boolean isWithdrawn() {
        return this == WITHDRAWN;
    }
    
    /**
     * 是否已取消
     */
    public boolean isCancelled() {
        return this == CANCELLED;
    }
}