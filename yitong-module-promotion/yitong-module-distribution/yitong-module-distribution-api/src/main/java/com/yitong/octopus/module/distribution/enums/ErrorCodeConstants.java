package com.yitong.octopus.module.distribution.enums;

import com.yitong.octopus.framework.common.exception.ErrorCode;

/**
 * 分销模块错误码枚举类
 *
 * 分销模块，使用 1-035-000-000 段
 *
 * <AUTHOR>
 */
public interface ErrorCodeConstants {

    // ========== 分销员 1-035-001-000 ==========
    ErrorCode DIST_AGENT_NOT_EXISTS = new ErrorCode(1_035_001_000, "分销员不存在");
    ErrorCode DIST_AGENT_ALREADY_EXISTS = new ErrorCode(1_035_001_001, "该会员已经是分销员");
    ErrorCode DIST_AGENT_STATUS_ERROR = new ErrorCode(1_035_001_002, "分销员状态异常");
    ErrorCode DIST_AGENT_FROZEN = new ErrorCode(1_035_001_003, "分销员已被冻结");
    ErrorCode DIST_AGENT_CANCELLED = new ErrorCode(1_035_001_004, "分销员已注销");
    ErrorCode DIST_AGENT_PARENT_NOT_EXISTS = new ErrorCode(1_035_001_005, "上级分销员不存在");
    ErrorCode DIST_AGENT_PARENT_STATUS_ERROR = new ErrorCode(1_035_001_006, "上级分销员状态异常");
    ErrorCode DIST_AGENT_INVITE_CODE_EXISTS = new ErrorCode(1_035_001_007, "邀请码已存在");
    ErrorCode DIST_AGENT_CANNOT_BE_SELF_PARENT = new ErrorCode(1_035_001_008, "不能成为自己的上级");
    ErrorCode DIST_AGENT_TEAM_LEVEL_EXCEEDED = new ErrorCode(1_035_001_009, "团队层级超过限制");
    ErrorCode DIST_AGENT_PARENT_EXISTS = new ErrorCode(1_035_001_010, "已有上级分销员，不能重新绑定");
    ErrorCode DIST_AGENT_INVITE_CODE_INVALID = new ErrorCode(1_035_001_011, "邀请码无效");
    ErrorCode DIST_AGENT_CANNOT_BIND_SELF = new ErrorCode(1_035_001_012, "不能绑定自己为上级");
    ErrorCode DIST_AGENT_CIRCULAR_RELATION = new ErrorCode(1_035_001_013, "不能形成循环关系");
    ErrorCode DIST_AGENT_LEVEL_NOT_SUPPORTED = new ErrorCode(1_035_001_014, "不支持的团队层级");
    ErrorCode DIST_AGENT_HAS_COMMISSION = new ErrorCode(1_035_001_015, "分销员还有未提现的佣金");
    ErrorCode DIST_AGENT_HAS_TEAM = new ErrorCode(1_035_001_016, "分销员还有下级团队成员");
    ErrorCode DIST_AGENT_ALREADY_FROZEN = new ErrorCode(1_035_001_017, "分销员已被冻结");
    ErrorCode DIST_AGENT_LEVEL_SAME = new ErrorCode(1_035_001_018, "新等级与当前等级相同，无需调整");
    ErrorCode DIST_AGENT_STATUS_NOT_ALLOW_ADJUST = new ErrorCode(1_035_001_019, "只有正常状态的分销员才能调整等级");
    ErrorCode DIST_AGENT_NOT_FROZEN = new ErrorCode(1_035_001_020, "分销员未被冻结");
    ErrorCode AGENT_NOT_EXISTS = new ErrorCode(1_035_001_021, "分销员不存在");
    ErrorCode AGENT_ALREADY_HAS_INVITER = new ErrorCode(1_035_001_022, "分销员已有邀请人，不能修改");
    ErrorCode INVITER_NOT_EXISTS = new ErrorCode(1_035_001_023, "邀请人不存在");
    ErrorCode INVITER_STATUS_ERROR = new ErrorCode(1_035_001_024, "邀请人状态异常");
    ErrorCode INVITER_APP_NOT_MATCH = new ErrorCode(1_035_001_025, "邀请人与分销员不在同一应用");

    // ========== 分销等级 1-035-002-000 ==========
    ErrorCode DIST_LEVEL_NOT_EXISTS = new ErrorCode(1_035_002_000, "分销等级不存在");
    ErrorCode DIST_LEVEL_CODE_EXISTS = new ErrorCode(1_035_002_001, "等级编码已存在");
    ErrorCode DIST_LEVEL_GRADE_EXISTS = new ErrorCode(1_035_002_002, "等级级别已存在");
    ErrorCode DIST_LEVEL_DEFAULT_EXISTS = new ErrorCode(1_035_002_003, "默认等级已存在");
    ErrorCode DIST_LEVEL_CANNOT_DELETE_DEFAULT = new ErrorCode(1_035_002_004, "不能删除默认等级");
    ErrorCode DIST_LEVEL_CANNOT_DELETE_IN_USE = new ErrorCode(1_035_002_005, "等级正在使用中，不能删除");

    // ========== 分销佣金 1-035-003-000 ==========
    ErrorCode DIST_COMMISSION_NOT_EXISTS = new ErrorCode(1_035_003_000, "佣金记录不存在");
    ErrorCode DIST_COMMISSION_STATUS_ERROR = new ErrorCode(1_035_003_001, "佣金状态异常");
    ErrorCode DIST_COMMISSION_ALREADY_SETTLED = new ErrorCode(1_035_003_002, "佣金已结算");
    ErrorCode DIST_COMMISSION_ALREADY_WITHDRAWN = new ErrorCode(1_035_003_003, "佣金已提现");
    ErrorCode DIST_COMMISSION_AMOUNT_ERROR = new ErrorCode(1_035_003_004, "佣金金额异常");
    ErrorCode DIST_COMMISSION_INSUFFICIENT_BALANCE = new ErrorCode(1_035_003_005, "可用佣金余额不足");

    // ========== 商品配置 1-035-004-000 ==========
    ErrorCode DIST_GOODS_CONFIG_NOT_EXISTS = new ErrorCode(1_035_004_000, "商品分销配置不存在");
    ErrorCode DIST_GOODS_CONFIG_EXISTS = new ErrorCode(1_035_004_001, "商品分销配置已存在");
    ErrorCode DIST_GOODS_CONFIG_DISABLED = new ErrorCode(1_035_004_002, "商品未开启分销");
    ErrorCode DIST_GOODS_CONFIG_STOCK_NOT_ENOUGH = new ErrorCode(1_035_004_003, "分销库存不足");
    ErrorCode DIST_GOODS_CONFIG_COMMISSION_ERROR = new ErrorCode(1_035_004_004, "佣金配置错误");
    ErrorCode DIST_COMMISSION_RATE_INVALID = new ErrorCode(1_035_004_005, "佣金比例必须在0-100之间");
    ErrorCode DIST_COMMISSION_RATE_EXCEED = new ErrorCode(1_035_004_006, "佣金比例总和不能超过100%");
    ErrorCode DIST_GOODS_CONFIG_ALREADY_EXISTS = new ErrorCode(1_035_004_007, "以下商品已存在分销配置，请使用修改功能：{}");
    ErrorCode DIST_GOODS_CONFIG_NOT_EXISTS_BATCH = new ErrorCode(1_035_004_008, "以下商品未配置分销，请先配置：{}");
    ErrorCode DIST_COMMISSION_AMOUNT_INVALID = new ErrorCode(1_035_004_009, "佣金金额必须大于0");
    ErrorCode DIST_GOODS_CONFIG_PERIOD_OVERLAP = new ErrorCode(1_035_004_010, "商品ID {} 在该有效期内已存在分销配置，有效期不能重叠");
    ErrorCode DIST_GOODS_CONFIG_COMMISSION_TYPE_NOT_NULL = new ErrorCode(1_035_004_011, "佣金类型不能为空");
    ErrorCode DIST_GOODS_CONFIG_COMMISSION_VALUE_INVALID = new ErrorCode(1_035_004_012, "统一佣金值必须大于0");
    ErrorCode DIST_GOODS_CONFIG_LEVEL_CONFIGS_EMPTY = new ErrorCode(1_035_004_013, "按等级差异化佣金时，等级配置不能为空");
    ErrorCode DIST_GOODS_CONFIG_LEVEL_COMMISSION_INVALID = new ErrorCode(1_035_004_014, "等级[{}]的佣金值必须大于0");
    ErrorCode DIST_GOODS_CONFIG_COMMISSION_TYPE_INVALID = new ErrorCode(1_035_004_015, "佣金类型值必须为1或2");
    ErrorCode DIST_GOODS_CONFIG_LEVEL_CONFIG_REQUIRED = new ErrorCode(1_035_004_016, "切换到按等级差异化佣金时，系统中必须存在启用的分销等级");
    ErrorCode DIST_GOODS_CONFIG_UNIFIED_COMMISSION_NO_LEVEL_CONFIG = new ErrorCode(1_035_004_017, "统一佣金模式下不能设置等级配置");
    ErrorCode DIST_GOODS_CONFIG_FIRST_COMMISSION_EXCLUSIVE = new ErrorCode(1_035_004_018, "一级佣金比例和金额只能设置其中一个");
    ErrorCode DIST_GOODS_CONFIG_SECOND_COMMISSION_EXCLUSIVE = new ErrorCode(1_035_004_019, "二级佣金比例和金额只能设置其中一个");
    ErrorCode DIST_GOODS_CONFIG_THIRD_COMMISSION_EXCLUSIVE = new ErrorCode(1_035_004_020, "三级佣金比例和金额只能设置其中一个");
    ErrorCode DIST_GOODS_CONFIG_TOTAL_RATE_EXCEED = new ErrorCode(1_035_004_021, "佣金比例总和不能超过100%");
    ErrorCode DIST_GOODS_CONFIG_FIRST_COMMISSION_REQUIRED = new ErrorCode(1_035_004_022, "统一佣金模式下必须设置一级佣金");

    // ========== 提现申请 1-035-005-000 ==========
    ErrorCode DIST_WITHDRAW_NOT_EXISTS = new ErrorCode(1_035_005_000, "提现记录不存在");
    ErrorCode DIST_WITHDRAW_STATUS_ERROR = new ErrorCode(1_035_005_001, "提现状态异常");
    ErrorCode DIST_WITHDRAW_ALREADY_PROCESSED = new ErrorCode(1_035_005_002, "提现已处理");
    ErrorCode DIST_WITHDRAW_AMOUNT_TOO_LOW = new ErrorCode(1_035_005_003, "提现金额低于最低限额");
    ErrorCode DIST_WITHDRAW_AMOUNT_TOO_HIGH = new ErrorCode(1_035_005_004, "提现金额超过最高限额");
    ErrorCode DIST_WITHDRAW_FREQUENCY_EXCEEDED = new ErrorCode(1_035_005_005, "提现频率超过限制");
    ErrorCode DIST_WITHDRAW_ACCOUNT_ERROR = new ErrorCode(1_035_005_006, "提现账户信息错误");

    // ========== 分销员标签 1-035-006-000 ==========
    ErrorCode DIST_AGENT_TAG_NOT_EXISTS = new ErrorCode(1_035_006_000, "分销员标签不存在");
    ErrorCode DIST_AGENT_TAG_NAME_EXISTS = new ErrorCode(1_035_006_001, "标签名称已存在");
    ErrorCode DIST_AGENT_TAG_MERGE_SOURCE_CONTAINS_TARGET = new ErrorCode(1_035_006_002, "源标签不能包含目标标签");
    ErrorCode DIST_AGENT_TAG_STATUS_ERROR = new ErrorCode(1_035_006_003, "标签状态异常，无法合并");

    // ========== 奖励方案配置 1-035-007-000 ==========
    ErrorCode REWARD_LEVEL_CONFIG_NOT_EXISTS = new ErrorCode(1_035_007_000, "等级配置不存在");
    ErrorCode REWARD_TAG_CONFIG_NOT_EXISTS = new ErrorCode(1_035_007_001, "标签配置不存在");
    ErrorCode REWARD_SCHEME_NOT_EXISTS = new ErrorCode(1_035_007_002, "奖励方案不存在");
    ErrorCode REWARD_SCHEME_NAME_EXISTS = new ErrorCode(1_035_007_003, "奖励方案名称已存在");
    ErrorCode REWARD_SCHEME_IN_USE = new ErrorCode(1_035_007_004, "奖励方案正在使用中，不能删除");
    ErrorCode REWARD_SCHEME_TIME_RANGE_ERROR = new ErrorCode(1_035_007_005, "奖励方案时间范围错误");

    // ========== 海报管理 1-035-008-000 ==========
    ErrorCode POSTER_TEMPLATE_NOT_EXISTS = new ErrorCode(1_035_008_000, "海报模板不存在");
    ErrorCode POSTER_TEMPLATE_NAME_EXISTS = new ErrorCode(1_035_008_001, "海报模板名称已存在");
    ErrorCode AGENT_POSTER_NOT_EXISTS = new ErrorCode(1_035_008_002, "分销员海报记录不存在");

    // ========== 结算管理 1-035-009-000 ==========
    ErrorCode DIST_SETTLEMENT_BATCH_NOT_EXISTS = new ErrorCode(1_035_009_000, "结算批次不存在");
    ErrorCode DIST_SETTLEMENT_BATCH_FAILED = new ErrorCode(1_035_009_001, "结算批次处理失败");
    ErrorCode DIST_COMMISSION_IDS_EMPTY = new ErrorCode(1_035_009_002, "请选择要结算的佣金记录");

}