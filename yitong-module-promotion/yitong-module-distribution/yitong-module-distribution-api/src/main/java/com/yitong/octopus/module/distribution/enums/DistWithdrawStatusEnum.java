package com.yitong.octopus.module.distribution.enums;

import com.yitong.octopus.framework.common.core.EnumKeyArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 分销提现状态枚举
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Getter
public enum DistWithdrawStatusEnum implements EnumKeyArrayValuable {

    PENDING(0, "待审核"),
    APPROVED(1, "审核通过"),
    REJECTED(2, "审核拒绝"),
    PROCESSING(3, "打款中"),
    TRANSFERRED(4, "已转账"),
    FAILED(5, "打款失败"),
    CANCELLED(6, "已取消");

    public static final Object[] ARRAYS = Arrays.stream(values()).map(DistWithdrawStatusEnum::getStatus).toArray();

    /**
     * 状态值
     */
    private final Integer status;
    /**
     * 状态名
     */
    private final String name;

    @Override
    public Object[] array() {
        return ARRAYS;
    }
}