package com.yitong.octopus.module.distribution.enums;

import com.yitong.octopus.framework.common.core.EnumKeyArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 分销佣金计算模式枚举
 * 用于定义佣金的计算方式
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Getter
public enum DistCommissionCalculationModeEnum implements EnumKeyArrayValuable {

    PERCENTAGE(1, "百分比模式"),
    FIXED_AMOUNT(2, "固定金额模式");

    public static final Object[] ARRAYS = Arrays.stream(values()).map(DistCommissionCalculationModeEnum::getMode).toArray();

    /**
     * 模式值
     */
    private final Integer mode;
    /**
     * 模式名
     */
    private final String name;

    @Override
    public Object[] array() {
        return ARRAYS;
    }

    /**
     * 根据模式值获取枚举
     *
     * @param mode 模式值
     * @return 枚举对象
     */
    public static DistCommissionCalculationModeEnum valueOf(Integer mode) {
        for (DistCommissionCalculationModeEnum modeEnum : values()) {
            if (modeEnum.getMode().equals(mode)) {
                return modeEnum;
            }
        }
        return null;
    }

    /**
     * 判断是否为百分比模式
     *
     * @param mode 模式值
     * @return 是否为百分比模式
     */
    public static boolean isPercentageMode(Integer mode) {
        return PERCENTAGE.getMode().equals(mode);
    }

    /**
     * 判断是否为固定金额模式
     *
     * @param mode 模式值
     * @return 是否为固定金额模式
     */
    public static boolean isFixedAmountMode(Integer mode) {
        return FIXED_AMOUNT.getMode().equals(mode);
    }
}