<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yitong.octopus</groupId>
        <artifactId>yitong-module-distribution</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>yitong-module-distribution-api</artifactId>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>
    <description>
        分销模块 API - 提供分销系统的对外接口定义、枚举、DTO等
    </description>
    <dependencies>
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-web</artifactId>
        </dependency>
    </dependencies>
</project>