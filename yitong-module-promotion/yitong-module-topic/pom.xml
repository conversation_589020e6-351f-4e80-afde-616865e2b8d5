<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yitong.octopus</groupId>
        <artifactId>yitong-module-promotion</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>yitong-module-topic</artifactId>
    <packaging>pom</packaging> <!-- 2. 新增 packaging 为 pom -->

    <name>${project.artifactId}</name> <!-- 3. 新增 name 为 ${project.artifactId} -->
    <description> <!-- 4. 新增 description 为该模块的描述 -->
        平台活动
    </description>
    <modules>
        <module>yitong-module-topic-api</module>
        <module>yitong-module-topic-biz</module>
    </modules>

</project>