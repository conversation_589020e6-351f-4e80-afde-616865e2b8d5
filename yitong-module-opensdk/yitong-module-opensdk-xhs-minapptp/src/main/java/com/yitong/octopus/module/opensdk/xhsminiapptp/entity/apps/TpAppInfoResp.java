package com.yitong.octopus.module.opensdk.xhsminiapptp.entity.apps;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * {
 *   "data": {
 *     "invitation_url": "INVITATION_URL"
 *   },
 *   "success": true,
 *   "msg": "success",
 *   "code": 0
 * }
 */
@Data
public class TpAppInfoResp {

    @JsonProperty("name")
    private String name;

    @JsonProperty("desc")
    private String desc;

    @JsonProperty("icon")
    private String icon;

    @JsonProperty("principal_name")
    private String principalName;

    @JsonProperty("principal_type")
    private Integer principalType;

    @JsonProperty("principal_license_no")
    private String principalLicenseNo;

    @JsonProperty("principal_second_trade_name")
    private String principalSecondTradeName;

    @JsonProperty("trade_ability")
    private Integer tradeAbility;

    @JsonProperty("app_category")
    private AppCategory[] appCategories;

    @JsonProperty("appid")
    private String appid;

    @JsonProperty("app_category_desc")
    private String appCategoryDesc;

    @Data
    public static class AppCategory {

        @JsonProperty("path")
        private String path;

        @JsonProperty("category_id")
        private String categoryId;

        @JsonProperty("require_claim_store")
        private boolean requireClaimStore;

        @JsonProperty("support_trade")
        private boolean supportTrade;

    }

}
