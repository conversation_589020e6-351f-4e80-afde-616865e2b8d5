package com.yitong.octopus.module.opensdk.xhsminiapptp.client;

import com.dtflys.forest.annotation.Post;
import com.yitong.octopus.module.opensdk.xhsbase.forest.converter.APIResultJacksonConverter;
import com.yitong.octopus.module.opensdk.xhsminiapptp.entity.apps.TpGetInvitationUrlResp;

import static com.yitong.octopus.module.opensdk.xhsminiapptp.constant.TpOpenSdkConstant.TP_APP_INFO_URL;
import static com.yitong.octopus.module.opensdk.xhsminiapptp.constant.TpOpenSdkConstant.TP_APP_INVITATION_URL;

public interface TpAuthAppsClient {

    /**
     * 获取小程序邀请链接
     * 小程序邀请链接可以让服务商邀请自己的商家入驻小红书。
     * @return
     */
    @Post(value = TP_APP_INVITATION_URL,decoder = APIResultJacksonConverter.class)
    TpGetInvitationUrlResp getInvitationUrl();

    /**
     * 获取小程序邀请链接
     * 小程序邀请链接可以让服务商邀请自己的商家入驻小红书。
     * @return
     */
    @Post(value = TP_APP_INFO_URL,decoder = APIResultJacksonConverter.class)
    TpGetInvitationUrlResp getAppInfo();


}
