package com.yitong.octopus.module.opensdk.xhsminiapptp.client;

import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.yitong.octopus.module.opensdk.xhsbase.forest.converter.APIResultJacksonConverter;
import com.yitong.octopus.module.opensdk.xhsminiapptp.entity.oauth.TpGetAccessTokenReq;
import com.yitong.octopus.module.opensdk.xhsminiapptp.entity.oauth.TpGetAccessTokenResp;

import static com.yitong.octopus.module.opensdk.xhsminiapptp.constant.TpOpenSdkConstant.TP_TOKEN;

public interface TpOauthClient {

    @Post(value = TP_TOKEN,decoder = APIResultJacksonConverter.class)
    TpGetAccessTokenResp execute(@JSONBody TpGetAccessTokenReq request);

}
