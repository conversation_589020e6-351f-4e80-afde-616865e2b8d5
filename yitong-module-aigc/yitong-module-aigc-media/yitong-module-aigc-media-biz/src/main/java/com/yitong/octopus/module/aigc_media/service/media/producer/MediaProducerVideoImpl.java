package com.yitong.octopus.module.aigc_media.service.media.producer;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import com.yitong.octopus.framework.common.exception.ServiceException;
import com.yitong.octopus.framework.common.util.date.DateUtils;
import com.yitong.octopus.framework.common.util.json.JsonUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.aliyun.sdk.service.ice20201109.AsyncClient;
import com.aliyun.sdk.service.ice20201109.models.GetMediaProducingJobRequest;
import com.aliyun.sdk.service.ice20201109.models.GetMediaProducingJobResponse;
import com.aliyun.sdk.service.ice20201109.models.GetMediaProducingJobResponseBody;
import com.aliyun.sdk.service.ice20201109.models.SubmitMediaProducingJobRequest;
import com.aliyun.sdk.service.ice20201109.models.SubmitMediaProducingJobResponse;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.gson.Gson;

import com.yitong.octopus.module.aigc_media.common.enums.MediaStatus;
import com.yitong.octopus.module.aigc_media.common.enums.OSSFileType;
import com.yitong.octopus.module.aigc_media.common.enums.SpeechEngineType;
import com.yitong.octopus.module.aigc_media.dal.mysql.formula.MediaMapper;
import com.yitong.octopus.module.aigc_media.dal.mysql.formula.MediaTrackMapper;
import com.yitong.octopus.module.aigc_media.dal.mysql.formula.MediaTrackMaterialMapper;
import com.yitong.octopus.module.aigc_media.dal.dataobject.formula.MediaDO;
import com.yitong.octopus.module.aigc_media.dal.dataobject.formula.MediaTrackDO;
import com.yitong.octopus.module.aigc_media.dal.dataobject.formula.MediaTrackMaterialDO;
import com.yitong.octopus.module.aigc_media.dal.model.vo.formula.OutputMediaConfig;
import com.yitong.octopus.module.aigc_media.dal.model.vo.formula.Timeline;
import com.yitong.octopus.module.aigc_media.dal.model.vo.formula.Timeline.AI_ASREffect;
import com.yitong.octopus.module.aigc_media.dal.model.vo.formula.Timeline.AudioTrack;
import com.yitong.octopus.module.aigc_media.dal.model.vo.formula.Timeline.AudioTrack.AudioTrackClip;
import com.yitong.octopus.module.aigc_media.dal.model.vo.formula.Timeline.AudioTrack.SpeechAudioTrackClip;
import com.yitong.octopus.module.aigc_media.dal.model.vo.formula.Timeline.Effect;
import com.yitong.octopus.module.aigc_media.dal.model.vo.formula.Timeline.TransitionEffect;
import com.yitong.octopus.module.aigc_media.dal.model.vo.formula.Timeline.VideoTrack;
import com.yitong.octopus.module.aigc_media.dal.model.vo.formula.Timeline.VideoTrack.VideoTrackClip;
import com.yitong.octopus.module.aigc_media.dal.model.vo.formula.Timeline.VolumeEffect;
import com.yitong.octopus.module.aigc_media.service.media.listener.MediaListener;
import com.yitong.octopus.module.aigc_media.service.speech.SpeechServiceFactory;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.lang.UUID;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.aigc_media.enums.ErrorCodeConstants.AIGC_ERROR;

@Service("video-producer")
@Slf4j
@DS("master")
public class MediaProducerVideoImpl implements MediaProducer {

	public static int VIDEO_WIDTH = 1080;

	public static int VIDEO_HEIGHT = 1920;

	public static String SPEECH_VOLUME = "2.00";

	@Value("${global.ossDomain}")
	private String ossBaseUrl;

	@Resource
	private AsyncClient asyncClient;

	@Resource
	private MediaMapper mediaMapper;

	@Resource
	private MediaTrackMapper mediaTrackMapper;

	@Resource
	private MediaTrackMaterialMapper mediaTrackMaterialMapper;

	@Resource
	private SpeechServiceFactory speechServiceFactory;

	public void produce(MediaDO media, MediaListener mediaListener) {
		log.info("[MediaJob-{}-Media-{}] Producing video ..., media:{}", media.getJobId(), media.getId(),
				JsonUtils.toJsonString(media));
		List<VideoTrack> videoTracks = new ArrayList<>();
		List<AudioTrack> audioTracks = new ArrayList<>();
		// 字幕轨道
		List<Timeline.SubtitleTrack> subtitleTracks = new ArrayList<>();
		mediaTrackMapper.selectByMediaId(media.getId()).forEach(track -> {
			List<MediaTrackMaterialDO> list = mediaTrackMaterialMapper.selectByMediaIdAndTrackId(media.getId(), track.getId());
			list.forEach(material -> {
				if (material.getSourceMediaId() != null) {
					MediaDO subMedia = mediaMapper.selectById(material.getSourceMediaId());
					if (subMedia.getStatus() == MediaStatus.INITIALIZED) {
						mediaListener.produce(subMedia);
					}
					if (subMedia.getStatus() == MediaStatus.READY) {
						material.setMaterialUrl(subMedia.getUrl());
						material.setContent(subMedia.getContent());
					}
				}
			});
			switch (track.getType()) {
			case VIDEO:
				videoTracks.add(this.createVideoTrack(track, list));
				// Todo 这里需要确定视频轨道就一条！
				this.createVideoFlowerTextTrack(track, list, subtitleTracks);
				break;
			case SPEECH:
				audioTracks.add(this.createSpeechTrack(track, list));
				break;
			case BGM:
				audioTracks.add(this.createBgmTrack(track, list));
				break;
			default:
				break;
			}
		});

		media.setWidth(VIDEO_WIDTH);
		media.setHeight(VIDEO_HEIGHT);
		media.setUrl(StringUtils.removeEnd(ossBaseUrl, "/") + "/media/generated/"
				+ DateUtils.format(new Date(), DatePattern.NORM_DATE_PATTERN)
				+ (media.getParentId() == null ? "/" : "/tmp/") + UUID.randomUUID() + ".mp4");

		Timeline timeline = new Timeline();
		if (!videoTracks.isEmpty()) {
			timeline.setVideoTracks(videoTracks.toArray(new VideoTrack[0]));
		}
		if (!audioTracks.isEmpty()) {
			timeline.setAudioTracks(audioTracks.toArray(new AudioTrack[0]));
		}
		// 视频花字
		if (CollUtil.isNotEmpty(subtitleTracks)) {
			timeline.setSubtitleTracks(subtitleTracks.toArray(new Timeline.SubtitleTrack[0]));
		}

		OutputMediaConfig outputMediaConfig = new OutputMediaConfig();
		outputMediaConfig.setMediaURL(media.getUrl());
		outputMediaConfig.setWidth(media.getWidth());
		outputMediaConfig.setHeight(media.getHeight());
		outputMediaConfig.setBitrate(4 * 1024); //4M码率

		log.info("[MediaJob-{}-Media-{}] outputMediaConfig:{}", media.getJobId(), media.getId(),
				new Gson().toJson(outputMediaConfig));
		log.info("[MediaJob-{}-Media-{}] timeline: {}", media.getJobId(), media.getId(), new Gson().toJson(timeline));

		try {
			SubmitMediaProducingJobRequest submitMediaProducingJobRequest = SubmitMediaProducingJobRequest.builder()
					.outputMediaConfig(new Gson().toJson(outputMediaConfig)).timeline(new Gson().toJson(timeline))
					.build();

			SubmitMediaProducingJobResponse submitMediaProducingJobResponse = asyncClient
					.submitMediaProducingJob(submitMediaProducingJobRequest).get();
			log.info("[MediaJob-{}-Media-{}] SubmitMediaProducingJobResponse: {}", media.getJobId(), media.getId(),
					new Gson().toJson(submitMediaProducingJobResponse));
			String scheduleJobId = submitMediaProducingJobResponse.getBody().getJobId();
			if (scheduleJobId == null) {

			}
			mediaListener.onScheduled(media, submitMediaProducingJobResponse.getBody().getJobId());

			Thread.sleep(15000);

			do {
				GetMediaProducingJobRequest getMediaProducingJobRequest = GetMediaProducingJobRequest.builder()
						.jobId(submitMediaProducingJobResponse.getBody().getJobId()).build();
				GetMediaProducingJobResponse getMediaProducingJobResponse = asyncClient
						.getMediaProducingJob(getMediaProducingJobRequest).get();
				log.info("[MediaJob-{}-Media-{}] GetMediaProducingJobResponse: {}", media.getJobId(), media.getId(),
						new Gson().toJson(getMediaProducingJobResponse));
				Optional<GetMediaProducingJobResponseBody.MediaProducingJob> job = Optional
						.ofNullable(getMediaProducingJobResponse).map(GetMediaProducingJobResponse::getBody)
						.map(GetMediaProducingJobResponseBody::getMediaProducingJob);
				String status = job.map(GetMediaProducingJobResponseBody.MediaProducingJob::getStatus).orElse(null);
				if (StringUtils.equalsIgnoreCase("Success", status)) {
					Long duration = job.map(GetMediaProducingJobResponseBody.MediaProducingJob::getDuration)
							.map(e -> e * 1000).map(Math::round).map(Integer::longValue).orElse(null);
					media.setDuration(ObjectUtils.defaultIfNull(duration, 0).intValue());
					return;
				} else if (StringUtils.equalsIgnoreCase("Failed", status)) {
					String error = job.map(GetMediaProducingJobResponseBody.MediaProducingJob::getMessage).orElse(null);
//					throw new BusinessException(error);
                    throw exception(AIGC_ERROR.getCode(),error);
				} else {
					Thread.sleep(5000);
				}
			} while (true);
		} catch (ServiceException e) {
			throw e;
		} catch (Exception e) {
//			throw new BusinessException("生成媒体异常:" + e.getMessage(), e);
            throw exception(AIGC_ERROR.getCode(),"生成媒体异常:" + e.getMessage());
		}
	}

	/**
	 * 视频花字轨道
	 *
	 * @param track            视频轨道信息
	 * @param list             视频轨道素材
	 * @param flowerTextTracks 花字轨道参数
	 * @return
	 */
	private void createVideoFlowerTextTrack(MediaTrackDO track, List<MediaTrackMaterialDO> list, List<Timeline.SubtitleTrack> flowerTextTracks) {
		List<MediaTrackMaterialDO> collect = list.stream().filter(m -> OSSFileType.FLOWER_WORD_STYLE.equals(m.getMaterialType())).collect(Collectors.toList());
		if (CollUtil.isNotEmpty(collect)) {
			List<Timeline.SubtitleTrack.SubtitleTrackClip> clipList = new ArrayList<>();
			for (MediaTrackMaterialDO mediaTrackMaterialDO : collect) {
				clipList.add(JsonUtils.parseObject(mediaTrackMaterialDO.getContent(), Timeline.SubtitleTrack.SubtitleTrackClip.class));
			}
			Timeline.SubtitleTrack subtitleTrack = new Timeline.SubtitleTrack();
			subtitleTrack.setSubtitleTrackClips(clipList.toArray(new Timeline.SubtitleTrack.SubtitleTrackClip[0]));
			flowerTextTracks.add(subtitleTrack);
		}
	}

	private VideoTrack createVideoTrack(MediaTrackDO track, List<MediaTrackMaterialDO> list) {
		List<VideoTrackClip> clips = new ArrayList<>(list.size());
		for (MediaTrackMaterialDO material : list) {
			if (OSSFileType.IMAGE != material.getMaterialType() && OSSFileType.VIDEO != material.getMaterialType()) {
				continue;
			}
			VideoTrackClip clip = new VideoTrackClip();
			clip.setWidth(Float.valueOf(VIDEO_WIDTH));
			clip.setHeight(Float.valueOf(VIDEO_HEIGHT));
			if (OSSFileType.IMAGE == material.getMaterialType()) {
				clip.setType("Image");
				clip.setDuration(2F);
			} else if (OSSFileType.VIDEO == material.getMaterialType()) {
				clip.setType("Video");
				if (material.getMaxOut() != null) {
					clip.setMaxOut(material.getMaxOut().floatValue() / 1000F);
				}
			}
			clip.setMediaURL(material.getMaterialUrl());
			// 特效
			List<Effect> effects = new ArrayList<>();
			Float volume = ObjectUtils.defaultIfNull(material.getVolume(), track.getVolume());
			if (volume != null) {
				effects.add(new VolumeEffect(new BigDecimal(volume).setScale(2,RoundingMode.HALF_UP).toString()));
			}

			if (material.getTransition() != null) {
				effects.add(new TransitionEffect(material.getTransition()));
			}
			if (!effects.isEmpty()) {
				clip.setEffects(effects.toArray(new Effect[0]));
			}
			clips.add(clip);
		}

		if (clips.isEmpty()) {
			String error = String.format("媒体[%s]轨道[%s]无可用素材", track.getMediaId(), track.getId());
			log.error(error);
//			throw new BusinessException(error);
            throw exception(AIGC_ERROR.getCode(),error);
		}
		VideoTrack videoTrack = new VideoTrack();
		videoTrack.setMainTrack(false);
		videoTrack.setTrackExpandMode("AutoSpeed");
		videoTrack.setTrackShortenMode("AutoSpeed");
		videoTrack.setVideoTrackClips(clips.toArray(new VideoTrackClip[0]));
		return videoTrack;
	}

	private AudioTrack createSpeechTrack(MediaTrackDO track, List<MediaTrackMaterialDO> list) {

		List<AudioTrackClip> clips = new ArrayList<>(list.size());
		for (MediaTrackMaterialDO material : list) {
			if (OSSFileType.COPYWRITING == material.getMaterialType()) {
				// 根据语音引擎类型选择合成方式
				if (null != track.getSpeechEngineType() && SpeechEngineType.VOLCENGINE_TTS == track.getSpeechEngineType()) {
					// 使用火山引擎TTS，生成MP3并返回URL
					String voiceUrl = speechServiceFactory.getSpeechService(track)
							.synthesizeSpeech(material.getContent(), track);

					// 创建普通音频片段
					AudioTrackClip clip = new AudioTrackClip();
					clip.setMediaURL(voiceUrl);

					// 设置音量效果
					List<Effect> effects = new ArrayList<>();
					Float volume = ObjectUtils.defaultIfNull(material.getVolume(), track.getVolume());
					if (volume != null) {
						effects.add(new VolumeEffect(new BigDecimal(volume).setScale(2, RoundingMode.HALF_UP).toString()));
					} else { // 默认音量
						effects.add(new VolumeEffect(SPEECH_VOLUME));
					}

					// 添加字幕效果（与原AI_TTS版本相同的字幕处理）
					if (track.getSubtitleStyle() != null) {
						// 口播字幕样式
						String style = track.getSubtitleStyle();
						if (StringUtils.isNotEmpty(style)) {
							AI_ASREffect aiAsrEffect = JsonUtils.parseObject(style, AI_ASREffect.class);
							aiAsrEffect.setType("Text");
							aiAsrEffect.setContent(material.getContent());
							effects.add(aiAsrEffect);
						}
					} else {
						// 增加默认样式
						AI_ASREffect defaultEffect = new AI_ASREffect();
						defaultEffect.setContent(material.getContent());
						defaultEffect.setType("Text");
						defaultEffect.setY(0.75F); // Y轴 -1050
						defaultEffect.setFont("SiYuan Heiti"); // 思源黑体
						defaultEffect.setAlignment("TopCenter");
						defaultEffect.setFontSize(80); // 11号
						defaultEffect.setFontColor("#ffffff"); // 白字
						defaultEffect.setAdaptMode("AutoWrap");
						effects.add(defaultEffect);
					}
					if (!effects.isEmpty()) {
						clip.setEffects(effects.toArray(new Effect[0]));
					}

					clips.add(clip);
				} else {
					// 使用原来的阿里云AI_TTS方式
					SpeechAudioTrackClip clip = new SpeechAudioTrackClip();
					clip.setContent(material.getContent());
					clip.setVoice(track.getSpeechVoice());

					List<Effect> effects = new ArrayList<>();
					Float volume = ObjectUtils.defaultIfNull(material.getVolume(), track.getVolume());
					if (volume != null) {
						effects.add(new VolumeEffect(new BigDecimal(volume).setScale(2, RoundingMode.HALF_UP).toString()));
					} else { // 默认音量
						effects.add(new VolumeEffect(SPEECH_VOLUME));
					}

					if (track.getSubtitleStyle() != null) {
						// 口播字幕样式
						String style = track.getSubtitleStyle();
						if (StringUtils.isNotEmpty(style)) {
							effects.add(JsonUtils.parseObject(style, AI_ASREffect.class));
						}
					} else {
						// 增加默认样式
						// 思源黑体、11号、白字、黑色阴影（透明度60%，距离5，模糊度15%）、Y轴 -1050
						AI_ASREffect defaultEffect = new AI_ASREffect();
//					defaultEffect.setX(0F);
						defaultEffect.setY(0.75F);//Y轴 -1050
						defaultEffect.setFont("SiYuan Heiti");//思源黑体
//					defaultEffect.setEffectColorStyle("CS0003-000001");//思源黑体
						defaultEffect.setAlignment("TopCenter");
						defaultEffect.setFontSize(80); //11号
						defaultEffect.setFontColor("#ffffff"); // 白字
//					defaultEffect.setFontColorOpacity("0.6");// 透明度60%
						// 自动换行
						defaultEffect.setAdaptMode("AutoWrap");
						effects.add(defaultEffect);
					}
					if (!effects.isEmpty()) {
						clip.setEffects(effects.toArray(new Effect[0]));
					}
					clips.add(clip);
				}
			}
		}
		if (clips.isEmpty()) {
			String error = String.format("媒体[%s]轨道[%s]无可用素材", track.getMediaId(), track.getId());
			log.error(error);
//			throw new BusinessException(error);
            throw exception(AIGC_ERROR.getCode(),error);
		}
		AudioTrack audioTrack = new AudioTrack();
		audioTrack.setMainTrack(true);
		audioTrack.setAudioTrackClips(clips.toArray(new AudioTrackClip[0]));
		return audioTrack;
	}

	private AudioTrack createBgmTrack(MediaTrackDO track, List<MediaTrackMaterialDO> list) {

		List<AudioTrackClip> clips = new ArrayList<>(list.size());
		for (MediaTrackMaterialDO material : list) {
			if (OSSFileType.VIDEO == material.getMaterialType()
					|| OSSFileType.BACKGROUND_AUDIO == material.getMaterialType()) {
				AudioTrackClip clip = new AudioTrackClip();
				clip.setMediaURL(material.getMaterialUrl());
				clip.setLoopMode(Boolean.TRUE);
				List<Effect> effects = new ArrayList<>();
				Float volume = ObjectUtils.defaultIfNull(material.getVolume(), track.getVolume());
				if (volume != null) {
					effects.add(new VolumeEffect(new BigDecimal(volume).setScale(2, RoundingMode.HALF_UP).toString()));
				}
				if (!effects.isEmpty()) {
					clip.setEffects(effects.toArray(new Effect[0]));
				}
				clips.add(clip);
			}
		}

		if (clips.isEmpty()) {
			String error = String.format("媒体[%s]轨道[%s]无可用素材", track.getMediaId(), track.getId());
			log.error(error);
//			throw new BusinessException(error);
            throw exception(AIGC_ERROR.getCode(),error);
		}
		AudioTrack audioTrack = new AudioTrack();
		audioTrack.setMainTrack(false);
		audioTrack.setAudioTrackClips(clips.toArray(new AudioTrackClip[0]));
		return audioTrack;
	}

	public int calculateDuration(MediaTrackDO track, List<MediaTrackMaterialDO> list) {
		// 根据语音引擎类型选择计算方式
		Integer duration = null;
		try {
			if (SpeechEngineType.VOLCENGINE_TTS == track.getSpeechEngineType()) {
				// 使用火山引擎TTS的时长计算
				duration = 0;
				for (MediaTrackMaterialDO material : list) {
					if (material.getContent() != null) {
						duration += speechServiceFactory.getSpeechService(track)
							.calculateDuration(material.getContent(), track);
					}
				}
			} else {
				//原阿里ICE-TTS的时长计算
			String outputUrl = StringUtils.removeEnd(ossBaseUrl, "/") + "/media/generated/"
					+ DateUtils.format(new Date(), DatePattern.NORM_DATE_PATTERN) + "/tmp/" + UUID.randomUUID()
					+ ".mp3";
			OutputMediaConfig outputMediaConfig = new OutputMediaConfig();
			outputMediaConfig.setMediaURL(outputUrl);

			AudioTrack audioTrack = new AudioTrack();
			audioTrack.setAudioTrackClips(list.stream().map((MediaTrackMaterialDO m) -> {
				SpeechAudioTrackClip clip = new SpeechAudioTrackClip();
				clip.setContent(m.getContent());
				clip.setVoice(track.getSpeechVoice());
				return clip;
			}).toArray(new SpeechAudioTrackClip[0]));

			Timeline timeline = new Timeline();
			timeline.setAudioTracks(new AudioTrack[] { audioTrack });

			log.info("outputMediaConfig:{}", new Gson().toJson(outputMediaConfig));
			log.info("timeline: {}", new Gson().toJson(timeline));

			SubmitMediaProducingJobRequest submitMediaProducingJobRequest = SubmitMediaProducingJobRequest.builder()
					.outputMediaConfig(new Gson().toJson(outputMediaConfig)).timeline(new Gson().toJson(timeline))
					.build();

			SubmitMediaProducingJobResponse submitMediaProducingJobResponse = asyncClient
					.submitMediaProducingJob(submitMediaProducingJobRequest).get();
			log.info("SubmitMediaProducingJobResponse: {}", new Gson().toJson(submitMediaProducingJobResponse));
			String scheduleJobId = submitMediaProducingJobResponse.getBody().getJobId();

			Thread.sleep(15000);

			do {
				GetMediaProducingJobRequest getMediaProducingJobRequest = GetMediaProducingJobRequest.builder()
						.jobId(scheduleJobId).build();
				GetMediaProducingJobResponse getMediaProducingJobResponse = asyncClient
						.getMediaProducingJob(getMediaProducingJobRequest).get();
				log.info("GetMediaProducingJobResponse: {}", new Gson().toJson(getMediaProducingJobResponse));
				Optional<GetMediaProducingJobResponseBody.MediaProducingJob> job = Optional
						.ofNullable(getMediaProducingJobResponse).map(GetMediaProducingJobResponse::getBody)
						.map(GetMediaProducingJobResponseBody::getMediaProducingJob);
				String status = job.map(GetMediaProducingJobResponseBody.MediaProducingJob::getStatus).orElse(null);
				if (StringUtils.equalsIgnoreCase("Success", status)) {
					duration = job.map(GetMediaProducingJobResponseBody.MediaProducingJob::getDuration)
							.map(e -> e * 1000).map(Math::round).get();
					break;
				} else if (StringUtils.equalsIgnoreCase("Failed", status)) {
					String error = job.map(GetMediaProducingJobResponseBody.MediaProducingJob::getMessage).get();
//					throw new BusinessException("生成口播音频失败:" + error);
                    throw exception(AIGC_ERROR.getCode(),"生成口播音频失败:" + error);
				} else {
					Thread.sleep(5000);
				}
			} while (true);
			}
		} catch (Exception e) {
			log.warn("获取口播时长失败", e);
		}
		return duration;
	}
}
