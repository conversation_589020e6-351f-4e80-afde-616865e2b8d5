package com.yitong.octopus.module.spider.enums;

import com.yitong.octopus.framework.common.core.EnumKeyArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 爬虫登录类型
 * 1，
 */
@Getter
@AllArgsConstructor
public enum SpiderLoginType implements EnumKeyArrayValuable {

	QRCODE(1, "验证码登录"),
	NAME_PASSWORD(2, "用户名密码登录");

	public static final Object[] ARRAYS = Arrays.stream(values()).map(SpiderLoginType::getType).toArray();

	private final Integer type;
	private final String name;

	@Override
	public Object[] array() {
		return ARRAYS;
	}
}
