package com.yitong.octopus.module.spider.api.note.vo;

import cn.hutool.core.annotation.Alias;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 笔记发布结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItemPublishRespVo {

    //笔记id
    private Long id;

    //笔记ID
    @Alias("item_id")
    private String itemId;

    //笔记url
    private String itemUrl;

    //截图地址
    @Alias("sh_path")
    private String screenshotPath;

    // 错误消息
    private String errorMsg;

}
