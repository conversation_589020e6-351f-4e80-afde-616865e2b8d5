package com.yitong.octopus.module.spider.api.note.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 笔记发布
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItemPublishReqVo {
    // 商家cookies
    private Object cookies;
    //笔记id
    private Long id;
    //笔记类型
    private String itemType;
    //发布地址
    private String itemAddr;
    //商家名称
    private String spName;
    //笔记图片
    private List<String> noteImages;
    //笔记标题
    private String noteTitle;
    //笔记内容
    private String noteContent;
    //是否poi
    private String isPoi;
    //商品渠道id
    private String  spuId;
    //权限类型
    private String roleType;
    //定时发布
    private String timeType;
    //发布时间
    private String timeInfo;
}
