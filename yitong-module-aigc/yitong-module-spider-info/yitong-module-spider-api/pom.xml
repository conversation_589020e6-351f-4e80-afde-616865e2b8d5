<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.yitong.octopus</groupId>
        <artifactId>yitong-module-spider-info</artifactId>
        <version>${revision}</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>yitong-module-spider-api</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
       爬虫模块API ，暴漏出爬虫接口给应用系统
    </description>

    <dependencies>
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-common</artifactId>
        </dependency>

        <!-- 参数校验 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

</project>
