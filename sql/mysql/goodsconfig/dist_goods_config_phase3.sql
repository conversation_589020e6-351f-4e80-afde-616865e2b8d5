-- =====================================================
-- 分销商品佣金结构统一化迁移脚本 - Phase 3 (最终清理)
-- 作者：一筒科技
-- 日期：2025-08-06
-- 描述：Phase 3 - 删除旧字段，完成迁移
-- 警告：此脚本将永久删除旧字段，执行前请确保：
--      1. Phase 2已执行至少30天
--      2. 所有应用已更新为使用新字段
--      3. 已完成数据备份
-- =====================================================

-- =====================================================
-- 前置条件检查
-- =====================================================

-- 1. 检查新字段是否存在
SELECT 
    CASE 
        WHEN COUNT(*) = 3 THEN 'Phase 1/2已执行：新字段存在'
        ELSE 'ERROR: 新字段不存在，请先执行Phase 1和Phase 2'
    END AS new_fields_status
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'yt_dist_goods_config' 
    AND COLUMN_NAME IN ('commission_value', 'parent_reward', 'grand_parent_reward');

-- 2. 检查数据完整性
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'READY: 所有记录都已使用新字段'
        ELSE CONCAT('WARNING: 还有 ', COUNT(*), ' 条记录新字段为空')
    END AS data_status
FROM `yt_dist_goods_config`
WHERE deleted = 0
    AND (commission_value IS NULL 
         AND parent_reward IS NULL 
         AND grand_parent_reward IS NULL);

-- 3. 显示即将删除的字段
SELECT 
    COLUMN_NAME,
    COLUMN_TYPE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'yt_dist_goods_config' 
    AND COLUMN_NAME IN (
        'first_commission_rate',
        'first_commission_amount',
        'second_commission_rate',
        'second_commission_amount',
        'third_commission_rate',
        'third_commission_amount'
    );

-- =====================================================
-- Step 1: 最终备份（包含旧字段的最后备份）
-- =====================================================

-- 创建带时间戳的最终备份表
SET @backup_table = CONCAT('yt_dist_goods_config_final_backup_', DATE_FORMAT(NOW(), '%Y%m%d_%H%i%s'));
SET @sql = CONCAT('CREATE TABLE ', @backup_table, ' AS SELECT * FROM yt_dist_goods_config');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT CONCAT('备份表已创建: ', @backup_table) AS backup_status;

-- =====================================================
-- Step 2: 删除旧字段
-- =====================================================

-- 2.1 删除六个旧佣金字段
ALTER TABLE `yt_dist_goods_config` 
DROP COLUMN IF EXISTS `first_commission_rate`,
DROP COLUMN IF EXISTS `first_commission_amount`,
DROP COLUMN IF EXISTS `second_commission_rate`,
DROP COLUMN IF EXISTS `second_commission_amount`,
DROP COLUMN IF EXISTS `third_commission_rate`,
DROP COLUMN IF EXISTS `third_commission_amount`;

-- 2.2 验证字段已删除
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'SUCCESS: 旧字段已成功删除'
        ELSE CONCAT('ERROR: 还有 ', COUNT(*), ' 个旧字段未删除')
    END AS deletion_status
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'yt_dist_goods_config' 
    AND COLUMN_NAME IN (
        'first_commission_rate',
        'first_commission_amount',
        'second_commission_rate',
        'second_commission_amount',
        'third_commission_rate',
        'third_commission_amount'
    );

-- =====================================================
-- Step 3: 清理相关对象
-- =====================================================

-- 3.1 删除Phase 1创建的一致性检查视图
DROP VIEW IF EXISTS `v_dist_goods_config_field_consistency`;

-- 3.2 删除Phase 2创建的迁移报告视图
DROP VIEW IF EXISTS `v_dist_goods_migration_report`;

-- 3.3 删除废弃字段使用监控表（如果存在）
DROP TABLE IF EXISTS `yt_dist_goods_deprecated_field_usage`;

-- 3.4 删除临时触发器（如果存在）
DROP TRIGGER IF EXISTS `trg_dist_goods_config_insert_sync`;
DROP TRIGGER IF EXISTS `trg_dist_goods_config_update_sync`;

-- =====================================================
-- Step 4: 优化表结构
-- =====================================================

-- 4.1 优化表，回收空间
OPTIMIZE TABLE `yt_dist_goods_config`;

-- 4.2 更新表注释
ALTER TABLE `yt_dist_goods_config` 
COMMENT='分销商品配置表（佣金结构重构完成-Phase3）';

-- =====================================================
-- Step 5: 创建迁移完成标记
-- =====================================================

-- 创建迁移完成记录表
CREATE TABLE IF NOT EXISTS `yt_system_migration_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `migration_name` VARCHAR(200) NOT NULL,
    `migration_phase` VARCHAR(50) NOT NULL,
    `execution_time` DATETIME NOT NULL,
    `execution_user` VARCHAR(100),
    `status` VARCHAR(20) NOT NULL,
    `details` TEXT,
    PRIMARY KEY (`id`),
    KEY `idx_migration_name` (`migration_name`),
    KEY `idx_execution_time` (`execution_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统迁移日志表';

-- 插入Phase 3完成记录
INSERT INTO `yt_system_migration_log` 
    (`migration_name`, `migration_phase`, `execution_time`, `execution_user`, `status`, `details`)
VALUES 
    ('dist_goods_commission_refactoring', 'Phase3-Cleanup', NOW(), USER(), 'COMPLETED', 
     '成功删除旧佣金字段，迁移完成');

-- =====================================================
-- Step 6: 最终验证
-- =====================================================

-- 显示当前表结构
SELECT 
    COLUMN_NAME,
    COLUMN_TYPE,
    IS_NULLABLE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'yt_dist_goods_config'
    AND COLUMN_NAME IN ('commission_value', 'parent_reward', 'grand_parent_reward')
ORDER BY ORDINAL_POSITION;

-- 统计数据情况
SELECT 
    'Phase 3 Complete Summary' as phase,
    COUNT(*) as total_records,
    SUM(CASE WHEN commission_value IS NOT NULL THEN 1 ELSE 0 END) as with_commission_value,
    SUM(CASE WHEN parent_reward IS NOT NULL THEN 1 ELSE 0 END) as with_parent_reward,
    SUM(CASE WHEN grand_parent_reward IS NOT NULL THEN 1 ELSE 0 END) as with_grand_parent_reward
FROM `yt_dist_goods_config`
WHERE deleted = 0;

-- =====================================================
-- 清理建议（30天后执行）
-- =====================================================
/*
-- 确认系统稳定后，可以删除备份表
-- 查看所有备份表
SELECT TABLE_NAME, CREATE_TIME, TABLE_ROWS
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME LIKE 'yt_dist_goods_config_%backup%'
ORDER BY CREATE_TIME DESC;

-- 删除旧备份（谨慎执行）
-- DROP TABLE IF EXISTS `yt_dist_goods_config_backup_20250806`;
-- DROP TABLE IF EXISTS `yt_dist_goods_config_backup_phase2_20250806`;
-- DROP TABLE IF EXISTS `yt_dist_goods_config_final_backup_[date]`;
*/

-- =====================================================
-- 回滚方案（紧急情况使用）
-- =====================================================
/*
警告：Phase 3执行后的回滚非常复杂，需要：
1. 从备份表恢复整个表结构和数据
2. 重新部署包含旧字段的应用代码
3. 重新执行Phase 1和Phase 2

回滚步骤：
-- 1. 重命名当前表
RENAME TABLE `yt_dist_goods_config` TO `yt_dist_goods_config_phase3_failed`;

-- 2. 从备份恢复（使用最新的final_backup表）
RENAME TABLE `yt_dist_goods_config_final_backup_[date]` TO `yt_dist_goods_config`;

-- 3. 验证数据
SELECT COUNT(*) FROM `yt_dist_goods_config`;

-- 4. 如果需要，重新创建索引和约束
*/

-- =====================================================
-- 执行完成
-- =====================================================
SELECT 
    '🎉 Phase 3执行完成！' as message,
    '佣金结构重构项目成功完成' as status,
    NOW() as completion_time;