-- =====================================================
-- 分销商品佣金结构统一化迁移脚本 - Phase 1 (兼容期)
-- 作者：一筒科技
-- 日期：2025-08-06
-- 描述：Phase 1 - 添加新字段，保持与旧字段兼容
-- =====================================================

-- =====================================================
-- Phase 1: 添加新字段 (保持旧字段不变)
-- =====================================================

-- 1.1 检查并添加新字段（避免重复添加）
-- 检查 commission_value 字段是否存在
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'yt_dist_goods_config' 
    AND COLUMN_NAME = 'commission_value'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE `yt_dist_goods_config` 
     ADD COLUMN `commission_value` DECIMAL(10, 2) NULL COMMENT ''直接佣金值（根据commission_mode决定是比例还是金额）'' AFTER `commission_mode`',
    'SELECT ''commission_value column already exists''');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查 parent_reward 字段是否存在
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'yt_dist_goods_config' 
    AND COLUMN_NAME = 'parent_reward'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE `yt_dist_goods_config` 
     ADD COLUMN `parent_reward` DECIMAL(10, 2) NULL COMMENT ''上级奖励值（根据commission_mode决定是比例还是金额）'' AFTER `commission_value`',
    'SELECT ''parent_reward column already exists''');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查 grand_parent_reward 字段是否存在
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'yt_dist_goods_config' 
    AND COLUMN_NAME = 'grand_parent_reward'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE `yt_dist_goods_config` 
     ADD COLUMN `grand_parent_reward` DECIMAL(10, 2) NULL COMMENT ''上上级奖励值（根据commission_mode决定是比例还是金额）'' AFTER `parent_reward`',
    'SELECT ''grand_parent_reward column already exists''');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 1.2 初始化新字段数据（从旧字段同步，仅同步NULL值的记录）
UPDATE `yt_dist_goods_config` 
SET 
  `commission_value` = CASE 
    WHEN `commission_value` IS NULL AND `commission_mode` = 1 THEN `first_commission_rate`
    WHEN `commission_value` IS NULL AND `commission_mode` = 2 THEN `first_commission_amount`
    ELSE `commission_value`
  END,
  `parent_reward` = CASE 
    WHEN `parent_reward` IS NULL AND `commission_mode` = 1 THEN `second_commission_rate`
    WHEN `parent_reward` IS NULL AND `commission_mode` = 2 THEN `second_commission_amount`
    ELSE `parent_reward`
  END,
  `grand_parent_reward` = CASE 
    WHEN `grand_parent_reward` IS NULL AND `commission_mode` = 1 THEN `third_commission_rate`
    WHEN `grand_parent_reward` IS NULL AND `commission_mode` = 2 THEN `third_commission_amount`
    ELSE `grand_parent_reward`
  END
WHERE `commission_value` IS NULL 
   OR `parent_reward` IS NULL 
   OR `grand_parent_reward` IS NULL;

-- 1.3 创建数据一致性检查视图（用于监控）
CREATE OR REPLACE VIEW `v_dist_goods_config_field_consistency` AS
SELECT 
  id,
  spu_id,
  spu_name,
  commission_mode,
  commission_value,
  parent_reward,
  grand_parent_reward,
  first_commission_rate,
  first_commission_amount,
  second_commission_rate,
  second_commission_amount,
  third_commission_rate,
  third_commission_amount,
  CASE 
    WHEN commission_mode = 1 THEN
      CASE 
        WHEN COALESCE(commission_value, 0) = COALESCE(first_commission_rate, 0)
          AND COALESCE(parent_reward, 0) = COALESCE(second_commission_rate, 0)
          AND COALESCE(grand_parent_reward, 0) = COALESCE(third_commission_rate, 0)
        THEN 'CONSISTENT'
        ELSE 'INCONSISTENT'
      END
    WHEN commission_mode = 2 THEN
      CASE 
        WHEN COALESCE(commission_value, 0) = COALESCE(first_commission_amount, 0)
          AND COALESCE(parent_reward, 0) = COALESCE(second_commission_amount, 0)
          AND COALESCE(grand_parent_reward, 0) = COALESCE(third_commission_amount, 0)
        THEN 'CONSISTENT'
        ELSE 'INCONSISTENT'
      END
    ELSE 'UNKNOWN'
  END as consistency_status,
  update_time
FROM `yt_dist_goods_config`
WHERE deleted = 0;

-- 1.4 验证数据迁移结果
SELECT 
  'Phase 1 Migration Summary' as phase,
  COUNT(*) as total_records,
  SUM(CASE WHEN commission_value IS NOT NULL THEN 1 ELSE 0 END) as records_with_new_fields,
  SUM(CASE 
    WHEN commission_mode = 1 AND commission_value != COALESCE(first_commission_rate, 0) THEN 1
    WHEN commission_mode = 2 AND commission_value != COALESCE(first_commission_amount, 0) THEN 1
    ELSE 0
  END) as inconsistent_records
FROM `yt_dist_goods_config`
WHERE deleted = 0;

-- 查看不一致的记录详情（如果有）
SELECT * FROM `v_dist_goods_config_field_consistency` 
WHERE consistency_status = 'INCONSISTENT' 
LIMIT 10;

-- =====================================================
-- 注意事项
-- =====================================================
-- 1. 执行前请确保已备份数据库
-- 2. 这是Phase 1脚本，仅添加新字段并初始化数据
-- 3. 旧字段保持不变，确保向后兼容
-- 4. 应用代码需要同步更新以支持双写模式
-- =====================================================