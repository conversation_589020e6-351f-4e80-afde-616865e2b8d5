-- =====================================================
-- 分销商品等级配置历史记录表设计
-- 作者：一筒科技
-- 日期：2025-08-05
-- 描述：记录等级配置的最终状态快照，不记录中间删除等操作
-- =====================================================

-- 创建等级配置历史记录表
CREATE TABLE `yt_dist_goods_level_config_history` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '历史记录ID',
  `snapshot_time` DATETIME NOT NULL COMMENT '快照时间，记录配置状态的时间点',
  `goods_config_id` BIGINT NOT NULL COMMENT '商品配置ID，关联yt_dist_goods_config表的id',
  `goods_config_version` INT NOT NULL COMMENT '商品配置版本号，关联yt_dist_goods_config_history的version',
  `level_config_snapshot` JSON NOT NULL COMMENT '等级配置快照，JSON格式存储该时间点所有等级配置的最终状态',
  `total_level_count` INT NOT NULL DEFAULT 0 COMMENT '等级配置总数',
  `active_level_count` INT NOT NULL DEFAULT 0 COMMENT '启用的等级配置数',
  `snapshot_type` TINYINT NOT NULL DEFAULT 1 COMMENT '快照类型：1-定期快照，2-重大变更快照，3-手动快照',
  `snapshot_reason` VARCHAR(500) NULL COMMENT '快照原因说明',
  `operator_id` BIGINT NULL COMMENT '操作人ID',
  `operator_name` VARCHAR(100) NULL COMMENT '操作人姓名',
  `remark` VARCHAR(500) NULL COMMENT '备注',
  `creator` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_goods_config_id` (`goods_config_id`),
  KEY `idx_goods_config_version` (`goods_config_id`, `goods_config_version`),
  KEY `idx_snapshot_time` (`snapshot_time`),
  KEY `idx_snapshot_type` (`snapshot_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销商品等级配置历史记录表（最终状态快照）';

-- 创建索引优化查询性能
ALTER TABLE `yt_dist_goods_level_config_history` ADD INDEX `idx_tenant_id` (`tenant_id`);

-- =====================================================
-- level_config_snapshot JSON结构示例：
-- [
--   {
--     "level_config_id": 1,
--     "level_id": 1,
--     "level_name": "初级代理",
--     "commission_mode": 1,
--     "commission_value": 10.00,
--     "parent_reward": 2.00,
--     "grand_parent_reward": 1.00,
--     "applicable_tags": ["VIP", "NEW"],
--     "min_order_amount": 100.00,
--     "max_order_amount": 10000.00,
--     "enabled": true,
--     "priority": 1,
--     "effective_time": "2025-01-01 00:00:00",
--     "expire_time": null
--   },
--   {
--     "level_config_id": 2,
--     "level_id": 2,
--     "level_name": "高级代理",
--     "commission_mode": 1,
--     "commission_value": 15.00,
--     "parent_reward": 3.00,
--     "grand_parent_reward": 1.50,
--     "applicable_tags": ["VIP", "PREMIUM"],
--     "min_order_amount": 100.00,
--     "max_order_amount": null,
--     "enabled": true,
--     "priority": 2,
--     "effective_time": "2025-01-01 00:00:00",
--     "expire_time": null
--   }
-- ]
-- =====================================================

-- =====================================================
-- 使用说明：
-- 1. 本表只记录配置的最终状态快照，不记录中间的增删改操作
-- 2. 在以下时机创建快照：
--    a. 定期快照：每天/每周/每月定时任务创建
--    b. 重大变更快照：批量修改或重要配置变更后自动创建
--    c. 手动快照：管理员手动触发创建
-- 3. level_config_snapshot 存储该时间点所有等级配置的完整状态
-- 4. 通过 snapshot_time 可以查询任意时间点的配置状态
-- 5. 通过 goods_config_version 关联商品配置的版本
-- =====================================================