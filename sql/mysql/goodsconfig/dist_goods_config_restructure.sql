-- =====================================================
-- 分销商品配置重构 SQL 脚本
-- 作者：一筒科技
-- 日期：2025-08-04
-- 描述：支持灵活的等级配置存储
-- =====================================================

-- 1. 修改主配置表，添加新字段
ALTER TABLE `yt_dist_goods_config` 
ADD COLUMN `commission_type` TINYINT NOT NULL DEFAULT 1 COMMENT '佣金类型：1-统一佣金（所有等级相同），2-按等级差异化佣金' AFTER `commission_mode`,
ADD COLUMN `commission_value` DECIMAL(10, 2) NULL COMMENT '统一佣金值，当commission_type=1时使用' AFTER `commission_type`,
ADD COLUMN `version` INT NOT NULL DEFAULT 0 COMMENT '版本号，用于乐观锁控制和版本管理' AFTER `remark`,
ADD COLUMN `enable_level_config` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否启用等级差异化佣金：true-启用等级配置，false-使用统一佣金' AFTER `version`;

-- 2. 创建商品等级配置表
CREATE TABLE `yt_dist_goods_level_config` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `goods_config_id` BIGINT NOT NULL COMMENT '商品配置ID，关联yt_dist_goods_config表的id',
  `level_id` BIGINT NOT NULL COMMENT '等级ID，关联yt_dist_level表的id',
  `level_name` VARCHAR(100) NOT NULL COMMENT '等级名称，冗余字段，方便展示和历史记录',
  `commission_mode` TINYINT NOT NULL DEFAULT 1 COMMENT '佣金计算方式：1-按比例，2-按固定金额',
  `commission_value` DECIMAL(10, 2) NOT NULL COMMENT '佣金值，根据commission_mode决定是比例还是固定金额',
  `parent_reward` DECIMAL(10, 2) NULL COMMENT '上级奖励值，推荐人（父级）的奖励金额或比例',
  `grand_parent_reward` DECIMAL(10, 2) NULL COMMENT '上上级奖励值，推荐人的推荐人（祖父级）的奖励金额或比例',
  `applicable_tags` JSON NULL COMMENT '适用标签，JSON数组格式存储，如：["VIP", "HIGH_VALUE"]',
  `min_order_amount` DECIMAL(10, 2) NULL COMMENT '最小订单金额，该等级配置生效的最小订单金额限制',
  `max_order_amount` DECIMAL(10, 2) NULL COMMENT '最大订单金额，该等级配置生效的最大订单金额限制，null表示不限制',
  `enabled` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用：true-启用，false-禁用',
  `priority` INT NOT NULL DEFAULT 0 COMMENT '优先级，数字越大优先级越高，用于多个配置冲突时的选择',
  `effective_time` DATETIME NULL COMMENT '生效时间',
  `expire_time` DATETIME NULL COMMENT '失效时间',
  `remark` VARCHAR(500) NULL COMMENT '备注',
  `creator` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_goods_config_id` (`goods_config_id`),
  KEY `idx_level_id` (`level_id`),
  KEY `idx_enabled_priority` (`enabled`, `priority`),
  KEY `idx_effective_expire` (`effective_time`, `expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销商品等级配置表';

-- 3. 创建商品配置历史记录表
CREATE TABLE `yt_dist_goods_config_history` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '历史记录ID',
  `goods_config_id` BIGINT NOT NULL COMMENT '商品配置ID，关联yt_dist_goods_config表的id',
  `version` INT NOT NULL COMMENT '版本号，递增版本号，用于标识配置的版本',
  `spu_id` BIGINT NOT NULL COMMENT '商品SPU ID',
  `app_id` BIGINT NULL COMMENT '应用ID',
  `channel_spu_id` VARCHAR(100) NULL COMMENT '渠道商品SPU ID',
  `spu_name` VARCHAR(255) NULL COMMENT '商品SPU名称',
  `enable_dist` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否开启分销',
  `commission_mode` TINYINT NULL COMMENT '佣金计算方式：1-按比例，2-按固定金额',
  `commission_type` TINYINT NOT NULL DEFAULT 1 COMMENT '佣金类型：1-统一佣金，2-按等级差异化佣金',
  `commission_value` DECIMAL(10, 2) NULL COMMENT '统一佣金值，当commission_type=1时使用',
  `commission_base_type` TINYINT NULL COMMENT '佣金基数类型：1-商品原价，2-实付金额，3-利润金额',
  `min_dist_price` DECIMAL(10, 2) NULL COMMENT '最低分销价',
  `dist_start_time` DATETIME NULL COMMENT '分销有效期开始时间',
  `dist_end_time` DATETIME NULL COMMENT '分销有效期结束时间',
  `dist_stock` INT NULL COMMENT '分销库存',
  `dist_sold_count` INT NULL DEFAULT 0 COMMENT '已分销数量（快照）',
  `limit_buy_count` INT NULL COMMENT '限购数量',
  `min_level_id` BIGINT NULL COMMENT '分销员等级限制',
  `priority` INT NULL DEFAULT 0 COMMENT '优先级',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态',
  `change_type` TINYINT NOT NULL COMMENT '变更类型：1-创建，2-更新，3-删除，4-启用，5-禁用',
  `change_reason` VARCHAR(500) NULL COMMENT '变更原因',
  `operator_id` BIGINT NULL COMMENT '操作人ID',
  `operator_name` VARCHAR(100) NULL COMMENT '操作人姓名',
  `operate_time` DATETIME NOT NULL COMMENT '操作时间',
  `before_data` JSON NULL COMMENT '变更前数据JSON，存储变更前的完整配置数据',
  `after_data` JSON NULL COMMENT '变更后数据JSON，存储变更后的完整配置数据',
  `remark` VARCHAR(500) NULL COMMENT '备注',
  `creator` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_goods_config_id` (`goods_config_id`),
  KEY `idx_spu_id` (`spu_id`),
  KEY `idx_version` (`goods_config_id`, `version`),
  KEY `idx_operator_time` (`operator_id`, `operate_time`),
  KEY `idx_change_type` (`change_type`, `operate_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销商品配置历史记录表';

-- 4. 创建商品操作日志表
CREATE TABLE `yt_dist_goods_operation_log` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `goods_config_id` BIGINT NULL COMMENT '商品配置ID，关联yt_dist_goods_config表的id',
  `spu_id` BIGINT NULL COMMENT '商品SPU ID',
  `spu_name` VARCHAR(255) NULL COMMENT '商品SPU名称',
  `app_id` BIGINT NULL COMMENT '应用ID',
  `operation_type` TINYINT NOT NULL COMMENT '操作类型：1-创建配置，2-更新配置，3-删除配置，4-启用配置，5-禁用配置，6-添加等级配置，7-更新等级配置，8-删除等级配置，9-批量导入，10-批量导出',
  `operation_type_name` VARCHAR(50) NOT NULL COMMENT '操作类型名称，如：创建配置、更新配置等',
  `operation_module` TINYINT NOT NULL COMMENT '操作模块：1-商品配置，2-等级配置，3-批量操作',
  `operation_detail` VARCHAR(1000) NULL COMMENT '操作详情，具体的操作描述',
  `operation_result` TINYINT NOT NULL COMMENT '操作结果：1-成功，2-失败',
  `error_message` VARCHAR(1000) NULL COMMENT '错误信息，操作失败时的错误描述',
  `request_params` JSON NULL COMMENT '请求参数，JSON格式存储操作时的请求参数',
  `response_data` JSON NULL COMMENT '响应数据，JSON格式存储操作的响应数据',
  `operator_id` BIGINT NULL COMMENT '操作人ID',
  `operator_name` VARCHAR(100) NULL COMMENT '操作人姓名',
  `operator_type` TINYINT NULL COMMENT '操作人类型：1-管理员，2-系统，3-定时任务',
  `operate_time` DATETIME NOT NULL COMMENT '操作时间',
  `cost_time` BIGINT NULL COMMENT '操作耗时（毫秒）',
  `client_ip` VARCHAR(50) NULL COMMENT '客户端IP',
  `user_agent` VARCHAR(500) NULL COMMENT '用户代理',
  `request_uri` VARCHAR(500) NULL COMMENT '请求URI',
  `request_method` VARCHAR(20) NULL COMMENT '请求方法：GET、POST、PUT、DELETE',
  `business_no` VARCHAR(100) NULL COMMENT '业务编号，用于关联相关业务，如订单号、批次号等',
  `remark` VARCHAR(500) NULL COMMENT '备注',
  `creator` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_goods_config_id` (`goods_config_id`),
  KEY `idx_spu_id` (`spu_id`),
  KEY `idx_operation_type` (`operation_type`, `operate_time`),
  KEY `idx_operator` (`operator_id`, `operate_time`),
  KEY `idx_result` (`operation_result`, `operate_time`),
  KEY `idx_client_ip` (`client_ip`),
  KEY `idx_business_no` (`business_no`),
  KEY `idx_operate_time` (`operate_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销商品操作日志表';

-- 5. 数据迁移：将现有的固定佣金配置迁移到新结构
-- 注意：执行前请备份数据
UPDATE `yt_dist_goods_config` 
SET 
  `commission_type` = 1,  -- 设置为统一佣金类型
  `commission_value` = CASE 
    WHEN `commission_mode` = 1 THEN `first_commission_rate`  -- 如果是比例，使用一级佣金比例
    WHEN `commission_mode` = 2 THEN `first_commission_amount` -- 如果是固定金额，使用一级佣金金额
    ELSE NULL
  END,
  `enable_level_config` = 0  -- 默认不启用等级配置
WHERE 1=1;

-- 6. 创建索引优化查询性能
-- 为tenant_id创建索引（如果尚未存在）
ALTER TABLE `yt_dist_goods_level_config` ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE `yt_dist_goods_config_history` ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE `yt_dist_goods_operation_log` ADD INDEX `idx_tenant_id` (`tenant_id`);

-- 7. 添加表注释
ALTER TABLE `yt_dist_goods_config` COMMENT='分销商品配置表（支持统一佣金和等级差异化佣金）';

-- =====================================================
-- 回滚脚本（如需要回滚，请执行以下语句）
-- =====================================================
-- ALTER TABLE `yt_dist_goods_config` 
-- DROP COLUMN `commission_type`,
-- DROP COLUMN `commission_value`,
-- DROP COLUMN `version`,
-- DROP COLUMN `enable_level_config`;
-- 
-- DROP TABLE IF EXISTS `yt_dist_goods_level_config`;
-- DROP TABLE IF EXISTS `yt_dist_goods_config_history`;
-- DROP TABLE IF EXISTS `yt_dist_goods_operation_log`;