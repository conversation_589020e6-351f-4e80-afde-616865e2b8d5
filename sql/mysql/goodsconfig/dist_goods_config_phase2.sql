-- =====================================================
-- 分销商品佣金结构统一化迁移脚本 - Phase 2 (完全迁移)
-- 作者：一筒科技
-- 日期：2025-08-06
-- 描述：Phase 2 - 停止使用旧字段，准备清理
-- =====================================================

-- =====================================================
-- 前置条件检查
-- =====================================================

-- 1. 检查Phase 1是否已执行
SELECT 
    CASE 
        WHEN COUNT(*) = 3 THEN 'Phase 1已执行：新字段存在'
        ELSE 'ERROR: Phase 1未执行，请先执行Phase 1脚本'
    END AS phase1_status
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'yt_dist_goods_config' 
    AND COLUMN_NAME IN ('commission_value', 'parent_reward', 'grand_parent_reward');

-- =====================================================
-- Step 1: 数据一致性验证
-- =====================================================

-- 1.1 创建数据迁移验证报告
CREATE OR REPLACE VIEW `v_dist_goods_migration_report` AS
SELECT 
    COUNT(*) as total_records,
    SUM(CASE 
        WHEN commission_value IS NOT NULL 
            OR parent_reward IS NOT NULL 
            OR grand_parent_reward IS NOT NULL 
        THEN 1 ELSE 0 
    END) as records_with_new_fields,
    SUM(CASE 
        WHEN commission_value IS NULL 
            AND parent_reward IS NULL 
            AND grand_parent_reward IS NULL
            AND (first_commission_rate IS NOT NULL 
                OR first_commission_amount IS NOT NULL
                OR second_commission_rate IS NOT NULL
                OR second_commission_amount IS NOT NULL
                OR third_commission_rate IS NOT NULL
                OR third_commission_amount IS NOT NULL)
        THEN 1 ELSE 0 
    END) as records_need_migration,
    SUM(CASE 
        WHEN commission_mode = 1 AND (
            COALESCE(commission_value, 0) != COALESCE(first_commission_rate, 0)
            OR COALESCE(parent_reward, 0) != COALESCE(second_commission_rate, 0)
            OR COALESCE(grand_parent_reward, 0) != COALESCE(third_commission_rate, 0)
        ) THEN 1
        WHEN commission_mode = 2 AND (
            COALESCE(commission_value, 0) != COALESCE(first_commission_amount, 0)
            OR COALESCE(parent_reward, 0) != COALESCE(second_commission_amount, 0)
            OR COALESCE(grand_parent_reward, 0) != COALESCE(third_commission_amount, 0)
        ) THEN 1
        ELSE 0
    END) as inconsistent_records
FROM `yt_dist_goods_config`
WHERE deleted = 0;

-- 1.2 查看迁移报告
SELECT * FROM `v_dist_goods_migration_report`;

-- 1.3 查看不一致的具体记录
SELECT 
    id,
    spu_id,
    spu_name,
    commission_mode,
    commission_value,
    parent_reward,
    grand_parent_reward,
    first_commission_rate,
    first_commission_amount,
    second_commission_rate,
    second_commission_amount,
    third_commission_rate,
    third_commission_amount
FROM `yt_dist_goods_config`
WHERE deleted = 0
    AND (
        (commission_mode = 1 AND (
            COALESCE(commission_value, 0) != COALESCE(first_commission_rate, 0)
            OR COALESCE(parent_reward, 0) != COALESCE(second_commission_rate, 0)
            OR COALESCE(grand_parent_reward, 0) != COALESCE(third_commission_rate, 0)
        ))
        OR (commission_mode = 2 AND (
            COALESCE(commission_value, 0) != COALESCE(first_commission_amount, 0)
            OR COALESCE(parent_reward, 0) != COALESCE(second_commission_amount, 0)
            OR COALESCE(grand_parent_reward, 0) != COALESCE(third_commission_amount, 0)
        ))
    )
LIMIT 20;

-- =====================================================
-- Step 2: 最终数据迁移（确保所有新字段都有值）
-- =====================================================

-- 2.1 备份当前数据
CREATE TABLE IF NOT EXISTS `yt_dist_goods_config_backup_phase2_20250806` AS 
SELECT * FROM `yt_dist_goods_config`;

-- 2.2 执行最终迁移（仅迁移新字段为NULL的记录）
UPDATE `yt_dist_goods_config` 
SET 
    `commission_value` = CASE 
        WHEN `commission_value` IS NULL THEN
            CASE 
                WHEN `commission_mode` = 1 THEN `first_commission_rate`
                WHEN `commission_mode` = 2 THEN `first_commission_amount`
                ELSE 0
            END
        ELSE `commission_value`
    END,
    `parent_reward` = CASE 
        WHEN `parent_reward` IS NULL THEN
            CASE 
                WHEN `commission_mode` = 1 THEN `second_commission_rate`
                WHEN `commission_mode` = 2 THEN `second_commission_amount`
                ELSE 0
            END
        ELSE `parent_reward`
    END,
    `grand_parent_reward` = CASE 
        WHEN `grand_parent_reward` IS NULL THEN
            CASE 
                WHEN `commission_mode` = 1 THEN `third_commission_rate`
                WHEN `commission_mode` = 2 THEN `third_commission_amount`
                ELSE 0
            END
        ELSE `grand_parent_reward`
    END,
    `update_time` = NOW(),
    `updater` = 'PHASE2_MIGRATION'
WHERE deleted = 0;

-- 2.3 验证迁移结果
SELECT 
    'Phase 2 Migration Complete' as status,
    COUNT(*) as total_records,
    SUM(CASE WHEN commission_value IS NOT NULL THEN 1 ELSE 0 END) as records_with_commission_value,
    SUM(CASE WHEN parent_reward IS NOT NULL THEN 1 ELSE 0 END) as records_with_parent_reward,
    SUM(CASE WHEN grand_parent_reward IS NOT NULL THEN 1 ELSE 0 END) as records_with_grand_parent_reward
FROM `yt_dist_goods_config`
WHERE deleted = 0;

-- =====================================================
-- Step 3: 清理旧字段（标记为即将删除）
-- =====================================================

-- 3.1 修改旧字段注释，明确标记为即将删除
ALTER TABLE `yt_dist_goods_config` 
MODIFY COLUMN `first_commission_rate` DECIMAL(10, 2) NULL COMMENT '@Deprecated @ToBeRemoved Phase3 - 一级佣金比例（即将删除）',
MODIFY COLUMN `first_commission_amount` DECIMAL(10, 2) NULL COMMENT '@Deprecated @ToBeRemoved Phase3 - 一级佣金金额（即将删除）',
MODIFY COLUMN `second_commission_rate` DECIMAL(10, 2) NULL COMMENT '@Deprecated @ToBeRemoved Phase3 - 二级佣金比例（即将删除）',
MODIFY COLUMN `second_commission_amount` DECIMAL(10, 2) NULL COMMENT '@Deprecated @ToBeRemoved Phase3 - 二级佣金金额（即将删除）',
MODIFY COLUMN `third_commission_rate` DECIMAL(10, 2) NULL COMMENT '@Deprecated @ToBeRemoved Phase3 - 三级佣金比例（即将删除）',
MODIFY COLUMN `third_commission_amount` DECIMAL(10, 2) NULL COMMENT '@Deprecated @ToBeRemoved Phase3 - 三级佣金金额（即将删除）';

-- 3.2 删除Phase 1创建的同步触发器（如果存在）
DROP TRIGGER IF EXISTS `trg_dist_goods_config_insert_sync`;
DROP TRIGGER IF EXISTS `trg_dist_goods_config_update_sync`;

-- =====================================================
-- Step 4: 创建旧字段使用监控
-- =====================================================

-- 4.1 创建旧字段使用监控表
CREATE TABLE IF NOT EXISTS `yt_dist_goods_deprecated_field_usage` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `table_name` VARCHAR(100) NOT NULL,
    `field_name` VARCHAR(100) NOT NULL,
    `access_type` VARCHAR(20) NOT NULL COMMENT 'READ or WRITE',
    `access_time` DATETIME NOT NULL,
    `access_source` VARCHAR(500) COMMENT '访问来源（如API路径）',
    `record_id` BIGINT COMMENT '相关记录ID',
    PRIMARY KEY (`id`),
    KEY `idx_access_time` (`access_time`),
    KEY `idx_field_name` (`field_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='废弃字段使用监控表';

-- =====================================================
-- Step 5: 生成Phase 3清理脚本（注释形式）
-- =====================================================

-- Phase 3执行脚本（保留30天后执行）:
/*
-- Phase 3: 最终清理旧字段
-- 执行日期：[Phase 2执行30天后]

-- 1. 最终备份
CREATE TABLE IF NOT EXISTS `yt_dist_goods_config_final_backup_[date]` AS 
SELECT * FROM `yt_dist_goods_config`;

-- 2. 删除旧字段
ALTER TABLE `yt_dist_goods_config` 
DROP COLUMN `first_commission_rate`,
DROP COLUMN `first_commission_amount`,
DROP COLUMN `second_commission_rate`,
DROP COLUMN `second_commission_amount`,
DROP COLUMN `third_commission_rate`,
DROP COLUMN `third_commission_amount`;

-- 3. 删除监控视图
DROP VIEW IF EXISTS `v_dist_goods_config_field_consistency`;
DROP VIEW IF EXISTS `v_dist_goods_migration_report`;

-- 4. 删除监控表
DROP TABLE IF EXISTS `yt_dist_goods_deprecated_field_usage`;

-- 5. 清理备份表（确认无问题后）
-- DROP TABLE IF EXISTS `yt_dist_goods_config_backup_20250806`;
-- DROP TABLE IF EXISTS `yt_dist_goods_config_backup_phase2_20250806`;
*/

-- =====================================================
-- 回滚脚本（紧急情况使用）
-- =====================================================
/*
-- 从Phase 2备份恢复数据
DROP TABLE IF EXISTS `yt_dist_goods_config`;
RENAME TABLE `yt_dist_goods_config_backup_phase2_20250806` TO `yt_dist_goods_config`;

-- 重新创建触发器（如需要）
-- [触发器创建脚本]
*/

-- =====================================================
-- 执行结果汇总
-- =====================================================
SELECT 
    'Phase 2 Execution Summary' as title,
    NOW() as execution_time,
    (SELECT COUNT(*) FROM `yt_dist_goods_config` WHERE deleted = 0) as total_records,
    (SELECT COUNT(*) FROM `yt_dist_goods_config` WHERE commission_value IS NOT NULL AND deleted = 0) as migrated_records,
    'Ready for Phase 3 (field removal) after 30 days' as next_step;