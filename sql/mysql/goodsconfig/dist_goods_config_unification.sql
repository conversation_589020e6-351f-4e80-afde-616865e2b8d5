-- =====================================================
-- 分销商品佣金结构统一化迁移脚本
-- 作者：一筒科技
-- 日期：2025-08-06
-- 描述：将六个独立的佣金字段统一为三个通用字段
-- =====================================================

-- =====================================================
-- Phase 1: 添加新字段
-- =====================================================

-- 1.1 为 yt_dist_goods_config 添加统一的佣金字段
ALTER TABLE `yt_dist_goods_config` 
ADD COLUMN `commission_value` DECIMAL(10, 2) NULL COMMENT '直接佣金值（根据commission_mode决定是比例还是金额）' AFTER `commission_mode`,
ADD COLUMN `parent_reward` DECIMAL(10, 2) NULL COMMENT '上级奖励值（根据commission_mode决定是比例还是金额）' AFTER `commission_value`,
ADD COLUMN `grand_parent_reward` DECIMAL(10, 2) NULL COMMENT '上上级奖励值（根据commission_mode决定是比例还是金额）' AFTER `parent_reward`;

-- 1.2 添加索引优化查询性能
ALTER TABLE `yt_dist_goods_config` 
ADD INDEX `idx_commission_value` (`commission_value`),
ADD INDEX `idx_commission_mode_value` (`commission_mode`, `commission_value`);

-- =====================================================
-- Phase 2: 数据迁移
-- =====================================================

-- 2.1 备份原始数据（建议在生产环境执行前先备份）
CREATE TABLE IF NOT EXISTS `yt_dist_goods_config_backup_20250806` AS 
SELECT * FROM `yt_dist_goods_config`;

-- 2.2 迁移现有数据到新字段
UPDATE `yt_dist_goods_config` 
SET 
  -- 迁移直接佣金（一级）
  `commission_value` = CASE 
    WHEN `commission_mode` = 1 THEN `first_commission_rate`     -- 比例模式
    WHEN `commission_mode` = 2 THEN `first_commission_amount`   -- 固定金额模式
    ELSE COALESCE(`first_commission_rate`, `first_commission_amount`, 0)
  END,
  -- 迁移上级奖励（二级）
  `parent_reward` = CASE 
    WHEN `commission_mode` = 1 THEN `second_commission_rate`    -- 比例模式
    WHEN `commission_mode` = 2 THEN `second_commission_amount`  -- 固定金额模式
    ELSE COALESCE(`second_commission_rate`, `second_commission_amount`, 0)
  END,
  -- 迁移上上级奖励（三级）
  `grand_parent_reward` = CASE 
    WHEN `commission_mode` = 1 THEN `third_commission_rate`     -- 比例模式
    WHEN `commission_mode` = 2 THEN `third_commission_amount`   -- 固定金额模式
    ELSE COALESCE(`third_commission_rate`, `third_commission_amount`, 0)
  END,
  `update_time` = NOW(),
  `updater` = 'SYSTEM_MIGRATION'
WHERE 1=1;

-- 2.3 验证数据迁移结果
SELECT 
  COUNT(*) as total_records,
  SUM(CASE WHEN commission_value IS NOT NULL THEN 1 ELSE 0 END) as migrated_records,
  SUM(CASE 
    WHEN commission_mode = 1 AND commission_value != first_commission_rate THEN 1
    WHEN commission_mode = 2 AND commission_value != first_commission_amount THEN 1
    ELSE 0
  END) as inconsistent_records
FROM `yt_dist_goods_config`;

-- =====================================================
-- Phase 3: 标记旧字段为废弃（保留数据用于回滚）
-- =====================================================

-- 3.1 修改字段注释，标记为废弃
ALTER TABLE `yt_dist_goods_config` 
MODIFY COLUMN `first_commission_rate` DECIMAL(10, 2) NULL COMMENT '@Deprecated 一级佣金比例（已废弃，请使用commission_value）',
MODIFY COLUMN `first_commission_amount` DECIMAL(10, 2) NULL COMMENT '@Deprecated 一级佣金金额（已废弃，请使用commission_value）',
MODIFY COLUMN `second_commission_rate` DECIMAL(10, 2) NULL COMMENT '@Deprecated 二级佣金比例（已废弃，请使用parent_reward）',
MODIFY COLUMN `second_commission_amount` DECIMAL(10, 2) NULL COMMENT '@Deprecated 二级佣金金额（已废弃，请使用parent_reward）',
MODIFY COLUMN `third_commission_rate` DECIMAL(10, 2) NULL COMMENT '@Deprecated 三级佣金比例（已废弃，请使用grand_parent_reward）',
MODIFY COLUMN `third_commission_amount` DECIMAL(10, 2) NULL COMMENT '@Deprecated 三级佣金金额（已废弃，请使用grand_parent_reward）';

-- =====================================================
-- Phase 4: 创建数据一致性检查视图
-- =====================================================

-- 4.1 创建视图用于监控新旧字段的一致性
CREATE OR REPLACE VIEW `v_dist_goods_config_consistency_check` AS
SELECT 
  id,
  spu_id,
  spu_name,
  commission_mode,
  commission_value,
  parent_reward,
  grand_parent_reward,
  first_commission_rate,
  first_commission_amount,
  second_commission_rate,
  second_commission_amount,
  third_commission_rate,
  third_commission_amount,
  CASE 
    WHEN commission_mode = 1 THEN
      CASE 
        WHEN commission_value = first_commission_rate 
          AND COALESCE(parent_reward, 0) = COALESCE(second_commission_rate, 0)
          AND COALESCE(grand_parent_reward, 0) = COALESCE(third_commission_rate, 0)
        THEN 'CONSISTENT'
        ELSE 'INCONSISTENT'
      END
    WHEN commission_mode = 2 THEN
      CASE 
        WHEN commission_value = first_commission_amount 
          AND COALESCE(parent_reward, 0) = COALESCE(second_commission_amount, 0)
          AND COALESCE(grand_parent_reward, 0) = COALESCE(third_commission_amount, 0)
        THEN 'CONSISTENT'
        ELSE 'INCONSISTENT'
      END
    ELSE 'UNKNOWN'
  END as consistency_status
FROM `yt_dist_goods_config`
WHERE deleted = 0;

-- =====================================================
-- Phase 5: 创建触发器保持数据同步（过渡期使用）
-- =====================================================

DELIMITER $$

-- 5.1 插入触发器
CREATE TRIGGER `trg_dist_goods_config_insert_sync` 
BEFORE INSERT ON `yt_dist_goods_config`
FOR EACH ROW
BEGIN
  -- 如果新字段为空但旧字段有值，从旧字段同步到新字段
  IF NEW.commission_value IS NULL THEN
    IF NEW.commission_mode = 1 AND NEW.first_commission_rate IS NOT NULL THEN
      SET NEW.commission_value = NEW.first_commission_rate;
    ELSEIF NEW.commission_mode = 2 AND NEW.first_commission_amount IS NOT NULL THEN
      SET NEW.commission_value = NEW.first_commission_amount;
    END IF;
  END IF;
  
  IF NEW.parent_reward IS NULL THEN
    IF NEW.commission_mode = 1 AND NEW.second_commission_rate IS NOT NULL THEN
      SET NEW.parent_reward = NEW.second_commission_rate;
    ELSEIF NEW.commission_mode = 2 AND NEW.second_commission_amount IS NOT NULL THEN
      SET NEW.parent_reward = NEW.second_commission_amount;
    END IF;
  END IF;
  
  IF NEW.grand_parent_reward IS NULL THEN
    IF NEW.commission_mode = 1 AND NEW.third_commission_rate IS NOT NULL THEN
      SET NEW.grand_parent_reward = NEW.third_commission_rate;
    ELSEIF NEW.commission_mode = 2 AND NEW.third_commission_amount IS NOT NULL THEN
      SET NEW.grand_parent_reward = NEW.third_commission_amount;
    END IF;
  END IF;
  
  -- 如果新字段有值但旧字段为空，从新字段同步到旧字段（向后兼容）
  IF NEW.commission_value IS NOT NULL THEN
    IF NEW.commission_mode = 1 THEN
      SET NEW.first_commission_rate = NEW.commission_value;
      SET NEW.first_commission_amount = NULL;
    ELSEIF NEW.commission_mode = 2 THEN
      SET NEW.first_commission_amount = NEW.commission_value;
      SET NEW.first_commission_rate = NULL;
    END IF;
  END IF;
  
  IF NEW.parent_reward IS NOT NULL THEN
    IF NEW.commission_mode = 1 THEN
      SET NEW.second_commission_rate = NEW.parent_reward;
      SET NEW.second_commission_amount = NULL;
    ELSEIF NEW.commission_mode = 2 THEN
      SET NEW.second_commission_amount = NEW.parent_reward;
      SET NEW.second_commission_rate = NULL;
    END IF;
  END IF;
  
  IF NEW.grand_parent_reward IS NOT NULL THEN
    IF NEW.commission_mode = 1 THEN
      SET NEW.third_commission_rate = NEW.grand_parent_reward;
      SET NEW.third_commission_amount = NULL;
    ELSEIF NEW.commission_mode = 2 THEN
      SET NEW.third_commission_amount = NEW.grand_parent_reward;
      SET NEW.third_commission_rate = NULL;
    END IF;
  END IF;
END$$

-- 5.2 更新触发器
CREATE TRIGGER `trg_dist_goods_config_update_sync` 
BEFORE UPDATE ON `yt_dist_goods_config`
FOR EACH ROW
BEGIN
  -- 同插入触发器逻辑
  IF NEW.commission_value IS NULL THEN
    IF NEW.commission_mode = 1 AND NEW.first_commission_rate IS NOT NULL THEN
      SET NEW.commission_value = NEW.first_commission_rate;
    ELSEIF NEW.commission_mode = 2 AND NEW.first_commission_amount IS NOT NULL THEN
      SET NEW.commission_value = NEW.first_commission_amount;
    END IF;
  END IF;
  
  IF NEW.parent_reward IS NULL THEN
    IF NEW.commission_mode = 1 AND NEW.second_commission_rate IS NOT NULL THEN
      SET NEW.parent_reward = NEW.second_commission_rate;
    ELSEIF NEW.commission_mode = 2 AND NEW.second_commission_amount IS NOT NULL THEN
      SET NEW.parent_reward = NEW.second_commission_amount;
    END IF;
  END IF;
  
  IF NEW.grand_parent_reward IS NULL THEN
    IF NEW.commission_mode = 1 AND NEW.third_commission_rate IS NOT NULL THEN
      SET NEW.grand_parent_reward = NEW.third_commission_rate;
    ELSEIF NEW.commission_mode = 2 AND NEW.third_commission_amount IS NOT NULL THEN
      SET NEW.grand_parent_reward = NEW.third_commission_amount;
    END IF;
  END IF;
  
  IF NEW.commission_value IS NOT NULL THEN
    IF NEW.commission_mode = 1 THEN
      SET NEW.first_commission_rate = NEW.commission_value;
      SET NEW.first_commission_amount = NULL;
    ELSEIF NEW.commission_mode = 2 THEN
      SET NEW.first_commission_amount = NEW.commission_value;
      SET NEW.first_commission_rate = NULL;
    END IF;
  END IF;
  
  IF NEW.parent_reward IS NOT NULL THEN
    IF NEW.commission_mode = 1 THEN
      SET NEW.second_commission_rate = NEW.parent_reward;
      SET NEW.second_commission_amount = NULL;
    ELSEIF NEW.commission_mode = 2 THEN
      SET NEW.second_commission_amount = NEW.parent_reward;
      SET NEW.second_commission_rate = NULL;
    END IF;
  END IF;
  
  IF NEW.grand_parent_reward IS NOT NULL THEN
    IF NEW.commission_mode = 1 THEN
      SET NEW.third_commission_rate = NEW.grand_parent_reward;
      SET NEW.third_commission_amount = NULL;
    ELSEIF NEW.commission_mode = 2 THEN
      SET NEW.third_commission_amount = NEW.grand_parent_reward;
      SET NEW.third_commission_rate = NULL;
    END IF;
  END IF;
END$$

DELIMITER ;

-- =====================================================
-- 回滚脚本（紧急情况下使用）
-- =====================================================

-- 以下为回滚脚本，请谨慎使用
/*
-- 恢复数据到旧字段
UPDATE `yt_dist_goods_config` 
SET 
  `first_commission_rate` = CASE 
    WHEN `commission_mode` = 1 THEN `commission_value`
    ELSE `first_commission_rate`
  END,
  `first_commission_amount` = CASE 
    WHEN `commission_mode` = 2 THEN `commission_value`
    ELSE `first_commission_amount`
  END,
  `second_commission_rate` = CASE 
    WHEN `commission_mode` = 1 THEN `parent_reward`
    ELSE `second_commission_rate`
  END,
  `second_commission_amount` = CASE 
    WHEN `commission_mode` = 2 THEN `parent_reward`
    ELSE `second_commission_amount`
  END,
  `third_commission_rate` = CASE 
    WHEN `commission_mode` = 1 THEN `grand_parent_reward`
    ELSE `third_commission_rate`
  END,
  `third_commission_amount` = CASE 
    WHEN `commission_mode` = 2 THEN `grand_parent_reward`
    ELSE `third_commission_amount`
  END
WHERE 1=1;

-- 删除触发器
DROP TRIGGER IF EXISTS `trg_dist_goods_config_insert_sync`;
DROP TRIGGER IF EXISTS `trg_dist_goods_config_update_sync`;

-- 删除视图
DROP VIEW IF EXISTS `v_dist_goods_config_consistency_check`;

-- 删除新字段
ALTER TABLE `yt_dist_goods_config` 
DROP COLUMN `commission_value`,
DROP COLUMN `parent_reward`,
DROP COLUMN `grand_parent_reward`;

-- 恢复原字段注释
ALTER TABLE `yt_dist_goods_config` 
MODIFY COLUMN `first_commission_rate` DECIMAL(10, 2) NULL COMMENT '一级佣金比例',
MODIFY COLUMN `first_commission_amount` DECIMAL(10, 2) NULL COMMENT '一级佣金金额',
MODIFY COLUMN `second_commission_rate` DECIMAL(10, 2) NULL COMMENT '二级佣金比例',
MODIFY COLUMN `second_commission_amount` DECIMAL(10, 2) NULL COMMENT '二级佣金金额',
MODIFY COLUMN `third_commission_rate` DECIMAL(10, 2) NULL COMMENT '三级佣金比例',
MODIFY COLUMN `third_commission_amount` DECIMAL(10, 2) NULL COMMENT '三级佣金金额';
*/

-- =====================================================
-- 验证脚本
-- =====================================================

-- 检查迁移是否成功
SELECT 
  'Total Records' as metric,
  COUNT(*) as value
FROM `yt_dist_goods_config`
UNION ALL
SELECT 
  'Records with New Fields' as metric,
  COUNT(*) as value
FROM `yt_dist_goods_config`
WHERE commission_value IS NOT NULL
UNION ALL
SELECT 
  'Inconsistent Records' as metric,
  COUNT(*) as value
FROM `v_dist_goods_config_consistency_check`
WHERE consistency_status = 'INCONSISTENT';

-- =====================================================
-- 清理脚本（完全迁移后执行）
-- =====================================================

/*
-- 最终清理（确认所有系统都已更新后执行）
-- 删除触发器
DROP TRIGGER IF EXISTS `trg_dist_goods_config_insert_sync`;
DROP TRIGGER IF EXISTS `trg_dist_goods_config_update_sync`;

-- 删除旧字段
ALTER TABLE `yt_dist_goods_config` 
DROP COLUMN `first_commission_rate`,
DROP COLUMN `first_commission_amount`,
DROP COLUMN `second_commission_rate`,
DROP COLUMN `second_commission_amount`,
DROP COLUMN `third_commission_rate`,
DROP COLUMN `third_commission_amount`;

-- 删除备份表（确认无问题后）
DROP TABLE IF EXISTS `yt_dist_goods_config_backup_20250806`;
*/