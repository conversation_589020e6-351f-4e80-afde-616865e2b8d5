-- =====================================================
-- 分销商品结算功能 - 回滚脚本
-- 执行时间: 2025-08-06
-- 功能说明: 完整回滚结算功能相关的所有数据库变更
-- 警告: 执行此脚本将删除所有结算相关的数据和表结构！
-- =====================================================

-- =====================================================
-- 安全检查
-- =====================================================

-- 1. 检查是否有未结算的佣金
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 
            CONCAT('WARNING: 存在 ', COUNT(*), ' 条未结算的佣金记录，请先处理！')
        ELSE 
            'OK: 没有未结算的佣金记录'
    END AS check_result,
    SUM(`commission_amount`) AS pending_amount
FROM `yt_dist_commission`
WHERE `settlement_status` IN (1, 2)  -- 冻结中或可结算
    AND `deleted` = 0;

-- 2. 确认要回滚（取消注释以执行）
-- SET @confirm_rollback = 'YES';

-- 仅在确认后执行以下回滚操作
-- IF @confirm_rollback = 'YES' THEN

-- =====================================================
-- Step 1: 备份当前数据（重要！）
-- =====================================================

-- 1.1 备份结算相关表
CREATE TABLE IF NOT EXISTS `yt_dist_goods_config_rollback_backup` AS 
SELECT * FROM `yt_dist_goods_config`;

CREATE TABLE IF NOT EXISTS `yt_dist_commission_rollback_backup` AS 
SELECT * FROM `yt_dist_commission`;

CREATE TABLE IF NOT EXISTS `yt_dist_settlement_batch_rollback_backup` AS 
SELECT * FROM `yt_dist_settlement_batch`;

CREATE TABLE IF NOT EXISTS `yt_dist_settlement_detail_rollback_backup` AS 
SELECT * FROM `yt_dist_settlement_detail`;

CREATE TABLE IF NOT EXISTS `yt_dist_settlement_config_history_rollback_backup` AS 
SELECT * FROM `yt_dist_settlement_config_history`;

SELECT 'Backup completed' AS status, NOW() AS backup_time;

-- =====================================================
-- Step 2: 删除视图
-- =====================================================

DROP VIEW IF EXISTS `v_dist_pending_settlement`;
DROP VIEW IF EXISTS `v_dist_settlement_statistics`;

SELECT 'Views dropped' AS status;

-- =====================================================
-- Step 3: 删除新创建的表
-- =====================================================

-- 3.1 删除结算明细表
DROP TABLE IF EXISTS `yt_dist_settlement_detail`;

-- 3.2 删除结算批次表
DROP TABLE IF EXISTS `yt_dist_settlement_batch`;

-- 3.3 删除配置历史表
DROP TABLE IF EXISTS `yt_dist_settlement_config_history`;

SELECT 'Settlement tables dropped' AS status;

-- =====================================================
-- Step 4: 回滚 yt_dist_commission 表的修改
-- =====================================================

-- 4.1 删除索引
ALTER TABLE `yt_dist_commission`
DROP INDEX `idx_settlement_status`,
DROP INDEX `idx_settlement_batch`,
DROP INDEX `idx_verification`;

-- 4.2 删除新增的字段
ALTER TABLE `yt_dist_commission`
DROP COLUMN `settlement_status`,
DROP COLUMN `freeze_time`,
DROP COLUMN `unfreeze_time`,
DROP COLUMN `settlement_time`,
DROP COLUMN `verification_time`,
DROP COLUMN `settlement_batch_no`,
DROP COLUMN `settlement_type`,
DROP COLUMN `settlement_remark`;

SELECT 'Commission table columns dropped' AS status;

-- =====================================================
-- Step 5: 回滚 yt_dist_goods_config 表的修改
-- =====================================================

-- 5.1 删除索引
ALTER TABLE `yt_dist_goods_config` 
DROP INDEX `idx_settlement`;

-- 5.2 删除新增的字段
ALTER TABLE `yt_dist_goods_config` 
DROP COLUMN `settlement_type`,
DROP COLUMN `settlement_days`,
DROP COLUMN `require_verification`,
DROP COLUMN `settlement_rule`;

SELECT 'Goods config table columns dropped' AS status;

-- =====================================================
-- Step 6: 清理测试数据（可选）
-- =====================================================

-- 6.1 清理测试佣金记录
DELETE FROM `yt_dist_commission` 
WHERE `creator` = 'TEST_INIT' 
    AND `order_no` LIKE 'TEST_ORDER_%';

SELECT 'Test data cleaned' AS status;

-- =====================================================
-- Step 7: 从原始备份恢复（可选，谨慎使用）
-- =====================================================

-- 如果需要完全恢复到修改前的状态，请执行以下操作：
/*
-- 7.1 恢复 yt_dist_goods_config 表
DROP TABLE IF EXISTS `yt_dist_goods_config`;
RENAME TABLE `yt_dist_goods_config_backup_settlement_20250806` TO `yt_dist_goods_config`;

-- 7.2 恢复 yt_dist_commission 表
DROP TABLE IF EXISTS `yt_dist_commission`;
RENAME TABLE `yt_dist_commission_backup_settlement_20250806` TO `yt_dist_commission`;
*/

-- =====================================================
-- Step 8: 验证回滚结果
-- =====================================================

-- 8.1 检查表结构
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME IN ('yt_dist_goods_config', 'yt_dist_commission')
    AND COLUMN_NAME IN (
        'settlement_type', 'settlement_days', 'settlement_status',
        'freeze_time', 'unfreeze_time', 'settlement_time'
    );

-- 8.2 检查表是否存在
SELECT 
    TABLE_NAME,
    CASE 
        WHEN COUNT(*) > 0 THEN 'ERROR: Table still exists!'
        ELSE 'OK: Table removed'
    END AS status
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME IN (
        'yt_dist_settlement_batch',
        'yt_dist_settlement_detail',
        'yt_dist_settlement_config_history'
    )
GROUP BY TABLE_NAME;

-- =====================================================
-- 最终输出
-- =====================================================

SELECT 
    'Rollback completed!' AS status,
    NOW() AS rollback_time,
    DATABASE() AS database_name,
    USER() AS executed_by,
    'Please verify all changes have been reverted correctly' AS note;

-- END IF; -- 结束确认检查

-- =====================================================
-- 保留备份表的清理脚本（确认无问题后手动执行）
-- =====================================================
/*
-- 清理回滚备份表（确认回滚成功后执行）
DROP TABLE IF EXISTS `yt_dist_goods_config_rollback_backup`;
DROP TABLE IF EXISTS `yt_dist_commission_rollback_backup`;
DROP TABLE IF EXISTS `yt_dist_settlement_batch_rollback_backup`;
DROP TABLE IF EXISTS `yt_dist_settlement_detail_rollback_backup`;
DROP TABLE IF EXISTS `yt_dist_settlement_config_history_rollback_backup`;

-- 清理原始备份表（确认系统运行正常后执行）
DROP TABLE IF EXISTS `yt_dist_goods_config_backup_settlement_20250806`;
DROP TABLE IF EXISTS `yt_dist_commission_backup_settlement_20250806`;
*/