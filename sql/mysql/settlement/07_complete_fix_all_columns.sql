-- =====================================================
-- Complete fix: Add all missing columns and fix column names
-- Execution time: 2025-08-08  
-- Purpose: Complete fix for all column issues in yt_dist_commission table
-- =====================================================

-- 1. Check if table exists
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'Table yt_dist_commission exists, proceeding with fixes...'
        ELSE 'WARNING: Table yt_dist_commission does not exist!'
    END AS table_check
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
    AND table_name = 'yt_dist_commission';

-- 2. Fix column names: goods_id → spu_id
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'yt_dist_commission' 
    AND column_name = 'goods_id'
);

SET @spu_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'yt_dist_commission' 
    AND column_name = 'spu_id'
);

-- Only rename if goods_id exists and spu_id doesn't exist
SET @sql = IF(@column_exists > 0 AND @spu_exists = 0, 
    'ALTER TABLE `yt_dist_commission` CHANGE COLUMN `goods_id` `spu_id` BIGINT NOT NULL COMMENT ''商品SPU ID''',
    'SELECT ''Column spu_id already exists or goods_id not found'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. Fix column names: goods_name → spu_name  
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'yt_dist_commission' 
    AND column_name = 'goods_name'
);

SET @spu_name_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'yt_dist_commission' 
    AND column_name = 'spu_name'
);

-- Only rename if goods_name exists and spu_name doesn't exist
SET @sql = IF(@column_exists > 0 AND @spu_name_exists = 0, 
    'ALTER TABLE `yt_dist_commission` CHANGE COLUMN `goods_name` `spu_name` VARCHAR(100) NOT NULL COMMENT ''商品SPU名称''',
    'SELECT ''Column spu_name already exists or goods_name not found'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. Add app_id column if missing
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'yt_dist_commission' 
    AND column_name = 'app_id'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `yt_dist_commission` ADD COLUMN `app_id` BIGINT NOT NULL DEFAULT 1 COMMENT ''应用ID'' AFTER `agent_id`',
    'SELECT ''Column app_id already exists'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. Update any NULL app_id values
UPDATE `yt_dist_commission` 
SET `app_id` = 1 
WHERE `app_id` IS NULL OR `app_id` = 0;

-- 6. Verify all required columns exist
SELECT 
    'Column Status Check' AS report_type,
    MAX(CASE WHEN COLUMN_NAME = 'spu_id' THEN 'EXISTS' ELSE NULL END) AS spu_id_status,
    MAX(CASE WHEN COLUMN_NAME = 'spu_name' THEN 'EXISTS' ELSE NULL END) AS spu_name_status,
    MAX(CASE WHEN COLUMN_NAME = 'app_id' THEN 'EXISTS' ELSE NULL END) AS app_id_status,
    MAX(CASE WHEN COLUMN_NAME = 'commission_amount' THEN 'EXISTS' ELSE NULL END) AS commission_amount_status,
    MAX(CASE WHEN COLUMN_NAME = 'order_id' THEN 'EXISTS' ELSE NULL END) AS order_id_status,
    MAX(CASE WHEN COLUMN_NAME = 'status' THEN 'EXISTS' ELSE NULL END) AS status_status,
    MAX(CASE WHEN COLUMN_NAME = 'deleted' THEN 'EXISTS' ELSE NULL END) AS deleted_status
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'yt_dist_commission';

-- 7. Output final result
SELECT 
    'Table structure fixed successfully!' AS status,
    NOW() AS execution_time,
    (SELECT COUNT(*) FROM `yt_dist_commission`) AS total_records,
    (SELECT COUNT(DISTINCT `app_id`) FROM `yt_dist_commission`) AS distinct_app_ids;