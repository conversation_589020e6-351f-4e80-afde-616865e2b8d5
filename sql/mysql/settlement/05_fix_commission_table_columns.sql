-- =====================================================
-- Fix yt_dist_commission table structure
-- Execution time: 2025-08-08
-- Purpose: Fix column name mismatches and add missing columns
-- =====================================================

-- 1. Check if table exists
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'Table yt_dist_commission exists, proceeding with fixes...'
        ELSE 'WARNING: Table yt_dist_commission does not exist!'
    END AS table_check
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
    AND table_name = 'yt_dist_commission';

-- 2. Backup existing data
CREATE TABLE IF NOT EXISTS `yt_dist_commission_backup_20250808` AS 
SELECT * FROM `yt_dist_commission`;

-- 3. Rename columns from goods_* to spu_*
ALTER TABLE `yt_dist_commission` 
CHANGE COLUMN `goods_id` `spu_id` BIGINT NOT NULL COMMENT '商品SPU ID',
CHANGE COLUMN `goods_name` `spu_name` VARCHAR(100) NOT NULL COMMENT '商品SPU名称';

-- 4. Add missing app_id column if it doesn't exist
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'yt_dist_commission' 
    AND column_name = 'app_id'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `yt_dist_commission` ADD COLUMN `app_id` BIGINT NOT NULL DEFAULT 1 COMMENT ''应用ID'' AFTER `agent_id`',
    'SELECT ''Column app_id already exists'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. Add missing columns if they don't exist
-- Check and add commission_level if missing
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'yt_dist_commission' 
    AND column_name = 'commission_level'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `yt_dist_commission` ADD COLUMN `commission_level` TINYINT DEFAULT 1 COMMENT ''佣金层级：1-直推，2-间推，3-团队'' AFTER `commission_mode`',
    'SELECT ''Column commission_level already exists'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. Update indexes if needed
-- Drop old index if exists (using stored procedure for conditional drop)
DROP PROCEDURE IF EXISTS drop_index_if_exists;
DELIMITER $$
CREATE PROCEDURE drop_index_if_exists(IN tableName VARCHAR(128), IN indexName VARCHAR(128))
BEGIN
    IF EXISTS (
        SELECT * FROM information_schema.statistics 
        WHERE table_schema = DATABASE() 
        AND table_name = tableName 
        AND index_name = indexName
    ) THEN
        SET @sql = CONCAT('ALTER TABLE `', tableName, '` DROP INDEX `', indexName, '`');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END$$
DELIMITER ;

-- Drop indexes if they exist
CALL drop_index_if_exists('yt_dist_commission', 'idx_order_goods');
CALL drop_index_if_exists('yt_dist_commission', 'idx_order_spu');
CALL drop_index_if_exists('yt_dist_commission', 'idx_app_id');

-- Add new indexes
ALTER TABLE `yt_dist_commission` ADD INDEX `idx_order_spu` (`order_id`, `spu_id`);
ALTER TABLE `yt_dist_commission` ADD INDEX `idx_app_id` (`app_id`);

-- Clean up
DROP PROCEDURE IF EXISTS drop_index_if_exists;

-- 7. Verify the changes
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'yt_dist_commission'
    AND COLUMN_NAME IN ('spu_id', 'spu_name', 'app_id', 'commission_level')
ORDER BY ORDINAL_POSITION;

-- 8. Output execution result
SELECT 
    'Table structure fixed successfully!' AS status,
    NOW() AS execution_time,
    (SELECT COUNT(*) FROM `yt_dist_commission`) AS total_records,
    DATABASE() AS database_name;

-- =====================================================
-- Rollback script (if needed)
-- =====================================================
/*
-- Restore from backup
DROP TABLE IF EXISTS `yt_dist_commission`;
RENAME TABLE `yt_dist_commission_backup_20250808` TO `yt_dist_commission`;

-- Or manually reverse changes
ALTER TABLE `yt_dist_commission` 
CHANGE COLUMN `spu_id` `goods_id` BIGINT NOT NULL COMMENT '商品ID',
CHANGE COLUMN `spu_name` `goods_name` VARCHAR(100) NOT NULL COMMENT '商品名称';

ALTER TABLE `yt_dist_commission` DROP COLUMN IF EXISTS `app_id`;
ALTER TABLE `yt_dist_commission` DROP COLUMN IF EXISTS `commission_level`;

ALTER TABLE `yt_dist_commission` DROP INDEX IF EXISTS `idx_order_spu`;
ALTER TABLE `yt_dist_commission` DROP INDEX IF EXISTS `idx_app_id`;
ALTER TABLE `yt_dist_commission` ADD INDEX `idx_order_goods` (`order_id`, `goods_id`);
*/