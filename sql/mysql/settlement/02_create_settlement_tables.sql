-- =====================================================
-- 分销商品结算功能 - Phase 2: 创建结算相关表和修改佣金表
-- 执行时间: 2025-08-06
-- 功能说明: 修改佣金记录表，创建结算批次表和明细表
-- =====================================================

-- =====================================================
-- Part 1: 修改佣金记录表
-- =====================================================

-- 1.1 检查佣金表是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'Table yt_dist_commission exists, proceeding...'
        ELSE 'WARNING: Table yt_dist_commission does not exist, please create it first!'
    END AS commission_table_check
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
    AND table_name = 'yt_dist_commission';

-- 1.2 备份佣金表数据
CREATE TABLE IF NOT EXISTS `yt_dist_commission_backup_settlement_20250806` AS 
SELECT * FROM `yt_dist_commission`;

-- 1.3 添加结算状态管理字段
-- 注意: freeze_time 和 settlement_time 字段可能已存在，需要先重命名
ALTER TABLE `yt_dist_commission` 
CHANGE COLUMN `freeze_time` `old_freeze_time` DATETIME DEFAULT NULL COMMENT '原冻结时间',
CHANGE COLUMN `settle_time` `old_settle_time` DATETIME DEFAULT NULL COMMENT '原结算时间';

-- 添加新的结算相关字段
ALTER TABLE `yt_dist_commission` 
ADD COLUMN `settlement_status` TINYINT NOT NULL DEFAULT 0 
    COMMENT '结算状态：0-待核销，1-冻结中，2-可结算，3-已结算，4-已失效' AFTER `status`,
ADD COLUMN `freeze_time` DATETIME DEFAULT NULL 
    COMMENT '冻结开始时间' AFTER `settlement_status`,
ADD COLUMN `unfreeze_time` DATETIME DEFAULT NULL 
    COMMENT '解冻时间（预计结算时间）' AFTER `freeze_time`,
ADD COLUMN `settlement_time` DATETIME DEFAULT NULL 
    COMMENT '实际结算时间' AFTER `unfreeze_time`,
ADD COLUMN `verification_time` DATETIME DEFAULT NULL 
    COMMENT '核销时间' AFTER `settlement_time`,
ADD COLUMN `order_time` DATETIME DEFAULT NULL 
    COMMENT '订单时间' AFTER `verification_time`,
ADD COLUMN `settlement_batch_no` VARCHAR(32) DEFAULT NULL 
    COMMENT '结算批次号' AFTER `order_time`,
ADD COLUMN `settlement_type` TINYINT DEFAULT NULL 
    COMMENT '结算类型：1-自动结算，2-手动结算' AFTER `settlement_batch_no`,
ADD COLUMN `settlement_remark` VARCHAR(500) DEFAULT NULL 
    COMMENT '结算备注' AFTER `settlement_type`;

-- 迁移原有数据
UPDATE `yt_dist_commission` 
SET `freeze_time` = `old_freeze_time`,
    `settlement_time` = `old_settle_time`,
    `order_time` = `create_time`
WHERE 1=1;

-- 删除旧字段
ALTER TABLE `yt_dist_commission` 
DROP COLUMN `old_freeze_time`,
DROP COLUMN `old_settle_time`;

-- 1.4 添加索引优化查询
ALTER TABLE `yt_dist_commission` 
ADD INDEX `idx_settlement_status` (`settlement_status`, `unfreeze_time`),
ADD INDEX `idx_settlement_batch` (`settlement_batch_no`),
ADD INDEX `idx_verification` (`verification_time`);

-- 1.5 更新现有数据的结算状态（根据实际业务逻辑调整）
UPDATE `yt_dist_commission` 
SET 
    `settlement_status` = CASE 
        WHEN `status` = 3 THEN 3  -- 已结算
        WHEN `status` = 4 THEN 4  -- 已失效
        WHEN `status` = 1 THEN 2  -- 可结算（假设status=1表示待结算）
        ELSE 1  -- 冻结中
    END,
    `update_time` = NOW()
WHERE `settlement_status` IS NULL;

-- =====================================================
-- Part 2: 创建结算批次表
-- =====================================================

-- 2.1 创建佣金结算批次表
CREATE TABLE IF NOT EXISTS `yt_dist_settlement_batch` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '批次ID',
  `batch_no` VARCHAR(32) NOT NULL COMMENT '批次号',
  `settlement_type` TINYINT NOT NULL COMMENT '结算类型：1-自动结算，2-手动结算',
  `settlement_count` INT NOT NULL DEFAULT 0 COMMENT '结算笔数',
  `settlement_amount` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '结算总金额',
  `start_time` DATETIME NOT NULL COMMENT '结算开始时间',
  `end_time` DATETIME DEFAULT NULL COMMENT '结算结束时间',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '批次状态：0-处理中，1-成功，2-部分成功，3-失败',
  `success_count` INT DEFAULT 0 COMMENT '成功笔数',
  `fail_count` INT DEFAULT 0 COMMENT '失败笔数',
  `fail_reason` TEXT COMMENT '失败原因',
  `operator` VARCHAR(64) COMMENT '操作人（手动结算时）',
  `creator` VARCHAR(64) DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) DEFAULT '' COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_batch_no` (`batch_no`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_status` (`status`),
  KEY `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销结算批次表';

-- =====================================================
-- Part 3: 创建结算明细表
-- =====================================================

-- 3.1 创建佣金结算明细表
CREATE TABLE IF NOT EXISTS `yt_dist_settlement_detail` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `batch_no` VARCHAR(32) NOT NULL COMMENT '批次号',
  `commission_id` BIGINT NOT NULL COMMENT '佣金记录ID',
  `agent_id` BIGINT NOT NULL COMMENT '分销员ID',
  `order_no` VARCHAR(64) NOT NULL COMMENT '订单号',
  `spu_id` BIGINT NOT NULL COMMENT '商品SPU ID',
  `commission_amount` DECIMAL(10,2) NOT NULL COMMENT '佣金金额',
  `settlement_status` TINYINT NOT NULL COMMENT '结算状态：0-待处理，1-成功，2-失败',
  `fail_reason` VARCHAR(500) COMMENT '失败原因',
  `process_time` DATETIME COMMENT '处理时间',
  `creator` VARCHAR(64) DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_commission_id` (`commission_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销结算明细表';

-- =====================================================
-- Part 4: 视图功能说明（已改为Java代码实现）
-- =====================================================

-- 4.1 待结算佣金监控功能
-- 功能说明：查询待结算的佣金记录，包含分销员信息和商品信息
-- Java实现：
--   VO类：DistPendingSettlementVO
--   Mapper方法：DistCommissionMapper.selectPendingSettlementList
--   Service方法：DistSettlementService.getPendingSettlementList

-- 4.2 结算统计功能
-- 功能说明：按日期统计结算情况
-- Java实现：
--   VO类：DistSettlementStatisticsVO
--   Mapper方法：DistCommissionMapper.selectSettlementStatistics
--   Service方法：DistSettlementService.getSettlementStatistics

-- =====================================================
-- Part 5: 初始化数据和验证
-- =====================================================

-- 5.1 验证表创建结果
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    ENGINE,
    TABLE_ROWS,
    CREATE_TIME
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME IN (
        'yt_dist_settlement_batch',
        'yt_dist_settlement_detail',
        'yt_dist_settlement_config_history'
    );

-- 5.2 验证字段添加结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'yt_dist_commission'
    AND COLUMN_NAME IN (
        'settlement_status', 'freeze_time', 'unfreeze_time',
        'settlement_time', 'verification_time', 'settlement_batch_no'
    );

-- 5.3 输出执行结果统计
SELECT 
    'Settlement tables created successfully!' AS status,
    (SELECT COUNT(*) FROM `yt_dist_commission` WHERE `settlement_status` = 0) AS pending_verification,
    (SELECT COUNT(*) FROM `yt_dist_commission` WHERE `settlement_status` = 1) AS frozen,
    (SELECT COUNT(*) FROM `yt_dist_commission` WHERE `settlement_status` = 2) AS can_settle,
    (SELECT COUNT(*) FROM `yt_dist_commission` WHERE `settlement_status` = 3) AS settled,
    (SELECT COUNT(*) FROM `yt_dist_commission` WHERE `settlement_status` = 4) AS invalid;

-- =====================================================
-- 回滚脚本（如需回滚请执行以下SQL）
-- =====================================================
/*
-- 1. 删除视图
DROP VIEW IF EXISTS `v_dist_pending_settlement`;
DROP VIEW IF EXISTS `v_dist_settlement_statistics`;

-- 2. 删除新创建的表
DROP TABLE IF EXISTS `yt_dist_settlement_detail`;
DROP TABLE IF EXISTS `yt_dist_settlement_batch`;

-- 3. 删除佣金表新增的字段
ALTER TABLE `yt_dist_commission`
DROP COLUMN IF EXISTS `settlement_status`,
DROP COLUMN IF EXISTS `freeze_time`,
DROP COLUMN IF EXISTS `unfreeze_time`,
DROP COLUMN IF EXISTS `settlement_time`,
DROP COLUMN IF EXISTS `verification_time`,
DROP COLUMN IF EXISTS `settlement_batch_no`,
DROP COLUMN IF EXISTS `settlement_type`,
DROP COLUMN IF EXISTS `settlement_remark`;

-- 4. 删除索引
ALTER TABLE `yt_dist_commission`
DROP INDEX IF EXISTS `idx_settlement_status`,
DROP INDEX IF EXISTS `idx_settlement_batch`,
DROP INDEX IF EXISTS `idx_verification`;

-- 5. 从备份恢复（如果需要）
-- DROP TABLE IF EXISTS `yt_dist_commission`;
-- RENAME TABLE `yt_dist_commission_backup_settlement_20250806` TO `yt_dist_commission`;
*/