-- =====================================================
-- 修复分销结算配置历史表缺失字段
-- 执行时间: 2025-08-06
-- 功能说明: 为 yt_dist_settlement_config_history 表添加缺失的基础字段
-- =====================================================

-- 1. 检查表是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'Table exists, proceeding with alterations...'
        ELSE 'ERROR: Table yt_dist_settlement_config_history does not exist!'
    END AS table_check_status
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
    AND table_name = 'yt_dist_settlement_config_history';

-- 2. 添加缺失的字段
ALTER TABLE `yt_dist_settlement_config_history`
ADD COLUMN IF NOT EXISTS `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`,
ADD COLUMN IF NOT EXISTS `updater` VARCHAR(64) DEFAULT '' COMMENT '更新者' AFTER `creator`,
ADD COLUMN IF NOT EXISTS `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除' AFTER `updater`,
ADD COLUMN IF NOT EXISTS `tenant_id` BIGINT NOT NULL DEFAULT 1 COMMENT '租户编号' AFTER `deleted`;

-- 3. 添加租户ID索引
ALTER TABLE `yt_dist_settlement_config_history`
ADD INDEX IF NOT EXISTS `idx_tenant_id` (`tenant_id`);

-- 4. 验证字段添加结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'yt_dist_settlement_config_history'
    AND COLUMN_NAME IN ('update_time', 'updater', 'deleted', 'tenant_id')
ORDER BY ORDINAL_POSITION;

-- 5. 输出执行结果
SELECT 
    'Settlement history table fields fixed successfully!' AS status,
    COUNT(*) AS column_count
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'yt_dist_settlement_config_history';

-- =====================================================
-- 回滚脚本（如需回滚请执行以下SQL）
-- =====================================================
/*
-- 删除新增的索引
ALTER TABLE `yt_dist_settlement_config_history` 
DROP INDEX IF EXISTS `idx_tenant_id`;

-- 删除新增的字段
ALTER TABLE `yt_dist_settlement_config_history` 
DROP COLUMN IF EXISTS `update_time`,
DROP COLUMN IF EXISTS `updater`,
DROP COLUMN IF EXISTS `deleted`,
DROP COLUMN IF EXISTS `tenant_id`;
*/