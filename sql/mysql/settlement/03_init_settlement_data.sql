-- =====================================================
-- 分销商品结算功能 - Phase 3: 初始化测试数据
-- 执行时间: 2025-08-06
-- 功能说明: 初始化结算配置测试数据，用于功能验证
-- 注意: 此脚本仅用于测试环境，生产环境请谨慎使用
-- =====================================================

-- =====================================================
-- Part 1: 更新现有商品的结算配置（示例）
-- =====================================================

-- 1.1 设置部分商品为核销后结算（示例数据，根据实际情况调整）
UPDATE `yt_dist_goods_config` 
SET 
    `settlement_type` = 2,  -- 核销后结算
    `settlement_days` = 7,  -- 核销后7天结算
    `require_verification` = b'1',  -- 需要核销
    `settlement_rule` = '订单核销后7天自动结算佣金，如遇退款则佣金失效',
    `update_time` = NOW(),
    `updater` = 'INIT_SCRIPT'
WHERE `spu_id` IN (
    SELECT `spu_id` FROM (
        SELECT `spu_id` FROM `yt_dist_goods_config` 
        WHERE `enable_dist` = 1 
        LIMIT 5
    ) AS temp
);

-- 1.2 设置部分商品为订单完成后结算
UPDATE `yt_dist_goods_config` 
SET 
    `settlement_type` = 3,  -- 订单完成后结算
    `settlement_days` = 15,  -- 订单完成后15天结算
    `require_verification` = b'0',  -- 不需要核销
    `settlement_rule` = '订单完成后15天自动结算佣金，超过售后期后结算更安全',
    `update_time` = NOW(),
    `updater` = 'INIT_SCRIPT'
WHERE `spu_id` IN (
    SELECT `spu_id` FROM (
        SELECT `spu_id` FROM `yt_dist_goods_config` 
        WHERE `enable_dist` = 1 
            AND `settlement_type` = 1
        LIMIT 3
    ) AS temp
);

-- =====================================================
-- Part 2: 创建测试佣金记录
-- =====================================================

-- 2.1 插入待核销的佣金记录（仅测试环境使用）
INSERT INTO `yt_dist_commission` (
    `order_no`, `agent_id`, `buyer_id`, `spu_id`, `sku_id`,
    `commission_amount`, `commission_rate`, `order_amount`,
    `settlement_status`, `status`, `order_time`,
    `creator`, `create_time`, `updater`, `update_time`
)
SELECT 
    CONCAT('TEST_ORDER_', UNIX_TIMESTAMP(), '_', ROW_NUMBER() OVER()) AS order_no,
    1 AS agent_id,  -- 假设分销员ID为1
    1001 AS buyer_id,  -- 假设买家ID
    dgc.`spu_id`,
    1 AS sku_id,  -- 假设SKU ID
    ROUND(RAND() * 100 + 10, 2) AS commission_amount,  -- 随机佣金10-110元
    10.00 AS commission_rate,  -- 10%佣金率
    ROUND(RAND() * 1000 + 100, 2) AS order_amount,  -- 随机订单金额100-1100元
    0 AS settlement_status,  -- 待核销
    1 AS status,  -- 有效
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY) AS order_time,  -- 过去30天内随机时间
    'TEST_INIT' AS creator,
    NOW() AS create_time,
    'TEST_INIT' AS updater,
    NOW() AS update_time
FROM `yt_dist_goods_config` dgc
WHERE dgc.`settlement_type` = 2  -- 需要核销的商品
    AND dgc.`enable_dist` = 1
LIMIT 10;

-- 2.2 插入冻结中的佣金记录
INSERT INTO `yt_dist_commission` (
    `order_no`, `agent_id`, `buyer_id`, `spu_id`, `sku_id`,
    `commission_amount`, `commission_rate`, `order_amount`,
    `settlement_status`, `freeze_time`, `unfreeze_time`,
    `verification_time`, `status`, `order_time`,
    `creator`, `create_time`, `updater`, `update_time`
)
SELECT 
    CONCAT('TEST_ORDER_FROZEN_', UNIX_TIMESTAMP(), '_', ROW_NUMBER() OVER()) AS order_no,
    1 AS agent_id,
    1002 AS buyer_id,
    dgc.`spu_id`,
    1 AS sku_id,
    ROUND(RAND() * 150 + 20, 2) AS commission_amount,
    15.00 AS commission_rate,
    ROUND(RAND() * 1000 + 200, 2) AS order_amount,
    1 AS settlement_status,  -- 冻结中
    DATE_SUB(NOW(), INTERVAL dgc.`settlement_days` - 2 DAY) AS freeze_time,  -- 冻结开始时间
    DATE_ADD(NOW(), INTERVAL 2 DAY) AS unfreeze_time,  -- 2天后解冻
    DATE_SUB(NOW(), INTERVAL dgc.`settlement_days` - 2 DAY) AS verification_time,  -- 核销时间
    1 AS status,
    DATE_SUB(NOW(), INTERVAL dgc.`settlement_days` DAY) AS order_time,
    'TEST_INIT' AS creator,
    NOW() AS create_time,
    'TEST_INIT' AS updater,
    NOW() AS update_time
FROM `yt_dist_goods_config` dgc
WHERE dgc.`settlement_type` IN (2, 3)
    AND dgc.`enable_dist` = 1
LIMIT 15;

-- 2.3 插入可结算的佣金记录
INSERT INTO `yt_dist_commission` (
    `order_no`, `agent_id`, `buyer_id`, `spu_id`, `sku_id`,
    `commission_amount`, `commission_rate`, `order_amount`,
    `settlement_status`, `freeze_time`, `unfreeze_time`,
    `verification_time`, `status`, `order_time`,
    `creator`, `create_time`, `updater`, `update_time`
)
SELECT 
    CONCAT('TEST_ORDER_READY_', UNIX_TIMESTAMP(), '_', ROW_NUMBER() OVER()) AS order_no,
    2 AS agent_id,  -- 不同的分销员
    1003 AS buyer_id,
    dgc.`spu_id`,
    1 AS sku_id,
    ROUND(RAND() * 200 + 30, 2) AS commission_amount,
    12.00 AS commission_rate,
    ROUND(RAND() * 1500 + 300, 2) AS order_amount,
    2 AS settlement_status,  -- 可结算
    DATE_SUB(NOW(), INTERVAL dgc.`settlement_days` + 5 DAY) AS freeze_time,
    DATE_SUB(NOW(), INTERVAL 1 DAY) AS unfreeze_time,  -- 已经过了解冻时间
    DATE_SUB(NOW(), INTERVAL dgc.`settlement_days` + 5 DAY) AS verification_time,
    1 AS status,
    DATE_SUB(NOW(), INTERVAL dgc.`settlement_days` + 10 DAY) AS order_time,
    'TEST_INIT' AS creator,
    NOW() AS create_time,
    'TEST_INIT' AS updater,
    NOW() AS update_time
FROM `yt_dist_goods_config` dgc
WHERE dgc.`settlement_type` IN (2, 3)
    AND dgc.`enable_dist` = 1
LIMIT 20;

-- =====================================================
-- Part 3: 创建结算批次测试数据
-- =====================================================

-- 3.1 创建历史结算批次记录
INSERT INTO `yt_dist_settlement_batch` (
    `batch_no`, `settlement_type`, `settlement_count`, `settlement_amount`,
    `start_time`, `end_time`, `status`, `success_count`, `fail_count`,
    `operator`, `creator`, `create_time`
)
VALUES
    ('BATCH_20250801_001', 1, 50, 5280.50, 
     '2025-08-01 02:00:00', '2025-08-01 02:05:30', 1, 50, 0,
     NULL, 'SYSTEM', '2025-08-01 02:00:00'),
    ('BATCH_20250802_001', 1, 45, 4850.00, 
     '2025-08-02 02:00:00', '2025-08-02 02:04:20', 1, 45, 0,
     NULL, 'SYSTEM', '2025-08-02 02:00:00'),
    ('BATCH_20250803_001', 1, 62, 6780.30, 
     '2025-08-03 02:00:00', '2025-08-03 02:06:15', 2, 60, 2,
     NULL, 'SYSTEM', '2025-08-03 02:00:00'),
    ('BATCH_20250804_MANUAL', 2, 10, 1250.00, 
     '2025-08-04 14:30:00', '2025-08-04 14:30:45', 1, 10, 0,
     'admin', 'admin', '2025-08-04 14:30:00');

-- =====================================================
-- Part 4: 初始化配置历史记录
-- =====================================================

-- 4.1 记录配置变更历史
INSERT INTO `yt_dist_settlement_config_history` (
    `config_id`, `spu_id`, `settlement_type`, `settlement_days`,
    `require_verification`, `change_reason`, `operator`, `operation_time`
)
SELECT 
    `id`, `spu_id`, `settlement_type`, `settlement_days`,
    `require_verification`, '初始化结算配置', 'SYSTEM', NOW()
FROM `yt_dist_goods_config`
WHERE `settlement_type` != 1  -- 非实时结算的配置
LIMIT 10;

-- =====================================================
-- Part 5: 数据验证和统计
-- =====================================================

-- 5.1 统计各状态的佣金记录数
SELECT 
    'Commission Status Distribution' AS report_type,
    SUM(CASE WHEN `settlement_status` = 0 THEN 1 ELSE 0 END) AS pending_verification,
    SUM(CASE WHEN `settlement_status` = 1 THEN 1 ELSE 0 END) AS frozen,
    SUM(CASE WHEN `settlement_status` = 2 THEN 1 ELSE 0 END) AS can_settle,
    SUM(CASE WHEN `settlement_status` = 3 THEN 1 ELSE 0 END) AS settled,
    SUM(CASE WHEN `settlement_status` = 4 THEN 1 ELSE 0 END) AS invalid,
    COUNT(*) AS total
FROM `yt_dist_commission`;

-- 5.2 统计结算配置分布
SELECT 
    'Settlement Config Distribution' AS report_type,
    SUM(CASE WHEN `settlement_type` = 1 THEN 1 ELSE 0 END) AS realtime_settlement,
    SUM(CASE WHEN `settlement_type` = 2 THEN 1 ELSE 0 END) AS after_verification,
    SUM(CASE WHEN `settlement_type` = 3 THEN 1 ELSE 0 END) AS after_completion,
    AVG(`settlement_days`) AS avg_settlement_days,
    MAX(`settlement_days`) AS max_settlement_days,
    COUNT(*) AS total_configs
FROM `yt_dist_goods_config`
WHERE `enable_dist` = 1;

-- 5.3 查看即将到期的冻结佣金（未来7天内）
SELECT 
    DATE(`unfreeze_time`) AS unfreeze_date,
    COUNT(*) AS count,
    SUM(`commission_amount`) AS total_amount,
    GROUP_CONCAT(DISTINCT `agent_id`) AS agent_ids
FROM `yt_dist_commission`
WHERE `settlement_status` = 1  -- 冻结中
    AND `unfreeze_time` BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(`unfreeze_time`)
ORDER BY unfreeze_date;

-- =====================================================
-- 清理测试数据脚本（仅在需要时执行）
-- =====================================================
/*
-- 清理测试佣金记录
DELETE FROM `yt_dist_commission` 
WHERE `creator` = 'TEST_INIT' 
    AND `order_no` LIKE 'TEST_ORDER_%';

-- 清理测试批次记录
DELETE FROM `yt_dist_settlement_batch` 
WHERE `batch_no` LIKE 'BATCH_2025%';

-- 重置商品配置为实时结算
UPDATE `yt_dist_goods_config` 
SET 
    `settlement_type` = 1,
    `settlement_days` = 0,
    `require_verification` = b'0',
    `settlement_rule` = NULL
WHERE `updater` = 'INIT_SCRIPT';

-- 清理配置历史
DELETE FROM `yt_dist_settlement_config_history` 
WHERE `operator` = 'SYSTEM';
*/

-- =====================================================
-- 输出最终统计结果
-- =====================================================
SELECT 
    'Test data initialization completed!' AS status,
    NOW() AS execution_time,
    DATABASE() AS database_name,
    USER() AS executed_by;