-- =====================================================
-- Quick fix: Add app_id column to yt_dist_commission table
-- Execution time: 2025-08-08
-- Purpose: Immediate fix for missing app_id column error
-- =====================================================

-- Add app_id column if it doesn't exist
SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'yt_dist_commission' 
    AND column_name = 'app_id'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `yt_dist_commission` ADD COLUMN `app_id` BIGINT NOT NULL DEFAULT 1 COMMENT ''应用ID'' AFTER `agent_id`',
    'SELECT ''Column app_id already exists'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Verify the column was added
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'yt_dist_commission'
    AND COLUMN_NAME = 'app_id';

-- Update existing records to have app_id = 1 if they have NULL (shouldn't happen with DEFAULT)
UPDATE `yt_dist_commission` 
SET `app_id` = 1 
WHERE `app_id` IS NULL OR `app_id` = 0;

-- Output result
SELECT 
    'app_id column added successfully!' AS status,
    NOW() AS execution_time,
    (SELECT COUNT(*) FROM `yt_dist_commission`) AS total_records,
    (SELECT COUNT(DISTINCT `app_id`) FROM `yt_dist_commission`) AS distinct_app_ids;