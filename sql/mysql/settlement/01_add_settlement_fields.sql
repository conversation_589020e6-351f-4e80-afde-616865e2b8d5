-- =====================================================
-- 分销商品结算功能 - Phase 1: 添加结算相关字段
-- 执行时间: 2025-08-06
-- 功能说明: 为 yt_dist_goods_config 表添加核销后N天结算相关字段
-- =====================================================

-- 1. 检查表是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'Table exists, proceeding with alterations...'
        ELSE 'ERROR: Table yt_dist_goods_config does not exist!'
    END AS table_check_status
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
    AND table_name = 'yt_dist_goods_config';

-- 2. 备份现有数据（安全起见）
CREATE TABLE IF NOT EXISTS `yt_dist_goods_config_backup_settlement_20250806` AS 
SELECT * FROM `yt_dist_goods_config`;

-- 3. 添加结算相关字段
ALTER TABLE `yt_dist_goods_config` 
ADD COLUMN `settlement_type` TINYINT NOT NULL DEFAULT 1 
    COMMENT '结算类型：1-实时结算，2-核销后结算，3-订单完成后结算' AFTER `commission_base_type`,
ADD COLUMN `settlement_days` INT NOT NULL DEFAULT 0 
    COMMENT '结算周期（天）：0-立即结算，N-核销/完成后N天结算' AFTER `settlement_type`,
ADD COLUMN `require_verification` BIT(1) NOT NULL DEFAULT b'0' 
    COMMENT '是否需要核销：0-不需要，1-需要' AFTER `settlement_days`,
ADD COLUMN `settlement_rule` VARCHAR(500) DEFAULT NULL 
    COMMENT '结算规则描述' AFTER `require_verification`;

-- 4. 添加索引优化查询性能
ALTER TABLE `yt_dist_goods_config` 
ADD INDEX `idx_settlement` (`settlement_type`, `settlement_days`, `status`);

-- 5. 更新现有数据的默认值（可选）
UPDATE `yt_dist_goods_config` 
SET 
    `settlement_type` = 1,  -- 默认为实时结算
    `settlement_days` = 0,  -- 默认为立即结算
    `require_verification` = b'0',  -- 默认不需要核销
    `update_time` = NOW(),
    `updater` = 'SYSTEM_MIGRATION'
WHERE `settlement_type` IS NULL;

-- 6. 验证字段添加结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'yt_dist_goods_config'
    AND COLUMN_NAME IN ('settlement_type', 'settlement_days', 'require_verification', 'settlement_rule')
ORDER BY ORDINAL_POSITION;

-- 7. 创建结算配置历史表
CREATE TABLE IF NOT EXISTS `yt_dist_settlement_config_history` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_id` BIGINT NOT NULL COMMENT '配置ID',
  `spu_id` BIGINT NOT NULL COMMENT '商品SPU ID',
  `settlement_type` TINYINT NOT NULL COMMENT '结算类型',
  `settlement_days` INT NOT NULL COMMENT '结算周期（天）',
  `require_verification` BIT(1) NOT NULL COMMENT '是否需要核销',
  `change_reason` VARCHAR(500) COMMENT '变更原因',
  `operator` VARCHAR(64) COMMENT '操作人',
  `operation_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `creator` VARCHAR(64) DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_spu_id` (`spu_id`),
  KEY `idx_operation_time` (`operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销结算配置历史表';

-- 8. 输出执行结果
SELECT 
    'Settlement fields added successfully!' AS status,
    COUNT(*) AS total_records,
    SUM(CASE WHEN settlement_type = 1 THEN 1 ELSE 0 END) AS realtime_count,
    SUM(CASE WHEN settlement_type = 2 THEN 1 ELSE 0 END) AS after_verification_count,
    SUM(CASE WHEN settlement_type = 3 THEN 1 ELSE 0 END) AS after_completion_count
FROM `yt_dist_goods_config`;

-- =====================================================
-- 回滚脚本（如需回滚请执行以下SQL）
-- =====================================================
/*
-- 1. 删除新增的索引
ALTER TABLE `yt_dist_goods_config` 
DROP INDEX IF EXISTS `idx_settlement`;

-- 2. 删除新增的字段
ALTER TABLE `yt_dist_goods_config` 
DROP COLUMN IF EXISTS `settlement_type`,
DROP COLUMN IF EXISTS `settlement_days`,
DROP COLUMN IF EXISTS `require_verification`,
DROP COLUMN IF EXISTS `settlement_rule`;

-- 3. 删除历史表
DROP TABLE IF EXISTS `yt_dist_settlement_config_history`;

-- 4. 从备份恢复（如果需要）
-- DROP TABLE IF EXISTS `yt_dist_goods_config`;
-- RENAME TABLE `yt_dist_goods_config_backup_settlement_20250806` TO `yt_dist_goods_config`;
*/