-- =====================================================
-- 分销结算功能 - 系统字典数据初始化
-- 执行时间: 2025-08-06
-- 功能说明: 初始化结算相关的系统字典数据
-- =====================================================

-- =====================================================
-- Part 1: 清理旧数据（防止重复）
-- =====================================================

-- 删除已存在的字典数据
DELETE FROM `system_dict_data` WHERE `dict_type` IN (
    'dist_settlement_type',
    'dist_settlement_status', 
    'dist_batch_status',
    'dist_settlement_method'
);

-- 删除已存在的字典类型
DELETE FROM `system_dict_type` WHERE `type` IN (
    'dist_settlement_type',
    'dist_settlement_status',
    'dist_batch_status', 
    'dist_settlement_method'
);

-- =====================================================
-- Part 2: 插入字典类型
-- =====================================================

INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES 
('分销结算类型', 'dist_settlement_type', 0, '分销佣金结算类型', 'admin', NOW(), 'admin', NOW(), b'0'),
('分销结算状态', 'dist_settlement_status', 0, '分销佣金结算状态', 'admin', NOW(), 'admin', NOW(), b'0'),
('分销批次状态', 'dist_batch_status', 0, '分销结算批次状态', 'admin', NOW(), 'admin', NOW(), b'0'),
('分销结算方式', 'dist_settlement_method', 0, '分销结算方式', 'admin', NOW(), 'admin', NOW(), b'0');

-- =====================================================
-- Part 3: 插入字典数据
-- =====================================================

-- 3.1 结算类型
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES 
(1, '实时结算', '1', 'dist_settlement_type', 0, 'success', '', '订单支付成功后立即结算佣金', 'admin', NOW(), 'admin', NOW(), b'0'),
(2, '核销后结算', '2', 'dist_settlement_type', 0, 'warning', '', '订单核销后按配置天数结算佣金', 'admin', NOW(), 'admin', NOW(), b'0'),
(3, '订单完成后结算', '3', 'dist_settlement_type', 0, 'info', '', '订单完成后按配置天数结算佣金', 'admin', NOW(), 'admin', NOW(), b'0');

-- 3.2 结算状态
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES 
(1, '待核销', '0', 'dist_settlement_status', 0, 'info', '', '等待订单核销', 'admin', NOW(), 'admin', NOW(), b'0'),
(2, '冻结中', '1', 'dist_settlement_status', 0, 'warning', '', '佣金冻结中，等待结算周期', 'admin', NOW(), 'admin', NOW(), b'0'),
(3, '可结算', '2', 'dist_settlement_status', 0, 'success', '', '已到结算时间，可以结算', 'admin', NOW(), 'admin', NOW(), b'0'),
(4, '已结算', '3', 'dist_settlement_status', 0, 'primary', '', '佣金已结算到账', 'admin', NOW(), 'admin', NOW(), b'0'),
(5, '已失效', '4', 'dist_settlement_status', 0, 'danger', '', '订单退款或取消，佣金失效', 'admin', NOW(), 'admin', NOW(), b'0');

-- 3.3 批次状态
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES 
(1, '处理中', '0', 'dist_batch_status', 0, 'warning', '', '结算批次正在处理', 'admin', NOW(), 'admin', NOW(), b'0'),
(2, '成功', '1', 'dist_batch_status', 0, 'success', '', '结算批次全部成功', 'admin', NOW(), 'admin', NOW(), b'0'),
(3, '部分成功', '2', 'dist_batch_status', 0, 'warning', '', '结算批次部分成功', 'admin', NOW(), 'admin', NOW(), b'0'),
(4, '失败', '3', 'dist_batch_status', 0, 'danger', '', '结算批次处理失败', 'admin', NOW(), 'admin', NOW(), b'0');

-- 3.4 结算方式
INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
VALUES 
(1, '自动结算', '1', 'dist_settlement_method', 0, 'primary', '', '系统定时任务自动结算', 'admin', NOW(), 'admin', NOW(), b'0'),
(2, '手动结算', '2', 'dist_settlement_method', 0, 'success', '', '运营人员手动触发结算', 'admin', NOW(), 'admin', NOW(), b'0');

-- =====================================================
-- Part 4: 补充已有字典（如果不存在）
-- =====================================================

-- 4.1 佣金模式
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
SELECT '分销佣金模式', 'dist_commission_mode', 0, '分销佣金计算模式', 'admin', NOW(), 'admin', NOW(), b'0'
FROM DUAL WHERE NOT EXISTS (SELECT 1 FROM `system_dict_type` WHERE `type` = 'dist_commission_mode');

INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
SELECT * FROM (
    SELECT 1 as sort, '按比例' as label, '1' as value, 'dist_commission_mode' as dict_type, 0 as status, 'primary' as color_type, '' as css_class, '按商品价格百分比计算' as remark, 'admin' as creator, NOW() as create_time, 'admin' as updater, NOW() as update_time, b'0' as deleted
    UNION ALL
    SELECT 2, '固定金额', '2', 'dist_commission_mode', 0, 'success', '', '固定佣金金额', 'admin', NOW(), 'admin', NOW(), b'0'
) t WHERE NOT EXISTS (SELECT 1 FROM `system_dict_data` WHERE `dict_type` = 'dist_commission_mode' AND `value` = t.value);

-- 4.2 佣金类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
SELECT '分销佣金类型', 'dist_commission_type', 0, '分销佣金类型', 'admin', NOW(), 'admin', NOW(), b'0'
FROM DUAL WHERE NOT EXISTS (SELECT 1 FROM `system_dict_type` WHERE `type` = 'dist_commission_type');

INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
SELECT * FROM (
    SELECT 1 as sort, '统一佣金' as label, '1' as value, 'dist_commission_type' as dict_type, 0 as status, 'primary' as color_type, '' as css_class, '所有等级相同佣金' as remark, 'admin' as creator, NOW() as create_time, 'admin' as updater, NOW() as update_time, b'0' as deleted
    UNION ALL
    SELECT 2, '差异化佣金', '2', 'dist_commission_type', 0, 'success', '', '按等级差异化佣金', 'admin', NOW(), 'admin', NOW(), b'0'
) t WHERE NOT EXISTS (SELECT 1 FROM `system_dict_data` WHERE `dict_type` = 'dist_commission_type' AND `value` = t.value);

-- 4.3 佣金基数类型
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
SELECT '分销佣金基数类型', 'dist_commission_base_type', 0, '分销佣金计算基数', 'admin', NOW(), 'admin', NOW(), b'0'
FROM DUAL WHERE NOT EXISTS (SELECT 1 FROM `system_dict_type` WHERE `type` = 'dist_commission_base_type');

INSERT INTO `system_dict_data` (`sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) 
SELECT * FROM (
    SELECT 1 as sort, '商品原价' as label, '1' as value, 'dist_commission_base_type' as dict_type, 0 as status, 'primary' as color_type, '' as css_class, '按商品原价计算佣金' as remark, 'admin' as creator, NOW() as create_time, 'admin' as updater, NOW() as update_time, b'0' as deleted
    UNION ALL
    SELECT 2, '实付金额', '2', 'dist_commission_base_type', 0, 'success', '', '按实际支付金额计算佣金', 'admin', NOW(), 'admin', NOW(), b'0'
    UNION ALL
    SELECT 3, '利润金额', '3', 'dist_commission_base_type', 0, 'warning', '', '按利润金额计算佣金', 'admin', NOW(), 'admin', NOW(), b'0'
) t WHERE NOT EXISTS (SELECT 1 FROM `system_dict_data` WHERE `dict_type` = 'dist_commission_base_type' AND `value` = t.value);

-- =====================================================
-- Part 5: 验证字典数据
-- =====================================================

-- 5.1 查询所有结算相关字典
SELECT 
    dt.name AS dict_name,
    dt.type AS dict_type,
    dd.label,
    dd.value,
    dd.color_type,
    dd.remark
FROM `system_dict_type` dt
LEFT JOIN `system_dict_data` dd ON dt.type = dd.dict_type
WHERE dt.type IN (
    'dist_settlement_type',
    'dist_settlement_status',
    'dist_batch_status',
    'dist_settlement_method',
    'dist_commission_mode',
    'dist_commission_type',
    'dist_commission_base_type'
)
AND dt.deleted = b'0'
AND dd.deleted = b'0'
ORDER BY dt.type, dd.sort;

-- 5.2 统计字典数据
SELECT 
    '字典初始化完成' AS status,
    COUNT(DISTINCT dt.type) AS dict_type_count,
    COUNT(dd.id) AS dict_data_count,
    NOW() AS init_time
FROM `system_dict_type` dt
LEFT JOIN `system_dict_data` dd ON dt.type = dd.dict_type
WHERE dt.type LIKE 'dist_%'
AND dt.deleted = b'0'
AND dd.deleted = b'0';

-- =====================================================
-- 回滚脚本（如需回滚请执行以下SQL）
-- =====================================================
/*
-- 删除字典数据
DELETE FROM `system_dict_data` WHERE `dict_type` IN (
    'dist_settlement_type',
    'dist_settlement_status',
    'dist_batch_status',
    'dist_settlement_method',
    'dist_commission_mode',
    'dist_commission_type',
    'dist_commission_base_type'
);

-- 删除字典类型
DELETE FROM `system_dict_type` WHERE `type` IN (
    'dist_settlement_type',
    'dist_settlement_status',
    'dist_batch_status',
    'dist_settlement_method',
    'dist_commission_mode',
    'dist_commission_type',
    'dist_commission_base_type'
);
*/