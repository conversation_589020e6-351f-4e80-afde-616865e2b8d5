/*
 Navicat Premium Dump SQL

 Source Server         : yitong-prod【dev】
 Source Server Type    : MySQL
 Source Server Version : 80030 (8.0.30-cynos)
 Source Host           : sh-cynosdbmysql-grp-dlfua43g.sql.tencentcdb.com:23555
 Source Schema         : paidan_aigc_prod

 Target Server Type    : MySQL
 Target Server Version : 80030 (8.0.30-cynos)
 File Encoding         : 65001

 Date: 10/08/2025 23:19:24
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for aigc_formula
-- ----------------------------
DROP TABLE IF EXISTS `aigc_formula`;
CREATE TABLE `aigc_formula` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `processing_style` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运算方式',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态 DRAFT-草稿 RELEASE-正式版本',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `variable_ids` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '变量ID列表 JSON格式',
  `tag_ids` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签ID列表 JSON格式',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version` bigint unsigned NOT NULL DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=676 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci AVG_ROW_LENGTH=227 ROW_FORMAT=DYNAMIC COMMENT='公式';

-- ----------------------------
-- Table structure for aigc_formula_track
-- ----------------------------
DROP TABLE IF EXISTS `aigc_formula_track`;
CREATE TABLE `aigc_formula_track` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `owned_formula_id` bigint unsigned NOT NULL COMMENT '所属公式ID',
  `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类型 VIDEO-视频 SPEECH-口播',
  `min_material_count` int DEFAULT NULL COMMENT '需要素材片段最小总数量',
  `max_material_count` int DEFAULT NULL COMMENT '需要素材片段最大总数量',
  `min_material_set_category_count` int DEFAULT NULL COMMENT '需要素材集类别最小总数量',
  `max_material_set_category_count` int DEFAULT NULL COMMENT '需要素材集类别最大总数量',
  `min_time` int unsigned DEFAULT NULL,
  `max_time` int unsigned DEFAULT NULL,
  `min_word_count` int DEFAULT NULL,
  `max_word_count` int DEFAULT NULL,
  `subtitle_style_material_set` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '视频字幕样式素材集',
  `flower_text_material_set` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '视频花字素材及样式',
  `speech_voice_material_set` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '-- 废弃',
  `width` int DEFAULT NULL,
  `height` int DEFAULT NULL,
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version` bigint unsigned NOT NULL DEFAULT '0' COMMENT '版本号',
  `first_image_material` varchar(10000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '首图样式',
  `first_image_styles` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `speech_styles` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '-- 废弃',
  `speech_voices` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `speech_engine_type` tinyint(1) DEFAULT NULL COMMENT '语音合成引擎类型: 1=阿里云智能媒体服务TTS, 2=火山引擎语音合成',
  `total_time_type` int DEFAULT NULL COMMENT '视频时长设置方式 0-手工指定 1-不限时长 2-参照口播时长',
  `material_use_limit_type` tinyint DEFAULT '1' COMMENT '素材使用限制类型: 1-系统限制(系统默认) 2-不限制 3-指定(需要配置次数)',
  `material_use_limit` int DEFAULT NULL COMMENT '素材使用次数限制 (当material_use_limit_type为3时有效)',
  `unified_timbre` tinyint(1) DEFAULT '0' COMMENT '使用统一音色，1-是，0-否',
  PRIMARY KEY (`id`),
  KEY `idx_formula_track_use_limit_type` (`material_use_limit_type`)
) ENGINE=InnoDB AUTO_INCREMENT=5665 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='公式轨道';

-- ----------------------------
-- Table structure for aigc_formula_track_clip
-- ----------------------------
DROP TABLE IF EXISTS `aigc_formula_track_clip`;
CREATE TABLE `aigc_formula_track_clip` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `owned_formula_id` bigint unsigned NOT NULL COMMENT '所属公式ID',
  `owned_formula_track_id` bigint DEFAULT NULL COMMENT '所属公式轨道Id',
  `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类型',
  `material_set_id` bigint unsigned DEFAULT NULL COMMENT '素材集ID',
  `formula_id` bigint DEFAULT NULL COMMENT '公式IdD',
  `first` bit(1) DEFAULT NULL COMMENT '可展示在首位',
  `min_material_count` int DEFAULT NULL COMMENT '最小素材数量',
  `max_material_count` int DEFAULT NULL COMMENT '最大素材数量',
  `dependency_id` bigint DEFAULT NULL COMMENT '所依赖的素材集或者公式',
  `dependency_relation` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '依赖关系',
  `before_dependency` bit(1) DEFAULT NULL COMMENT '在依赖的素材的前/后展示',
  `transition_on` bit(1) DEFAULT NULL COMMENT '是否转场',
  `transitions` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '转场效果',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version` bigint unsigned NOT NULL DEFAULT '0' COMMENT '版本号',
  `dependencies` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `mute` bit(1) DEFAULT NULL COMMENT '是否消除原音 0-不消除 1-消除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9001 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='轨道片段';

-- ----------------------------
-- Table structure for aigc_formula_track_materialset_ratio
-- ----------------------------
DROP TABLE IF EXISTS `aigc_formula_track_materialset_ratio`;
CREATE TABLE `aigc_formula_track_materialset_ratio` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `formula_track_id` bigint DEFAULT NULL COMMENT '公式轨道',
  `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型',
  `material_set_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类型 VIDEO-视频 SPEECH-口播',
  `ratio` int DEFAULT NULL COMMENT '需要素材片段最小总数量',
  `formula_id` bigint unsigned NOT NULL COMMENT '所属公式ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `update_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
  `version` bigint unsigned NOT NULL DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=787 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='公式素材占比';

-- ----------------------------
-- Table structure for aigc_media
-- ----------------------------
DROP TABLE IF EXISTS `aigc_media`;
CREATE TABLE `aigc_media` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `parent_id` bigint DEFAULT NULL COMMENT '父媒体ID',
  `job_id` bigint unsigned DEFAULT NULL COMMENT 'JOB ID',
  `formula_id` bigint DEFAULT NULL COMMENT '公式ID',
  `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '媒体类型',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '状态',
  `width` int unsigned DEFAULT NULL COMMENT '视频或图片宽度',
  `height` int unsigned DEFAULT NULL COMMENT '视频或图片宽度',
  `produce_job_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '智能云job id',
  `file_id` bigint DEFAULT NULL COMMENT '成品文件ID',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '媒体URL',
  `error` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '失败描述',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version` bigint unsigned NOT NULL DEFAULT '0' COMMENT '版本号',
  `duration` bigint DEFAULT NULL COMMENT '音频/视频时长(毫秒)',
  `content` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文案内容',
  `word_count` int DEFAULT NULL COMMENT '文案字符数',
  `del_flag` tinyint DEFAULT '0' COMMENT '逻辑删除标识 0-未删除 1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`) USING BTREE,
  KEY `idx_job_id` (`job_id`) USING BTREE,
  KEY `idx_formula_id` (`formula_id`) USING BTREE,
  KEY `idx_produce_job_id` (`produce_job_id`) USING BTREE,
  KEY `idx_type` (`type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=483554 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='生成的媒体';

-- ----------------------------
-- Table structure for aigc_media_producing_job
-- ----------------------------
DROP TABLE IF EXISTS `aigc_media_producing_job`;
CREATE TABLE `aigc_media_producing_job` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `model_id` bigint unsigned DEFAULT NULL COMMENT '模型ID(基于模型的任务)',
  `formula_id` bigint DEFAULT NULL COMMENT '公式ID(基于公式的任务)',
  `create_method` int DEFAULT NULL COMMENT '运行方式 1-立即全部生成 2-自动补足 3-等待触发生成',
  `event_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态',
  `target_size` int NOT NULL COMMENT '目标数量',
  `initialize_size` int DEFAULT '0',
  `accomplish_success_size` int DEFAULT NULL,
  `accomplish_fail_size` int DEFAULT NULL,
  `accomplish_size` int NOT NULL DEFAULT '0' COMMENT '已完成数量',
  `launch_time` datetime DEFAULT NULL COMMENT '启动时间',
  `accomplish_time` datetime DEFAULT NULL COMMENT '完成时间',
  `failed_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '失败描述',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version` bigint unsigned NOT NULL DEFAULT '0' COMMENT '版本号',
  `plan_size` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=31629 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='媒体生成任务';

-- ----------------------------
-- Table structure for aigc_media_producing_job_variable_value
-- ----------------------------
DROP TABLE IF EXISTS `aigc_media_producing_job_variable_value`;
CREATE TABLE `aigc_media_producing_job_variable_value` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `variable_id` bigint NOT NULL,
  `value1` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `value2` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version` bigint unsigned NOT NULL DEFAULT '0' COMMENT '版本号',
  `job_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='媒体生成任务变量取值';

-- ----------------------------
-- Table structure for aigc_media_track
-- ----------------------------
DROP TABLE IF EXISTS `aigc_media_track`;
CREATE TABLE `aigc_media_track` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `media_id` bigint unsigned DEFAULT NULL COMMENT '媒体ID',
  `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类型',
  `z_index` int unsigned DEFAULT NULL COMMENT 'Z Index 大的在小的上面',
  `x` decimal(11,10) DEFAULT NULL COMMENT '左上相对坐标X[0-1)',
  `y` decimal(11,10) DEFAULT NULL COMMENT '左上相对坐标Y[0-1)',
  `width` decimal(11,10) DEFAULT NULL COMMENT '相对宽度(0-1]',
  `height` decimal(11,10) DEFAULT NULL COMMENT '相对高度(0-1]',
  `keep_origin_voice` bit(1) DEFAULT NULL COMMENT '是否保留原声',
  `subtitle_style` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字幕样式',
  `speech_voice` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '口播音色',
  `speech_engine_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '语音合成引擎类型',
  `loop` bit(1) DEFAULT NULL COMMENT '是否循环播放',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version` bigint unsigned NOT NULL DEFAULT '0' COMMENT '版本号',
  `volume` decimal(4,2) DEFAULT NULL COMMENT '音量[0-10] 1-元音',
  `word_count` int DEFAULT NULL COMMENT '口播/文案轨道总字数',
  `duration` int DEFAULT NULL COMMENT '音频/视频时长（毫秒)',
  `del_flag` tinyint DEFAULT '0' COMMENT '逻辑删除标识 0-未删除 1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_media_id` (`media_id`) USING BTREE,
  KEY `idx_type` (`type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=729117 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='媒体轨道';

-- ----------------------------
-- Table structure for aigc_media_track_material
-- ----------------------------
DROP TABLE IF EXISTS `aigc_media_track_material`;
CREATE TABLE `aigc_media_track_material` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `media_id` bigint unsigned DEFAULT NULL COMMENT '媒体ID',
  `track_id` bigint unsigned DEFAULT NULL COMMENT '媒体轨道ID',
  `seq_id` int DEFAULT NULL COMMENT '在轨道上的序号',
  `material_id` bigint unsigned DEFAULT NULL COMMENT '素材ID',
  `material_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材类型',
  `material_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图片/音频/视频类素材的地址',
  `transition` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '转场效果代码',
  `keep_origin_voice` bit(1) DEFAULT NULL COMMENT '废弃-挪到轨道上配置',
  `voice` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '废弃-挪到轨道上配置',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version` bigint unsigned NOT NULL DEFAULT '0' COMMENT '版本号',
  `bg_color` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '背景颜色',
  `content` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文本素材的内容',
  `source_media_id` bigint DEFAULT NULL COMMENT '生成素材的媒体ID',
  `max_out` int DEFAULT NULL COMMENT '视音频类素材使用的长度[0-max_out], NULL表示整段素材',
  `volume` decimal(4,2) DEFAULT NULL COMMENT '素材音量按原音的倍数[0-10]',
  `source` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '素材来源',
  `clip_id` bigint DEFAULT NULL COMMENT '公式片段ID',
  `duration` int DEFAULT NULL COMMENT '素材时长(毫秒)',
  `word_count` int DEFAULT NULL COMMENT '素材字符数',
  `del_flag` tinyint DEFAULT '0' COMMENT '逻辑删除标识 0-未删除 1-已删除',
  PRIMARY KEY (`id`),
  KEY `IDX_MATERIAL_ID` (`material_id`) USING BTREE,
  KEY `IDX_MEDIA_ID` (`media_id`) USING BTREE,
  KEY `idx_source_media_id` (`source_media_id`) USING BTREE,
  KEY `idx_track_id` (`track_id`) USING BTREE,
  KEY `idx_seq_id` (`seq_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1304132 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='媒体轨道素材';

-- ----------------------------
-- Table structure for aigc_model
-- ----------------------------
DROP TABLE IF EXISTS `aigc_model`;
CREATE TABLE `aigc_model` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `type` tinyint DEFAULT NULL COMMENT '类型：1视频，2图文',
  `status` tinyint DEFAULT NULL COMMENT '类型：1草稿，2 已完成 ',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型名称',
  `cover_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型封面图',
  `core_values` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '核心价值',
  `model_highlights` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型亮点',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `scene` tinyint DEFAULT NULL COMMENT '场景',
  `industry` tinyint DEFAULT NULL COMMENT '行业',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是，0-否',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=137 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='aigc模型';

-- ----------------------------
-- Table structure for aigc_model_item
-- ----------------------------
DROP TABLE IF EXISTS `aigc_model_item`;
CREATE TABLE `aigc_model_item` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `model_id` bigint DEFAULT NULL COMMENT '模型Id',
  `type` tinyint DEFAULT NULL COMMENT '类型 ：0 标题 1首图，2 视频 3 视频介绍 4 tag 5 次图 6文案内容',
  `tag_group_id` bigint DEFAULT NULL COMMENT '标签集Id',
  `formula_id` bigint DEFAULT NULL COMMENT '公式Id',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1003 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='aigc模型规则项';

-- ----------------------------
-- Table structure for aigc_model_scene
-- ----------------------------
DROP TABLE IF EXISTS `aigc_model_scene`;
CREATE TABLE `aigc_model_scene` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `model_id` bigint DEFAULT NULL COMMENT '模型Id',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场景描述',
  `script_style` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '脚本风格',
  `sort` int DEFAULT '0' COMMENT '排序',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=478 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='aigc模型脚本场景';

-- ----------------------------
-- Table structure for aigc_model_scene_storyboard
-- ----------------------------
DROP TABLE IF EXISTS `aigc_model_scene_storyboard`;
CREATE TABLE `aigc_model_scene_storyboard` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `model_id` bigint DEFAULT NULL COMMENT '模型Id',
  `model_scene_id` bigint DEFAULT NULL COMMENT '模型场景Id',
  `storyboard_num` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分镜编号',
  `storyboard_remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分镜描述',
  `storyboard_times` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分镜时长',
  `sort` int DEFAULT '99' COMMENT '排序',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=478 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='aigc模型脚本场景分镜';

-- ----------------------------
-- Table structure for aigc_model_varible
-- ----------------------------
DROP TABLE IF EXISTS `aigc_model_varible`;
CREATE TABLE `aigc_model_varible` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'Id',
  `model_id` bigint DEFAULT NULL COMMENT '模型Id',
  `formula_id` bigint DEFAULT NULL COMMENT '归属公式id',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '名称',
  `type` int DEFAULT NULL COMMENT '类型',
  `varible_id` bigint DEFAULT NULL COMMENT '变量Id',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='aigc模型变量';

-- ----------------------------
-- Table structure for aigc_oss_file
-- ----------------------------
DROP TABLE IF EXISTS `aigc_oss_file`;
CREATE TABLE `aigc_oss_file` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '文件名',
  `is_dir` bigint DEFAULT '0' COMMENT '是否文件夹，0是文件，1是文件夹',
  `dir_id` bigint DEFAULT '0' COMMENT '所属文件夹id',
  `dir_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '所属文件夹父级Id，多个用,隔开',
  `file_size` bigint DEFAULT '0' COMMENT '文件大小，单位B',
  `file_type` tinyint(1) DEFAULT '0' COMMENT '文件类型，0文件夹，1-视频，2-图片，3-文案，4-背景音，5-旁白配音，6-音色，7-混合',
  `md5` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '文件MD5',
  `content` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '文案',
  `suffix` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '文件后缀',
  `duration` bigint DEFAULT '0' COMMENT '音视频时长（秒）',
  `pages` int DEFAULT '0' COMMENT '文档页数',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '文件相对路径',
  `source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '来源 1 人工创建 2 AI生成 3 人工导入',
  `thum_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '文件缩略图相对路径',
  `sp_id` bigint DEFAULT NULL COMMENT '所属商家ID',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是，0-否',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_md5` (`md5`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=221560 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='素材库表';

-- ----------------------------
-- Table structure for aigc_publish
-- ----------------------------
DROP TABLE IF EXISTS `aigc_publish`;
CREATE TABLE `aigc_publish` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '规则名称',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '规则描述',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `account_type` tinyint(1) DEFAULT '1' COMMENT '发布规则账号类型（1-按职人、2-按门店）',
  `task_num` int DEFAULT '0' COMMENT '任务总务',
  `account_num` int DEFAULT '0' COMMENT '账号总数',
  `type` tinyint DEFAULT NULL COMMENT '规则类型 1 按日 2 轮询',
  `status` tinyint DEFAULT '1' COMMENT '类型：1草稿，2 完成 ',
  `user_day_limit` int DEFAULT '1' COMMENT '每日每用户最多生成数量',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是，0-否',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_spu_id` (`name`) USING BTREE,
  KEY `idx_store_id` (`start_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=72 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='aigc发布';

-- ----------------------------
-- Table structure for aigc_publish_account
-- ----------------------------
DROP TABLE IF EXISTS `aigc_publish_account`;
CREATE TABLE `aigc_publish_account` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `publish_id` bigint DEFAULT NULL COMMENT '发布规则Id',
  `store_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '门店Id',
  `user_mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '员工手机号',
  `publish_account` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '发布账号',
  `platform_type` tinyint DEFAULT NULL COMMENT '发布平台，1 小红书 ，2 抖音',
  `publish_num` int DEFAULT '1' COMMENT '已发布总数',
  `total_limit` int DEFAULT '0' COMMENT '总发布数限制',
  `user_day_limit` int DEFAULT '0' COMMENT '每日每用户最多生成数量',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '规则描述',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是，0-否',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_publish_id` (`publish_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6626 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='aigc发布账号';

-- ----------------------------
-- Table structure for aigc_publish_record
-- ----------------------------
DROP TABLE IF EXISTS `aigc_publish_record`;
CREATE TABLE `aigc_publish_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `publish_id` bigint DEFAULT NULL COMMENT '发布规则Id',
  `publish_account_id` bigint DEFAULT NULL COMMENT '发布规则账号Id',
  `task_id` bigint DEFAULT NULL COMMENT '任务Id',
  `task_record_id` bigint DEFAULT NULL COMMENT '任务记录Id',
  `model_id` bigint DEFAULT NULL COMMENT '模型Id',
  `store_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '门店Id',
  `user_mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '员工手机号',
  `publish_account` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '发布账号',
  `platform_type` tinyint DEFAULT NULL COMMENT '发布平台，1 小红书 ，2 抖音',
  `item_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '笔记名称',
  `item_url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '笔记地址',
  `item_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '笔记Id',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是，0-否',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11035 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='aigc发布账号记录';

-- ----------------------------
-- Table structure for aigc_publish_task
-- ----------------------------
DROP TABLE IF EXISTS `aigc_publish_task`;
CREATE TABLE `aigc_publish_task` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `publish_id` bigint DEFAULT NULL COMMENT '发布规则Id',
  `task_id` bigint DEFAULT NULL COMMENT '任务Id',
  `platform_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '发布平台，多个'',''',
  `total_num` int DEFAULT '1' COMMENT '生成总数, 0 不限制',
  `used_num` int DEFAULT '1' COMMENT '已生成总数',
  `sort` int DEFAULT '99' COMMENT '排序',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是，0-否',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_publish_id` (`publish_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5598 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='aigc发布任务';

-- ----------------------------
-- Table structure for aigc_speech_voice
-- ----------------------------
DROP TABLE IF EXISTS `aigc_speech_voice`;
CREATE TABLE `aigc_speech_voice` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `file_id` bigint NOT NULL COMMENT '素材库id（aigc_oss_file表id）',
  `tts_platform` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'TTS平台，如 ali_ice_tts、volcengine_tts',
  `scene_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '场景标签，如 客服、通用、角色扮演等',
  `language` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '语种，例：中文、美式英语、日语、西语',
  `accent` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '口音（可选），如 北京、广东、四川等',
  `is_emotional` tinyint(1) DEFAULT '0' COMMENT '是否支持情感（如启用多情感）',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'voice值',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `short_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version` bigint unsigned NOT NULL DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code_platform` (`tts_platform`,`code`,`language`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=859 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='口播音色';

-- ----------------------------
-- Table structure for aigc_speech_voice_emotion
-- ----------------------------
DROP TABLE IF EXISTS `aigc_speech_voice_emotion`;
CREATE TABLE `aigc_speech_voice_emotion` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `voice_id` bigint NOT NULL COMMENT '音色ID',
  `emotion_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '情感code',
  `emotion` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '情感名称，如 开心、悲伤、中性 等',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '是否删除，1-是，0-否',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_voice_emotion` (`voice_id`,`emotion_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='TTS音色情感支持';

-- ----------------------------
-- Table structure for aigc_tag_category
-- ----------------------------
DROP TABLE IF EXISTS `aigc_tag_category`;
CREATE TABLE `aigc_tag_category` (
  `category_id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '名称',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '路径',
  `p_id` bigint DEFAULT NULL COMMENT '父ID',
  `type` tinyint(1) DEFAULT NULL COMMENT '类型',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是，0-否',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`category_id`) USING BTREE,
  KEY `idx_name` (`name`) USING BTREE,
  KEY `idx_path` (`path`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='标签分类表';

-- ----------------------------
-- Table structure for aigc_tag_group
-- ----------------------------
DROP TABLE IF EXISTS `aigc_tag_group`;
CREATE TABLE `aigc_tag_group` (
  `group_id` bigint NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL COMMENT '类型:1-视频，2-图片，3-文案，4-背景音，5-旁白配音，6-音色，7-混合',
  `group_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签集名称',
  `query_group` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签集Json',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '是否删除:1-是，0-否',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`group_id`) USING BTREE,
  KEY `idx_group_name` (`group_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=901 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='标签集【素材集】';

-- ----------------------------
-- Table structure for aigc_tag_info_keys
-- ----------------------------
DROP TABLE IF EXISTS `aigc_tag_info_keys`;
CREATE TABLE `aigc_tag_info_keys` (
  `tag_key_id` bigint NOT NULL AUTO_INCREMENT,
  `category_id` bigint DEFAULT NULL COMMENT '分类id',
  `tag_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签key',
  `p_id` bigint DEFAULT NULL COMMENT '父id',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '路径',
  `is_mutex` tinyint(1) DEFAULT NULL COMMENT '是否互斥',
  `rule` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '规则',
  `weight` int DEFAULT NULL COMMENT '权重',
  `ext` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '扩展JSON',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是，0-否',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`tag_key_id`) USING BTREE,
  KEY `idx_category_id` (`category_id`) USING BTREE,
  KEY `idx_p_id` (`p_id`) USING BTREE,
  KEY `idx_tag_key` (`tag_key`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='标签key表';

-- ----------------------------
-- Table structure for aigc_tag_info_rel
-- ----------------------------
DROP TABLE IF EXISTS `aigc_tag_info_rel`;
CREATE TABLE `aigc_tag_info_rel` (
  `rel_id` bigint NOT NULL AUTO_INCREMENT,
  `category_id` bigint DEFAULT NULL COMMENT '分类id',
  `tag_key_id` bigint DEFAULT NULL COMMENT '标签key id',
  `tag_value_id` bigint DEFAULT NULL COMMENT '标签value id',
  `object_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '对象id',
  `weight` int DEFAULT NULL COMMENT '权重',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是，0-否',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`rel_id`) USING BTREE,
  KEY `idx_tag_key_id` (`tag_key_id`) USING BTREE,
  KEY `idx_tag_value_id` (`tag_value_id`) USING BTREE,
  KEY `idx_category_id` (`category_id`) USING BTREE,
  KEY `idx_object_id` (`object_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='标签关联表';

-- ----------------------------
-- Table structure for aigc_tag_info_values
-- ----------------------------
DROP TABLE IF EXISTS `aigc_tag_info_values`;
CREATE TABLE `aigc_tag_info_values` (
  `tag_value_id` bigint NOT NULL AUTO_INCREMENT,
  `tag_key_id` bigint DEFAULT NULL COMMENT '标签key id',
  `tag_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签值',
  `rule` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '规则',
  `weight` int DEFAULT NULL COMMENT '权重',
  `ext` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '扩展JSON',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是，0-否',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`tag_value_id`) USING BTREE,
  KEY `idx_tag_key_id` (`tag_key_id`) USING BTREE,
  KEY `idx_tag_value` (`tag_value`) USING BTREE,
  KEY `idx_weight` (`weight`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=699 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='标签值表';

-- ----------------------------
-- Table structure for aigc_tag_object_rel
-- ----------------------------
DROP TABLE IF EXISTS `aigc_tag_object_rel`;
CREATE TABLE `aigc_tag_object_rel` (
  `rel_id` bigint NOT NULL AUTO_INCREMENT,
  `object_type_id` bigint DEFAULT NULL,
  `tag_key_id` bigint DEFAULT NULL,
  `tag_value_id` bigint DEFAULT NULL,
  `object_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`rel_id`) USING BTREE,
  KEY `idx_category_id` (`object_type_id`) USING BTREE,
  KEY `idx_tag_key_id` (`tag_key_id`) USING BTREE,
  KEY `idx_tag_value_id` (`tag_value_id`) USING BTREE,
  KEY `idx_object_id` (`object_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=257774 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='标签对象关联表';

-- ----------------------------
-- Table structure for aigc_tag_object_types
-- ----------------------------
DROP TABLE IF EXISTS `aigc_tag_object_types`;
CREATE TABLE `aigc_tag_object_types` (
  `type_id` bigint NOT NULL AUTO_INCREMENT,
  `object_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '对象名称',
  `object_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '对象编码',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是，0-否',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`type_id`) USING BTREE,
  KEY `idx_object_name` (`object_name`) USING BTREE,
  KEY `idx_object_code` (`object_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='标签对象类型表';

-- ----------------------------
-- Table structure for aigc_tag_rel_types
-- ----------------------------
DROP TABLE IF EXISTS `aigc_tag_rel_types`;
CREATE TABLE `aigc_tag_rel_types` (
  `type_id` bigint NOT NULL AUTO_INCREMENT,
  `type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是，0-否',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`type_id`) USING BTREE,
  KEY `idx_type_name` (`type_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='标签关系类型表';

-- ----------------------------
-- Table structure for aigc_task
-- ----------------------------
DROP TABLE IF EXISTS `aigc_task`;
CREATE TABLE `aigc_task` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务名称',
  `model_id` bigint DEFAULT NULL COMMENT '模型Id',
  `type` tinyint DEFAULT NULL COMMENT '生成方式 1 立即全部生成 2 自动补足 3 等待触发生成',
  `total_num` int DEFAULT NULL COMMENT '生成总数量',
  `used_num` int DEFAULT NULL COMMENT '已使用数量',
  `generated_num` int DEFAULT NULL COMMENT '已生成数量',
  `cancel_num` int DEFAULT '0' COMMENT '已作废数量',
  `pre_generate_num` int DEFAULT '0' COMMENT '每次生成数量',
  `pre_threshold_num` int DEFAULT '0' COMMENT '每次生成阈值',
  `preview_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型预览地址',
  `file_id` bigint DEFAULT NULL COMMENT '预览文件ID，关联aigc_oss_file表\n',
  `preview_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '预览内容完整信息(JSON格式)',
  `media_job_id` bigint DEFAULT NULL COMMENT '媒体任务Id',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务描述',
  `status` tinyint DEFAULT '1' COMMENT '类型：1草稿，2 执行中 3 已完成 ',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:1-是，0-否',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_spu_id` (`name`) USING BTREE,
  KEY `idx_model` (`model_id`)
) ENGINE=InnoDB AUTO_INCREMENT=486 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='aigc任务【模型使用】';

-- ----------------------------
-- Table structure for aigc_task_media_his
-- ----------------------------
DROP TABLE IF EXISTS `aigc_task_media_his`;
CREATE TABLE `aigc_task_media_his` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `task_id` bigint NOT NULL COMMENT '任务id（aigc_task表主键）',
  `media_job_id` bigint NOT NULL COMMENT '所属生成任务id（aigc_media_producing_job表id）',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `del_flag` tinyint(1) unsigned zerofill DEFAULT '0' COMMENT '是否删除（1-是/0-否）',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=31599 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='aigc任务生成历史记录表';

-- ----------------------------
-- Table structure for aigc_task_record
-- ----------------------------
DROP TABLE IF EXISTS `aigc_task_record`;
CREATE TABLE `aigc_task_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `status` tinyint DEFAULT '1' COMMENT '状态：1.未使用，2 已使用 3 已作废',
  `task_id` bigint DEFAULT NULL COMMENT '任务Id',
  `media_job_id` bigint DEFAULT NULL COMMENT '媒体任务Id',
  `model_id` bigint DEFAULT NULL COMMENT '模型Id',
  `model_variable` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型参数JSON',
  `item_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '笔记名称',
  `item_type` tinyint DEFAULT NULL COMMENT '笔记类型：1视频，2图文',
  `item_cover_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '笔记封面图',
  `item_img` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '笔记图多个","分隔',
  `item_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '笔记内容',
  `item_video_url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '笔记视频地址',
  `item_tags` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '笔记标签多个","分隔',
  `file_id` bigint DEFAULT NULL COMMENT '预览文件ID，关联aigc_oss_file表\n',
  `remark` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_spu_id` (`task_id`) USING BTREE,
  KEY `idx_store_id` (`model_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=38483 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='aigc任务记录【模型使用】';

-- ----------------------------
-- Table structure for aigc_task_variable
-- ----------------------------
DROP TABLE IF EXISTS `aigc_task_variable`;
CREATE TABLE `aigc_task_variable` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `task_id` bigint DEFAULT NULL COMMENT '任务Id',
  `model_id` bigint DEFAULT NULL COMMENT '模型Id',
  `variable_id` bigint DEFAULT NULL COMMENT '模型参数id',
  `variable_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型参数值',
  `remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_spu_id` (`task_id`) USING BTREE,
  KEY `idx_store_id` (`model_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='aigc任务参数【模型使用】';

-- ----------------------------
-- Table structure for aigc_transition
-- ----------------------------
DROP TABLE IF EXISTS `aigc_transition`;
CREATE TABLE `aigc_transition` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version` bigint unsigned NOT NULL DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='转场效果';

-- ----------------------------
-- Table structure for aigc_variable
-- ----------------------------
DROP TABLE IF EXISTS `aigc_variable`;
CREATE TABLE `aigc_variable` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `type` int NOT NULL COMMENT '类型',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `tag_ids` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version` bigint unsigned NOT NULL DEFAULT '0' COMMENT '版本号',
  `default_value` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '默认值',
  `options` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '选项列表JSON，格式[{"key":"", "name":""}, ...]',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='aigc-变量';

-- ----------------------------
-- Table structure for aigc_vfx_config
-- ----------------------------
DROP TABLE IF EXISTS `aigc_vfx_config`;
CREATE TABLE `aigc_vfx_config` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '特效code',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '特效名称',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '特效预览地址',
  `type` tinyint(1) DEFAULT NULL COMMENT '类型（1-转场特效，2-字幕入场特效，3-字幕出场特效）',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '是否删除（1-是，0-否）',
  `create_by` bigint DEFAULT '0' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT '0' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version` bigint unsigned NOT NULL DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='特效配置表';

-- ----------------------------
-- Table structure for aigc_word_style
-- ----------------------------
DROP TABLE IF EXISTS `aigc_word_style`;
CREATE TABLE `aigc_word_style` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `file_id` bigint NOT NULL COMMENT '素材库id（aigc_oss_file表id）',
  `style_title` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '样式标题',
  `background_type` tinyint(1) DEFAULT NULL COMMENT '预览背景类型（1-纯色，2-图片，3-视频）',
  `background_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '预览背景内容（rgb颜色、图片、视频地址）',
  `preview_text` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '预览文字',
  `height` int DEFAULT NULL COMMENT '高',
  `width` int DEFAULT NULL COMMENT '宽',
  `font_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字体名称',
  `font_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字体code',
  `font_type` tinyint(1) DEFAULT NULL COMMENT '字体样式（1-普通样式/2-花字样式）',
  `font_size` int DEFAULT NULL COMMENT '字体大小，单位px',
  `horizontal_align` tinyint(1) DEFAULT NULL COMMENT '水平对齐方式（1-左对齐、2-居中、3-右对齐）',
  `horizontal_position` int DEFAULT NULL COMMENT '水平位置，比例（%）',
  `vertical_position` int DEFAULT NULL COMMENT '垂直位置，比例（%）',
  `font_color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字体颜色',
  `stroke_color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文字描边颜色',
  `stroke_width` int DEFAULT NULL COMMENT '文字描边宽度',
  `font_bg_color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文字背景颜色',
  `font_shadow_color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '文字阴影颜色',
  `font_shadow_offset_x` int DEFAULT NULL COMMENT '文字阴影x轴偏移量',
  `font_shadow_offset_y` int DEFAULT NULL COMMENT '文字阴影y轴偏移量',
  `rotate` int DEFAULT NULL COMMENT '旋转角度',
  `effects_in` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '入场特效',
  `effects_out` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出场特效',
  `time_line_in` int DEFAULT NULL COMMENT '特效入场时间',
  `time_line_out` int DEFAULT NULL COMMENT '特效出场时间',
  `wordNum` int DEFAULT NULL COMMENT '最多文案字数',
  `status` tinyint(1) DEFAULT NULL COMMENT '状态（1-草稿，2-完成）',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '是否删除（1-是/0-否）',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_file_id` (`file_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=54 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='文字样式素材库配置表';

-- ----------------------------
-- Table structure for font_config
-- ----------------------------
DROP TABLE IF EXISTS `font_config`;
CREATE TABLE `font_config` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字体名称',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字体代码',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字体文件URL',
  `preview_text` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '预览文字',
  `category` tinyint(1) DEFAULT NULL COMMENT '字体分类',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认字体（1-是/0-否）',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '是否删除（1-是/0-否）',
  `status` tinyint(1) DEFAULT NULL COMMENT '状态（1-启用/0-禁用）',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间 ',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_code` (`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='字体配置表';
SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data` (
                                 `dict_code` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
                                 `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '000000' COMMENT '租户编号',
                                 `dict_sort` int DEFAULT '0' COMMENT '字典排序',
                                 `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典标签',
                                 `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典键值',
                                 `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典类型',
                                 `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
                                 `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '表格回显样式',
                                 `edit_disable` tinyint(1) DEFAULT '0' COMMENT '编辑状态是否禁止选择（1-是/0-否）',
                                 `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
                                 `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
                                 `create_by` bigint DEFAULT NULL COMMENT '创建者',
                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `update_by` bigint DEFAULT NULL COMMENT '更新者',
                                 `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
                                 PRIMARY KEY (`dict_code`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='字典数据表';

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type` (
                                 `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
                                 `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '000000' COMMENT '租户编号',
                                 `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典名称',
                                 `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典类型',
                                 `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
                                 `create_by` bigint DEFAULT NULL COMMENT '创建者',
                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `update_by` bigint DEFAULT NULL COMMENT '更新者',
                                 `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
                                 PRIMARY KEY (`dict_id`),
                                 UNIQUE KEY `uk_tenant_ dict_type` (`tenant_id`,`dict_type`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='字典类型表';
