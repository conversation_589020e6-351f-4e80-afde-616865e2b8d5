-- =====================================================
-- AIGC Media 模块数据库迁移脚本 - 第二阶段：添加租户支持
-- 执行时间：2025-08-10
-- 说明：为所有业务表添加tenant_id字段
-- =====================================================

-- 1. 为所有业务表添加租户ID字段（默认租户ID为1）
ALTER TABLE `yt_aigc_formula` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_formula_track` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_formula_track_clip` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_formula_track_materialset_ratio` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_media` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_media_producing_job` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_media_producing_job_variable_value` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_media_track` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_media_track_material` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_model` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_model_item` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_model_scene` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_model_scene_storyboard` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_model_varible` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_oss_file` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_publish` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_publish_account` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_publish_record` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_publish_task` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_task` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_task_media_his` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_task_record` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_task_variable` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE `yt_aigc_tag_group` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `group_id`;

-- 系统级配置表使用租户ID=0
ALTER TABLE `yt_aigc_speech_voice` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户ID(0表示系统级)' AFTER `id`;
ALTER TABLE `yt_aigc_speech_voice_emotion` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户ID(0表示系统级)' AFTER `id`;
ALTER TABLE `yt_aigc_transition` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户ID(0表示系统级)' AFTER `id`;
ALTER TABLE `yt_aigc_vfx_config` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户ID(0表示系统级)' AFTER `id`;
ALTER TABLE `yt_aigc_word_style` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户ID(0表示系统级)' AFTER `id`;
ALTER TABLE `yt_font_config` ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户ID(0表示系统级)' AFTER `id`;

-- 2. 添加租户索引以优化查询性能
ALTER TABLE `yt_aigc_formula` ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE `yt_aigc_formula_track` ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE `yt_aigc_media` ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE `yt_aigc_media_producing_job` ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE `yt_aigc_model` ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE `yt_aigc_oss_file` ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE `yt_aigc_publish` ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE `yt_aigc_publish_account` ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE `yt_aigc_task` ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE `yt_aigc_tag_group` ADD INDEX `idx_tenant_id` (`tenant_id`);

-- 3. 创建复合索引优化常用查询
ALTER TABLE `yt_aigc_task` ADD INDEX `idx_tenant_status` (`tenant_id`, `status`);
ALTER TABLE `yt_aigc_task` ADD INDEX `idx_tenant_model` (`tenant_id`, `model_id`);
ALTER TABLE `yt_aigc_media` ADD INDEX `idx_tenant_type` (`tenant_id`, `type`);
ALTER TABLE `yt_aigc_media` ADD INDEX `idx_tenant_formula` (`tenant_id`, `formula_id`);
ALTER TABLE `yt_aigc_task_record` ADD INDEX `idx_tenant_task_status` (`tenant_id`, `task_id`, `status`);
ALTER TABLE `yt_aigc_publish_record` ADD INDEX `idx_tenant_publish` (`tenant_id`, `publish_id`);

-- 4. 回滚脚本
/*
-- 删除复合索引
ALTER TABLE `yt_aigc_task` DROP INDEX `idx_tenant_status`;
ALTER TABLE `yt_aigc_task` DROP INDEX `idx_tenant_model`;
ALTER TABLE `yt_aigc_media` DROP INDEX `idx_tenant_type`;
ALTER TABLE `yt_aigc_media` DROP INDEX `idx_tenant_formula`;
ALTER TABLE `yt_aigc_task_record` DROP INDEX `idx_tenant_task_status`;
ALTER TABLE `yt_aigc_publish_record` DROP INDEX `idx_tenant_publish`;

-- 删除租户索引
ALTER TABLE `yt_aigc_formula` DROP INDEX `idx_tenant_id`;
ALTER TABLE `yt_aigc_formula_track` DROP INDEX `idx_tenant_id`;
ALTER TABLE `yt_aigc_media` DROP INDEX `idx_tenant_id`;
ALTER TABLE `yt_aigc_media_producing_job` DROP INDEX `idx_tenant_id`;
ALTER TABLE `yt_aigc_model` DROP INDEX `idx_tenant_id`;
ALTER TABLE `yt_aigc_oss_file` DROP INDEX `idx_tenant_id`;
ALTER TABLE `yt_aigc_publish` DROP INDEX `idx_tenant_id`;
ALTER TABLE `yt_aigc_publish_account` DROP INDEX `idx_tenant_id`;
ALTER TABLE `yt_aigc_task` DROP INDEX `idx_tenant_id`;
ALTER TABLE `yt_aigc_tag_group` DROP INDEX `idx_tenant_id`;

-- 删除tenant_id字段
ALTER TABLE `yt_aigc_formula` DROP COLUMN `tenant_id`;
ALTER TABLE `yt_aigc_formula_track` DROP COLUMN `tenant_id`;
-- ... 其他表类似
*/