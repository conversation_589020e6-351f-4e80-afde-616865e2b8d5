-- AIGC Media 模块数据库迁移脚本 v1.0
-- 执行顺序：1.重命名表 -> 2.添加租户字段 -> 3.字段标准化 -> 4.索引优化
-- 注意：执行前请备份数据库！

-- =====================================================
-- 第一步：批量重命名表（添加 yt_ 前缀）
-- =====================================================
RENAME TABLE 
  aigc_formula TO yt_aigc_formula,
  aigc_formula_track TO yt_aigc_formula_track,
  aigc_formula_track_clip TO yt_aigc_formula_track_clip,
  aigc_formula_track_materialset_ratio TO yt_aigc_formula_track_materialset_ratio,
  aigc_media TO yt_aigc_media,
  aigc_media_producing_job TO yt_aigc_media_producing_job,
  aigc_media_producing_job_variable_value TO yt_aigc_media_producing_job_variable_value,
  aigc_media_track TO yt_aigc_media_track,
  aigc_media_track_material TO yt_aigc_media_track_material,
  aigc_model TO yt_aigc_model,
  aigc_model_item TO yt_aigc_model_item,
  aigc_model_scene TO yt_aigc_model_scene,
  aigc_model_scene_storyboard TO yt_aigc_model_scene_storyboard,
  aigc_model_varible TO yt_aigc_model_varible,
  aigc_oss_file TO yt_aigc_oss_file,
  aigc_publish TO yt_aigc_publish,
  aigc_publish_account TO yt_aigc_publish_account,
  aigc_publish_record TO yt_aigc_publish_record,
  aigc_publish_task TO yt_aigc_publish_task,
  aigc_speech_voice TO yt_aigc_speech_voice,
  aigc_speech_voice_emotion TO yt_aigc_speech_voice_emotion,
  aigc_tag_category TO yt_aigc_tag_category,
  aigc_tag_group TO yt_aigc_tag_group,
  aigc_tag_info_keys TO yt_aigc_tag_info_keys,
  aigc_tag_info_rel TO yt_aigc_tag_info_rel,
  aigc_tag_info_values TO yt_aigc_tag_info_values,
  aigc_tag_object_rel TO yt_aigc_tag_object_rel,
  aigc_tag_object_types TO yt_aigc_tag_object_types,
  aigc_tag_rel_types TO yt_aigc_tag_rel_types,
  aigc_task TO yt_aigc_task,
  aigc_task_media_his TO yt_aigc_task_media_his,
  aigc_task_record TO yt_aigc_task_record,
  aigc_task_variable TO yt_aigc_task_variable,
  aigc_transition TO yt_aigc_transition,
  aigc_variable TO yt_aigc_variable,
  aigc_vfx_config TO yt_aigc_vfx_config,
  aigc_word_style TO yt_aigc_word_style,
  font_config TO yt_font_config;

-- =====================================================
-- 第二步：添加租户字段（tenant_id）
-- =====================================================

-- 核心业务表添加租户ID
ALTER TABLE yt_aigc_formula ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_formula_track ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_formula_track_clip ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_formula_track_materialset_ratio ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;

ALTER TABLE yt_aigc_media ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_media_producing_job ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_media_producing_job_variable_value ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_media_track ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_media_track_material ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;

ALTER TABLE yt_aigc_model ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_model_item ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_model_scene ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_model_scene_storyboard ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_model_varible ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;

ALTER TABLE yt_aigc_oss_file ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;

ALTER TABLE yt_aigc_publish ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_publish_account ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_publish_record ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_publish_task ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;

ALTER TABLE yt_aigc_task ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_task_media_his ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_task_record ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;
ALTER TABLE yt_aigc_task_variable ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;

ALTER TABLE yt_aigc_tag_group ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `group_id`;
ALTER TABLE yt_aigc_tag_category ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `category_id`;
ALTER TABLE yt_aigc_tag_info_keys ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `tag_key_id`;
ALTER TABLE yt_aigc_tag_info_rel ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `rel_id`;
ALTER TABLE yt_aigc_tag_info_values ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `tag_value_id`;
ALTER TABLE yt_aigc_tag_object_rel ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `rel_id`;

ALTER TABLE yt_aigc_variable ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户ID' AFTER `id`;

-- 字典类表添加租户ID（0表示系统级）
ALTER TABLE yt_aigc_speech_voice ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户ID(0表示系统级)' AFTER `id`;
ALTER TABLE yt_aigc_speech_voice_emotion ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户ID(0表示系统级)' AFTER `id`;
ALTER TABLE yt_aigc_tag_object_types ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户ID(0表示系统级)' AFTER `type_id`;
ALTER TABLE yt_aigc_tag_rel_types ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户ID(0表示系统级)' AFTER `type_id`;
ALTER TABLE yt_aigc_transition ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户ID(0表示系统级)' AFTER `id`;
ALTER TABLE yt_aigc_vfx_config ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户ID(0表示系统级)' AFTER `id`;
ALTER TABLE yt_aigc_word_style ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户ID(0表示系统级)' AFTER `id`;
ALTER TABLE yt_font_config ADD COLUMN `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户ID(0表示系统级)' AFTER `id`;

-- =====================================================
-- 第三步：字段标准化（转换为框架标准）
-- =====================================================

-- 3.1 转换 create_by/update_by 字段类型（bigint -> varchar）
ALTER TABLE yt_aigc_formula 
  MODIFY COLUMN `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  MODIFY COLUMN `update_by` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_formula_track 
  MODIFY COLUMN `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  MODIFY COLUMN `update_by` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_formula_track_clip 
  MODIFY COLUMN `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  MODIFY COLUMN `update_by` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_media 
  MODIFY COLUMN `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  MODIFY COLUMN `update_by` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_media_producing_job 
  MODIFY COLUMN `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  MODIFY COLUMN `update_by` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_media_track 
  MODIFY COLUMN `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  MODIFY COLUMN `update_by` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_media_track_material 
  MODIFY COLUMN `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  MODIFY COLUMN `update_by` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_model 
  MODIFY COLUMN `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  MODIFY COLUMN `update_by` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_task 
  MODIFY COLUMN `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  MODIFY COLUMN `update_by` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_publish 
  MODIFY COLUMN `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  MODIFY COLUMN `update_by` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_oss_file 
  MODIFY COLUMN `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  MODIFY COLUMN `update_by` varchar(64) DEFAULT '' COMMENT '更新者';

-- 继续为其他表转换...

-- 3.2 重命名字段（create_by -> creator, update_by -> updater）
ALTER TABLE yt_aigc_formula 
  CHANGE COLUMN `create_by` `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  CHANGE COLUMN `update_by` `updater` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_formula_track 
  CHANGE COLUMN `create_by` `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  CHANGE COLUMN `update_by` `updater` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_media 
  CHANGE COLUMN `create_by` `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  CHANGE COLUMN `update_by` `updater` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_model 
  CHANGE COLUMN `create_by` `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  CHANGE COLUMN `update_by` `updater` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_task 
  CHANGE COLUMN `create_by` `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  CHANGE COLUMN `update_by` `updater` varchar(64) DEFAULT '' COMMENT '更新者';

-- 继续为其他表重命名...

-- 3.3 转换 del_flag -> deleted
ALTER TABLE yt_aigc_media 
  CHANGE COLUMN `del_flag` `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除';

ALTER TABLE yt_aigc_media_track 
  CHANGE COLUMN `del_flag` `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除';

ALTER TABLE yt_aigc_media_track_material 
  CHANGE COLUMN `del_flag` `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除';

ALTER TABLE yt_aigc_model 
  CHANGE COLUMN `del_flag` `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除';

ALTER TABLE yt_aigc_oss_file 
  CHANGE COLUMN `del_flag` `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除';

ALTER TABLE yt_aigc_publish 
  CHANGE COLUMN `del_flag` `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除';

ALTER TABLE yt_aigc_task 
  CHANGE COLUMN `del_flag` `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除';

ALTER TABLE yt_aigc_task_media_his 
  CHANGE COLUMN `del_flag` `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除';

-- 继续为其他表转换...

-- =====================================================
-- 第四步：添加索引优化
-- =====================================================

-- 4.1 添加租户ID索引
ALTER TABLE yt_aigc_formula ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE yt_aigc_formula_track ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE yt_aigc_media ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE yt_aigc_model ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE yt_aigc_task ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE yt_aigc_publish ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE yt_aigc_oss_file ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE yt_aigc_tag_group ADD INDEX `idx_tenant_id` (`tenant_id`);

-- 4.2 添加租户复合索引
ALTER TABLE yt_aigc_task ADD INDEX `idx_tenant_status` (`tenant_id`, `status`);
ALTER TABLE yt_aigc_task ADD INDEX `idx_tenant_model` (`tenant_id`, `model_id`);
ALTER TABLE yt_aigc_media ADD INDEX `idx_tenant_type` (`tenant_id`, `type`);
ALTER TABLE yt_aigc_media ADD INDEX `idx_tenant_formula` (`tenant_id`, `formula_id`);
ALTER TABLE yt_aigc_media ADD INDEX `idx_tenant_parent` (`tenant_id`, `parent_id`);

ALTER TABLE yt_aigc_task_record ADD INDEX `idx_tenant_task` (`tenant_id`, `task_id`);
ALTER TABLE yt_aigc_task_record ADD INDEX `idx_tenant_status` (`tenant_id`, `status`);
ALTER TABLE yt_aigc_publish_record ADD INDEX `idx_tenant_publish` (`tenant_id`, `publish_id`);

ALTER TABLE yt_aigc_media_track ADD INDEX `idx_tenant_media` (`tenant_id`, `media_id`);
ALTER TABLE yt_aigc_media_track_material ADD INDEX `idx_tenant_track` (`tenant_id`, `track_id`);
ALTER TABLE yt_aigc_media_track_material ADD INDEX `idx_tenant_media` (`tenant_id`, `media_id`);

ALTER TABLE yt_aigc_model_item ADD INDEX `idx_tenant_model` (`tenant_id`, `model_id`);
ALTER TABLE yt_aigc_model_scene ADD INDEX `idx_tenant_model` (`tenant_id`, `model_id`);

-- =====================================================
-- 第五步：数据字典迁移
-- =====================================================

-- 5.1 创建字典类型
-- 注意：system_dict_type 表在项目中的实际表名，根据实际情况调整
INSERT INTO system_dict_type (dict_name, dict_type, status, remark, creator, create_time)
VALUES 
  ('AIGC语音引擎类型', 'aigc_speech_engine_type', 0, 'AIGC语音合成引擎类型', '1', NOW()),
  ('AIGC媒体类型', 'aigc_media_type', 0, 'AIGC媒体类型', '1', NOW()),
  ('AIGC媒体状态', 'aigc_media_status', 0, 'AIGC媒体处理状态', '1', NOW()),
  ('AIGC公式状态', 'aigc_formula_status', 0, 'AIGC公式状态', '1', NOW()),
  ('AIGC任务状态', 'aigc_task_status', 0, 'AIGC任务执行状态', '1', NOW()),
  ('AIGC模型类型', 'aigc_model_type', 0, 'AIGC模型类型', '1', NOW()),
  ('AIGC模型状态', 'aigc_model_status', 0, 'AIGC模型状态', '1', NOW()),
  ('AIGC发布平台', 'aigc_platform_type', 0, 'AIGC发布平台类型', '1', NOW()),
  ('AIGC素材类型', 'aigc_material_type', 0, 'AIGC素材文件类型', '1', NOW()),
  ('AIGC转场特效', 'aigc_transition_effect', 0, 'AIGC视频转场特效', '1', NOW()),
  ('AIGC特效类型', 'aigc_vfx_type', 0, 'AIGC特效类型', '1', NOW()),
  ('AIGC字体样式类型', 'aigc_font_style_type', 0, 'AIGC字体样式类型', '1', NOW()),
  ('AIGC背景类型', 'aigc_background_type', 0, 'AIGC字幕背景类型', '1', NOW()),
  ('AIGC对齐方式', 'aigc_align_type', 0, 'AIGC文字对齐方式', '1', NOW()),
  ('AIGC任务生成方式', 'aigc_task_create_method', 0, 'AIGC任务生成方式', '1', NOW()),
  ('AIGC发布规则类型', 'aigc_publish_rule_type', 0, 'AIGC发布规则类型', '1', NOW());

-- 5.2 创建字典数据
INSERT INTO system_dict_data (dict_type, dict_label, dict_value, dict_sort, status, remark, creator, create_time)
VALUES 
  -- 语音引擎类型
  ('aigc_speech_engine_type', '阿里云智能媒体服务TTS', '1', 1, 0, '阿里云TTS服务', '1', NOW()),
  ('aigc_speech_engine_type', '火山引擎语音合成', '2', 2, 0, '火山引擎TTS服务', '1', NOW()),
  
  -- 媒体类型
  ('aigc_media_type', '视频', 'VIDEO', 1, 0, '视频类型媒体', '1', NOW()),
  ('aigc_media_type', '图片', 'IMAGE', 2, 0, '图片类型媒体', '1', NOW()),
  ('aigc_media_type', '音频', 'AUDIO', 3, 0, '音频类型媒体', '1', NOW()),
  ('aigc_media_type', '文本', 'TEXT', 4, 0, '文本类型媒体', '1', NOW()),
  
  -- 媒体状态
  ('aigc_media_status', '处理中', 'PROCESSING', 1, 0, '媒体正在处理', '1', NOW()),
  ('aigc_media_status', '成功', 'SUCCESS', 2, 0, '媒体处理成功', '1', NOW()),
  ('aigc_media_status', '失败', 'FAILED', 3, 0, '媒体处理失败', '1', NOW()),
  ('aigc_media_status', '已取消', 'CANCELLED', 4, 0, '媒体处理已取消', '1', NOW()),
  
  -- 公式状态
  ('aigc_formula_status', '草稿', 'DRAFT', 1, 0, '公式草稿状态', '1', NOW()),
  ('aigc_formula_status', '正式版本', 'RELEASE', 2, 0, '公式正式发布', '1', NOW()),
  
  -- 任务状态
  ('aigc_task_status', '草稿', '1', 1, 0, '任务草稿状态', '1', NOW()),
  ('aigc_task_status', '执行中', '2', 2, 0, '任务执行中', '1', NOW()),
  ('aigc_task_status', '已完成', '3', 3, 0, '任务已完成', '1', NOW()),
  
  -- 模型类型
  ('aigc_model_type', '视频', '1', 1, 0, '视频生成模型', '1', NOW()),
  ('aigc_model_type', '图文', '2', 2, 0, '图文生成模型', '1', NOW()),
  
  -- 模型状态
  ('aigc_model_status', '草稿', '1', 1, 0, '模型草稿状态', '1', NOW()),
  ('aigc_model_status', '已完成', '2', 2, 0, '模型已完成', '1', NOW()),
  
  -- 发布平台
  ('aigc_platform_type', '小红书', '1', 1, 0, '小红书平台', '1', NOW()),
  ('aigc_platform_type', '抖音', '2', 2, 0, '抖音平台', '1', NOW()),
  
  -- 素材类型
  ('aigc_material_type', '文件夹', '0', 1, 0, '文件夹类型', '1', NOW()),
  ('aigc_material_type', '视频', '1', 2, 0, '视频素材', '1', NOW()),
  ('aigc_material_type', '图片', '2', 3, 0, '图片素材', '1', NOW()),
  ('aigc_material_type', '文案', '3', 4, 0, '文案素材', '1', NOW()),
  ('aigc_material_type', '背景音', '4', 5, 0, '背景音乐', '1', NOW()),
  ('aigc_material_type', '旁白配音', '5', 6, 0, '旁白配音', '1', NOW()),
  ('aigc_material_type', '音色', '6', 7, 0, '音色素材', '1', NOW()),
  ('aigc_material_type', '混合', '7', 8, 0, '混合类型', '1', NOW()),
  
  -- 特效类型
  ('aigc_vfx_type', '转场特效', '1', 1, 0, '视频转场特效', '1', NOW()),
  ('aigc_vfx_type', '字幕入场特效', '2', 2, 0, '字幕入场动画', '1', NOW()),
  ('aigc_vfx_type', '字幕出场特效', '3', 3, 0, '字幕出场动画', '1', NOW()),
  
  -- 字体样式类型
  ('aigc_font_style_type', '普通样式', '1', 1, 0, '普通字体样式', '1', NOW()),
  ('aigc_font_style_type', '花字样式', '2', 2, 0, '花字特效样式', '1', NOW()),
  
  -- 背景类型
  ('aigc_background_type', '纯色', '1', 1, 0, '纯色背景', '1', NOW()),
  ('aigc_background_type', '图片', '2', 2, 0, '图片背景', '1', NOW()),
  ('aigc_background_type', '视频', '3', 3, 0, '视频背景', '1', NOW()),
  
  -- 对齐方式
  ('aigc_align_type', '左对齐', '1', 1, 0, '文字左对齐', '1', NOW()),
  ('aigc_align_type', '居中', '2', 2, 0, '文字居中对齐', '1', NOW()),
  ('aigc_align_type', '右对齐', '3', 3, 0, '文字右对齐', '1', NOW()),
  
  -- 任务生成方式
  ('aigc_task_create_method', '立即全部生成', '1', 1, 0, '创建后立即生成所有内容', '1', NOW()),
  ('aigc_task_create_method', '自动补足', '2', 2, 0, '根据使用情况自动补充生成', '1', NOW()),
  ('aigc_task_create_method', '等待触发生成', '3', 3, 0, '等待手动或事件触发生成', '1', NOW()),
  
  -- 发布规则类型
  ('aigc_publish_rule_type', '按日发布', '1', 1, 0, '按照日期规则发布', '1', NOW()),
  ('aigc_publish_rule_type', '轮询发布', '2', 2, 0, '轮询方式发布', '1', NOW());

-- 5.3 注意：以下表的数据可以在字典中创建对应的映射，但原表保留
-- yt_aigc_transition - 转场特效表保留
-- yt_aigc_vfx_config - 特效配置表保留

-- 可选：在字典中创建对应的类型映射（不影响原表）
-- INSERT INTO system_dict_data (dict_type, dict_label, dict_value, dict_sort, remark, creator, create_time)
-- VALUES 
--   ('aigc_transition_type', '淡入淡出', 'fade', 1, '淡入淡出转场', '1', NOW()),
--   ('aigc_transition_type', '滑动', 'slide', 2, '滑动转场', '1', NOW());

-- 5.5 注意：以下表保持独立表，不迁移到字典
-- yt_font_config - 字体配置（包含字体文件URL等复杂配置）
-- yt_aigc_speech_voice - 音色配置（包含多语言、情感等复杂配置）
-- yt_aigc_word_style - 文字样式（包含多个样式参数）

-- font_config 字段标准化
ALTER TABLE yt_font_config 
  MODIFY COLUMN `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  MODIFY COLUMN `update_by` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_font_config 
  CHANGE COLUMN `create_by` `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  CHANGE COLUMN `update_by` `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  CHANGE COLUMN `del_flag` `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除';

ALTER TABLE yt_font_config ADD INDEX `idx_tenant_id` (`tenant_id`);
ALTER TABLE yt_font_config ADD INDEX `idx_status` (`status`);

-- speech_voice 字段标准化
ALTER TABLE yt_aigc_speech_voice 
  MODIFY COLUMN `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  MODIFY COLUMN `update_by` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_speech_voice 
  CHANGE COLUMN `create_by` `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  CHANGE COLUMN `update_by` `updater` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_speech_voice ADD INDEX `idx_tenant_id` (`tenant_id`);

-- word_style 字段标准化
ALTER TABLE yt_aigc_word_style 
  MODIFY COLUMN `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  MODIFY COLUMN `update_by` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_word_style 
  CHANGE COLUMN `create_by` `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  CHANGE COLUMN `update_by` `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  CHANGE COLUMN `del_flag` `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除';

ALTER TABLE yt_aigc_word_style ADD INDEX `idx_tenant_id` (`tenant_id`);

-- =====================================================
-- 第六步：transition 和 vfx_config 表字段标准化
-- =====================================================

-- aigc_transition 字段标准化（保留表）
ALTER TABLE yt_aigc_transition 
  MODIFY COLUMN `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  MODIFY COLUMN `update_by` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_transition 
  CHANGE COLUMN `create_by` `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  CHANGE COLUMN `update_by` `updater` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_transition ADD INDEX `idx_tenant_id` (`tenant_id`);

-- aigc_vfx_config 字段标准化（保留表）
ALTER TABLE yt_aigc_vfx_config 
  MODIFY COLUMN `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  MODIFY COLUMN `update_by` varchar(64) DEFAULT '' COMMENT '更新者';

ALTER TABLE yt_aigc_vfx_config 
  CHANGE COLUMN `create_by` `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  CHANGE COLUMN `update_by` `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  CHANGE COLUMN `del_flag` `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除';

ALTER TABLE yt_aigc_vfx_config ADD INDEX `idx_tenant_id` (`tenant_id`);

-- =====================================================
-- 验证脚本
-- =====================================================

-- 检查表重命名是否成功
SELECT TABLE_NAME FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME LIKE 'yt_aigc_%'
ORDER BY TABLE_NAME;

-- 检查租户字段是否添加成功
SELECT TABLE_NAME, COLUMN_NAME 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME LIKE 'yt_aigc_%' 
AND COLUMN_NAME = 'tenant_id';

-- 检查字段重命名是否成功
SELECT TABLE_NAME, COLUMN_NAME 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME LIKE 'yt_aigc_%' 
AND COLUMN_NAME IN ('creator', 'updater', 'deleted');

-- =====================================================
-- 回滚脚本（如需要）
-- =====================================================
-- 请参考 rollback_v1.0.sql