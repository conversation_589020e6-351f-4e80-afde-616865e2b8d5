-- =====================================================
-- AIGC Media 模块数据库迁移脚本 - 第三阶段：字段标准化
-- 执行时间：2025-08-10
-- 说明：统一字段命名和类型，符合框架规范
-- =====================================================

-- 1. 修改创建者和更新者字段（从bigint改为varchar）
-- 注意：需要先备份原始数据，然后转换类型

-- 1.1 创建临时表存储用户ID映射关系（如果需要保留历史数据）
CREATE TEMPORARY TABLE IF NOT EXISTS temp_user_mapping (
    user_id_long BIGINT,
    user_id_string VARCHAR(64)
);

-- 1.2 插入映射数据（将数字ID转为字符串）
INSERT INTO temp_user_mapping (user_id_long, user_id_string)
SELECT DISTINCT create_by, CAST(create_by AS CHAR) 
FROM yt_aigc_formula WHERE create_by IS NOT NULL
UNION
SELECT DISTINCT update_by, CAST(update_by AS CHAR) 
FROM yt_aigc_formula WHERE update_by IS NOT NULL;

-- 2. 修改表结构 - 以yt_aigc_formula为例
ALTER TABLE `yt_aigc_formula` 
  ADD COLUMN `creator` varchar(64) DEFAULT '' COMMENT '创建者' AFTER `create_time`,
  ADD COLUMN `updater` varchar(64) DEFAULT '' COMMENT '更新者' AFTER `update_time`;

-- 2.1 迁移数据
UPDATE `yt_aigc_formula` SET `creator` = CAST(`create_by` AS CHAR) WHERE `create_by` IS NOT NULL;
UPDATE `yt_aigc_formula` SET `updater` = CAST(`update_by` AS CHAR) WHERE `update_by` IS NOT NULL;

-- 2.2 删除旧字段
ALTER TABLE `yt_aigc_formula` 
  DROP COLUMN `create_by`,
  DROP COLUMN `update_by`;

-- 3. 修改删除标记字段（从int改为bit）
ALTER TABLE `yt_aigc_formula` 
  ADD COLUMN `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除' AFTER `updater`;

-- 3.1 迁移数据（假设del_flag: 1=已删除, 0=未删除）
UPDATE `yt_aigc_formula` SET `deleted` = b'1' WHERE `del_flag` = 1;
UPDATE `yt_aigc_formula` SET `deleted` = b'0' WHERE `del_flag` = 0 OR `del_flag` IS NULL;

-- 3.2 删除旧字段
ALTER TABLE `yt_aigc_formula` DROP COLUMN `del_flag`;

-- 4. 对其他表执行相同操作
-- yt_aigc_media
ALTER TABLE `yt_aigc_media` 
  ADD COLUMN `creator` varchar(64) DEFAULT '' COMMENT '创建者' AFTER `create_time`,
  ADD COLUMN `updater` varchar(64) DEFAULT '' COMMENT '更新者' AFTER `update_time`,
  ADD COLUMN `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除' AFTER `updater`;

UPDATE `yt_aigc_media` SET 
  `creator` = CAST(`create_by` AS CHAR),
  `updater` = CAST(`update_by` AS CHAR),
  `deleted` = IF(`del_flag` = 1, b'1', b'0');

ALTER TABLE `yt_aigc_media` 
  DROP COLUMN `create_by`,
  DROP COLUMN `update_by`,
  DROP COLUMN `del_flag`;

-- yt_aigc_model
ALTER TABLE `yt_aigc_model` 
  ADD COLUMN `creator` varchar(64) DEFAULT '' COMMENT '创建者' AFTER `create_time`,
  ADD COLUMN `updater` varchar(64) DEFAULT '' COMMENT '更新者' AFTER `update_time`,
  ADD COLUMN `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除' AFTER `updater`;

UPDATE `yt_aigc_model` SET 
  `creator` = CAST(`create_by` AS CHAR),
  `updater` = CAST(`update_by` AS CHAR),
  `deleted` = IF(`del_flag` = 1, b'1', b'0');

ALTER TABLE `yt_aigc_model` 
  DROP COLUMN `create_by`,
  DROP COLUMN `update_by`,
  DROP COLUMN `del_flag`;

-- yt_aigc_task
ALTER TABLE `yt_aigc_task` 
  ADD COLUMN `creator` varchar(64) DEFAULT '' COMMENT '创建者' AFTER `create_time`,
  ADD COLUMN `updater` varchar(64) DEFAULT '' COMMENT '更新者' AFTER `update_time`,
  ADD COLUMN `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除' AFTER `updater`;

UPDATE `yt_aigc_task` SET 
  `creator` = CAST(`create_by` AS CHAR),
  `updater` = CAST(`update_by` AS CHAR),
  `deleted` = IF(`del_flag` = 1, b'1', b'0');

ALTER TABLE `yt_aigc_task` 
  DROP COLUMN `create_by`,
  DROP COLUMN `update_by`,
  DROP COLUMN `del_flag`;

-- yt_aigc_publish
ALTER TABLE `yt_aigc_publish` 
  ADD COLUMN `creator` varchar(64) DEFAULT '' COMMENT '创建者' AFTER `create_time`,
  ADD COLUMN `updater` varchar(64) DEFAULT '' COMMENT '更新者' AFTER `update_time`,
  ADD COLUMN `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除' AFTER `updater`;

UPDATE `yt_aigc_publish` SET 
  `creator` = CAST(`create_by` AS CHAR),
  `updater` = CAST(`update_by` AS CHAR),
  `deleted` = IF(`del_flag` = 1, b'1', b'0');

ALTER TABLE `yt_aigc_publish` 
  DROP COLUMN `create_by`,
  DROP COLUMN `update_by`,
  DROP COLUMN `del_flag`;

-- 5. 为其他表批量执行（创建存储过程自动化处理）
DELIMITER $$
CREATE PROCEDURE migrate_table_fields(IN table_name VARCHAR(100))
BEGIN
    SET @sql = CONCAT('ALTER TABLE `', table_name, '` ',
        'ADD COLUMN `creator` varchar(64) DEFAULT '''' COMMENT ''创建者'' AFTER `create_time`, ',
        'ADD COLUMN `updater` varchar(64) DEFAULT '''' COMMENT ''更新者'' AFTER `update_time`, ',
        'ADD COLUMN `deleted` bit(1) NOT NULL DEFAULT b''0'' COMMENT ''是否删除'' AFTER `updater`');
    
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SET @sql = CONCAT('UPDATE `', table_name, '` SET ',
        '`creator` = CAST(`create_by` AS CHAR), ',
        '`updater` = CAST(`update_by` AS CHAR), ',
        '`deleted` = IF(`del_flag` = 1, b''1'', b''0'')');
    
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SET @sql = CONCAT('ALTER TABLE `', table_name, '` ',
        'DROP COLUMN `create_by`, ',
        'DROP COLUMN `update_by`, ',
        'DROP COLUMN `del_flag`');
    
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END$$
DELIMITER ;

-- 6. 对剩余的表执行迁移
-- CALL migrate_table_fields('yt_aigc_formula_track');
-- CALL migrate_table_fields('yt_aigc_oss_file');
-- ... 其他表

-- 7. 删除存储过程
DROP PROCEDURE IF EXISTS migrate_table_fields;

-- 8. 回滚脚本（如需要）
/*
-- 恢复原字段
ALTER TABLE `yt_aigc_formula` 
  ADD COLUMN `create_by` bigint(20) COMMENT '创建者',
  ADD COLUMN `update_by` bigint(20) COMMENT '更新者',
  ADD COLUMN `del_flag` int(11) DEFAULT '0' COMMENT '删除标记';

UPDATE `yt_aigc_formula` SET 
  `create_by` = CAST(`creator` AS UNSIGNED),
  `update_by` = CAST(`updater` AS UNSIGNED),
  `del_flag` = IF(`deleted` = b'1', 1, 0);

ALTER TABLE `yt_aigc_formula` 
  DROP COLUMN `creator`,
  DROP COLUMN `updater`,
  DROP COLUMN `deleted`;
*/