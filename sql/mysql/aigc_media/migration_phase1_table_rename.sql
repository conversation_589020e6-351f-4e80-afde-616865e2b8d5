-- =====================================================
-- AIGC Media 模块数据库迁移脚本 - 第一阶段：表名规范化
-- 执行时间：2025-08-10
-- 说明：将所有aigc表添加yt_前缀
-- =====================================================

-- 1. 批量重命名所有aigc表，添加yt_前缀
RENAME TABLE 
  `aigc_formula` TO `yt_aigc_formula`,
  `aigc_formula_track` TO `yt_aigc_formula_track`,
  `aigc_formula_track_clip` TO `yt_aigc_formula_track_clip`,
  `aigc_formula_track_materialset_ratio` TO `yt_aigc_formula_track_materialset_ratio`,
  `aigc_media` TO `yt_aigc_media`,
  `aigc_media_producing_job` TO `yt_aigc_media_producing_job`,
  `aigc_media_producing_job_variable_value` TO `yt_aigc_media_producing_job_variable_value`,
  `aigc_media_track` TO `yt_aigc_media_track`,
  `aigc_media_track_material` TO `yt_aigc_media_track_material`,
  `aigc_model` TO `yt_aigc_model`,
  `aigc_model_item` TO `yt_aigc_model_item`,
  `aigc_model_scene` TO `yt_aigc_model_scene`,
  `aigc_model_scene_storyboard` TO `yt_aigc_model_scene_storyboard`,
  `aigc_model_varible` TO `yt_aigc_model_varible`,
  `aigc_oss_file` TO `yt_aigc_oss_file`,
  `aigc_publish` TO `yt_aigc_publish`,
  `aigc_publish_account` TO `yt_aigc_publish_account`,
  `aigc_publish_record` TO `yt_aigc_publish_record`,
  `aigc_publish_task` TO `yt_aigc_publish_task`,
  `aigc_speech_voice` TO `yt_aigc_speech_voice`,
  `aigc_speech_voice_emotion` TO `yt_aigc_speech_voice_emotion`,
  `aigc_tag_category` TO `yt_aigc_tag_category`,
  `aigc_tag_group` TO `yt_aigc_tag_group`,
  `aigc_tag_info_keys` TO `yt_aigc_tag_info_keys`,
  `aigc_tag_info_rel` TO `yt_aigc_tag_info_rel`,
  `aigc_tag_info_values` TO `yt_aigc_tag_info_values`,
  `aigc_tag_object_rel` TO `yt_aigc_tag_object_rel`,
  `aigc_tag_object_types` TO `yt_aigc_tag_object_types`,
  `aigc_tag_rel_types` TO `yt_aigc_tag_rel_types`,
  `aigc_task` TO `yt_aigc_task`,
  `aigc_task_media_his` TO `yt_aigc_task_media_his`,
  `aigc_task_record` TO `yt_aigc_task_record`,
  `aigc_task_variable` TO `yt_aigc_task_variable`,
  `aigc_transition` TO `yt_aigc_transition`,
  `aigc_variable` TO `yt_aigc_variable`,
  `aigc_vfx_config` TO `yt_aigc_vfx_config`,
  `aigc_word_style` TO `yt_aigc_word_style`,
  `font_config` TO `yt_font_config`;

-- 2. 创建回滚脚本备份
-- 如需回滚，执行以下语句：
/*
RENAME TABLE 
  `yt_aigc_formula` TO `aigc_formula`,
  `yt_aigc_formula_track` TO `aigc_formula_track`,
  `yt_aigc_formula_track_clip` TO `aigc_formula_track_clip`,
  `yt_aigc_formula_track_materialset_ratio` TO `aigc_formula_track_materialset_ratio`,
  `yt_aigc_media` TO `aigc_media`,
  `yt_aigc_media_producing_job` TO `aigc_media_producing_job`,
  `yt_aigc_media_producing_job_variable_value` TO `aigc_media_producing_job_variable_value`,
  `yt_aigc_media_track` TO `aigc_media_track`,
  `yt_aigc_media_track_material` TO `aigc_media_track_material`,
  `yt_aigc_model` TO `aigc_model`,
  `yt_aigc_model_item` TO `aigc_model_item`,
  `yt_aigc_model_scene` TO `aigc_model_scene`,
  `yt_aigc_model_scene_storyboard` TO `aigc_model_scene_storyboard`,
  `yt_aigc_model_varible` TO `aigc_model_varible`,
  `yt_aigc_oss_file` TO `aigc_oss_file`,
  `yt_aigc_publish` TO `aigc_publish`,
  `yt_aigc_publish_account` TO `aigc_publish_account`,
  `yt_aigc_publish_record` TO `aigc_publish_record`,
  `yt_aigc_publish_task` TO `aigc_publish_task`,
  `yt_aigc_speech_voice` TO `aigc_speech_voice`,
  `yt_aigc_speech_voice_emotion` TO `aigc_speech_voice_emotion`,
  `yt_aigc_tag_category` TO `aigc_tag_category`,
  `yt_aigc_tag_group` TO `aigc_tag_group`,
  `yt_aigc_tag_info_keys` TO `aigc_tag_info_keys`,
  `yt_aigc_tag_info_rel` TO `aigc_tag_info_rel`,
  `yt_aigc_tag_info_values` TO `aigc_tag_info_values`,
  `yt_aigc_tag_object_rel` TO `aigc_tag_object_rel`,
  `yt_aigc_tag_object_types` TO `aigc_tag_object_types`,
  `yt_aigc_tag_rel_types` TO `aigc_tag_rel_types`,
  `yt_aigc_task` TO `aigc_task`,
  `yt_aigc_task_media_his` TO `aigc_task_media_his`,
  `yt_aigc_task_record` TO `aigc_task_record`,
  `yt_aigc_task_variable` TO `aigc_task_variable`,
  `yt_aigc_transition` TO `aigc_transition`,
  `yt_aigc_variable` TO `aigc_variable`,
  `yt_aigc_vfx_config` TO `aigc_vfx_config`,
  `yt_aigc_word_style` TO `aigc_word_style`,
  `yt_font_config` TO `font_config`;
*/