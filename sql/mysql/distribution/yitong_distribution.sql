/*
 Navicat Premium Dump SQL

 Source Server         : yitong-prod【dev】
 Source Server Type    : MySQL
 Source Server Version : 80030 (8.0.30-cynos)
 Source Host           : sh-cynosdbmysql-grp-dlfua43g.sql.tencentcdb.com:23555
 Source Schema         : yitong_dev

 Target Server Type    : MySQL
 Target Server Version : 80030 (8.0.30-cynos)
 File Encoding         : 65001

 Date: 28/07/2025 15:10:42
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for yt_dist_agent
-- ----------------------------
DROP TABLE IF EXISTS `yt_dist_agent`;
CREATE TABLE `yt_dist_agent` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分销员ID',
  `member_id` bigint NOT NULL COMMENT '会员ID',
  `invite_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '邀请码',
  `real_name` varchar(32) DEFAULT NULL COMMENT '真实姓名',
  `mobile` varchar(11) DEFAULT NULL COMMENT '手机号',
  `level_id` bigint NOT NULL COMMENT '等级ID',
  `parent_id` bigint DEFAULT '0' COMMENT '上级分销员ID',
  `ancestor_path` varchar(500) DEFAULT NULL COMMENT '祖先路径',
  `team_depth` int DEFAULT '0' COMMENT '团队深度',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态：0-待审核 1-正常 2-冻结 3-已注销',
  `apply_time` datetime NOT NULL COMMENT '申请时间',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(200) DEFAULT NULL COMMENT '审核备注',
  `total_sales` decimal(10,2) DEFAULT '0.00' COMMENT '累计销售额',
  `total_commission` decimal(10,2) DEFAULT '0.00' COMMENT '累计佣金',
  `available_commission` decimal(10,2) DEFAULT '0.00' COMMENT '可用佣金',
  `frozen_commission` decimal(10,2) DEFAULT '0.00' COMMENT '冻结佣金',
  `withdrawn_commission` decimal(10,2) DEFAULT '0.00' COMMENT '已提现佣金',
  `direct_member_count` int DEFAULT '0' COMMENT '直推人数',
  `team_member_count` int DEFAULT '0' COMMENT '团队人数',
  `last_active_time` datetime DEFAULT NULL COMMENT '最后活跃时间',
  `freeze_reason` varchar(200) DEFAULT NULL COMMENT '冻结原因',
  `freeze_time` datetime DEFAULT NULL COMMENT '冻结时间',
  `extra_info` text COMMENT '扩展信息',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_member_id` (`member_id`,`deleted`) USING BTREE,
  UNIQUE KEY `uk_invite_code` (`invite_code`,`deleted`) USING BTREE,
  KEY `idx_parent_id` (`parent_id`) USING BTREE,
  KEY `idx_level_id` (`level_id`) USING BTREE,
  KEY `idx_mobile` (`mobile`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_ancestor_path` (`ancestor_path`(191)) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1948149045648871427 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分销员表';

-- ----------------------------
-- Records of yt_dist_agent
-- ----------------------------
BEGIN;
INSERT INTO `yt_dist_agent` (`id`, `member_id`, `invite_code`, `real_name`, `mobile`, `level_id`, `parent_id`, `ancestor_path`, `team_depth`, `status`, `apply_time`, `audit_time`, `audit_remark`, `total_sales`, `total_commission`, `available_commission`, `frozen_commission`, `withdrawn_commission`, `direct_member_count`, `team_member_count`, `last_active_time`, `freeze_reason`, `freeze_time`, `extra_info`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`) VALUES (1948149045648871426, 1, 'ZF4EP7', '张三', '18516128609', 1, 0, '1948149045648871426', 1, 1, '2025-07-24 06:31:42', '2025-07-24 21:53:17', '的点点滴滴订单', 0.00, 0.00, 0.00, 0.00, 0.00, 0, 0, NULL, '123', '2025-07-25 15:27:45', NULL, '1', '2025-07-24 06:31:42', '1', '2025-07-25 18:19:12', b'0', 1);
COMMIT;

-- ----------------------------
-- Table structure for yt_dist_agent_poster
-- ----------------------------
DROP TABLE IF EXISTS `yt_dist_agent_poster`;
CREATE TABLE `yt_dist_agent_poster` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `agent_id` bigint NOT NULL COMMENT '分销员ID',
  `template_id` bigint NOT NULL COMMENT '模板ID',
  `poster_url` varchar(500) NOT NULL COMMENT '海报URL',
  `qrcode_url` varchar(500) DEFAULT NULL COMMENT '二维码URL',
  `share_count` int DEFAULT '0' COMMENT '分享次数',
  `view_count` int DEFAULT '0' COMMENT '浏览次数',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agent_template` (`agent_id`,`template_id`) USING BTREE,
  KEY `idx_agent_id` (`agent_id`) USING BTREE,
  KEY `idx_template_id` (`template_id`) USING BTREE,
  KEY `idx_tenant_id` (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分销员海报记录表';

-- ----------------------------
-- Records of yt_dist_agent_poster
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for yt_dist_agent_tag
-- ----------------------------
DROP TABLE IF EXISTS `yt_dist_agent_tag`;
CREATE TABLE `yt_dist_agent_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `color` varchar(20) DEFAULT NULL COMMENT '标签颜色',
  `sort` int DEFAULT '0' COMMENT '排序',
  `status` tinyint DEFAULT '0' COMMENT '状态(0-禁用,1-启用)',
  `usage_count` int NOT NULL DEFAULT '0' COMMENT '使用人数',
  `share_count` int NOT NULL DEFAULT '0' COMMENT '分享次数',
  `order_count` int NOT NULL DEFAULT '0' COMMENT '成交订单数',
  `conversion_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '转化率(%)',
  `usage_trend` varchar(10) DEFAULT 'flat' COMMENT '使用人数趋势：up-上升，down-下降，flat-持平',
  `share_trend` varchar(10) DEFAULT 'flat' COMMENT '分享趋势：up-上升，down-下降，flat-持平',
  `conversion_trend` varchar(10) DEFAULT 'flat' COMMENT '转化率趋势：up-上升，down-下降，flat-持平',
  `usage_trend_rate` decimal(8,2) DEFAULT '0.00' COMMENT '使用人数增长率(%)',
  `share_trend_rate` decimal(8,2) DEFAULT '0.00' COMMENT '分享增长率(%)',
  `conversion_trend_rate` decimal(8,2) DEFAULT '0.00' COMMENT '转化率变化率(%)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1948629751924695042 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分销员标签表';

-- ----------------------------
-- Records of yt_dist_agent_tag
-- ----------------------------
BEGIN;
INSERT INTO `yt_dist_agent_tag` (`id`, `name`, `color`, `sort`, `status`, `usage_count`, `share_count`, `order_count`, `conversion_rate`, `usage_trend`, `share_trend`, `conversion_trend`, `usage_trend_rate`, `share_trend_rate`, `conversion_trend_rate`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`) VALUES (1948312408806789121, '测试', '#00CED1', 0, 1, 1, 0, 0, 0.00, 'flat', 'flat', 'flat', 0.00, 0.00, 0.00, NULL, '1', '2025-07-24 17:20:51', '1', '2025-07-25 18:35:36', b'0', 1);
INSERT INTO `yt_dist_agent_tag` (`id`, `name`, `color`, `sort`, `status`, `usage_count`, `share_count`, `order_count`, `conversion_rate`, `usage_trend`, `share_trend`, `conversion_trend`, `usage_trend_rate`, `share_trend_rate`, `conversion_trend_rate`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`) VALUES (1948629751924695041, '测试2', '#409EFF', 1, 1, 0, 0, 0, 0.00, 'flat', 'flat', 'flat', 0.00, 0.00, 0.00, NULL, '1', '2025-07-25 14:21:51', '1', '2025-07-25 18:35:36', b'0', 1);
COMMIT;

-- ----------------------------
-- Table structure for yt_dist_agent_tag_merge_log
-- ----------------------------
DROP TABLE IF EXISTS `yt_dist_agent_tag_merge_log`;
CREATE TABLE `yt_dist_agent_tag_merge_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `source_tag_ids` varchar(500) NOT NULL COMMENT '源标签ID列表（JSON格式）',
  `target_tag_id` bigint NOT NULL COMMENT '目标标签ID',
  `merged_count` int NOT NULL DEFAULT '0' COMMENT '合并的分销员数量',
  `duplicate_count` int NOT NULL DEFAULT '0' COMMENT '重复的分销员数量',
  `deleted_tag_count` int NOT NULL DEFAULT '0' COMMENT '删除的标签数量',
  `operator_id` bigint DEFAULT NULL COMMENT '操作人ID',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_target_tag_id` (`target_tag_id`) USING BTREE,
  KEY `idx_operator_id` (`operator_id`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分销员标签合并日志表';

-- ----------------------------
-- Records of yt_dist_agent_tag_merge_log
-- ----------------------------
BEGIN;
INSERT INTO `yt_dist_agent_tag_merge_log` (`id`, `source_tag_ids`, `target_tag_id`, `merged_count`, `duplicate_count`, `deleted_tag_count`, `operator_id`, `remark`, `create_time`, `creator`, `update_time`, `updater`, `deleted`, `tenant_id`) VALUES (1, '[\"1948312408806789121\"]', 1948629751924695041, 1, 0, 0, 1, '', '2025-07-25 18:27:52', '1', '2025-07-25 18:27:52', '1', b'0', 1);
INSERT INTO `yt_dist_agent_tag_merge_log` (`id`, `source_tag_ids`, `target_tag_id`, `merged_count`, `duplicate_count`, `deleted_tag_count`, `operator_id`, `remark`, `create_time`, `creator`, `update_time`, `updater`, `deleted`, `tenant_id`) VALUES (2, '[\"1948629751924695041\"]', 1948312408806789121, 1, 0, 0, 1, '', '2025-07-25 18:35:32', '1', '2025-07-25 18:35:32', '1', b'0', 1);
COMMIT;

-- ----------------------------
-- Table structure for yt_dist_agent_tag_relation
-- ----------------------------
DROP TABLE IF EXISTS `yt_dist_agent_tag_relation`;
CREATE TABLE `yt_dist_agent_tag_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `agent_id` bigint NOT NULL COMMENT '分销员ID',
  `tag_id` bigint NOT NULL COMMENT '标签ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `tenant_id` bigint NOT NULL DEFAULT '1' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agent_tag` (`agent_id`,`tag_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1948693592620552195 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分销员标签关联表';

-- ----------------------------
-- Records of yt_dist_agent_tag_relation
-- ----------------------------
BEGIN;
INSERT INTO `yt_dist_agent_tag_relation` (`id`, `agent_id`, `tag_id`, `create_time`, `tenant_id`) VALUES (1948693592620552194, 1948149045648871426, 1948312408806789121, '2025-07-25 18:35:32', 1);
COMMIT;

-- ----------------------------
-- Table structure for yt_dist_agent_tag_share
-- ----------------------------
DROP TABLE IF EXISTS `yt_dist_agent_tag_share`;
CREATE TABLE `yt_dist_agent_tag_share` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `agent_id` bigint NOT NULL COMMENT '分销员ID',
  `tag_id` bigint NOT NULL COMMENT '标签ID',
  `share_type` varchar(20) NOT NULL COMMENT '分享类型：poster-海报，link-链接，qrcode-二维码',
  `share_channel` varchar(20) DEFAULT NULL COMMENT '分享渠道：wechat-微信，moments-朋友圈，qq-QQ，weibo-微博',
  `share_url` varchar(500) DEFAULT NULL COMMENT '分享链接',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_tag_id` (`tag_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_agent_tag` (`agent_id`,`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分销员标签分享记录表';

-- ----------------------------
-- Records of yt_dist_agent_tag_share
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for yt_dist_commission
-- ----------------------------
DROP TABLE IF EXISTS `yt_dist_commission`;
CREATE TABLE `yt_dist_commission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '佣金ID',
  `commission_no` varchar(64) NOT NULL COMMENT '佣金单号',
  `agent_id` bigint NOT NULL COMMENT '分销员ID',
  `member_id` bigint NOT NULL COMMENT '会员ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `order_no` varchar(64) NOT NULL COMMENT '订单编号',
  `order_item_id` bigint DEFAULT NULL COMMENT '订单项ID',
  `goods_id` bigint NOT NULL COMMENT '商品ID',
  `goods_name` varchar(100) NOT NULL COMMENT '商品名称',
  `goods_price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `quantity` int NOT NULL COMMENT '数量',
  `order_amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `commission_mode` tinyint NOT NULL COMMENT '佣金模式：1-固定金额 2-百分比 3-阶梯式',
  `commission_rate` decimal(5,2) DEFAULT NULL COMMENT '佣金比例(%)',
  `commission_amount` decimal(10,2) NOT NULL COMMENT '佣金金额',
  `extra_rate` decimal(5,2) DEFAULT '0.00' COMMENT '额外奖励比例(%)',
  `extra_amount` decimal(10,2) DEFAULT '0.00' COMMENT '额外奖励金额',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态：0-待结算 1-已冻结 2-已结算 3-已提现 4-已取消',
  `freeze_time` datetime DEFAULT NULL COMMENT '冻结时间',
  `settle_time` datetime DEFAULT NULL COMMENT '结算时间',
  `withdraw_id` bigint DEFAULT NULL COMMENT '提现ID',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_commission_no` (`commission_no`,`deleted`) USING BTREE,
  KEY `idx_agent_id` (`agent_id`) USING BTREE,
  KEY `idx_order_id` (`order_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_agent_status_time` (`agent_id`,`status`,`create_time`) USING BTREE,
  KEY `idx_order_goods` (`order_id`,`goods_id`),
  KEY `idx_settle_time` (`settle_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='佣金记录表';

-- ----------------------------
-- Records of yt_dist_commission
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for yt_dist_goods_config
-- ----------------------------
DROP TABLE IF EXISTS `yt_dist_goods_config`;
CREATE TABLE `yt_dist_goods_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `goods_id` bigint NOT NULL COMMENT '商品ID',
  `goods_name` varchar(100) NOT NULL COMMENT '商品名称',
  `goods_type` tinyint NOT NULL COMMENT '商品类型',
  `enable_dist` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否开启分销',
  `commission_mode` tinyint NOT NULL COMMENT '佣金模式：1-固定金额 2-百分比 3-阶梯式',
  `commission_rate` decimal(5,2) DEFAULT NULL COMMENT '佣金比例(%)',
  `commission_amount` decimal(10,2) DEFAULT NULL COMMENT '固定佣金金额',
  `tiered_config` text COMMENT '阶梯配置(JSON)',
  `level_config` text COMMENT '等级差异配置(JSON)',
  `dist_limit` tinyint DEFAULT '0' COMMENT '分销限制：0-无限制 1-限制等级 2-限制人群',
  `limit_config` text COMMENT '限制配置(JSON)',
  `dist_stock` int DEFAULT '-1' COMMENT '分销库存(-1表示不限)',
  `sold_count` int DEFAULT '0' COMMENT '已售数量',
  `total_commission` decimal(10,2) DEFAULT '0.00' COMMENT '累计佣金',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用 1-启用',
  `priority` int DEFAULT '0' COMMENT '优先级',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_goods_id` (`goods_id`,`deleted`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_enable_dist` (`enable_dist`) USING BTREE,
  KEY `idx_goods_enable` (`goods_id`,`enable_dist`),
  KEY `idx_status_priority` (`status`,`priority`),
  KEY `idx_time_range` (`start_time`,`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商品分销配置表';

-- ----------------------------
-- Records of yt_dist_goods_config
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for yt_dist_level
-- ----------------------------
DROP TABLE IF EXISTS `yt_dist_level`;
CREATE TABLE `yt_dist_level` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '等级ID',
  `level_code` varchar(32) NOT NULL COMMENT '等级编码',
  `level_name` varchar(32) NOT NULL COMMENT '等级名称',
  `level_grade` int NOT NULL COMMENT '等级级别',
  `first_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '一级佣金比例（直接推荐的佣金比例）',
  `second_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '二级佣金比例（间接推荐的佣金比例）',
  `third_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '三级佣金比例（三级推荐的佣金比例）',
  `upgrade_commission` decimal(10,2) DEFAULT '0.00' COMMENT '升级条件-累计佣金（达到该金额可升级到该等级）',
  `upgrade_direct_count` int DEFAULT '0' COMMENT '升级条件-直推人数（直接推荐的分销员数量）',
  `upgrade_team_count` int DEFAULT '0' COMMENT '升级条件-团队人数（团队总人数要求）',
  `upgrade_sales_amount` decimal(10,2) DEFAULT '0.00' COMMENT '升级条件-销售额（团队累计销售额要求）',
  `is_default` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否默认等级',
  `privileges` text COMMENT '等级权益说明',
  `icon` varchar(255) DEFAULT NULL COMMENT '等级图标',
  `commission_rate` decimal(5,2) DEFAULT NULL COMMENT '佣金比例(%)',
  `extra_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '额外佣金比例(%)',
  `team_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '团队佣金比例(%)',
  `upgrade_condition` text COMMENT '升级条件(JSON)',
  `level_benefits` text COMMENT '等级权益说明',
  `icon_url` varchar(255) DEFAULT NULL COMMENT '等级图标',
  `bg_color` varchar(32) DEFAULT NULL COMMENT '背景颜色',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0-禁用 1-启用',
  `sort` int DEFAULT '0' COMMENT '排序',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_level_code` (`level_code`,`deleted`) USING BTREE,
  UNIQUE KEY `uk_level_grade` (`level_grade`,`deleted`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_level_code` (`level_code`),
  KEY `idx_grade_status` (`level_grade`,`status`),
  KEY `idx_default_status` (`is_default`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1948702973722525698 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分销等级表';

-- ----------------------------
-- Records of yt_dist_level
-- ----------------------------
BEGIN;
INSERT INTO `yt_dist_level` (`id`, `level_code`, `level_name`, `level_grade`, `first_commission_rate`, `second_commission_rate`, `third_commission_rate`, `upgrade_commission`, `upgrade_direct_count`, `upgrade_team_count`, `upgrade_sales_amount`, `is_default`, `privileges`, `icon`, `commission_rate`, `extra_commission_rate`, `team_commission_rate`, `upgrade_condition`, `level_benefits`, `icon_url`, `bg_color`, `status`, `sort`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`) VALUES (1, 'BRONZE', '青铜会员1', 1, 0.20, 0.30, 0.30, 1.00, 1, 1, 1.00, b'0', '1,2', NULL, 10.00, 0.00, 0.00, '{\"type\":\"auto\",\"conditions\":[]}', '1. 基础佣金比例10%\n2. 可发展下级分销员\n3. 享受专属客服服务', NULL, '#CD7F32', 1, 2, '默认等级，新分销员自动获得1', '', '2025-07-21 12:11:14', '1', '2025-07-25 18:59:14', b'0', 1);
INSERT INTO `yt_dist_level` (`id`, `level_code`, `level_name`, `level_grade`, `first_commission_rate`, `second_commission_rate`, `third_commission_rate`, `upgrade_commission`, `upgrade_direct_count`, `upgrade_team_count`, `upgrade_sales_amount`, `is_default`, `privileges`, `icon`, `commission_rate`, `extra_commission_rate`, `team_commission_rate`, `upgrade_condition`, `level_benefits`, `icon_url`, `bg_color`, `status`, `sort`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`) VALUES (2, 'SILVER', '白银会员', 2, 0.00, 0.00, 0.00, 0.00, 0, 0, 0.00, b'1', NULL, NULL, 15.00, 2.00, 1.00, '{\"type\":\"manual\",\"conditions\":[{\"field\":\"total_sales\",\"operator\":\">=\",\"value\":10000},{\"field\":\"direct_member_count\",\"operator\":\">=\",\"value\":5}]}', '1. 基础佣金比例15%\n2. 额外奖励2%\n3. 团队佣金1%\n4. 优先参与促销活动', NULL, '#C0C0C0', 0, 2, '累计销售额≥1万元，直推人数≥5人', '', '2025-07-21 12:11:14', '1', '2025-07-25 18:59:14', b'0', 1);
INSERT INTO `yt_dist_level` (`id`, `level_code`, `level_name`, `level_grade`, `first_commission_rate`, `second_commission_rate`, `third_commission_rate`, `upgrade_commission`, `upgrade_direct_count`, `upgrade_team_count`, `upgrade_sales_amount`, `is_default`, `privileges`, `icon`, `commission_rate`, `extra_commission_rate`, `team_commission_rate`, `upgrade_condition`, `level_benefits`, `icon_url`, `bg_color`, `status`, `sort`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`) VALUES (3, 'GOLD', '黄金会员', 3, 0.00, 0.00, 0.00, 0.00, 0, 0, 0.00, b'0', NULL, NULL, 20.00, 3.00, 2.00, '{\"type\":\"manual\",\"conditions\":[{\"field\":\"total_sales\",\"operator\":\">=\",\"value\":50000},{\"field\":\"direct_member_count\",\"operator\":\">=\",\"value\":10},{\"field\":\"team_member_count\",\"operator\":\">=\",\"value\":30}]}', '1. 基础佣金比例20%\n2. 额外奖励3%\n3. 团队佣金2%\n4. 专属运营支持\n5. 线下培训机会', NULL, '#FFD700', 1, 3, '累计销售额≥5万元，直推人数≥10人，团队人数≥30人', '', '2025-07-21 12:11:14', '1', '2025-07-25 18:59:14', b'0', 1);
INSERT INTO `yt_dist_level` (`id`, `level_code`, `level_name`, `level_grade`, `first_commission_rate`, `second_commission_rate`, `third_commission_rate`, `upgrade_commission`, `upgrade_direct_count`, `upgrade_team_count`, `upgrade_sales_amount`, `is_default`, `privileges`, `icon`, `commission_rate`, `extra_commission_rate`, `team_commission_rate`, `upgrade_condition`, `level_benefits`, `icon_url`, `bg_color`, `status`, `sort`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`) VALUES (4, 'PLATINUM', '铂金会员', 4, 0.00, 0.00, 0.00, 0.00, 0, 0, 0.00, b'0', NULL, NULL, 25.00, 5.00, 3.00, '{\"type\":\"manual\",\"conditions\":[{\"field\":\"total_sales\",\"operator\":\">=\",\"value\":200000},{\"field\":\"direct_member_count\",\"operator\":\">=\",\"value\":20},{\"field\":\"team_member_count\",\"operator\":\">=\",\"value\":100}]}', '1. 基础佣金比例25%\n2. 额外奖励5%\n3. 团队佣金3%\n4. 区域独家代理权\n5. 品牌合作机会\n6. 年度分红资格', NULL, '#E5E4E2', 1, 4, '累计销售额≥20万元，直推人数≥20人，团队人数≥100人', '', '2025-07-21 12:11:14', '1', '2025-07-25 18:59:14', b'0', 1);
INSERT INTO `yt_dist_level` (`id`, `level_code`, `level_name`, `level_grade`, `first_commission_rate`, `second_commission_rate`, `third_commission_rate`, `upgrade_commission`, `upgrade_direct_count`, `upgrade_team_count`, `upgrade_sales_amount`, `is_default`, `privileges`, `icon`, `commission_rate`, `extra_commission_rate`, `team_commission_rate`, `upgrade_condition`, `level_benefits`, `icon_url`, `bg_color`, `status`, `sort`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`) VALUES (5, 'DIAMOND', '钻石会员', 5, 0.00, 0.00, 0.00, 0.00, 0, 0, 0.00, b'0', NULL, NULL, 30.00, 8.00, 5.00, '{\"type\":\"manual\",\"conditions\":[{\"field\":\"total_sales\",\"operator\":\">=\",\"value\":1000000},{\"field\":\"direct_member_count\",\"operator\":\">=\",\"value\":50},{\"field\":\"team_member_count\",\"operator\":\">=\",\"value\":500}]}', '1. 基础佣金比例30%\n2. 额外奖励8%\n3. 团队佣金5%\n4. 战略合伙人待遇\n5. 股权激励计划\n6. 定制化服务支持\n7. 品牌联合推广', NULL, '#B9F2FF', 1, 5, '累计销售额≥100万元，直推人数≥50人，团队人数≥500人', '', '2025-07-21 12:11:14', '1', '2025-07-25 18:59:14', b'0', 1);
INSERT INTO `yt_dist_level` (`id`, `level_code`, `level_name`, `level_grade`, `first_commission_rate`, `second_commission_rate`, `third_commission_rate`, `upgrade_commission`, `upgrade_direct_count`, `upgrade_team_count`, `upgrade_sales_amount`, `is_default`, `privileges`, `icon`, `commission_rate`, `extra_commission_rate`, `team_commission_rate`, `upgrade_condition`, `level_benefits`, `icon_url`, `bg_color`, `status`, `sort`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`) VALUES (1948702973722525697, 'BRONZE_copy', '青铜会员1_副本', 8, 0.20, 0.30, 0.30, 1.00, 1, 1, 1.00, b'0', '1,2', NULL, NULL, 0.00, 0.00, NULL, NULL, NULL, NULL, 1, 2, '默认等级，新分销员自动获得1', '1', '2025-07-25 19:12:49', '1', '2025-07-25 19:13:11', b'0', 1);
COMMIT;

-- ----------------------------
-- Table structure for yt_dist_poster_template
-- ----------------------------
DROP TABLE IF EXISTS `yt_dist_poster_template`;
CREATE TABLE `yt_dist_poster_template` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `type` varchar(20) NOT NULL COMMENT '模板类型',
  `background_url` varchar(500) NOT NULL COMMENT '背景图片URL',
  `config` json NOT NULL COMMENT '配置信息',
  `status` tinyint DEFAULT '0' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_type_status` (`type`,`status`) USING BTREE,
  KEY `idx_tenant_id` (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分销海报模板表';

-- ----------------------------
-- Records of yt_dist_poster_template
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for yt_dist_reward_level_config
-- ----------------------------
DROP TABLE IF EXISTS `yt_dist_reward_level_config`;
CREATE TABLE `yt_dist_reward_level_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `scheme_id` bigint NOT NULL COMMENT '方案ID',
  `level_id` bigint NOT NULL COMMENT '等级ID',
  `commission_rate` decimal(5,2) NOT NULL COMMENT '佣金比例',
  `extra_reward` decimal(10,2) DEFAULT '0.00' COMMENT '额外奖励',
  `conditions` json DEFAULT NULL COMMENT '条件配置',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_level` (`scheme_id`,`level_id`) USING BTREE,
  KEY `idx_scheme_id` (`scheme_id`) USING BTREE,
  KEY `idx_level_id` (`level_id`) USING BTREE,
  KEY `idx_tenant_id` (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分销等级配置表';

-- ----------------------------
-- Records of yt_dist_reward_level_config
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for yt_dist_reward_scheme
-- ----------------------------
DROP TABLE IF EXISTS `yt_dist_reward_scheme`;
CREATE TABLE `yt_dist_reward_scheme` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) NOT NULL COMMENT '方案名称',
  `description` varchar(500) DEFAULT NULL COMMENT '方案描述',
  `type` varchar(20) NOT NULL COMMENT '方案类型',
  `status` tinyint DEFAULT '0' COMMENT '状态：0-禁用，1-启用',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_type_status` (`type`,`status`) USING BTREE,
  KEY `idx_time_range` (`start_time`,`end_time`) USING BTREE,
  KEY `idx_tenant_id` (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分销奖励方案表';

-- ----------------------------
-- Records of yt_dist_reward_scheme
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for yt_dist_reward_tag_config
-- ----------------------------
DROP TABLE IF EXISTS `yt_dist_reward_tag_config`;
CREATE TABLE `yt_dist_reward_tag_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `scheme_id` bigint NOT NULL COMMENT '方案ID',
  `tag_id` bigint NOT NULL COMMENT '标签ID',
  `commission_rate` decimal(5,2) NOT NULL COMMENT '佣金比例',
  `extra_reward` decimal(10,2) DEFAULT '0.00' COMMENT '额外奖励',
  `conditions` json DEFAULT NULL COMMENT '条件配置',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_tag` (`scheme_id`,`tag_id`) USING BTREE,
  KEY `idx_scheme_id` (`scheme_id`) USING BTREE,
  KEY `idx_tag_id` (`tag_id`) USING BTREE,
  KEY `idx_tenant_id` (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分销标签配置表';

-- ----------------------------
-- Records of yt_dist_reward_tag_config
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for yt_dist_withdraw
-- ----------------------------
DROP TABLE IF EXISTS `yt_dist_withdraw`;
CREATE TABLE `yt_dist_withdraw` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '提现ID',
  `withdraw_no` varchar(64) NOT NULL COMMENT '提现单号',
  `agent_id` bigint NOT NULL COMMENT '分销员ID',
  `member_id` bigint NOT NULL COMMENT '会员ID',
  `amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `fee` decimal(10,2) DEFAULT '0.00' COMMENT '手续费',
  `real_amount` decimal(10,2) NOT NULL COMMENT '实际到账金额',
  `account_type` tinyint NOT NULL COMMENT '账户类型：1-银行卡 2-支付宝 3-微信',
  `account_name` varchar(32) NOT NULL COMMENT '账户名',
  `account_no` varchar(64) NOT NULL COMMENT '账户号',
  `bank_name` varchar(50) DEFAULT NULL COMMENT '银行名称',
  `bank_branch` varchar(100) DEFAULT NULL COMMENT '开户支行',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态：0-待审核 1-审核通过 2-审核拒绝 3-打款中 4-打款成功 5-打款失败',
  `apply_time` datetime NOT NULL COMMENT '申请时间',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_user` varchar(64) DEFAULT NULL COMMENT '审核人',
  `audit_remark` varchar(200) DEFAULT NULL COMMENT '审核备注',
  `pay_time` datetime DEFAULT NULL COMMENT '打款时间',
  `pay_no` varchar(64) DEFAULT NULL COMMENT '三方支付单号',
  `fail_reason` varchar(200) DEFAULT NULL COMMENT '失败原因',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_withdraw_no` (`withdraw_no`,`deleted`) USING BTREE,
  KEY `idx_agent_id` (`agent_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_apply_time` (`apply_time`) USING BTREE,
  KEY `idx_agent_status_time` (`agent_id`,`status`,`apply_time`) USING BTREE,
  KEY `idx_audit_time` (`audit_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='提现记录表';

-- ----------------------------
-- Records of yt_dist_withdraw
-- ----------------------------
BEGIN;
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
