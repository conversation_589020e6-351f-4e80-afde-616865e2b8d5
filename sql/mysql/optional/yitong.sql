/*
 Navicat Premium Data Transfer

 Source Server         : yitong-test
 Source Server Type    : MySQL
 Source Server Version : 50718 (5.7.18-txsql-log)
 Source Host           : sh-cdb-1tc6zk6g.sql.tencentcdb.com:60196
 Source Schema         : yitong

 Target Server Type    : MySQL
 Target Server Version : 50718 (5.7.18-txsql-log)
 File Encoding         : 65001

 Date: 24/04/2023 19:21:45
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_account
-- ----------------------------
DROP TABLE IF EXISTS `t_account`;
CREATE TABLE `t_account` (
  `code` varchar(16) NOT NULL,
  `name` varchar(64) DEFAULT NULL,
  `parent_code` varchar(16) DEFAULT NULL,
  `merchant_id` bigint(20) DEFAULT NULL,
  `channel_code` varchar(45) DEFAULT NULL,
  `balance` bigint(20) DEFAULT NULL,
  `frozen_balance` bigint(20) DEFAULT NULL,
  `crt_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `upd_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) DEFAULT '0',
  `overdraft` int(11) DEFAULT '0' COMMENT '是否允许透支 0-不允许(默认)  1-允许',
  `agent_id` bigint(20) DEFAULT NULL COMMENT '服务商ID',
  PRIMARY KEY (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of t_account
-- ----------------------------
BEGIN;
INSERT INTO `t_account` (`code`, `name`, `parent_code`, `merchant_id`, `channel_code`, `balance`, `frozen_balance`, `crt_time`, `upd_time`, `version`, `overdraft`, `agent_id`) VALUES ('112201XHS', '渠道商应收账款', '112201', NULL, 'XHS', 83, 0, '2023-04-23 11:21:48', '2023-04-23 12:06:33', 0, 0, NULL);
INSERT INTO `t_account` (`code`, `name`, `parent_code`, `merchant_id`, `channel_code`, `balance`, `frozen_balance`, `crt_time`, `upd_time`, `version`, `overdraft`, `agent_id`) VALUES ('********-6', '商户应付待结算', '********', 6, NULL, 75, 0, '2023-04-23 10:46:49', '2023-04-23 12:06:16', 0, 0, NULL);
INSERT INTO `t_account` (`code`, `name`, `parent_code`, `merchant_id`, `channel_code`, `balance`, `frozen_balance`, `crt_time`, `upd_time`, `version`, `overdraft`, `agent_id`) VALUES ('********-6', '商户应付可提现', '********', 6, NULL, 0, 0, '2023-04-23 10:46:49', '2023-04-23 12:06:16', 0, 0, NULL);
INSERT INTO `t_account` (`code`, `name`, `parent_code`, `merchant_id`, `channel_code`, `balance`, `frozen_balance`, `crt_time`, `upd_time`, `version`, `overdraft`, `agent_id`) VALUES ('********-1', '服务商应付账款', '********', NULL, NULL, 7, 0, '2023-04-23 11:45:15', '2023-04-23 12:06:33', 0, 0, 1);
COMMIT;

-- ----------------------------
-- Table structure for t_account_entry
-- ----------------------------
DROP TABLE IF EXISTS `t_account_entry`;
CREATE TABLE `t_account_entry` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_code` varchar(32) NOT NULL,
  `op_type` int(11) NOT NULL COMMENT '变更类型 1:进 -1:出 0: 其他',
  `amount` bigint(20) NOT NULL COMMENT '变动金额',
  `post_balance` bigint(20) NOT NULL COMMENT '变更后的余额',
  `post_frozen_balance` bigint(20) NOT NULL COMMENT '变更后的冻结余额',
  `rule_code` varchar(16) DEFAULT NULL COMMENT '记账规则代码 ',
  `order_id` varchar(32) DEFAULT NULL COMMENT '记账凭证号',
  `crt_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `upd_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COMMENT='账户变更记录';

-- ----------------------------
-- Records of t_account_entry
-- ----------------------------
BEGIN;
INSERT INTO `t_account_entry` (`id`, `account_code`, `op_type`, `amount`, `post_balance`, `post_frozen_balance`, `rule_code`, `order_id`, `crt_time`, `upd_time`, `version`) VALUES (26, '112201XHS', 1, 83, 83, 0, NULL, NULL, '2023-04-23 12:04:39', '2023-04-23 12:04:39', 0);
INSERT INTO `t_account_entry` (`id`, `account_code`, `op_type`, `amount`, `post_balance`, `post_frozen_balance`, `rule_code`, `order_id`, `crt_time`, `upd_time`, `version`) VALUES (27, '********-1', 1, 7, 7, 0, NULL, NULL, '2023-04-23 12:04:39', '2023-04-23 12:04:39', 0);
INSERT INTO `t_account_entry` (`id`, `account_code`, `op_type`, `amount`, `post_balance`, `post_frozen_balance`, `rule_code`, `order_id`, `crt_time`, `upd_time`, `version`) VALUES (28, '********-6', 1, 75, 75, 0, NULL, NULL, '2023-04-23 12:04:39', '2023-04-23 12:04:39', 0);
COMMIT;

-- ----------------------------
-- Table structure for t_agent
-- ----------------------------
DROP TABLE IF EXISTS `t_agent`;
CREATE TABLE `t_agent` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(64) NOT NULL COMMENT '名称',
  `status` int(11) DEFAULT NULL COMMENT '状态',
  `bank_account_no` varchar(32) NOT NULL COMMENT '银行账号',
  `bank_account_name` varchar(64) DEFAULT NULL COMMENT '银行账户名',
  `bank_name` varchar(64) DEFAULT NULL COMMENT '开户行名称',
  `crt_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `upd_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='服务商';

-- ----------------------------
-- Records of t_agent
-- ----------------------------
BEGIN;
INSERT INTO `t_agent` (`id`, `name`, `status`, `bank_account_no`, `bank_account_name`, `bank_name`, `crt_time`, `upd_time`, `version`) VALUES (1, '三见', 1, '1', '三见', '交通银行上海分行', '2023-04-23 10:23:56', '2023-04-23 10:23:56', 0);
COMMIT;

-- ----------------------------
-- Table structure for t_channel
-- ----------------------------
DROP TABLE IF EXISTS `t_channel`;
CREATE TABLE `t_channel` (
  `code` varchar(16) NOT NULL COMMENT '渠道代码',
  `name` varchar(64) NOT NULL COMMENT '渠道名称',
  `app_id` varchar(32) NOT NULL,
  `app_key` varchar(128) NOT NULL,
  `crt_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `upd_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='渠道商';

-- ----------------------------
-- Records of t_channel
-- ----------------------------
BEGIN;
INSERT INTO `t_channel` (`code`, `name`, `app_id`, `app_key`, `crt_time`, `upd_time`, `version`) VALUES ('XHS', '小红书', 'XHS001', 'MTAwMDA=', '2023-04-23 09:38:52', '2023-04-23 11:05:45', 0);
COMMIT;

-- ----------------------------
-- Table structure for t_channel_sku
-- ----------------------------
DROP TABLE IF EXISTS `t_channel_sku`;
CREATE TABLE `t_channel_sku` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sku_id` bigint(20) NOT NULL COMMENT '商品ID',
  `channel_code` varchar(16) NOT NULL COMMENT '渠道代码',
  `channel_fee_rate` decimal(8,6) DEFAULT NULL COMMENT '通道费率',
  `channel_sku_id` varchar(32) DEFAULT NULL,
  `audit_status` int(11) DEFAULT NULL COMMENT '审核状态 0-待审核 1-审核通过 2-审核不通过 3-审核中',
  `audit_info` varchar(64) DEFAULT NULL COMMENT '审核结果描述',
  `crt_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `upd_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COMMENT='渠道商品关系';

-- ----------------------------
-- Records of t_channel_sku
-- ----------------------------
BEGIN;
INSERT INTO `t_channel_sku` (`id`, `sku_id`, `channel_code`, `channel_fee_rate`, `channel_sku_id`, `audit_status`, `audit_info`, `crt_time`, `upd_time`, `version`) VALUES (9, 8, 'XHS', 0.000000, NULL, 0, NULL, '2023-04-23 10:52:45', '2023-04-23 11:03:58', 0);
INSERT INTO `t_channel_sku` (`id`, `sku_id`, `channel_code`, `channel_fee_rate`, `channel_sku_id`, `audit_status`, `audit_info`, `crt_time`, `upd_time`, `version`) VALUES (10, 9, 'XHS', 0.000000, NULL, 0, NULL, '2023-04-23 11:03:39', '2023-04-23 11:03:39', 0);
COMMIT;

-- ----------------------------
-- Table structure for t_channel_statement
-- ----------------------------
DROP TABLE IF EXISTS `t_channel_statement`;
CREATE TABLE `t_channel_statement` (
  `id` bigint(20) NOT NULL,
  `channel_code` varchar(45) DEFAULT NULL,
  `acct_date` date DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  `amount` bigint(20) DEFAULT NULL,
  `channel_cost` bigint(20) DEFAULT NULL,
  `settle_amount` bigint(20) DEFAULT NULL,
  `crt_time` datetime DEFAULT NULL,
  `upd_time` datetime DEFAULT NULL,
  `version` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='渠道商对账单';

-- ----------------------------
-- Records of t_channel_statement
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for t_channel_statement_list
-- ----------------------------
DROP TABLE IF EXISTS `t_channel_statement_list`;
CREATE TABLE `t_channel_statement_list` (
  `id` bigint(20) NOT NULL,
  `channel_statement_id` bigint(20) DEFAULT NULL,
  `code` varchar(64) DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  `channel_amount` bigint(20) DEFAULT NULL,
  `channel_cost` bigint(20) DEFAULT NULL,
  `channel_settle_amount` bigint(20) DEFAULT NULL,
  `amount` bigint(20) DEFAULT NULL,
  `fee` bigint(20) DEFAULT NULL,
  `settle_amount` bigint(20) DEFAULT NULL,
  `crt_time` datetime DEFAULT NULL,
  `upd_time` datetime DEFAULT NULL,
  `version` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对账单明细';

-- ----------------------------
-- Records of t_channel_statement_list
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for t_channel_store
-- ----------------------------
DROP TABLE IF EXISTS `t_channel_store`;
CREATE TABLE `t_channel_store` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `channel_code` varchar(16) NOT NULL COMMENT '渠道代码',
  `store_id` bigint(20) NOT NULL COMMENT '门店ID',
  `channel_store_id` varchar(32) DEFAULT NULL COMMENT '门店渠道商侧的ID(POI ID)',
  `crt_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `upd_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='渠道门店关系';

-- ----------------------------
-- Records of t_channel_store
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for t_coupon
-- ----------------------------
DROP TABLE IF EXISTS `t_coupon`;
CREATE TABLE `t_coupon` (
  `code` varchar(64) NOT NULL COMMENT '码',
  `amount` bigint(20) NOT NULL COMMENT '核销金额',
  `status` int(11) NOT NULL COMMENT '0-待核销 1-已核销 2-作废',
  `use_date_from` datetime DEFAULT NULL COMMENT '使用有效期开始',
  `use_date_to` datetime DEFAULT NULL COMMENT '使用有效期截至（包括）',
  `order_id` bigint(20) DEFAULT NULL COMMENT '订单ID',
  `sku_id` bigint(20) NOT NULL COMMENT '商品ID',
  `redeem_time` datetime DEFAULT NULL COMMENT '核销时间',
  `redeem_user_id` bigint(20) DEFAULT NULL COMMENT '核销人ID',
  `redeem_store_id` bigint(20) DEFAULT NULL COMMENT '核销门店ID',
  `channel_code` varchar(16) NOT NULL COMMENT '渠道代码',
  `channel_order_id` varchar(64) DEFAULT NULL COMMENT '通道订单号',
  `channel_redeem_code` int(11) DEFAULT NULL COMMENT '渠道核销结果  1-通过 2-不通过',
  `channel_redeem_msg` varchar(64) DEFAULT NULL COMMENT '渠道核销结果描述',
  `channel_cost` bigint(20) DEFAULT NULL COMMENT '渠道成本',
  `channel_recon_code` int(11) DEFAULT NULL COMMENT '对账结果 1-对账成功 2-对账失败',
  `channel_recon_msg` varchar(45) DEFAULT NULL COMMENT '对账结果描述',
  `merchant_id` bigint(20) DEFAULT NULL COMMENT '商户ID',
  `merchant_amount_settled` bigint(20) DEFAULT NULL COMMENT '商户实际结算金额',
  `agent_id` bigint(20) DEFAULT NULL COMMENT '服务商ID',
  `agent_fee_rate` decimal(8,6) DEFAULT NULL COMMENT '服务商费率',
  `agent_fee` bigint(20) DEFAULT NULL COMMENT '服务商金额',
  `agent_fee_settled` bigint(20) DEFAULT NULL COMMENT '服务商实际结算金额',
  `fee_rete` decimal(8,6) DEFAULT NULL COMMENT '平台服务费率',
  `fee` bigint(20) DEFAULT NULL COMMENT '平台服务费',
  `fee_settled` bigint(20) DEFAULT NULL COMMENT '平台服务费实际结算金额',
  `settle_date` date DEFAULT NULL COMMENT '结算日期',
  `settle_batch_no` varchar(16) DEFAULT NULL COMMENT '结算批次号',
  `settle_order_id` bigint(20) DEFAULT NULL COMMENT '结算指令ID',
  `crt_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `upd_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) NOT NULL DEFAULT '0',
  `settle_status` int(11) DEFAULT NULL COMMENT '结算状态 0-应收应付待记账 1-收款成功 2-收款失败  3-应收应付已记账待收款',
  PRIMARY KEY (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='券码';

-- ----------------------------
-- Records of t_coupon
-- ----------------------------
BEGIN;
INSERT INTO `t_coupon` (`code`, `amount`, `status`, `use_date_from`, `use_date_to`, `order_id`, `sku_id`, `redeem_time`, `redeem_user_id`, `redeem_store_id`, `channel_code`, `channel_order_id`, `channel_redeem_code`, `channel_redeem_msg`, `channel_cost`, `channel_recon_code`, `channel_recon_msg`, `merchant_id`, `merchant_amount_settled`, `agent_id`, `agent_fee_rate`, `agent_fee`, `agent_fee_settled`, `fee_rete`, `fee`, `fee_settled`, `settle_date`, `settle_batch_no`, `settle_order_id`, `crt_time`, `upd_time`, `version`, `settle_status`) VALUES ('1099652344724848640', 83, 0, NULL, NULL, 18, 9, NULL, NULL, NULL, 'XHS', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2023-04-23 11:06:51', '2023-04-23 11:06:51', 0, 0);
INSERT INTO `t_coupon` (`code`, `amount`, `status`, `use_date_from`, `use_date_to`, `order_id`, `sku_id`, `redeem_time`, `redeem_user_id`, `redeem_store_id`, `channel_code`, `channel_order_id`, `channel_redeem_code`, `channel_redeem_msg`, `channel_cost`, `channel_recon_code`, `channel_recon_msg`, `merchant_id`, `merchant_amount_settled`, `agent_id`, `agent_fee_rate`, `agent_fee`, `agent_fee_settled`, `fee_rete`, `fee`, `fee_settled`, `settle_date`, `settle_batch_no`, `settle_order_id`, `crt_time`, `upd_time`, `version`, `settle_status`) VALUES ('1099654806114402304', 83, 1, '1987-10-27 08:00:00', '2998-09-08 08:00:00', 19, 9, '2023-04-23 11:31:14', 6, 3, 'XHS', '99', NULL, NULL, 0, NULL, NULL, 6, 75, 1, NULL, 8, 7, NULL, 1, 1, NULL, NULL, NULL, '2023-04-23 11:16:38', '2023-04-23 12:04:39', 2, 3);
COMMIT;

-- ----------------------------
-- Table structure for t_holiday
-- ----------------------------
DROP TABLE IF EXISTS `t_holiday`;
CREATE TABLE `t_holiday` (
  `dt` varchar(10) NOT NULL,
  `holiday` int(11) DEFAULT NULL,
  PRIMARY KEY (`dt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of t_holiday
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for t_merchant
-- ----------------------------
DROP TABLE IF EXISTS `t_merchant`;
CREATE TABLE `t_merchant` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商户',
  `name` varchar(64) NOT NULL COMMENT '商户名称',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态  0-停用 1-正常（默认)',
  `agent_id` bigint(20) DEFAULT NULL COMMENT '服务商',
  `contactor_mobile` varchar(16) DEFAULT NULL COMMENT '联系人电话',
  `contactor_name` varchar(32) DEFAULT NULL COMMENT '联系人名称',
  `bank_acct_no` varchar(32) DEFAULT NULL COMMENT '银行账号',
  `bank_acct_name` varchar(64) DEFAULT NULL COMMENT '银行账号名',
  `bank_name` varchar(64) DEFAULT NULL COMMENT '开户行名称',
  `crt_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `upd_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='商户';

-- ----------------------------
-- Records of t_merchant
-- ----------------------------
BEGIN;
INSERT INTO `t_merchant` (`id`, `name`, `status`, `agent_id`, `contactor_mobile`, `contactor_name`, `bank_acct_no`, `bank_acct_name`, `bank_name`, `crt_time`, `upd_time`, `version`) VALUES (6, '上海为生电子商务有限公司', 1, 1, '***********', '陈亮', NULL, NULL, NULL, '2023-04-23 10:46:49', '2023-04-23 10:46:49', 0);
COMMIT;

-- ----------------------------
-- Table structure for t_order
-- ----------------------------
DROP TABLE IF EXISTS `t_order`;
CREATE TABLE `t_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `channel_code` varchar(16) NOT NULL,
  `channel_order_id` varchar(64) DEFAULT NULL,
  `sku_id` bigint(20) NOT NULL,
  `quantity` bigint(20) NOT NULL COMMENT '购买数量',
  `price` bigint(20) NOT NULL COMMENT '单价',
  `amount` bigint(20) NOT NULL COMMENT '订单金额',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '0-待支付 1-成功 2-失败 3-处理中',
  `expire_time` datetime DEFAULT NULL COMMENT '支付超时时间',
  `crt_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `upd_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) NOT NULL DEFAULT '0',
  `error_info` varchar(64) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COMMENT='订单';

-- ----------------------------
-- Records of t_order
-- ----------------------------
BEGIN;
INSERT INTO `t_order` (`id`, `channel_code`, `channel_order_id`, `sku_id`, `quantity`, `price`, `amount`, `status`, `expire_time`, `crt_time`, `upd_time`, `version`, `error_info`) VALUES (18, 'XHS', '99', 9, 1, 83, 83, 1, '2023-04-23 11:19:49', '2023-04-23 11:05:51', '2023-04-23 11:06:51', 2, NULL);
INSERT INTO `t_order` (`id`, `channel_code`, `channel_order_id`, `sku_id`, `quantity`, `price`, `amount`, `status`, `expire_time`, `crt_time`, `upd_time`, `version`, `error_info`) VALUES (19, 'XHS', '99', 9, 1, 83, 83, 1, '2023-04-23 11:20:17', '2023-04-23 11:06:19', '2023-04-23 11:16:38', 2, NULL);
COMMIT;

-- ----------------------------
-- Table structure for t_settle_coupon
-- ----------------------------
DROP TABLE IF EXISTS `t_settle_coupon`;
CREATE TABLE `t_settle_coupon` (
  `code` varchar(64) NOT NULL COMMENT '券码',
  `amount` bigint(20) DEFAULT NULL COMMENT '核销金额',
  `channel_code` varchar(8) DEFAULT NULL COMMENT '通道代码',
  `channel_cost_rate` decimal(8,6) DEFAULT NULL COMMENT '通道费率',
  `channel_cost` bigint(20) DEFAULT NULL COMMENT '通道成本',
  `channel_cost_pay_method` int(11) DEFAULT NULL COMMENT '通道成本支付方式 1-实收 2-后收',
  `fee_rate` decimal(8,6) DEFAULT NULL COMMENT '平台服务费率',
  `fee` bigint(20) DEFAULT NULL COMMENT '平台服务费',
  `fee_settle_amount` bigint(20) DEFAULT NULL COMMENT '平台服务费结算金额',
  `fee_payer` int(11) DEFAULT NULL COMMENT '平台服务费付款方 1-代理商  2-商户',
  `agent_id` bigint(20) DEFAULT NULL COMMENT '代理商ID',
  `agent_fee_rate` decimal(8,6) DEFAULT NULL COMMENT '代理商费率',
  `agent_fee` bigint(20) DEFAULT NULL COMMENT '代理商服务费',
  `agent_fee_settle_amount` bigint(20) DEFAULT NULL COMMENT '代理商服务费结算金额',
  `merchant_id` bigint(20) DEFAULT NULL COMMENT '结算商户ID',
  `merchant_settle_amount` bigint(20) DEFAULT NULL COMMENT '商户结算金额',
  `crt_time` datetime DEFAULT NULL,
  `upd_time` datetime DEFAULT NULL,
  `version` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Records of t_settle_coupon
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for t_settle_order
-- ----------------------------
DROP TABLE IF EXISTS `t_settle_order`;
CREATE TABLE `t_settle_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `batch_no` varchar(16) NOT NULL COMMENT '结算批次号',
  `merchant_id` bigint(20) NOT NULL COMMENT '结算商户ID',
  `settle_amount` bigint(20) NOT NULL COMMENT '结算金额',
  `settle_date` datetime NOT NULL COMMENT '结算日期',
  `crt_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `upd_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) NOT NULL DEFAULT '0',
  `trade_no` varchar(32) DEFAULT NULL COMMENT '付款流水号',
  `bank_acct_no` varchar(32) DEFAULT NULL COMMENT '收款账号',
  `bank_acct_name` varchar(32) DEFAULT NULL COMMENT '收款账号名称',
  `bank_name` varchar(64) DEFAULT NULL COMMENT '开户行',
  `status` int(11) DEFAULT NULL COMMENT '0-待付款 1-付款成功 2-付款失败 3-付款中',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='结算指令';

-- ----------------------------
-- Records of t_settle_order
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for t_sku
-- ----------------------------
DROP TABLE IF EXISTS `t_sku`;
CREATE TABLE `t_sku` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) NOT NULL COMMENT '商品名称',
  `merchant_id` bigint(20) NOT NULL COMMENT '商户ID',
  `origin_price` bigint(20) DEFAULT NULL COMMENT '原价',
  `price` bigint(20) NOT NULL COMMENT '售卖价格',
  `status` int(11) DEFAULT NULL COMMENT '状态',
  `sold_time_from` datetime DEFAULT NULL COMMENT '销售起始时间',
  `sold_time_to` datetime DEFAULT NULL COMMENT '销售截止时间',
  `valid_time_type` int(11) DEFAULT NULL COMMENT '有效期类型 1-固定时间',
  `valid_time_from` datetime DEFAULT NULL COMMENT '有效期开始时间',
  `valid_time_to` datetime DEFAULT NULL COMMENT '有效期截止时间',
  `fee_rate` decimal(8,6) DEFAULT NULL COMMENT '平台服务费率',
  `fee_payer` int(11) DEFAULT '0' COMMENT '平台服务费付款方 0-服务商(默认) 1-商户',
  `agent_id` bigint(20) DEFAULT NULL COMMENT '服务商ID',
  `agent_fee_rate` decimal(8,6) DEFAULT NULL COMMENT '服务商费率',
  `crt_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `upd_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COMMENT='商品';

-- ----------------------------
-- Records of t_sku
-- ----------------------------
BEGIN;
INSERT INTO `t_sku` (`id`, `name`, `merchant_id`, `origin_price`, `price`, `status`, `sold_time_from`, `sold_time_to`, `valid_time_type`, `valid_time_from`, `valid_time_to`, `fee_rate`, `fee_payer`, `agent_id`, `agent_fee_rate`, `crt_time`, `upd_time`, `version`) VALUES (8, '是持完观党', 6, 100, 83, 1, '2010-03-03 08:00:00', '2001-05-04 08:00:00', 1, '1987-10-27 08:00:00', '1998-09-08 08:00:00', 0.010000, 0, 1, 0.100000, '2023-04-23 10:52:44', '2023-04-23 10:54:29', 0);
INSERT INTO `t_sku` (`id`, `name`, `merchant_id`, `origin_price`, `price`, `status`, `sold_time_from`, `sold_time_to`, `valid_time_type`, `valid_time_from`, `valid_time_to`, `fee_rate`, `fee_payer`, `agent_id`, `agent_fee_rate`, `crt_time`, `upd_time`, `version`) VALUES (9, '是持完观党', 6, 100, 83, 1, '2010-03-03 08:00:00', '2001-05-04 08:00:00', 1, '1987-10-27 08:00:00', '1998-09-08 08:00:00', 0.010000, 0, 1, 0.100000, '2023-04-23 11:03:38', '2023-04-23 11:03:38', 0);
COMMIT;

-- ----------------------------
-- Table structure for t_sku_definition
-- ----------------------------
DROP TABLE IF EXISTS `t_sku_definition`;
CREATE TABLE `t_sku_definition` (
  `sku_id` bigint(20) NOT NULL,
  `definition` varchar(5000) NOT NULL COMMENT 'SKU JSON',
  `crt_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `upd_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`sku_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SKU定义';

-- ----------------------------
-- Records of t_sku_definition
-- ----------------------------
BEGIN;
INSERT INTO `t_sku_definition` (`sku_id`, `definition`, `crt_time`, `upd_time`, `version`) VALUES (8, '{\"appointment\":0,\"detailImages\":[\"http://dummyimage.com/400x400\"],\"images\":[\"http://dummyimage.com/400x400\"],\"name\":\"是持完观党\",\"originPrice\":100,\"packages\":[{\"items\":[{\"name\":\"段年花示最容其\",\"originPrice\":51,\"quantity\":85,\"unit\":\"sunt ullamco dolor\"},{\"name\":\"其极量江\",\"originPrice\":85,\"quantity\":26,\"unit\":\"velit nisi Lorem\"},{\"name\":\"至话体越周\",\"originPrice\":33,\"quantity\":12,\"unit\":\"non\"}],\"name\":\"并意比酸\"}],\"price\":83,\"quantity\":75,\"refundPolicy\":8,\"ruleNotifications\":[{\"content\":\"eu esse\"},{\"content\":\"occaecat\"},{\"content\":\"cillum est in minim\"}],\"shortname\":\"设你代进\",\"soldTime\":{\"endDate\":\"2001-05-04 08:00:00\",\"startDate\":\"2010-03-03 08:00:00\"},\"useDateTime\":{\"holidays\":[7],\"timeDurations\":[{\"endTime\":\"15:28:36\",\"startTime\":\"23:10:59\"}],\"weekdays\":[51,54,27]},\"useInHoliday\":67,\"useInWeekend\":1,\"validDate\":{\"endDate\":\"1998-09-08 08:00:00\",\"startDate\":\"1987-10-27 08:00:00\"},\"validDateType\":65}', '2023-04-23 10:52:44', '2023-04-23 10:52:44', 0);
INSERT INTO `t_sku_definition` (`sku_id`, `definition`, `crt_time`, `upd_time`, `version`) VALUES (9, '{\"appointment\":0,\"detailImages\":[\"http://dummyimage.com/400x400\"],\"images\":[\"http://dummyimage.com/400x400\"],\"name\":\"是持完观党\",\"originPrice\":100,\"packages\":[{\"items\":[{\"name\":\"段年花示最容其\",\"originPrice\":51,\"quantity\":85,\"unit\":\"sunt ullamco dolor\"},{\"name\":\"其极量江\",\"originPrice\":85,\"quantity\":26,\"unit\":\"velit nisi Lorem\"},{\"name\":\"至话体越周\",\"originPrice\":33,\"quantity\":12,\"unit\":\"non\"}],\"name\":\"并意比酸\"}],\"price\":83,\"quantity\":75,\"refundPolicy\":8,\"ruleNotifications\":[{\"content\":\"eu esse\"},{\"content\":\"occaecat\"},{\"content\":\"cillum est in minim\"}],\"shortname\":\"设你代进\",\"soldTime\":{\"endDate\":\"2001-05-04 08:00:00\",\"startDate\":\"2010-03-03 08:00:00\"},\"useDateTime\":{\"holidays\":[7],\"timeDurations\":[{\"endTime\":\"15:28:36\",\"startTime\":\"23:10:59\"}],\"weekdays\":[51,54,27]},\"useInHoliday\":67,\"useInWeekend\":1,\"validDate\":{\"endDate\":\"1998-09-08 08:00:00\",\"startDate\":\"1987-10-27 08:00:00\"},\"validDateType\":65}', '2023-04-23 11:03:39', '2023-04-23 11:03:39', 0);
COMMIT;

-- ----------------------------
-- Table structure for t_sku_quantity
-- ----------------------------
DROP TABLE IF EXISTS `t_sku_quantity`;
CREATE TABLE `t_sku_quantity` (
  `sku_id` bigint(20) NOT NULL,
  `quantity` bigint(20) NOT NULL,
  PRIMARY KEY (`sku_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SKU库存';

-- ----------------------------
-- Records of t_sku_quantity
-- ----------------------------
BEGIN;
INSERT INTO `t_sku_quantity` (`sku_id`, `quantity`) VALUES (8, 75);
INSERT INTO `t_sku_quantity` (`sku_id`, `quantity`) VALUES (9, 73);
COMMIT;

-- ----------------------------
-- Table structure for t_sku_store
-- ----------------------------
DROP TABLE IF EXISTS `t_sku_store`;
CREATE TABLE `t_sku_store` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `sku_id` bigint(20) NOT NULL,
  `store_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COMMENT='商品投放的门店';

-- ----------------------------
-- Records of t_sku_store
-- ----------------------------
BEGIN;
INSERT INTO `t_sku_store` (`id`, `sku_id`, `store_id`) VALUES (8, 8, 3);
INSERT INTO `t_sku_store` (`id`, `sku_id`, `store_id`) VALUES (9, 9, 3);
COMMIT;

-- ----------------------------
-- Table structure for t_sms_code
-- ----------------------------
DROP TABLE IF EXISTS `t_sms_code`;
CREATE TABLE `t_sms_code` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `mobile` varchar(16) DEFAULT NULL,
  `biz_type` varchar(8) DEFAULT NULL,
  `code` varchar(8) DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  `send_time` datetime DEFAULT NULL,
  `valid_minutes` int(11) DEFAULT NULL,
  `expire_time` datetime DEFAULT NULL,
  `crt_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `upd_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信验证码';

-- ----------------------------
-- Records of t_sms_code
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for t_store
-- ----------------------------
DROP TABLE IF EXISTS `t_store`;
CREATE TABLE `t_store` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `merchant_id` bigint(20) NOT NULL,
  `name` varchar(64) NOT NULL,
  `address` varchar(45) DEFAULT NULL,
  `crt_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `upd_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='门店';

-- ----------------------------
-- Records of t_store
-- ----------------------------
BEGIN;
INSERT INTO `t_store` (`id`, `merchant_id`, `name`, `address`, `crt_time`, `upd_time`, `version`) VALUES (3, 6, '为生-浦东店', '上海市浦东新区', '2023-04-23 10:49:09', '2023-04-23 10:49:09', 0);
COMMIT;

-- ----------------------------
-- Table structure for t_user
-- ----------------------------
DROP TABLE IF EXISTS `t_user`;
CREATE TABLE `t_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) NOT NULL,
  `mobile` varchar(11) NOT NULL,
  `password` varchar(32) DEFAULT NULL,
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '0-注销 1-正常(默认)',
  `crt_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `upd_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `version` bigint(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='商户子账号';

-- ----------------------------
-- Records of t_user
-- ----------------------------
BEGIN;
INSERT INTO `t_user` (`id`, `name`, `mobile`, `password`, `status`, `crt_time`, `upd_time`, `version`) VALUES (6, '陈亮', '***********', '78MF6g', 1, '2023-04-23 10:46:49', '2023-04-23 10:46:49', 0);
COMMIT;

-- ----------------------------
-- Table structure for t_user_store
-- ----------------------------
DROP TABLE IF EXISTS `t_user_store`;
CREATE TABLE `t_user_store` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `merchant_id` bigint(20) NOT NULL,
  `store_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='商户子账号门店关系';

-- ----------------------------
-- Records of t_user_store
-- ----------------------------
BEGIN;
INSERT INTO `t_user_store` (`id`, `user_id`, `merchant_id`, `store_id`) VALUES (5, 6, 6, NULL);
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
