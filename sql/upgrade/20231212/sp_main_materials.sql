/*
 Navicat Premium Data Transfer

 Source Server         : yitong-dev
 Source Server Type    : MySQL
 Source Server Version : 50718 (5.7.18-txsql-log)
 Source Host           : sh-cdb-1tc6zk6g.sql.tencentcdb.com:60196
 Source Schema         : yitong_dev

 Target Server Type    : MySQL
 Target Server Version : 50718 (5.7.18-txsql-log)
 File Encoding         : 65001

 Date: 26/12/2023 19:42:09
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sp_main_materials
-- ----------------------------
DROP TABLE IF EXISTS `sp_main_materials`;
CREATE TABLE `sp_main_materials` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `sp_id` bigint(20) DEFAULT NULL COMMENT '商家名称ID',
  `sp_name` varchar(255) DEFAULT NULL COMMENT '商家名称',
  `store_id` bigint(20) DEFAULT NULL COMMENT '门店ID',
  `store_name` varchar(255) DEFAULT NULL COMMENT '门店名称',
  `spu_id` bigint(20) DEFAULT NULL COMMENT 'SpuID',
  `sku_id` bigint(20) DEFAULT NULL COMMENT 'SkuID',
  `type` tinyint(4) DEFAULT NULL COMMENT '类型：1头图2 环境 ',
  `size` int(11) NOT NULL COMMENT '文件大小',
  `status` tinyint(1) DEFAULT NULL COMMENT '状态： 1.待审核，2. 已通过，3.已拒绝',
  `audit_msg` varchar(255) DEFAULT NULL COMMENT '审核意见',
  `audit_user` varchar(255) DEFAULT NULL COMMENT '审核人',
  `file_name` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '文件名称',
  `file_path` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '文件路径',
  `file_url` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '文件url',
  `file_type` tinyint(4) DEFAULT NULL COMMENT '文件类型：1图片 2 视频 ',
  `remark` varchar(800) CHARACTER SET utf8 DEFAULT '' COMMENT '备注',
  `creator` varchar(64) CHARACTER SET utf8mb4  DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4  DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='主体素材';

SET FOREIGN_KEY_CHECKS = 1;
