/*
 Navicat Premium Data Transfer

 Source Server         : yitong-dev
 Source Server Type    : MySQL
 Source Server Version : 50718 (5.7.18-txsql-log)
 Source Host           : sh-cdb-1tc6zk6g.sql.tencentcdb.com:60196
 Source Schema         : yitong_dev

 Target Server Type    : MySQL
 Target Server Version : 50718 (5.7.18-txsql-log)
 File Encoding         : 65001

 Date: 12/12/2023 11:33:01
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for yt_activity_coupon
-- ----------------------------
DROP TABLE IF EXISTS `yt_activity_coupon`;
CREATE TABLE `yt_activity_coupon` (
      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
      `activity_id` bigint(20) NOT NULL COMMENT '活动ID',
      `activity_name` varchar(512) DEFAULT NULL COMMENT '活动名称',
      `member_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
      `coupon_num` varchar(64) NOT NULL COMMENT '优惠券编号',
      `status` tinyint(4) NOT NULL COMMENT '优惠码状态\n     *\n     * 1-未使用\n     * 2-已完成',
      `valid_start_time` datetime NOT NULL COMMENT '生效开始时间',
      `valid_end_time` datetime NOT NULL COMMENT '生效结束时间',
      `product_spu_ids` varchar(512) DEFAULT NULL COMMENT '商品 SPU 编号的数组',
      `order_id` bigint(20) NOT NULL COMMENT '订单ID',
      `coupon_id` bigint(20) DEFAULT NULL COMMENT '券码Id',
      `coupon_code` varchar(16) DEFAULT NULL COMMENT '券码Code',
      `coupon_amount` varchar(16) DEFAULT NULL COMMENT '券码金额',
      `coupon_sp_id` bigint(20) DEFAULT NULL COMMENT '券码商家ID',
      `coupon_sp_name` varchar(255) DEFAULT NULL COMMENT '券码商家名称',
      `coupon_spu_id` bigint(20) DEFAULT NULL COMMENT '券码商品SPUID',
      `coupon_sku_id` bigint(20) NOT NULL COMMENT '券码商品SKUID',
      `coupon_sku_name` varchar(64) DEFAULT NULL COMMENT '券码商品名称',
      `channel_code` varchar(16) NOT NULL COMMENT '渠道代码',
      `channel_name` varchar(64) DEFAULT NULL COMMENT '渠道名称',
      `channel_order_id` varchar(64) DEFAULT NULL COMMENT '渠道订单号',
      `total_num` int(11) unsigned zerofill DEFAULT NULL COMMENT '总兑换数量',
      `use_num` int(11) unsigned zerofill DEFAULT NULL COMMENT '总使用数量',
      `first_use_time` datetime DEFAULT NULL COMMENT '初次使用时间',
      `creator` varchar(64) DEFAULT '' COMMENT '创建者',
      `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      `updater` varchar(64) DEFAULT '' COMMENT '更新者',
      `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
      `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
      PRIMARY KEY (`id`) USING BTREE,
      UNIQUE KEY `coupon_num` (`coupon_num`) USING BTREE COMMENT '券码唯一'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='平台活动优惠劵';

-- ----------------------------
-- Table structure for yt_activity_coupon_redeem
-- ----------------------------
DROP TABLE IF EXISTS `yt_activity_coupon_redeem`;
CREATE TABLE `yt_activity_coupon_redeem` (
     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
     `activity_id` bigint(20) NOT NULL COMMENT '活动ID',
     `coupon_id` bigint(20) NOT NULL COMMENT '券码Id',
     `member_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
     `redeem_time` datetime DEFAULT NULL COMMENT '核销时间',
     `redeem_code` varchar(64) DEFAULT NULL COMMENT '核销券码',
     `redeem_user_id` bigint(20) DEFAULT NULL COMMENT '核销人ID',
     `redeem_user_name` varchar(64) DEFAULT NULL COMMENT '核销人名称',
     `redeem_store_id` bigint(20) DEFAULT NULL COMMENT '核销门店ID',
     `redeem_store_name` varchar(64) DEFAULT NULL COMMENT '核销门店名称',
     `redeem_sp_id` bigint(20) DEFAULT NULL COMMENT '核销商家ID',
     `redeem_sp_name` varchar(512) DEFAULT NULL COMMENT '核销商家名称',
     `redeem_spu_id` bigint(20) DEFAULT NULL COMMENT '核销商品SPUID',
     `redeem_sku_id` bigint(20) DEFAULT NULL COMMENT '核销商品SKUID',
     `redeem_sku_name` varchar(64) DEFAULT NULL COMMENT '核销商品名称',
     `redeem_sku_amount` int(12) DEFAULT NULL COMMENT '核销商品金额',
     `creator` varchar(64) DEFAULT '' COMMENT '创建者',
     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `updater` varchar(64) DEFAULT '' COMMENT '更新者',
     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
     `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='平台活动优惠劵核销记录';

-- ----------------------------
-- Table structure for yt_activity_info
-- ----------------------------
DROP TABLE IF EXISTS `yt_activity_info`;
CREATE TABLE `yt_activity_info` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `activity_name` varchar(512) DEFAULT NULL COMMENT '活动名称',
    `activity_image_url` varchar(512) DEFAULT NULL COMMENT '活动图片',
    `activity_type` tinyint(1) DEFAULT NULL COMMENT '活动类型 1：商品报名',
    `activity_start_time` datetime DEFAULT NULL COMMENT '开始时间',
    `activity_end_time` datetime DEFAULT NULL COMMENT '结束时间',
    `activity_status` tinyint(1) DEFAULT NULL COMMENT '状态: 1 活动待开始，2活动进行中，3活动已结束',
    `apply_start_time` datetime DEFAULT NULL COMMENT '报名开始束时间',
    `apply_end_time` datetime DEFAULT NULL COMMENT '报名结束时间',
    `apply_status` tinyint(1) DEFAULT NULL COMMENT '状态: 1 报名进行中，2报名已结束',
    `activity_desc` varchar(1024) DEFAULT NULL COMMENT '活动详情',
    `activity_rule_info` varchar(1024) DEFAULT NULL COMMENT '活动条件详情',
    `merchant_apply_limit` int(11) DEFAULT NULL COMMENT '商家报名限制：-1 不限制',
    `publish_channels` varchar(500) DEFAULT NULL COMMENT '发布渠道',
    `privileges` varchar(500) DEFAULT NULL COMMENT '活动权益',
    `validity_type` tinyint(4) NOT NULL COMMENT '生效日期类型 1 固定日期 2 领取之后',
    `valid_start_time` datetime DEFAULT NULL COMMENT '固定日期-生效开始时间',
    `valid_end_time` datetime DEFAULT NULL COMMENT '固定日期-生效结束时间',
    `fixed_start_term` int(11) DEFAULT NULL COMMENT '领取日期-开始天数',
    `fixed_end_term` int(11) DEFAULT NULL COMMENT '领取日期-结束天数',
    `exchange_type` int(11) NOT NULL DEFAULT '0' COMMENT '兑换类型：1 N:1, 2 N:N',
    `exchange_count` int(11) NOT NULL DEFAULT '0' COMMENT '兑换数量',
    `exchange_spu_limit` tinyint(4) NOT NULL DEFAULT '0' COMMENT '兑换商品限制，1：商品不重复，2 商品可重复',
    `take_count` int(11) NOT NULL DEFAULT '0' COMMENT '领取优惠券的数量',
    `use_count` int(11) NOT NULL DEFAULT '0' COMMENT '使用优惠券的次数',
    `remark` varchar(800) DEFAULT '' COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COLLATE utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='平台活动';

-- ----------------------------
-- Table structure for yt_activity_sp_apply
-- ----------------------------
DROP TABLE IF EXISTS `yt_activity_sp_apply`;
CREATE TABLE `yt_activity_sp_apply` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `activity_id` bigint(20) DEFAULT NULL COMMENT '活动Id',
    `sp_id` bigint(20) DEFAULT NULL COMMENT '商家Id',
    `spu_id` bigint(20) DEFAULT NULL COMMENT '商品Spu Id',
    `sku_id` bigint(20) DEFAULT NULL COMMENT '商品Sku Id',
    `apply_status` tinyint(1) DEFAULT NULL COMMENT '申请状态:1.待审批，2 审批通过，3 审批拒绝，5 已失效 20 待商家确认',
    `submit_channel` tinyint(1) DEFAULT NULL COMMENT '提报类型:1.商家自提，2 BD待提报，3 服务商待提报',
    `apply_status_remark` varchar(500) DEFAULT NULL COMMENT '申请变更原因',
    `apply_time` datetime DEFAULT NULL COMMENT '申请时间',
    `remark` varchar(800) DEFAULT '' COMMENT '备注',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='平台活动商家报名';

SET FOREIGN_KEY_CHECKS = 1;
