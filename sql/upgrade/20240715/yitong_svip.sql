/*
 Navicat Premium Data Transfer

 Source Server         : yitong-prod【dev】
 Source Server Type    : MySQL
 Source Server Version : 80022 (8.0.22-cynos)
 Source Host           : sh-cynosdbmysql-grp-dlfua43g.sql.tencentcdb.com:23555
 Source Schema         : yitong_dev

 Target Server Type    : MySQL
 Target Server Version : 80022 (8.0.22-cynos)
 File Encoding         : 65001

 Date: 15/07/2024 11:06:36
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for yt_svip_note_sp
-- ----------------------------
DROP TABLE IF EXISTS `yt_svip_note_sp`;
CREATE TABLE `yt_svip_note_sp` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `sp_id` bigint DEFAULT NULL COMMENT '商家ID',
  `name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '笔记计划名称',
  `model_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型ID',
  `model_params` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型参数',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `status` tinyint(1) DEFAULT NULL COMMENT '状态: 1 启用，0 禁用',
  `item_addr` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发布地址',
  `item_count` int NOT NULL DEFAULT '0' COMMENT '笔记数量',
  `item_topic` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '笔记话题',
  `remark` varchar(800) CHARACTER SET utf8 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='增值服务商家笔记计划';

-- ----------------------------
-- Table structure for yt_svip_note_sp_item
-- ----------------------------
DROP TABLE IF EXISTS `yt_svip_note_sp_item`;
CREATE TABLE `yt_svip_note_sp_item` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `note_sp_id` bigint DEFAULT NULL COMMENT '商家笔记ID',
  `note_sp_store_id` bigint DEFAULT NULL COMMENT '商家门店笔记ID',
  `sp_id` bigint DEFAULT NULL COMMENT '商家ID',
  `store_id` bigint DEFAULT NULL COMMENT '门店ID',
  `status` tinyint DEFAULT '0' COMMENT '状态: 0 未发布,1 已发布',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `product_id` bigint DEFAULT NULL COMMENT '商品ID',
  `product_sp_id` bigint DEFAULT NULL COMMENT '商家商品ID',
  `product_privilege_used_num` int DEFAULT NULL COMMENT '产品权益使用数量',
  `item_title` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '笔记标题',
  `item_images` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '笔记图像',
  `item_content` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '笔记内容',
  `item_topic` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '笔记话题',
  `item_addr` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '笔记发布地址',
  `item_publish_time` datetime DEFAULT NULL COMMENT '笔记发布时间',
  `u_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '笔记生成用户Id',
  `u_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'ip',
  `u_agent` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'agent',
  `remark` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_product_id` (`product_id`),
  KEY `idx_sp_id` (`sp_id`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_note_sp_id` (`note_sp_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='增值服务笔记商家发布笔记';

-- ----------------------------
-- Table structure for yt_svip_note_sp_item_images
-- ----------------------------
DROP TABLE IF EXISTS `yt_svip_note_sp_item_images`;
CREATE TABLE `yt_svip_note_sp_item_images` (
  `id` bigint NOT NULL COMMENT 'Id',
  `item_id` bigint DEFAULT NULL COMMENT '笔记ID',
  `image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片url',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='增值服务商家笔记图片';

-- ----------------------------
-- Table structure for yt_svip_note_sp_store
-- ----------------------------
DROP TABLE IF EXISTS `yt_svip_note_sp_store`;
CREATE TABLE `yt_svip_note_sp_store` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `note_sp_id` bigint DEFAULT NULL COMMENT '商家笔记ID',
  `sp_id` bigint DEFAULT NULL COMMENT '商家ID',
  `store_id` bigint DEFAULT NULL COMMENT '门店ID',
  `customize_type` tinyint(1) DEFAULT '0' COMMENT '自定义类型：1 是，0 否',
  `model_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型ID',
  `model_params` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型参数',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `status` tinyint(1) DEFAULT NULL COMMENT '状态: 1 启用，2 禁用',
  `item_addr` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发布地址',
  `item_count` int NOT NULL DEFAULT '0' COMMENT '笔记数量',
  `item_topic` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '笔记话题',
  `remark` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_sp_id` (`sp_id`) USING BTREE,
  KEY `idx_store_id` (`store_id`) USING BTREE,
  KEY `idx_note_sp_id` (`note_sp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='增值服务笔记商家门店';

-- ----------------------------
-- Table structure for yt_svip_note_sp_store_template_rel
-- ----------------------------
DROP TABLE IF EXISTS `yt_svip_note_sp_store_template_rel`;
CREATE TABLE `yt_svip_note_sp_store_template_rel` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `note_sp_id` bigint DEFAULT NULL COMMENT '商家笔记ID',
  `note_sp_store_id` bigint DEFAULT NULL COMMENT '商家门店笔记ID',
  `sp_id` bigint DEFAULT NULL COMMENT '商家ID',
  `store_id` bigint DEFAULT NULL COMMENT '门店ID',
  `model_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型ID',
  `model_params` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型参数',
  `status` tinyint(1) DEFAULT NULL COMMENT '状态: 1 启用，2 禁用',
  `weight` int DEFAULT '1' COMMENT '权重',
  `remark` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_sp_id` (`sp_id`) USING BTREE,
  KEY `idx_store_id` (`store_id`) USING BTREE,
  KEY `idx_note_sp_id` (`note_sp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='增值服务笔记商家门店笔记模板';

-- ----------------------------
-- Table structure for yt_svip_note_sp_template_rel
-- ----------------------------
DROP TABLE IF EXISTS `yt_svip_note_sp_template_rel`;
CREATE TABLE `yt_svip_note_sp_template_rel` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `sp_id` bigint DEFAULT NULL COMMENT '商家ID',
  `note_sp_id` bigint DEFAULT NULL COMMENT '商家笔记ID',
  `model_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型ID',
  `model_params` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型参数',
  `status` tinyint(1) DEFAULT NULL COMMENT '状态: 1 启用，0 禁用',
  `weight` int DEFAULT '1' COMMENT '权重',
  `remark` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='增值服务商家笔记计划';

-- ----------------------------
-- Table structure for yt_svip_note_sp_xhs_item
-- ----------------------------
DROP TABLE IF EXISTS `yt_svip_note_sp_xhs_item`;
CREATE TABLE `yt_svip_note_sp_xhs_item` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `note_sp_id` bigint DEFAULT NULL COMMENT '商家笔记ID',
  `sp_id` bigint DEFAULT NULL COMMENT '商家ID',
  `store_id` bigint DEFAULT NULL COMMENT '门店ID',
  `product_id` bigint DEFAULT NULL COMMENT '商品ID',
  `user_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '笔记用户Id',
  `nick_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '笔记用户',
  `user_avatar` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '笔记用户图形',
  `item_title` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '笔记标题',
  `item_images` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '笔记图像',
  `item_content` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '笔记内容',
  `item_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '笔记id',
  `item_url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '笔记链接',
  `item_addr` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '笔记发布地址',
  `item_publish_time` datetime DEFAULT NULL COMMENT '笔记发布时间',
  `item_expire_time` datetime DEFAULT NULL COMMENT '笔记失效时间',
  `item_like_total` int(11) unsigned zerofill DEFAULT NULL COMMENT '笔记点赞',
  `item_like_collect` int(11) unsigned zerofill DEFAULT NULL COMMENT '笔记收藏',
  `item_like_comment` int(11) unsigned zerofill DEFAULT NULL COMMENT '笔记评论',
  `item_like_share` int(11) unsigned zerofill DEFAULT NULL COMMENT '笔记分享',
  `item_like_attention` int(11) unsigned zerofill DEFAULT NULL COMMENT '笔记关注',
  `item_exposure_total` int(11) unsigned zerofill DEFAULT NULL COMMENT '全部-笔记曝光',
  `item_click_total` int(11) unsigned zerofill DEFAULT NULL COMMENT '全部-笔记点击',
  `item_topic` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '笔记话题',
  `remark` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_product_id` (`product_id`),
  KEY `idx_sp_id` (`sp_id`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_note_sp_id` (`note_sp_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='增值服务笔记商家发布笔记[小红书]';

-- ----------------------------
-- Table structure for yt_svip_order
-- ----------------------------
DROP TABLE IF EXISTS `yt_svip_order`;
CREATE TABLE `yt_svip_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `sp_id` bigint DEFAULT NULL COMMENT '商家ID',
  `order_num` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '订单编码',
  `order_time` datetime DEFAULT NULL COMMENT '下单时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `out_order_num` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '三方订单编码',
  `status` tinyint(1) DEFAULT NULL COMMENT '状态: 1 待支付，2 已支付，3 已取消 4 已退款',
  `product_amount` int DEFAULT NULL COMMENT '商品金额\n',
  `total_amount` int DEFAULT NULL COMMENT '总金额',
  `pay_amount` int DEFAULT NULL COMMENT '付款金额',
  `discount_amount` int DEFAULT NULL COMMENT '平台和商家优惠',
  `pay_channel` tinyint DEFAULT NULL COMMENT '支付渠道 1 微信，2支付宝 ',
  `pay_channel_amount` int DEFAULT NULL COMMENT '支付渠道费',
  `remark` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='增值服务产品订单';

-- ----------------------------
-- Table structure for yt_svip_order_item
-- ----------------------------
DROP TABLE IF EXISTS `yt_svip_order_item`;
CREATE TABLE `yt_svip_order_item` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_id` bigint DEFAULT NULL COMMENT '订单Id',
  `product_id` bigint DEFAULT NULL COMMENT '产品Id',
  `product_nanme` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产品名称',
  `market_amount` int DEFAULT NULL COMMENT '市场价',
  `total` int DEFAULT NULL COMMENT '市场价',
  `sale_amount` int DEFAULT NULL COMMENT '销售价',
  `remark` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='增值服务产品订单项';

-- ----------------------------
-- Table structure for yt_svip_product
-- ----------------------------
DROP TABLE IF EXISTS `yt_svip_product`;
CREATE TABLE `yt_svip_product` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `type` tinyint DEFAULT NULL COMMENT '类型1：小红书笔记',
  `name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产品名称',
  `image_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产品图片',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `status` tinyint(1) DEFAULT NULL COMMENT '状态: 1 上线，2下线',
  `rule_info` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '活动规则',
  `privilege_type` tinyint DEFAULT NULL COMMENT '权益类型 1 数量，2 按月',
  `privilege_num` int DEFAULT NULL COMMENT '权益数量',
  `giveaway_num` int DEFAULT NULL COMMENT '赠送数量',
  `validity_type` tinyint DEFAULT NULL COMMENT '生效日期类型 1 固定日期 2 领取之后',
  `valid_start_time` datetime DEFAULT NULL COMMENT '固定日期-生效开始时间',
  `valid_end_time` datetime DEFAULT NULL COMMENT '固定日期-生效结束时间',
  `fixed_start_term` int DEFAULT NULL COMMENT '领取日期-开始天数',
  `fixed_end_term` int DEFAULT NULL COMMENT '领取日期-结束天数',
  `market_amount` int DEFAULT NULL COMMENT '市场价',
  `sale_amount` int DEFAULT NULL COMMENT '销售价',
  `remark` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='增值服务产品';

-- ----------------------------
-- Table structure for yt_svip_product_sp
-- ----------------------------
DROP TABLE IF EXISTS `yt_svip_product_sp`;
CREATE TABLE `yt_svip_product_sp` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `product_id` bigint DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产品名称',
  `sp_id` bigint DEFAULT NULL COMMENT '商家ID',
  `status` tinyint(1) DEFAULT NULL COMMENT '状态: 1 待生效，2 已生效  3 已失效 4 已退款',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `privilege_num` int DEFAULT NULL COMMENT '权益数量',
  `privilege_start_time` datetime DEFAULT NULL COMMENT '权益开始时间',
  `privilege_used_num` int DEFAULT NULL COMMENT '权益使用数量',
  `remark` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='增值服务产品商家订单';

SET FOREIGN_KEY_CHECKS = 1;
