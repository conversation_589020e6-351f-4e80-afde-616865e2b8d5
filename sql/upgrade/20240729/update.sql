-- ALTER TABLE `yt_channel_info_config`
--     ADD COLUMN `app_id` int NULL DEFAULT 1 COMMENT '应用ID' AFTER `channel_id`,
--     ADD INDEX `idx_app_id`(`app_id`),
--     ADD INDEX `idx_channel_id`(`channel_id`);

ALTER TABLE `t_order`
    ADD COLUMN `app_id` int NULL DEFAULT 1 COMMENT '应用ID' AFTER `channel_order_id`,
    ADD COLUMN `member_id` bigint NULL DEFAULT NULL COMMENT '用户ID' AFTER `merchant_id`,
    ADD COLUMN `pay_channel` int NULL DEFAULT 99 COMMENT '支付渠道 1.微信 2.支付宝 99.其他' AFTER `pay_amount`,
    ADD INDEX `idx_member_id`(`member_id`),
    ADD INDEX `idx_app_id`(`app_id`);

ALTER TABLE `t_coupon`
    ADD COLUMN `app_id` int NULL DEFAULT 1 COMMENT '应用ID' AFTER `id`,
    ADD INDEX `idx_app_id`(`app_id`);

ALTER TABLE `sp_goods_spu`
    ADD COLUMN `type` int NULL DEFAULT 1 COMMENT '商品类型 1-团购，2-预售券，3-日历商品' AFTER `id`;

ALTER TABLE `sp_goods_spu_channel`
    ADD COLUMN `sku_id` bigint NULL DEFAULT NULL COMMENT '商品SkuID' AFTER `spu_id`,
    ADD COLUMN `app_id` bigint NULL DEFAULT 1 COMMENT '应用D' AFTER `channel_id`,
    ADD COLUMN `channel_sku_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '渠道商品Sku ID' AFTER `channel_spu_id`,
    ADD COLUMN `channel_ext` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '渠道商品自定义字段(Map对象的形式)，对于补差商品可传入key=saleMode,value=INDIRECT，补差商品将不会在POI页展示)' AFTER `channel_sku_id`,
    ADD INDEX `idx_channel_spu_id`(`channel_spu_id`),
    ADD INDEX `idx_channel_sku_id`(`channel_sku_id`),
    ADD INDEX `idx_spu_id`(`spu_id`),
    ADD INDEX `idx_sku_id`(`sku_id`),
    ADD INDEX `idx_app_id`(`app_id`);


ALTER TABLE `sp_main_channel_config`
    ADD COLUMN `app_id` bigint NULL DEFAULT 1 COMMENT '应用D' AFTER `channel_id`,
    ADD INDEX `idx_app_id`(`app_id`);

ALTER TABLE `sp_store_channel_config`
    ADD COLUMN `app_id` bigint NULL DEFAULT 1 COMMENT '应用D' AFTER `channel_id`,
    ADD COLUMN `sp_id` bigint NULL DEFAULT NULL COMMENT '商家ID' AFTER `app_id`,
    ADD COLUMN `category_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '渠道类目Id' AFTER `sp_id`,
    ADD COLUMN `category_name` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '渠道类目名称' AFTER `category_id`,
    ADD INDEX `idx_app_id`(`app_id`);


ALTER TABLE `system_social_user`
    MODIFY COLUMN `openid` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL AFTER `type`;

ALTER TABLE `yt_activity_info`
    ADD COLUMN `spu_count` int NULL DEFAULT 0 COMMENT '商品数量' AFTER `use_count`,
    ADD COLUMN `sp_count` int NULL DEFAULT 0 COMMENT '商家数量' AFTER `spu_count`,
    ADD COLUMN `sp_store_count` int NULL DEFAULT 0 COMMENT '商家门店数量' AFTER `sp_count`,
    ADD COLUMN `total_count` int NOT NULL COMMENT '发放数量, -1 - 则表示不限制' AFTER `sp_store_count`,
    ADD COLUMN `take_limit_count` tinyint NOT NULL DEFAULT -1 COMMENT '每人限领个数, -1 - 则表示不限制' AFTER `total_count`,
    ADD COLUMN `take_type` tinyint NOT NULL DEFAULT 1 COMMENT '领取类型\n     *\n     * 1 - 用户购买 \n     * 2 - 自动发放' AFTER `take_limit_count`,
    ADD COLUMN `use_price` int NOT NULL COMMENT '是否设置满多少金额可用，单位：分' AFTER `take_type` ,
    ADD COLUMN `discount_type` int NOT NULL DEFAULT 0 COMMENT '优惠类型\n     *\n     * 0-通兑卷\n  1-代金卷\n     * 2-折扣卷' AFTER `use_price` ,
    ADD COLUMN `discount_percent` tinyint NULL DEFAULT NULL COMMENT '折扣百分比' AFTER `discount_type` ,
    ADD COLUMN `discount_price` int NULL DEFAULT NULL COMMENT '优惠金额，单位：分' AFTER `discount_percent` ,
    ADD COLUMN `discount_limit_price` int NULL DEFAULT NULL COMMENT '折扣上限，仅在 {@link #preferentialType} 等于 2 时生效。\n     *\n     * 例如，折扣上限为 20 元，当使用 8 折优惠券，订单金额为 1000 元时，最高只可折扣 20 元，而非 80  元。' AFTER `discount_price` ;

ALTER TABLE `yt_activity_coupon`
    ADD COLUMN `take_type` tinyint NOT NULL DEFAULT 1 COMMENT '领取类型\n     *\n     * 1 - 用户购买 \n     * 2 - 自动发放' AFTER `channel_order_id`,
    ADD COLUMN `use_price` int NOT NULL COMMENT '是否设置满多少金额可用，单位：分' AFTER `take_type`,
    ADD COLUMN `discount_type` tinyint NOT NULL DEFAULT 0 COMMENT '优惠类型\n     *\n     * 0-通兑卷\n  1-代金卷\n     * 2-折扣卷' AFTER `use_price`,
    ADD COLUMN `discount_percent` tinyint NULL DEFAULT NULL COMMENT '折扣百分比' AFTER `discount_type`,
    ADD COLUMN `discount_price` int NULL DEFAULT NULL COMMENT '优惠金额，单位：分' AFTER `discount_percent`,
    ADD COLUMN `discount_limit_price` int NULL DEFAULT NULL COMMENT '折扣上限' AFTER `discount_price`;