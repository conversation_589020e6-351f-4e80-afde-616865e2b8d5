package com.yitong.octopus.module.infra.dal.mysql.file;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.module.infra.controller.admin.file.vo.file.FilePageReqVO;
import com.yitong.octopus.module.infra.dal.dataobject.file.FileDO;

/**
 * 文件操作 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FileMapper extends BaseMapperX<FileDO> {

    default PageResult<FileDO> selectPage(FilePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<FileDO>()
                .likeIfPresent(FileDO::getPath, reqVO.getPath())
                .likeIfPresent(FileDO::getType, reqVO.getType())
                .betweenIfPresent(FileDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(FileDO::getId));
    }

	default List<FileDO> selectByUrl(String url){
		return selectList(new LambdaQueryWrapperX<FileDO>()
                .eq(FileDO::getUrl, url)
                .orderByDesc(FileDO::getId));
	}

}
