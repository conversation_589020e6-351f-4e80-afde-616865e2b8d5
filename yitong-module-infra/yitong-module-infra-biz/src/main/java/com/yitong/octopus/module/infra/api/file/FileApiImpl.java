package com.yitong.octopus.module.infra.api.file;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.yitong.octopus.module.infra.dal.dataobject.file.FileDO;
import com.yitong.octopus.module.infra.dal.mysql.file.FileMapper;
import com.yitong.octopus.module.infra.service.file.FileService;

/**
 * 文件 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FileApiImpl implements FileApi {

	@Resource
	private FileService fileService;
	
	@Resource
	private FileMapper fileMapper;

	@Override
	public String createFile(String name, String path, byte[] content) {
		return fileService.createFile(name, path, content);
	}

	@Override
	public Long getFileId(String url) {
		
		List<FileDO> list = fileMapper.selectByUrl(url);
		
		return list.isEmpty()? null: list.get(0).getId();
	}
}
