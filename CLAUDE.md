# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a multi-module Spring Boot application for 一筒科技 (Yitong Technology), based on the RuoYi-Vue Pro framework. It's an enterprise-level platform with modules for CRM, e-commerce, content management, and various business domains.

## Key Commands

### Build and Run

#### Using Maven Daemon (mvnd) - Recommended for faster builds
```bash
# Build the entire project with mvnd (faster)
mvnd clean package -DskipTests

# Run the application locally with mvnd
cd yitong-server
mvnd spring-boot:run -Dspring.profiles.active=local

# Run tests with mvnd
mvnd test                                    # All tests
mvnd test -Dtest=TestClassName              # Specific test class
mvnd test -Dtest=TestClassName#methodName   # Specific test method

# Use the provided build script
./bin/build-mvnd.sh                         # Build with default settings
./bin/build-mvnd.sh prod false              # Build with prod profile and run tests
```

#### Using Traditional Maven
```bash
# Build the entire project
mvn clean package -DskipTests

# Run the application locally
cd yitong-server
mvn spring-boot:run -Dspring.profiles.active=local

# Run tests
mvn test                                    # All tests
mvn test -Dtest=TestClassName              # Specific test class
mvn test -Dtest=TestClassName#methodName   # Specific test method
```

### Deployment
```bash
# Uses Jenkins pipeline and deployment script
./bin/deploy.sh
```

## Architecture Overview

### Module Structure
The project follows a modular architecture with these key modules:
- `yitong-server/` - Main Spring Boot application that aggregates all modules
- `yitong-dependencies/` - Centralized dependency management
- `yitong-framework/` - Core framework with common utilities and configurations
- `yitong-module-*` - Business modules organized by domain:
  - `system` - System management, users, roles, permissions
  - `infra` - Infrastructure components (file storage, config, jobs)
  - `member` - Member/user management
  - `mall` - E-commerce functionality
  - `trade` - Trading/transaction system
  - `merchant` - Merchant management
  - `aigc` - AI-generated content integration

### Technology Stack
- **Backend**: Spring Boot 2.7.18, Java 8
- **Database**: MySQL 5.7/8.0+ with MyBatis Plus *******
- **Cache**: Redis 5.0/6.0
- **API Docs**: SpringDoc with Knife4j UI
- **Security**: Spring Security with JWT tokens

### Key Patterns
1. **Multi-tenancy**: Built-in SaaS support with tenant isolation
2. **Modular Design**: Each business domain is a separate Maven module
3. **Service Layer**: Business logic in service classes with @Service annotation
4. **MyBatis Plus**: Repository pattern with BaseMapper interfaces
5. **Unified Response**: Common response wrapper for API endpoints
6. **Validation**: JSR303 validation with custom validators

## Development Environment

### Database Setup
- MySQL database: `yitong_dev` (connection details in application-local.yaml)
- Redis on localhost:6379 (database 1)
- Database scripts in `/sql/mysql/` directory

### Application Configuration
- Main config: `application.yaml`
- Environment configs: `application-{env}.yaml` (local, dev, prod)
- Default port: 48080
- Health check: `http://127.0.0.1:48080/actuator/health/`

### Important URLs
- Swagger UI: `http://localhost:48080/swagger-ui`
- API Docs: `http://localhost:48080/v3/api-docs`

## Code Conventions

### Package Structure
```
cn.jianwoo.octopus.admin
├── controller    # REST API endpoints
├── service       # Business logic interfaces
│   └── impl      # Service implementations
├── dal           # Data access layer
│   ├── dataobject # Database entities (DO)
│   └── mapper     # MyBatis mappers
├── convert       # MapStruct converters
└── api           # Module API interfaces
```

### Naming Conventions
- Controllers: `*Controller`
- Services: `*Service` (interface) and `*ServiceImpl`
- Data Objects: `*DO`
- Mappers: `*Mapper`
- API responses use `CommonResult<T>` wrapper

### Common Annotations
- `@Tag` - Swagger API documentation
- `@PreAuthorize` - Security permissions
- `@Validated` - Request validation
- `@TenantIgnore` - Bypass tenant filtering

### Security & Authentication
- Get current logged-in user ID: `SecurityFrameworkUtils.getLoginUserId()`
- Get current tenant ID: `TenantContextHolder.getTenantId()`
- Check permissions: Use `@PreAuthorize` annotation with permission expressions

## Testing Approach
- Unit tests use JUnit 5 and Mockito
- Test classes follow `*Test` naming convention
- Integration tests use `@SpringBootTest`
- Mock external dependencies with `@MockBean`

## Build Properties
- Maven version management: Uses `${revision}` property (1.7.2-snapshot)
- Java version: 1.8
- Spring Boot parent: 2.7.18
- Character encoding: UTF-8

## Maven Daemon (mvnd) Configuration
The project is configured to support Maven Daemon for faster builds. Configuration files:
- `.mvn/mvnd.properties` - mvnd specific settings (heap size, threads, caching)
- `.mvn/jvm.config` - JVM settings for both mvn and mvnd
- `.mvn/extensions.xml` - Maven extensions for smart building

### Installing mvnd
- **macOS**: `brew install mvndaemon/tap/mvnd`
- **Linux/Windows**: Download from https://github.com/apache/maven-mvnd/releases

### mvnd Benefits
- Daemon process reuses JVM between builds
- Parallel module building by default
- Smart incremental compilation
- Typically 2-5x faster than traditional Maven

## Refactoring Guidelines

### Critical Principles
**⚠️ NEVER refactor in a broken compilation state. Every change must maintain compilable code.**

### Pre-Refactoring Checklist
```bash
# 1. Full compilation check (use mvnd for faster builds)
mvnd clean compile -q
# or traditional: mvn clean compile -q

# 2. Run existing tests
mvnd test -Dtest=*ControllerTest
# or traditional: mvn test -Dtest=*ControllerTest

# 3. Check module dependencies
mvnd dependency:tree -pl [module-name]
# or traditional: mvn dependency:tree -pl [module-name]
```

### Global Consistency Verification

#### 1. Cross-Layer Type Consistency
Before any Controller/Service modification, verify the complete call chain:
- **Controller Parameter Types** ↔ **Service Interface Parameters**
- **Service Interface** ↔ **Service Implementation**
- **Service Methods** ↔ **Mapper Methods**
- **VO Class Naming** consistency across layers

#### 2. Dependency Analysis Protocol
```bash
# Before deleting any class, check all usages
# Use IDE "Find Usages" or command line tools:
grep -r "ClassName" --include="*.java" yitong-module-*

# Check import statements
grep -r "import.*ClassName" --include="*.java" yitong-module-*
```

#### 3. Incremental Refactoring Strategy
1. **Small Steps**: Make one logical change at a time
2. **Immediate Verification**: Compile after each change
3. **Error Isolation**: Fix compilation errors before proceeding
4. **Layer-by-Layer**: Complete one layer before moving to the next

### Common Anti-Patterns to Avoid

#### ❌ Naming Inconsistencies
- Different parameter types between Controller and Service (e.g., `DistWithdrawRecordPageReqVO` vs `AppWithdrawRecordPageReqVO`)
- Inconsistent enum references (`DistAgentStatusEnum` vs `DistributionBusinessEnum.AgentStatus`)

#### ❌ Import Path Errors
- Wrong package references (e.g., `dal.dataobject.tag.DistAgentTagDO` should be `dal.dataobject.agent.DistAgentTagDO`)

#### ❌ Service Interface Mismatches
- Service implementation using different parameter types than interface declaration
- Using `Object` parameters instead of specific types

### Post-Refactoring Verification

#### 1. Compilation Verification
```bash
# Module-level compilation (use mvnd for faster builds)
cd [module-directory]
mvnd clean compile -q
# or traditional: mvn clean compile -q

# Full project compilation  
mvnd clean compile -DskipTests
# or traditional: mvn clean compile -DskipTests
```

#### 2. Cross-Layer Consistency Check
```bash
# Check Controller-Service consistency
grep -r "public.*Result.*(" */controller/ | grep -o "(@Valid [^)]*)" > controller_params.txt
grep -r "Result.*(" */service/ | grep -o "([^)]*ReqVO[^)]*)" > service_params.txt
diff controller_params.txt service_params.txt
```

#### 3. Import Statement Validation
```bash
# Check for missing or incorrect imports
grep -r "import.*\.\*;" --include="*.java" yitong-module-*
```

### Emergency Recovery Protocol
If refactoring breaks compilation:
1. **Stop immediately** - Do not make additional changes
2. **Identify the last working state** - Use git to find the last compilable commit
3. **Fix one error at a time** - Focus on the first compilation error only
4. **Test incrementally** - Compile after each fix

### Naming Convention Standards
- **Admin Controllers**: `Dist*Controller` with `Dist*PageReqVO`
- **App Controllers**: `AppDist*Controller` with `AppDist*PageReqVO` 
- **Service Interfaces**: `*Service` with explicit parameter types
- **Service Implementations**: `*ServiceImpl` matching interface exactly
- **Enums**: Use centralized enums in `*BusinessEnum` rather than scattered individual enums