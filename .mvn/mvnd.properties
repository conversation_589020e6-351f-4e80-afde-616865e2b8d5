# Maven Daemon Configuration
# This file configures mvnd for better build performance

# Number of daemon instances to keep alive
mvnd.daemonMaxIdleTime=1h

# Maximum heap size for daemon
mvnd.maxHeapSize=2G

# Number of threads to use for parallel builds
mvnd.threads=1C

# Enable/disable colored output
mvnd.color=auto

# Build caching
mvnd.buildCache=true

# JVM options for the daemon
mvnd.jvmArgs=

# Timeout for client connections
mvnd.clientIdleTimeout=60000

# Enable experimental features
mvnd.serial=false

# Skip plugin resolution optimization for better compatibility
mvnd.pluginRealmCache=false

# Disable daemon for specific modules if needed
# mvnd.noDaemon.1=yitong-module-system-biz