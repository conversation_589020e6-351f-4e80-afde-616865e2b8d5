package com.yitong.octopus.module.sp.service.goodsspu;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.platform.enums.ErrorCodeConstants.PROPERTIES_SELECT_DELETED;
import static com.yitong.octopus.module.platform.enums.ErrorCodeConstants.TAG_SELECT_DELETED;
import static com.yitong.octopus.module.sp.enums.CouponUseDateTypeEnum.DAY_DURATION;
import static com.yitong.octopus.module.sp.enums.CouponUseDateTypeEnum.DAY_RANGE;
import static com.yitong.octopus.module.sp.enums.ErrorCodeConstants.*;
import static com.yitong.octopus.module.trade.enums.ErrorCodeConstants.TRADE_COUPON_NOT_SUPPORT_ERROR;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Sets;
import com.yitong.octopus.framework.common.util.json.JsonUtils;
import com.yitong.octopus.framework.common.util.string.StringUtils;
import com.yitong.octopus.framework.excel.core.util.ExcelImportRespVO;
import com.yitong.octopus.module.channel.api.channel.ChannelInfoApi;
import com.yitong.octopus.module.channel.api.channel.vo.ChannelInfoVo;
import com.yitong.octopus.module.channel.enums.ChannelCodeEnum;
import com.yitong.octopus.module.sp.api.spu.dto.SpGoodsSkuSampleVo;
import com.yitong.octopus.module.sp.api.spu.dto.SpGoodsSpuSimpleVO;
import com.yitong.octopus.module.sp.api.spu.dto.SpGoodsSpuStoreLbsRespVO;
import com.yitong.octopus.module.sp.api.spu.dto.SpGoodsSpuStoreNumRespVO;
import com.yitong.octopus.module.sp.api.store.vo.SpGoodsSpuStoreVO;
import com.yitong.octopus.module.sp.service.goodsspu.channel.SpGoodsSpuChannel;
import com.yitong.octopus.module.sp.service.goodsspu.channel.vo.SpuChannelSyncReq;
import com.yitong.octopus.module.sp.controller.admin.goodsspu.vo.*;
import com.yitong.octopus.module.sp.controller.admin.maininfo.vo.SpMainInfoWithOwnerRespVO;
import com.yitong.octopus.module.sp.dal.dataobject.goodsspu.*;
import com.yitong.octopus.module.sp.dal.dataobject.storeinfo.SpStoreInfoDO;
import com.yitong.octopus.module.sp.dal.dto.spgoods.AppXhsMiniAppGoodsSpuBookRespVo;
import com.yitong.octopus.module.sp.dal.dto.spgoods.AppXhsMiniAppGoodsSpuRespVo;
import com.yitong.octopus.module.sp.dal.dto.spgoods.AppXhsMiniAppStoreBySpPageReqVo;
import com.yitong.octopus.module.sp.service.maininfo.SpMainInfoService;
import com.yitong.octopus.module.trade.api.coupon.TradeCouponGeneratorApi;
import com.yitong.octopus.module.trade.api.coupon.dto.TradeCouponGeneratorDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Maps;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.util.MyBatisUtils;
import com.yitong.octopus.module.platform.api.property.PlatformPropertyApi;
import com.yitong.octopus.module.platform.api.property.vo.PlatformPropertyValueVo;
import com.yitong.octopus.module.platform.api.property.vo.PlatformPropertyVo;
import com.yitong.octopus.module.platform.api.tag.PlatformTagApi;
import com.yitong.octopus.module.platform.api.tag.vo.PlatformTagGroupVo;
import com.yitong.octopus.module.platform.api.tag.vo.PlatformTagVo;
import com.yitong.octopus.module.sp.controller.admin.goodssku.vo.SpGoodsSkuRespVO;
import com.yitong.octopus.module.sp.controller.admin.goodsspu.dto.SpGoodsSpuChannelConfigPageVO;
import com.yitong.octopus.module.sp.controller.admin.goodsspu.vo.SpGoodsSpuBaseVO.DateDuration;
import com.yitong.octopus.module.sp.controller.admin.goodsspu.vo.SpGoodsSpuBaseVO.UseDateDuration;
import com.yitong.octopus.module.sp.controller.admin.goodsspu.vo.SpGoodsSpuCreateReqVO.SpGoodsSpuCreateVO;
import com.yitong.octopus.module.sp.controller.admin.goodsspu.vo.SpGoodsSpuUpdateReqVO.SpGoodsSpuUpdateVO;
import com.yitong.octopus.module.sp.controller.admin.storeinfo.vo.SpStoreInfoReqVO;
import com.yitong.octopus.module.sp.dal.dataobject.goodsspupropertiesrel.SpGoodsSpuPropertiesRelDO;
import com.yitong.octopus.module.sp.dal.dataobject.goodssputagrel.GoodsSpuTagRelDO;
import com.yitong.octopus.module.sp.dal.mysql.goodsspu.SpGoodsSpuMapper;
import com.yitong.octopus.module.sp.dal.mysql.goodsspu.SpGoodsSpuChannelMapper;
import com.yitong.octopus.module.sp.dal.mysql.goodsspu.SpSpuStoreBookingQuantityMapper;
import com.yitong.octopus.module.sp.dal.mysql.goodsspu.SpSpuStoreMapper;
import com.yitong.octopus.module.sp.dal.mysql.storeinfo.SpStoreInfoMapper;
import com.yitong.octopus.module.sp.enums.SpGoodsSpuStatusEnum;
import com.yitong.octopus.module.sp.enums.SpGoodsSpuTableEnum;
import com.yitong.octopus.module.sp.mq.commons.SpGoodsCommandType;
import com.yitong.octopus.module.sp.mq.message.goodsspu.SpGoodsSpuPushChannelMessage;
import com.yitong.octopus.module.sp.mq.producer.goodsspu.SpGoodsSpuProducer;
import com.yitong.octopus.module.sp.service.goodssku.SpGoodsSkuService;
import com.yitong.octopus.module.sp.service.goodsspubilllitem.SpGoodsSpuBillItemService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * 商品spu Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class SpGoodsSpuServiceImpl extends ServiceImpl<SpGoodsSpuMapper,SpGoodsSpuDO> implements SpGoodsSpuService {

	@Lazy
	@Resource
	private SpGoodsSpuMapper goodsSpuMapper;

	@Lazy
	@Resource
	private SpSpuStoreMapper spuStoreMapper;

	@Lazy
	@Resource
	private SpGoodsSpuChannelMapper spuChannelMapper;

	@Lazy
	@Resource
	private SpGoodsSkuService spGoodsSkuService;

	@Resource
	private SpGoodsSpuBillItemService spGoodsSpuBillItemService;

	@Lazy
	@Resource
	private SpGoodsSpuProducer spGoodsSpuProducer;

	@Resource
	private GoodsSpuTagRelService goodsSpuTagRelService;

	@Lazy
	@Resource
	private GoodsSpuPropertiesRelService goodsSpuPropertiesRelService;

	@Lazy
	@Resource
	private PlatformTagApi tagApi;

	@Lazy
	@Resource
	private PlatformPropertyApi platformPropertyApi;

	@Lazy
	@Resource
	private SpSpuStoreBookingQuantityMapper spSpuStoreBookingQuantityMapper;

	@Lazy
	@Resource
	private SpStoreInfoMapper spStoreInfoMapper;

	@Lazy
	@Resource
	private List<SpGoodsSpuChannel> spGoodsSpuChannelList;

	@Lazy
	@Resource
	private SpGoodsSpuChannelService spGoodsSpuChannelService;

	@Lazy
	@Resource
	private TradeCouponGeneratorApi tradeCouponGeneratorApi;

	@Lazy
	@Resource
	private SpMainInfoService spMainInfoService;

	@Lazy
	@Resource
	private PlatformTransactionManager platformTransactionManager;

	@Lazy
	@Resource
	private TransactionDefinition transactionDefinition;

	@Lazy
	@Resource
	private ChannelInfoApi channelInfoApi;

	@Override
	@Transactional
	public Long createGoodsSpu(SpGoodsSpuCreateReqVO createReqVO) {
		// 插入
		SpGoodsSpuCreateVO spuVo = createReqVO.getSpu();
		SpGoodsSpuDO spu = BeanUtil.toBean(spuVo,SpGoodsSpuDO.class);
		spu.setRating(spuVo.getRank());
		spu.setMainImg(spuVo.getMainImage());
		spu.setRotationImg(spuVo.getRotationImages());
		spu.setGoodLabels(spuVo.getLabels());
		spu.setOnShelve(spuVo.getOnlineStatus());
		spu.setSettleType(spuVo.getSettleType());
		if (spuVo.getSoldDate() != null) {
			DateDuration duration = spuVo.getSoldDate();
			spu.setSoldDateFrom(duration.getStartDate());
			spu.setSoldDateTo(duration.getEndDate());
		}
		if (ObjectUtil.isNotNull(spuVo.getUseRule()) && ObjectUtil.isNotNull(spuVo.getUseRule().getValidDate())) {
			UseDateDuration validDuration = spuVo.getUseRule().getValidDate();
			if (DAY_DURATION.getType().equals(validDuration.getUseDateType())){
				spu.setUseDateType(validDuration.getUseDateType());
				spu.setDayDuration(validDuration.getDayDuration());
			}else {
				spu.setValidDateFrom(validDuration.getStartDate());
				spu.setValidDateTo(validDuration.getEndDate());
			}
		}
		if (spuVo.getUseRule() != null) {
			UseRuleVo useRule = spuVo.getUseRule();
			spu.setBooking(Objects.equals(useRule.getAppointment(), 1)); //将是否需要预约从JSON中独立出来
			if(ObjectUtil.isNotNull(useRule.getRefundPolicy())){
				spu.setAfterSaleRule(useRule.getRefundPolicy().toString());
			}

		}
		spu.setAllStoreAvailable(ObjectUtil.isEmpty(createReqVO.getStoreIds()));  //不指定门店默认为商户下所有门店有效
		goodsSpuMapper.insert(spu);

		spuVo.getSkus().forEach(sku -> {
			sku.setSpuId(spu.getId());
			sku.setSpId(spu.getSpId());
			spGoodsSkuService.createGoodsSku(sku);
		});

		if (ObjectUtil.isNotEmpty(createReqVO.getStoreIds())){
			HashSet<SpStoreInfoReqVO> set = new HashSet(createReqVO.getStoreIds());
			set.forEach(store -> {
				SpGoodsSpuStoreDO spuStore = new SpGoodsSpuStoreDO();
				spuStore.setStoreId(store.getId());
				spuStore.setSpuId(spu.getId());
				spuStoreMapper.insert(spuStore);
			});
		}

		if (ObjectUtil.isNotEmpty(createReqVO.getChannels())){
			createReqVO.getChannels().forEach(channelConfig -> {
				SpGoodsSpuChannelDO spuChannel = new SpGoodsSpuChannelDO();
				spuChannel.setSpuId(spu.getId());
				spuChannel.setChannelId(channelConfig.getChannelId());
				spuChannel.setChannelCategoryType(channelConfig.getChannelCategoryType());
				spuChannel.setChannelCategoryId(channelConfig.getChannelCategoryId());
				spuChannel.setStatus(0); //待审核
				spuChannel.setOnShelve(0); //未上架
				spuChannelMapper.insert(spuChannel);
			});
		}
		//商品分佣
		if (CollectionUtil.isNotEmpty(createReqVO.getSpu().getBillItems())){
			spGoodsSpuBillItemService.createOrUpdateGoodsSpuBillItem(spu.getId(),spu.getSpId(),createReqVO.getSpu().getBillItems());
		}
		return spu.getId();
	}

	@Override
	@Transactional
	public void updateGoodsSpu(SpGoodsSpuUpdateReqVO updateReqVO) {
		// 校验存在
		SpGoodsSpuDO spu = validateGoodsSpuExists(updateReqVO.getId());
		// 更新
		SpGoodsSpuUpdateVO spuVo = updateReqVO.getSpu();
		BeanUtil.copyProperties(spuVo,spu);
		spu.setRating(spuVo.getRank());
		spu.setMainImg(spuVo.getMainImage());
		spu.setRotationImg(spuVo.getRotationImages());
		spu.setGoodLabels(spuVo.getLabels());
		spu.setOnShelve(spuVo.getOnlineStatus());
		if (spuVo.getSoldDate() != null) {
			DateDuration duration = spuVo.getSoldDate();
			spu.setSoldDateFrom(duration.getStartDate());
			spu.setSoldDateTo(duration.getEndDate());
		}
		if (ObjectUtil.isNotNull(spuVo.getUseRule())){
			UseRuleVo useRule = spuVo.getUseRule();
			if(ObjectUtil.isNotNull(useRule.getValidDate())) {
				UseDateDuration validDuration = useRule.getValidDate();
				spu.setUseDateType(validDuration.getUseDateType());
				//券有效期类型 - 购买之后N天
				if (DAY_DURATION.getType().equals(validDuration.getUseDateType())){
					spu.setDayDuration(validDuration.getDayDuration());
					spu.setValidDateFrom(null);
					spu.setValidDateTo(null);
				}else {
					spu.setDayDuration(null);
					spu.setValidDateFrom(validDuration.getStartDate());
					spu.setValidDateTo(validDuration.getEndDate());
				}
			}
			spu.setBooking(Objects.equals(useRule.getAppointment(), 1)); //将是否需要预约从JSON中独立出来
		}
		if(updateReqVO.getStoreIds() != null) {
			spu.setAllStoreAvailable(ObjectUtil.isEmpty(updateReqVO.getStoreIds())); //清空门店列表默认为商户下所有门店有效
		}
		goodsSpuMapper.updateById(spu);

		spuVo.getSkus().forEach(e -> {
			//用商品的SPid，替换本地的spId
			e.setSpId(spu.getSpId());
			if (e.getId() == null) {
				spGoodsSkuService.createGoodsSku(e);
			} else {
				spGoodsSkuService.updateGoodsSku(e.getId(), e);
			}
		});

		if (updateReqVO.getStoreIds() != null) {
			spuStoreMapper.deleteBySpuId(spu.getId());
			HashSet<SpStoreInfoReqVO> updateSet = new HashSet(updateReqVO.getStoreIds());
			updateSet.forEach(store -> {
				SpGoodsSpuStoreDO spuStore = new SpGoodsSpuStoreDO();
				spuStore.setStoreId(store.getId());
				spuStore.setSpuId(updateReqVO.getId());
				spuStoreMapper.insert(spuStore);
			});
		}

		

		if(CollectionUtil.isNotEmpty(updateReqVO.getChannels())){
			Map<Long, SpGoodsSpuChannelDO> originChannels = spuChannelMapper.selectBySpuId(updateReqVO.getId()).stream()
					.collect(Collectors.toMap(e -> e.getChannelId(), e -> e));
			updateReqVO.getChannels().forEach(channelConfig -> {
				SpGoodsSpuChannelDO origin = originChannels.remove(channelConfig.getChannelId());

				if (origin == null) {
					SpGoodsSpuChannelDO spuChannel = new SpGoodsSpuChannelDO();
					spuChannel.setChannelId(channelConfig.getChannelId());
					spuChannel.setChannelCategoryType(channelConfig.getChannelCategoryType());
					spuChannel.setChannelCategoryId(channelConfig.getChannelCategoryId());
					spuChannel.setStatus(0);
					spuChannelMapper.insert(spuChannel);
				} else {
					origin.setChannelCategoryType(channelConfig.getChannelCategoryType());
					origin.setChannelCategoryId(channelConfig.getChannelCategoryId());
					spuChannelMapper.updateById(origin);
				}
			});
//			if (!originChannels.values().isEmpty()) {
//				spuChannelMapper
//						.deleteBatchIds(originChannels.values().stream().map(e -> e.getId()).collect(Collectors.toList()));
//			}
		}

		

		//商品分佣
		if (CollectionUtil.isNotEmpty(updateReqVO.getSpu().getBillItems())){
			//过滤数据
			spGoodsSpuBillItemService.createOrUpdateGoodsSpuBillItem(spu.getId(),spuVo.getSpId(),updateReqVO.getSpu().getBillItems());
		}
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void updateGoodsSpuCooperates(SpGoodsSpuCooperateUpdateReqVO updateReqVO) {
		updateReqVO.getIds().forEach(id ->{
			SpGoodsSpuDO spuDO=getGoodsSpu(id);
			//过滤数据
			spuDO.setSettleType(updateReqVO.getSettleType());
			goodsSpuMapper.updateById(spuDO);
			spGoodsSpuBillItemService.createOrUpdateGoodsSpuBillItem(spuDO.getId(),spuDO.getSpId(),updateReqVO.getBillItems());
		});
	}

	@Override
	public void deleteGoodsSpu(Long id) {
		// 校验存在
		validateGoodsSpuExists(id);
		// 删除
		goodsSpuMapper.deleteById(id);
	}

	private SpGoodsSpuDO validateGoodsSpuExists(Long id) {
		SpGoodsSpuDO entity = goodsSpuMapper.selectById(id);
		if (entity == null) {
			throw exception(GOODS_SPU_NOT_EXISTS);
		}
		return entity;
	}

	@Override
	public SpGoodsSpuDO getGoodsSpu(Long id) {
		return goodsSpuMapper.selectById(id);
	}

	@Override
	public List<SpGoodsSpuDO> getGoodsSpuList(Collection<Long> ids) {
		return goodsSpuMapper.selectBatchIds(ids);
	}

	@Override
	public PageResult<SpGoodsSpuDO> getGoodsSpuPage(SpGoodsSpuPageReqVO pageReqVO) {
		IPage<SpGoodsSpuDO> page = goodsSpuMapper.selectListPage(MyBatisUtils.buildPage(pageReqVO),pageReqVO);
		return new PageResult<>(page.getRecords(), page.getTotal());
	}

	@Override
	public PageResult<SpGoodsSpuDO> getGoodsSpuPageByAppId(SpGoodsSpuChannelPageReqVO pageReqVO) {
		IPage<SpGoodsSpuDO> page = goodsSpuMapper.selectListPageByAppId(MyBatisUtils.buildPage(pageReqVO), pageReqVO);
		return new PageResult<>(page.getRecords(), page.getTotal());
	}

	@Override
	public Map<Integer, Long> getTabsCount(SpGoodsSpuPageReqVO pageVO) {
		Map<Integer, Long> counts = Maps.newLinkedHashMapWithExpectedSize(SpGoodsSpuTableEnum.ARRAYS.length);
		pageVO.setTabType(SpGoodsSpuTableEnum.ALL.getType());
		counts.put(SpGoodsSpuTableEnum.ALL.getType(),goodsSpuMapper.selectListCount(pageVO));

		pageVO.setTabType(SpGoodsSpuTableEnum.DRAFT.getType());
		counts.put(SpGoodsSpuTableEnum.DRAFT.getType(),goodsSpuMapper.selectListCount(pageVO));

		pageVO.setTabType(SpGoodsSpuTableEnum.PENDING_APPROVAL.getType());
		counts.put(SpGoodsSpuTableEnum.PENDING_APPROVAL.getType(),goodsSpuMapper.selectListCount(pageVO));

		pageVO.setTabType(SpGoodsSpuTableEnum.FOR_SALE.getType());
		counts.put(SpGoodsSpuTableEnum.FOR_SALE.getType(),goodsSpuMapper.selectListCount(pageVO));

		pageVO.setTabType(SpGoodsSpuTableEnum.FORBIDDEN_SALE.getType());
		counts.put(SpGoodsSpuTableEnum.FORBIDDEN_SALE.getType(),goodsSpuMapper.selectListCount(pageVO));

		//售罄和临期单独处理
		pageVO.setTabType(SpGoodsSpuTableEnum.SOLD_OUT.getType());
		counts.put(SpGoodsSpuTableEnum.SOLD_OUT.getType(),goodsSpuMapper.selectListCount(pageVO));

		pageVO.setTabType(SpGoodsSpuTableEnum.SOLD_EXPIRE.getType());
		counts.put(SpGoodsSpuTableEnum.SOLD_EXPIRE.getType(),goodsSpuMapper.selectListCount(pageVO));
		return counts;
	}

	@Override
	public List<SpGoodsSpuDO> getGoodsSpuList(SpGoodsSpuPageReqVO pageReqVO) {
		IPage<SpGoodsSpuDO> page = goodsSpuMapper.selectListPage(MyBatisUtils.buildAllPage(pageReqVO),pageReqVO);
		return page.getRecords();
	}

	@Override
	public PageResult<SpGoodsSpuChannelVo> getChannelConfigPageBySpId(SpGoodsSpuChannelConfigPageVO pageReqVO) {
		IPage<SpGoodsSpuChannelVo> page = goodsSpuMapper.getChannelConfigPageBySpId(MyBatisUtils.buildPage(pageReqVO),pageReqVO.getSpGoodsId());
		return new PageResult<>(page.getRecords(), page.getTotal());
	}

	@Override
	public List<SpGoodsSpuStoreVO> getGoodsSpuStoreListBySpuId(Long id) {
		return spuStoreMapper.getGoodsSpuStoreListBySpuId(id);
	}

	@Override
	public List<SpGoodsSpuStoreVO> getGoodsSpuValidStoreListBySpuId(Long id) {
		return spuStoreMapper.getGoodsSpuValidStoreListBySpuId(id);
	}

	@Override
	public String getSpuChannelCode(Long spuId, String channelCode) {
		return spuStoreMapper.getSpuChannelCode(spuId,channelCode);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void updateSpuStatus(SpGoodsSpuUpdateStatusReqVO updateReqVO) {
		updateReqVO.getIds().forEach(spuId ->{
			SpGoodsSpuDO productSpuDO = validateGoodsSpuExists(spuId);
			// 更新状态 草稿 -> 待审核
			productSpuDO.setStatus(updateReqVO.getStatus());
			// 更新状态 下架 -> 上架
			if (SpGoodsSpuStatusEnum.FOR_SALE.getStatus().equals(updateReqVO.getStatus())){
				spGoodsSpuProducer.sendSpMainInfoRefreshMessage(new SpGoodsSpuPushChannelMessage(spuId, SpGoodsCommandType.FOR_SALE_CMD.getType()));
			}
			// 更新状态 上架 -> 下架
			if (SpGoodsSpuStatusEnum.FORBIDDEN_SALE.getStatus().equals(updateReqVO.getStatus())){
				spGoodsSpuProducer.sendSpMainInfoRefreshMessage(new SpGoodsSpuPushChannelMessage(spuId, SpGoodsCommandType.FORBIDDEN_SALE_CMD.getType()));
			}
			goodsSpuMapper.updateById(productSpuDO);
		});
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void updateSpuStock(SpGoodsSpuUpdateStockReqVO updateReqVO) {
		updateReqVO.getIds().forEach(spuId ->{
			List<SpGoodsSkuRespVO>  skuList = spGoodsSkuService.getGoodsSkuListBySpuId(spuId);
			if (CollectionUtil.isEmpty(skuList)){
				return;
			}
			// 更新库存
			 Boolean result = spGoodsSkuService.updateSkuStockBySkuId(CollectionUtil.getFirst(skuList).getId(),updateReqVO.getStock());
			if (result){
				log.info("update stock success send channel, spu :{}",spuId);
				spGoodsSpuProducer.sendSpMainInfoRefreshMessage(new SpGoodsSpuPushChannelMessage(spuId,SpGoodsCommandType.STORE_CHANGE.getType()));
			}else {
				log.info("update stock error, spu :{}",spuId);
			}
		});
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void auditSpu(SpGoodsSpuAuditReqVO updateReqVO) {
		updateReqVO.getIds().forEach(spuId ->{
			SpGoodsSpuDO productSpuDO = validateGoodsSpuExists(spuId);
			// 更新状态 待审核 -> 上架
			// 更新状态 待审核 -> 草稿
			if (!SpGoodsSpuStatusEnum.PENDING_APPROVAL.getStatus().equals(productSpuDO.getStatus())){
				throw exception(GOODS_SPU_STATUS_NOT_VALIDATE);
			}
			if (SpGoodsSpuStatusEnum.FOR_SALE.getStatus().equals(updateReqVO.getStatus()) || SpGoodsSpuStatusEnum.DRAFT.getStatus().equals(updateReqVO.getStatus())){
				productSpuDO.setStatus(updateReqVO.getStatus());
				goodsSpuMapper.updateById(productSpuDO);
			}
		});
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void makeLabel(SpGoodsSpuTagRelSaveReqVO tagRelSaveReqVO) {
		SpGoodsSpuDO spuDO = getGoodsSpu(tagRelSaveReqVO.getId());
		if (ObjectUtil.isNull(spuDO)){
			throw exception(GOODS_SPU_NOT_EXISTS);
		}
		//移除相关标签
		List<PlatformTagVo> removeTag = tagRelSaveReqVO.getRemoveTag();
		if (CollectionUtil.isNotEmpty(removeTag)){
			goodsSpuTagRelService.remove(new LambdaQueryWrapperX<GoodsSpuTagRelDO>()
					.eq(GoodsSpuTagRelDO::getSpuId,tagRelSaveReqVO.getId())
					.in(GoodsSpuTagRelDO::getTagId,removeTag.stream().map(PlatformTagVo::getId).collect(Collectors.toList()))
			);
		}

		//新增的标签
		List<PlatformTagVo> addTag = tagRelSaveReqVO.getAddTag();
		if (CollectionUtil.isNotEmpty(addTag)){
			List<GoodsSpuTagRelDO> tags = addTag.stream().map(tag ->{
				//检查标签的有效性
				PlatformTagVo platformTagVo = tagApi.getTagById(tag.getId());
				if (ObjectUtil.isNull(platformTagVo)){
					throw exception(TAG_SELECT_DELETED);
				}
				GoodsSpuTagRelDO tagRel = new GoodsSpuTagRelDO();
				tagRel.setTagId(tag.getId());
				tagRel.setSpId(spuDO.getSpId());
				tagRel.setSpuId(spuDO.getId());
				return tagRel;
			}).collect(Collectors.toList());
			goodsSpuTagRelService.saveOrUpdateBatch(tags);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void batchMakeLabel(SpGoodsSpuBatchTagRelSaveReqVO tagRelSaveReqVO) {
		tagRelSaveReqVO.getTags().forEach(tag -> makeLabel(tag));
	}

	@Override
	public List<PlatformTagGroupVo> getTagList(SpGoodsSpuTagReqVO reqVO) {
		List<GoodsSpuTagRelDO> goodsSpuTagList = goodsSpuTagRelService.getSpuTagRelBySpuId(reqVO.getId());
		if (CollectionUtil.isEmpty(goodsSpuTagList)){
			return ListUtil.empty();
		}
		return tagApi.getTagGroupByIds(goodsSpuTagList.stream().map(GoodsSpuTagRelDO::getTagId).collect(Collectors.toList()),reqVO.getGroupId());
	}

	@Override
	public void makeProperty(SpGoodsSpuPropertySaveReqVO reqVO) {
		SpGoodsSpuDO goodsSpu = getGoodsSpu(reqVO.getId());
		if (ObjectUtil.isNull(goodsSpu)){
			throw exception(GOODS_SPU_NOT_EXISTS);
		}
		//移除相关标签
		List<PlatformPropertyValueVo> removeProperties = reqVO.getRemove();
		if (CollectionUtil.isNotEmpty(removeProperties)){
			Set<Long> removeIds =  removeProperties.stream().map(PlatformPropertyValueVo::getId).collect(Collectors.toSet());
			goodsSpuPropertiesRelService.deleteGoodsSpuPropertiesRel(reqVO.getId(),removeIds);
		}

		//新增的标签
		List<PlatformPropertyValueVo> addTag = reqVO.getAdd();
		if (CollectionUtil.isNotEmpty(addTag)){
			List<SpGoodsSpuPropertiesRelDO> valueDtoList = addTag.stream().map(v ->{
				//检查属性的有效性
				PlatformPropertyValueVo propertyValueVo = platformPropertyApi.getPlatformPropertyById(v.getId());
				if (ObjectUtil.isNull(propertyValueVo)){
					throw exception(PROPERTIES_SELECT_DELETED);
				}
				SpGoodsSpuPropertiesRelDO valueDto = new SpGoodsSpuPropertiesRelDO();
				valueDto.setPropertyId(v.getPropertyId());
				valueDto.setValueId(v.getId());
				valueDto.setValueName(v.getName());
				valueDto.setSpId(goodsSpu.getSpId());
				valueDto.setSpuId(goodsSpu.getId());
				return valueDto;
			}).collect(Collectors.toList());
			goodsSpuPropertiesRelService.saveBatch(valueDtoList);
		}
	}

	@Override
	public void batchProperties(SpGoodsSpuBatchPropertySaveReqVO reqVO) {
		if (CollectionUtil.isNotEmpty(reqVO.getProperties())){
			reqVO.getProperties().stream().forEach(p ->makeProperty(p));
		}
	}

	@Override
	public List<PlatformPropertyVo> getPropertyList(SpGoodsSpuPropertyReqVO reqVO) {
		List<SpGoodsSpuPropertiesRelDO>  goodsSpuRelList = goodsSpuPropertiesRelService.getGoodsSpuPropertiesRelBySpuId(reqVO.getId());
		if (CollectionUtil.isEmpty(goodsSpuRelList)){
			return ListUtil.empty();
		}
		return platformPropertyApi.getPropertyValueListByValueIdPropertyId(goodsSpuRelList.stream().map(SpGoodsSpuPropertiesRelDO::getValueId).collect(Collectors.toList()),reqVO.getPropertyId());
	}

	@Override
	public List<SpGoodsSpuSampleRespVO> getGoodsSpuSampleListByIds(Collection<Long> ids) {
		List<SpGoodsSpuDO> spuList = goodsSpuMapper.selectList(new LambdaQueryWrapperX<SpGoodsSpuDO>().in(SpGoodsSpuDO::getId,ids));
		return BeanUtil.copyToList(spuList,SpGoodsSpuSampleRespVO.class);
	}

	@Override
	@Transactional
	public void setStoreBookingQuantity(SpGoodsSpuStoreBookingQuantityVO vo) {
		Long spuId = vo.getId();
		SpGoodsSpuDO spu = goodsSpuMapper.selectById(spuId);
		Map<Long, SpGoodsSpuStoreBookingQuantityDO> spuStoreBookingQuantityList = spSpuStoreBookingQuantityMapper
				.selectBySpuId(spuId).stream()
				.collect(Collectors.toMap(SpGoodsSpuStoreBookingQuantityDO::getStoreId, e -> e));
		vo.getList().forEach(ss -> {
			SpGoodsSpuStoreBookingQuantityDO spuStoreBookingQuantity = spuStoreBookingQuantityList.get(ss.getStoreId());
			if (spuStoreBookingQuantity != null) {
				spuStoreBookingQuantity.setBookingQuantity(ss.getBookingQuantity());
				spSpuStoreBookingQuantityMapper.updateById(spuStoreBookingQuantity);
			} else {
				spuStoreBookingQuantity = new SpGoodsSpuStoreBookingQuantityDO();
				spuStoreBookingQuantity.setStoreId(ss.getStoreId());
				spuStoreBookingQuantity.setSpuId(spuId);
				spuStoreBookingQuantity.setBookingQuantity(ss.getBookingQuantity());
				spuStoreBookingQuantity.setBookingedQuantity(0);
				spSpuStoreBookingQuantityMapper.insert(spuStoreBookingQuantity);
			}
		});
		// TODO 商品库存与门店预约配额不强相关，仅在核销的时候校验门店是否有预约配置
//		// 没有无限预约配额的门店需要保证总预约配额数大于等于库存
//		if (!this.hasInfiniteBookingQuantity(spu)) {
//			long totalBookingQuantity = spSpuStoreBookingQuantityMapper.sumTotalBookingQuantity(spuId);
//			SpGoodsSkuDO sku = spGoodsSkuMapper.selectBySpuId(spuId).get(0);
//			if (sku.getBillStock() != null && sku.getBillStock() > 0 && totalBookingQuantity < sku.getBillStock()) { // 不允许预约配额总数小于库存数
//				log.error("Total booking quantity less than stock! spuId={}, totalBookingQuantity={}, stock={}", spu.getId(), totalBookingQuantity, sku.getBillStock());
//				throw exception(BOOKING_QUANTITY_LT_STOCK);
//			}
//		}
	}

	private boolean hasInfiniteBookingQuantity(SpGoodsSpuDO spu) {
		return spStoreInfoMapper.countInfiniteBookingQuantityStore(spu.getSpId(), spu.getId(),
				spu.isAllStoreAvailable()) > 0;
	}

	@Override
	public PageResult<SpGoodsSpuStoreRespVO> getGoodsSpuStorePage(@Valid SpGoodsSpuStorePageReqVO vo) {
		SpGoodsSpuDO spu = goodsSpuMapper.selectById(vo.getSpuId());
		if (vo.getSpId() == null) {
			vo.setSpId(spu.getSpId());
		}
		return spStoreInfoMapper.selectStoreBookingQuantityPage(vo, spu.isAllStoreAvailable());
	}

	@Override
	public SpGoodsSpuImportRespVO importGoodsStock(List<SpGoodsSpuStockImportExcelVO> importStocks, boolean syncChannel) {
		if (CollUtil.isEmpty(importStocks)) {
			throw exception(GOODS_SKU_SYNC_STOCK_IMPORT_LIST_IS_EMPTY);
		}
		SpGoodsSpuImportRespVO respVO = SpGoodsSpuImportRespVO.builder().createList(new ArrayList<>())
				.updateList(new ArrayList<>()).failureList(new LinkedHashMap<>()).build();
		importStocks.forEach(spuStock -> {
			// 校验，判断是否有不符合的原因
			TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
			try {
				log.info("importGoodsStock:{}", JsonUtils.toJsonString(spuStock));
				List<SpGoodsSkuRespVO> skuRespVOList =	spGoodsSkuService.getGoodsSkuListBySpuId(Long.valueOf(spuStock.getSpuId()));
				if (CollectionUtil.isEmpty(skuRespVOList)){
					respVO.getFailureList().put(spuStock.getSpuId().toString(), "未找到Sku");
					return;
				}else {
					SpGoodsSkuRespVO skuRespVO = CollectionUtil.getFirst(skuRespVOList);
					//保存数据入库
					spGoodsSkuService.setSkuStockBySkuId(skuRespVO.getId(),spuStock.getStoreNum());
				}
				platformTransactionManager.commit(transaction);
				// 如果存在，判断是否允许更新
				if (syncChannel) {
					ChannelInfoVo channelInfoVo = channelInfoApi.getChannelInfoByChannelCode(spuStock.getChannelCode());
					if (ObjectUtil.isNull(channelInfoVo)){
						respVO.getFailureList().put(spuStock.getSpuId().toString(),"渠道不存在");
						return;
					}
					List<SpGoodsSpuChannelDO>  spuChannelList = spGoodsSpuChannelService.getSpGoodsSpuChannelByChannelIdAndSpuId(channelInfoVo.getId(),spuStock.getSpuId());
					spuChannelList.forEach(spuChannel -> {
						SpuChannelSyncReq req = new SpuChannelSyncReq()
//								.setSpuChannelId(spuChannel.getId())
								.setSpuId(spuStock.getSpuId())
								.setChannelCode(spuStock.getChannelCode())
								.setAppId(spuChannel.getAppId())
								;
						spGoodsSpuChannelList.forEach( channel ->  channel.syncStock(req));
					});
				}
			} catch (Exception e) {
				respVO.getFailureList().put(spuStock.getSpuId().toString(), e.getMessage());
				platformTransactionManager.rollback(transaction);
				return;
			}
			respVO.getUpdateList().add(spuStock.getSpuId().toString());
		});
		return respVO;
	}

	@Override
	public SpGoodsSpuImportRespVO importGoodsSync(List<SpGoodsSpuSyncImportExcelVO> importStocks) {
		if (CollUtil.isEmpty(importStocks)) {
			throw exception(GOODS_SKU_SYNC_SYNC_IMPORT_LIST_IS_EMPTY);
		}
		SpGoodsSpuImportRespVO respVO = SpGoodsSpuImportRespVO.builder().createList(new ArrayList<>())
				.updateList(new ArrayList<>()).failureList(new LinkedHashMap<>()).build();
		importStocks.forEach(spu -> {
			// 校验，判断是否有不符合的原因
			try {
				log.info("importGoodsSync:{}", JsonUtils.toJsonString(spu));
				ChannelInfoVo channelInfoVo = channelInfoApi.getChannelInfoByChannelCode(spu.getChannelCode());
				if (ObjectUtil.isNull(channelInfoVo)){
					respVO.getFailureList().put(spu.getSpuId().toString(),"渠道不存在");
					return;
				}
				List<SpGoodsSpuChannelDO>  spuChannelList = spGoodsSpuChannelService.getSpGoodsSpuChannelByChannelIdAndSpuId(channelInfoVo.getId(),spu.getSpuId());
				spuChannelList.forEach(spuChannel -> {
					SpuChannelSyncReq req = new SpuChannelSyncReq()
//							.setSpuChannelId(spuChannel.getId())
							.setSpuId(spu.getSpuId())
							.setChannelCode(spu.getChannelCode())
							.setAppId(spuChannel.getAppId())
							;
					spGoodsSpuChannelList.forEach( channel ->  channel.syncStock(req));
				});
			} catch (Exception e) {
				respVO.getFailureList().put(spu.getSpuId().toString(), e.getMessage());
				return;
			}
			respVO.getUpdateList().add(spu.getSpuId().toString());
		});
		return respVO;
	}

	@Override
	public SpGoodsSpuImportRespVO importGoodsSoldDate(List<SpGoodsSpuSoldDateImportExcelVO> importSoldDate, boolean syncChannel) {
		if (CollUtil.isEmpty(importSoldDate)) {
			throw exception(GOODS_SKU_SYNC_SOLD_DATE_IMPORT_LIST_IS_EMPTY);
		}
		SpGoodsSpuImportRespVO respVO = SpGoodsSpuImportRespVO.builder().createList(new ArrayList<>())
				.updateList(new ArrayList<>()).failureList(new LinkedHashMap<>()).build();
		importSoldDate.forEach(spu -> {
			// 校验，判断是否有不符合的原因
			TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
			try {
				log.info("importGoodsSoldDate:{}", JsonUtils.toJsonString(spu));
				SpGoodsSpuDO goodsSpu=	getGoodsSpu(Long.valueOf(spu.getSpuId()));
				if (ObjectUtil.isNull(goodsSpu)){
					respVO.getFailureList().put(spu.getSpuId().toString(), "未找到Spu");
					return;
				}else {
					goodsSpu.setSoldDateFrom(spu.getSoldDateFrom());
					goodsSpu.setSoldDateTo(spu.getSoldDateTo());
					updateById(goodsSpu);
				}
				platformTransactionManager.commit(transaction);
				// 如果存在，判断是否允许更新
				if (syncChannel) {
//					spGoodsSpuChannelList.forEach( channel ->  channel.syncStock(new SpuChannelSyncReq(spu.getSpuId(),spu.getChannelCode(),Long.valueOf(spu.getSpuId()))));

					ChannelInfoVo channelInfoVo = channelInfoApi.getChannelInfoByChannelCode(spu.getChannelCode());
					if (ObjectUtil.isNull(channelInfoVo)){
						respVO.getFailureList().put(spu.getSpuId().toString(),"渠道不存在");
						return;
					}
					List<SpGoodsSpuChannelDO>  spuChannelList = spGoodsSpuChannelService.getSpGoodsSpuChannelByChannelIdAndSpuId(channelInfoVo.getId(),spu.getSpuId());
					spuChannelList.forEach(spuChannel -> {
						SpuChannelSyncReq req = new SpuChannelSyncReq()
								.setSpuId(spu.getSpuId())
								.setChannelCode(spu.getChannelCode())
								.setAppId(spuChannel.getAppId())
								;
						spGoodsSpuChannelList.forEach( channel ->  channel.syncStock(req));
					});
				}
			} catch (Exception e) {
				log.error("importGoodsSoldDate error spu:{}",spu.getSpuId());
				respVO.getFailureList().put(spu.getSpuId().toString(), e.getMessage());
				platformTransactionManager.rollback(transaction);
				return;
			}
			respVO.getUpdateList().add(spu.getSpuId().toString());
		});
		return respVO;
	}

	@Override
	public SpGoodsSpuImportRespVO importGoodsRedeemDate(List<SpGoodsSpuRedeemDateImportExcelVO> importRedeemDate, boolean syncChannel) {
		if (CollUtil.isEmpty(importRedeemDate)) {
			throw exception(GOODS_SKU_SYNC_REDEEM_DATE_IMPORT_LIST_IS_EMPTY);
		}
		SpGoodsSpuImportRespVO respVO = SpGoodsSpuImportRespVO.builder().createList(new ArrayList<>())
				.updateList(new ArrayList<>()).failureList(new LinkedHashMap<>()).build();
		importRedeemDate.forEach(spu -> {
			if (DAY_RANGE.getType().equals(spu.getUseDateType()) && ( ObjectUtil.isNull(spu.getStartDate()) ||  ObjectUtil.isNull(spu.getEndDate()))){
				respVO.getFailureList().put(spu.getSpuId().toString(),"有效期不能为空");
			}
			if (DAY_DURATION.getType().equals(spu.getUseDateType()) && ObjectUtil.isNull(spu.getDayDuration()) ){
				respVO.getFailureList().put(spu.getSpuId().toString(),"有效期天数不能为空");
			}
			// 校验，判断是否有不符合的原因
			TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
			try {
				log.info("importGoodsSoldDate:{}", JsonUtils.toJsonString(spu));
				SpGoodsSpuDO goodsSpu =	getGoodsSpu(Long.valueOf(spu.getSpuId()));
				if (ObjectUtil.isNull(goodsSpu)){
					respVO.getFailureList().put(spu.getSpuId().toString(), "未找到Spu");
					return;
				}else {
					//更新配置文件中的数据
					UseDateDuration validDate = goodsSpu.getUseRule().getValidDate();
					validDate.setUseDateType(spu.getUseDateType());

					goodsSpu.setUseDateType(spu.getUseDateType());
					if (DAY_RANGE.getType().equals(spu.getUseDateType())){
						goodsSpu.setValidDateFrom(spu.getStartDate().toLocalDate());
						goodsSpu.setValidDateTo(spu.getEndDate().toLocalDate());
						goodsSpu.setDayDuration(null);

						validDate.setStartDate(spu.getStartDate().toLocalDate());
						validDate.setEndDate(spu.getEndDate().toLocalDate());
					}
					if (DAY_DURATION.getType().equals(spu.getUseDateType())){
						goodsSpu.setDayDuration(spu.getDayDuration());
						goodsSpu.setValidDateFrom(null);
						goodsSpu.setValidDateTo(null);

						validDate.setDayDuration(spu.getDayDuration());
					}

					updateById(goodsSpu);
				}
				platformTransactionManager.commit(transaction);
				// 如果存在，判断是否允许更新
				if (syncChannel) {
					// TODO 测试是啊 啊啊啊啊
//					SpGoodsSpuChannelDO spGoodsSpuChannel = spGoodsSpuChannelService.getSpGoodsSpuChannelByAppIdAndSpuId();
//					spGoodsSpuChannelList.forEach( channel ->  channel.syncStock(new SpuChannelSyncReq(spGoodsSpuChannel.getId(),spu.getChannelCode(),Long.valueOf(spu.getSpuId()))));
				}
			} catch (Exception e) {
				log.error("importGoodsSoldDate error spu:{}",spu.getSpuId());
				respVO.getFailureList().put(spu.getSpuId().toString(), e.getMessage());
				platformTransactionManager.rollback(transaction);
				return;
			}
			respVO.getUpdateList().add(spu.getSpuId().toString());
		});
		return respVO;
	}

	@Override
	public Integer getStatisticsTotalBySpuDays(Long spuId, LocalDateTime date) {
		return null;
	}

	@Value("${spring.profiles.active:prod}")
	private String activeProfile;

	@Override
	public String generatorCouponBySpuId(Long spuId) {
		// TODO 正式环境不支持券码MOC生成
		if ("prod".equals(activeProfile)){
			throw exception(TRADE_COUPON_NOT_SUPPORT_ERROR);
		}
		SpGoodsSpuDO spGoodsSpuDO = getGoodsSpu(spuId);
		if (ObjectUtil.isNull(spGoodsSpuDO)){
			throw exception(GOODS_SPU_NOT_EXISTS);
		}

		List<SpGoodsSkuRespVO>  spGoodsSkuRespList = spGoodsSkuService.getGoodsSkuListBySpuId(spuId);
		if (CollectionUtil.isEmpty(spGoodsSkuRespList)){
			throw exception(GOODS_SKU_NOT_EXISTS);
		}
		SpGoodsSkuRespVO sku = CollectionUtil.getFirst(spGoodsSkuRespList);

		SpMainInfoWithOwnerRespVO spMainInfo = spMainInfoService.getMainInfoWithOwnerById(spGoodsSpuDO.getSpId());

		LocalDate validDateFrom =spGoodsSpuDO.getValidDateFrom();
		LocalDate validDateTo =spGoodsSpuDO.getValidDateTo();
		if (DAY_DURATION.getType().equals(spGoodsSpuDO.getUseDateType())){
			validDateFrom = LocalDate.now();
			validDateTo = validDateFrom.plusDays(spGoodsSpuDO.getDayDuration());
		}

		TradeCouponGeneratorDto generatorDto = new TradeCouponGeneratorDto()
				.setSpuId(spuId)
				.setSkuId(sku.getId())
				.setSkuName(spGoodsSpuDO.getFullName())
				.setAmount(sku.getSalePrice())
				.setUseDateFrom(validDateFrom.atTime(LocalTime.now()))
				.setUseDateTo(validDateTo.atTime(23,59,59))
				.setMerchantId(spGoodsSpuDO.getSpId())
				.setMerchantName(spMainInfo.getSpName())
				.setAgentId(spMainInfo.getOwnerSp().getId())
				.setAgentName(spMainInfo.getOwnerSp().getSpName());
		String couponCode =	tradeCouponGeneratorApi.getTradeCouponByCode(generatorDto);
		return couponCode;
	}

	@Override
	public PageResult<SpGoodsSpuChannelAppVo> getChannelAppConfigPageBySpId(SpGoodsSpuChannelConfigPageVO pageReqVO) {
		IPage<SpGoodsSpuChannelAppVo> page = getBaseMapper().getChannelAppConfigPageBySpId(
				MyBatisUtils.buildPage(pageReqVO),pageReqVO.getSpGoodsId()
				, CollectionUtil.newArrayList(ChannelCodeEnum.XHS_MINI_APP.getCode(),ChannelCodeEnum.XHS.getCode())
				, pageReqVO.getAppName()
				, pageReqVO.getAppId()
		);
		return new PageResult<>(page.getRecords(), page.getTotal());
	}

	@Override
	public List<AppXhsMiniAppGoodsSpuRespVo> getSpGoodsSpuByStoreId(Long storeId, Long appId) {
		return getBaseMapper().getSpGoodsSpuByStoreId(storeId,appId);
	}

	@Override
	public List<AppXhsMiniAppGoodsSpuBookRespVo> getSpGoodsBookSpuByStoreId(Long storeId, Long appId) {
		return getBaseMapper().getSpGoodsBookSpuByStoreId(storeId,appId);
	}

	@Override
	public List<SpGoodsSpuStoreLbsRespVO> getStoreLbsLimitOneListBySpuId(Collection<Long> spuIds, Double longitude, Double latitude) {
		return getBaseMapper().getStoreLbsLimitOneListBySpuId(spuIds,longitude,latitude);
	}

	@Override
	public List<SpGoodsSpuStoreNumRespVO>  getStoreNumListBySpuId(Collection<Long> spuId) {
		return getBaseMapper().getStoreNumListBySpuId(spuId);
	}

	@Override
	public List<SpGoodsSpuSimpleVO> selectSimpleVOList(Long spId, Integer featuredType, Integer limit) {
		return getBaseMapper().selectSimpleVOList(spId,featuredType,limit);
	}

	@Override
	public SpGoodsSpuSimpleVO selectSimplePackageBySpuId(Long spuId) {
		return getBaseMapper().selectSimplePackageBySpuId(spuId);
	}

	@Override
	public List<SpGoodsSpuSimpleVO> selectSimplePackageListBySpuIds(Collection<Long> spuIds) {
		return getBaseMapper().selectSimplePackageListBySpuIds(spuIds);
	}

	@Override
	public List<SpGoodsSkuSampleVo> getSkuListBySpuId(Long spuId) {
		return getBaseMapper().getSkuListBySpuId(spuId);
	}

	@Override
	public List<AppXhsMiniAppGoodsSpuRespVo> getSpGoodsSpuHotBySpuIds(List<Long> spuIds, Long appId) {
		return getBaseMapper().getSpGoodsSpuHotBySpuIds(spuIds,appId);
	}

	@Override
	public AppXhsMiniAppGoodsSpuRespVo getSpGoodsSpuBySpuId(Long spuId, Long appId) {
		return getBaseMapper().getSpGoodsSkuBySpuId(spuId, appId);
	}

	@Override
	public PageResult<AppXhsMiniAppGoodsSpuRespVo> getSpGoodsSpuBySpIdAndAppId(AppXhsMiniAppStoreBySpPageReqVo reqVo, Long appId) {
		IPage<AppXhsMiniAppGoodsSpuRespVo> result = getBaseMapper().getSpGoodsSpuBySpIdAndAppId(MyBatisUtils.buildPage(reqVo),reqVo.getSpId(),appId);
		return new PageResult<>(result.getRecords(), result.getTotal());
	}

	@Override
	public ExcelImportRespVO importUpdateSpuStoreExcel(List<SpGoodsSpuStoreImportExcelVO> importStocks,Boolean isDeleted,List<Long> spuIds) {
		if (CollUtil.isEmpty(importStocks)) {
			throw exception(GOODS_SPU_STORE_IMPORT_LIST_IS_EMPTY);
		}
		ExcelImportRespVO respVO = ExcelImportRespVO.builder().creates(new ArrayList<>())
				.updates(new ArrayList<>()).failures(new LinkedHashMap<>()).build();
		Set<Long> spuIdSet = Sets.newHashSet(spuIds);
		spuIdSet.forEach(spuId -> {
			// 校验，判断是否有不符合的原因
			TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
			try {
				SpGoodsSpuDO goodsSpu = validateGoodsSpuExists(spuId);
				log.info("importGoodsSpuStore begin:{}", spuId);
				Set<Long> storeList = importStocks.stream().filter(i -> ObjectUtil.isNotNull(i.getStoreId())).map(SpGoodsSpuStoreImportExcelVO::getStoreId).collect(Collectors.toSet());
				if (isDeleted){
					spuStoreMapper.deleteBySpuId(spuId);
				}else {
					//更新，筛选出不在的门店
					List<SpGoodsSpuStoreVO> spuStoreList = spuStoreMapper.getGoodsSpuStoreListBySpuId(spuId);
					if (CollectionUtil.isNotEmpty(spuStoreList)){
						List<Long> dbStoreList = spuStoreList.stream().map(SpGoodsSpuStoreVO::getStoreId).collect(Collectors.toList());
						storeList.removeAll(dbStoreList);
					}
				}
				if (CollectionUtil.isEmpty(storeList)){
					return;
				}

				List<SpStoreInfoDO>	spStoreList = spStoreInfoMapper.getStoreListBySpIdAndStoreIds(goodsSpu.getSpId(),storeList);
				if (spStoreList.size() != storeList.size()){
					storeList.removeAll(spStoreList.stream().map(SpStoreInfoDO::getId).collect(Collectors.toList()));
					respVO.getFailures().put(spuId.toString(), "不属于商品所属商家的门店:【"+ StringUtils.join(storeList, ",")+"】");
					return;
				}

				List<SpGoodsSpuStoreDO> SpGoodsSpuStoreList = storeList.stream().map(storeId -> new SpGoodsSpuStoreDO()
                    .setStoreId(storeId)
                    .setSpuId(spuId)
				).collect(Collectors.toList());
				//批量插入商品门店关系
				spuStoreMapper.insertBatch(SpGoodsSpuStoreList);
				respVO.getUpdates().add("商品："+spuId.toString()+"，更新门店："+storeList.size());
				platformTransactionManager.commit(transaction);
				log.info("importGoodsSpuStore end:{}", spuId);
			} catch (Exception e) {
				log.error("importGoodsSoldDate error spuId:{}",spuId);
				respVO.getFailures().put(spuId.toString(), e.getMessage());
				platformTransactionManager.rollback(transaction);
            }
		});
		return respVO;
	}

}
