package com.yitong.octopus.module.account.controller.admin.account.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 账户 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccountRespVO extends AccountBaseVO {

    @Schema(description = "账户编码", required = true)
    private String code;

}
