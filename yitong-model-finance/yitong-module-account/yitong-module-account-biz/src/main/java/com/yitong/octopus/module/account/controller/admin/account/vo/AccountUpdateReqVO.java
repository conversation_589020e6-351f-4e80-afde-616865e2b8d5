package com.yitong.octopus.module.account.controller.admin.account.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 账户更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccountUpdateReqVO extends AccountBaseVO {

    @Schema(description = "账户编码", required = true)
    @NotNull(message = "账户编码不能为空")
    private String code;

}
