package com.yitong.octopus.module.account.service.account;

import java.util.*;
import javax.validation.*;

import com.yitong.octopus.module.account.controller.admin.account.dto.SettleOrderDto;
import com.yitong.octopus.module.account.controller.admin.account.vo.*;
import com.yitong.octopus.module.account.dal.dataobject.account.AccountDO;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.module.account.enums.AcctCodeType;

/**
 * 账户 Service 接口
 *
 * <AUTHOR>
 */
public interface AccountService {

    /**
     * 创建账户
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String create(@Valid AccountCreateReqVO createReqVO);

    /**
     * 更新账户
     *
     * @param updateReqVO 更新信息
     */
    void update(@Valid AccountUpdateReqVO updateReqVO);

    /**
     * 删除账户
     *
     * @param id 编号
     */
    void delete(String id);

    /**
     * 获得账户
     *
     * @param id 编号
     * @return 账户
     */
    AccountDO get(String id);

    /**
     * 获得账户列表
     *
     * @param ids 编号
     * @return 账户列表
     */
    List<AccountDO> getList(Collection<String> ids);

    /**
     * 获得账户分页
     *
     * @param pageReqVO 分页查询
     * @return 账户分页
     */
    PageResult<AccountDO> getPage(AccountPageReqVO pageReqVO);

    /**
     * 获得账户列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 账户列表
     */
    List<AccountDO> getList(AccountExportReqVO exportReqVO);

    /**
     * 结算成功记账
     * 目标待结算账户- 目标余额账户+
     * @param order 结算单详情
     */
    String onPaySuccessfully(SettleOrderDto order);

    /**
     * 提现申请受理成功记账
     * 目标余额账户-, 银行存款-, 目标已提现账户+
     */
    String onWithdrawApply(SettleOrderDto order);

    /**
     * 提现失败记账
     * @param withdrawApplyAccountingNo 结算申请账号编码
     */
    String onWithdrawFailed(String withdrawApplyAccountingNo);

}
