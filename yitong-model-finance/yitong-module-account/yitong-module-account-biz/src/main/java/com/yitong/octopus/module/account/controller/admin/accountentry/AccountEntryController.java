package com.yitong.octopus.module.account.controller.admin.accountentry;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;

import com.yitong.octopus.module.account.controller.admin.accountentry.vo.*;
import com.yitong.octopus.module.account.dal.dataobject.accountentry.AccountEntryDO;
import com.yitong.octopus.module.account.convert.accountentry.AccountEntryConvert;
import com.yitong.octopus.module.account.service.accountentry.AccountEntryService;

@Tag(name = "管理后台 - 账户记账流水")
@RestController
@RequestMapping("/account/entry")
@Validated
public class AccountEntryController {

    @Resource
    private AccountEntryService entryService;

    @PostMapping("/create")
    @Operation(summary = "创建账户记账流水")
    @PreAuthorize("@ss.hasPermission('account:entry:create')")
    public CommonResult<Long> createEntry(@Valid @RequestBody AccountEntryCreateReqVO createReqVO) {
        return success(entryService.createEntry(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新账户记账流水")
    @PreAuthorize("@ss.hasPermission('account:entry:update')")
    public CommonResult<Boolean> updateEntry(@Valid @RequestBody AccountEntryUpdateReqVO updateReqVO) {
        entryService.updateEntry(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除账户记账流水")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('account:entry:delete')")
    public CommonResult<Boolean> deleteEntry(@RequestParam("id") Long id) {
        entryService.deleteEntry(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得账户记账流水")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('account:entry:query')")
    public CommonResult<AccountEntryRespVO> getEntry(@RequestParam("id") Long id) {
        AccountEntryDO entry = entryService.getEntry(id);
        return success(AccountEntryConvert.INSTANCE.convert(entry));
    }

    @GetMapping("/list")
    @Operation(summary = "获得账户记账流水列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('account:entry:query')")
    public CommonResult<List<AccountEntryRespVO>> getEntryList(@RequestParam("ids") Collection<Long> ids) {
        List<AccountEntryDO> list = entryService.getEntryList(ids);
        return success(AccountEntryConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得账户记账流水分页")
    @PreAuthorize("@ss.hasPermission('account:entry:query')")
    public CommonResult<PageResult<AccountEntryRespVO>> getEntryPage(@Valid AccountEntryPageReqVO pageVO) {
        PageResult<AccountEntryDO> pageResult = entryService.getEntryPage(pageVO);
        return success(AccountEntryConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出账户记账流水 Excel")
    @PreAuthorize("@ss.hasPermission('account:entry:export')")
    public void exportEntryExcel(@Valid AccountEntryExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<AccountEntryDO> list = entryService.getEntryList(exportReqVO);
        // 导出 Excel
        List<AccountEntryExcelVO> datas = AccountEntryConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "账户记账流水.xls", "数据", AccountEntryExcelVO.class, datas);
    }

}
