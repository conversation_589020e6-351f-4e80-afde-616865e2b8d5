package com.yitong.octopus.module.account.controller.admin.accountentry.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 账户记账流水更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccountEntryUpdateReqVO extends AccountEntryBaseVO {

    @Schema(description = "主键", required = true, example = "14021")
    @NotNull(message = "主键不能为空")
    private Long id;

}
