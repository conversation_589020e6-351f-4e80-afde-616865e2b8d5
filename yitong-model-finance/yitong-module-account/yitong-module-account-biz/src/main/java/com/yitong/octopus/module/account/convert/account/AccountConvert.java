package com.yitong.octopus.module.account.convert.account;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yitong.octopus.module.account.controller.admin.account.vo.*;
import com.yitong.octopus.module.account.dal.dataobject.account.AccountDO;

/**
 * 账户 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AccountConvert {

    AccountConvert INSTANCE = Mappers.getMapper(AccountConvert.class);

    AccountDO convert(AccountCreateReqVO bean);

    AccountDO convert(AccountUpdateReqVO bean);

    AccountRespVO convert(AccountDO bean);

    List<AccountRespVO> convertList(List<AccountDO> list);

    PageResult<AccountRespVO> convertPage(PageResult<AccountDO> page);

    List<AccountExcelVO> convertList02(List<AccountDO> list);

}
