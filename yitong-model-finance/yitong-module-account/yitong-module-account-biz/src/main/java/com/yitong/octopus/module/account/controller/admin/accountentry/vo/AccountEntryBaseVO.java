package com.yitong.octopus.module.account.controller.admin.accountentry.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 账户记账流水 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class AccountEntryBaseVO {

    @Schema(description = "记账流水号", required = true)
    @NotNull(message = "记账流水号不能为空")
    private String tradeNo;

    @Schema(description = "账户代码", required = true)
    @NotNull(message = "账户代码不能为空")
    private String acctCode;

    @Schema(description = "变更类型 1:进 -1:出 0: 其他", required = true, example = "1")
    @NotNull(message = "变更类型 1:进 -1:出 0: 其他不能为空")
    private Integer opType;

    @Schema(description = "变动金额", required = true)
    @NotNull(message = "变动金额不能为空")
    private Long amount;

    @Schema(description = "是否操作冻结金额 0-否 1-是", required = true)
    @NotNull(message = "是否操作冻结金额 0-否 1-是不能为空")
    private Boolean frozen;

    @Schema(description = "变更后的余额", required = true)
    @NotNull(message = "变更后的余额不能为空")
    private Long postBalance;

    @Schema(description = "变更后的冻结余额", required = true)
    @NotNull(message = "变更后的冻结余额不能为空")
    private Long postFrozenBalance;

    @Schema(description = "是否被冲正 0-未冲正（默认) 1-冲正", required = true)
    @NotNull(message = "是否被冲正 0-未冲正（默认) 1-冲正不能为空")
    private Boolean reversed;

    @Schema(description = "冲正源记账流水")
    private String originTradeNo;

    @Schema(description = "记账规则代码 ", example = "2")
    private String bizType;

    @Schema(description = "记账凭证号", example = "31028")
    private String bizOrderId;

}
