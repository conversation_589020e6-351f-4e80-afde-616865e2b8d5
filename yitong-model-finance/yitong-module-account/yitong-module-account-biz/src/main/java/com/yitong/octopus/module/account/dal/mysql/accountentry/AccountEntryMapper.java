package com.yitong.octopus.module.account.dal.mysql.accountentry;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.account.dal.dataobject.accountentry.AccountEntryDO;
import org.apache.ibatis.annotations.Mapper;
import com.yitong.octopus.module.account.controller.admin.accountentry.vo.*;

/**
 * 账户记账流水 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AccountEntryMapper extends BaseMapperX<AccountEntryDO> {

    default PageResult<AccountEntryDO> selectPage(AccountEntryPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccountEntryDO>()
                .eqIfPresent(AccountEntryDO::getTradeNo, reqVO.getTradeNo())
                .eqIfPresent(AccountEntryDO::getAcctCode, reqVO.getAcctCode())
                .eqIfPresent(AccountEntryDO::getOpType, reqVO.getOpType())
                .eqIfPresent(AccountEntryDO::getReversed, reqVO.getReversed())
                .eqIfPresent(AccountEntryDO::getOriginTradeNo, reqVO.getOriginTradeNo())
                .eqIfPresent(AccountEntryDO::getBizType, reqVO.getBizType())
                .eqIfPresent(AccountEntryDO::getBizOrderId, reqVO.getBizOrderId())
                .betweenIfPresent(AccountEntryDO::getCrtTime, reqVO.getCrtTime())
                .orderByDesc(AccountEntryDO::getId));
    }

    default List<AccountEntryDO> selectList(AccountEntryExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AccountEntryDO>()
                .eqIfPresent(AccountEntryDO::getTradeNo, reqVO.getTradeNo())
                .eqIfPresent(AccountEntryDO::getAcctCode, reqVO.getAcctCode())
                .eqIfPresent(AccountEntryDO::getOpType, reqVO.getOpType())
                .eqIfPresent(AccountEntryDO::getReversed, reqVO.getReversed())
                .eqIfPresent(AccountEntryDO::getOriginTradeNo, reqVO.getOriginTradeNo())
                .eqIfPresent(AccountEntryDO::getBizType, reqVO.getBizType())
                .eqIfPresent(AccountEntryDO::getBizOrderId, reqVO.getBizOrderId())
                .betweenIfPresent(AccountEntryDO::getCrtTime, reqVO.getCrtTime())
                .orderByDesc(AccountEntryDO::getId));
    }
}
