package com.yitong.octopus.module.account.controller.admin.account.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 账户 Excel 导出 Request VO，参数和 AccountPageReqVO 是一致的")
@Data
public class AccountExportReqVO {

    @Schema(description = "账户名", example = "王五")
    private String name;

    @Schema(description = "父账户代码")
    private String parentCode;

    @Schema(description = "余额（包含冻结部分)")
    private Long balance;

    @Schema(description = "冻结余额")
    private Long frozenBalance;

    @Schema(description = "是否允许透支 0-不允许(默认)  1-允许")
    private Integer overdraft;

    @Schema(description = "账户所有者类型 01-服务商 02-商户 03-渠道商", example = "1")
    private String ownerType;

    @Schema(description = "账户所有者ID", example = "23308")
    private String ownerId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] crtTime;

    @Schema(description = "更新时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] updTime;

    @Schema(description = "版本号")
    private Long version;

}
