package com.yitong.octopus.module.account.convert.accountentry;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yitong.octopus.module.account.controller.admin.accountentry.vo.*;
import com.yitong.octopus.module.account.dal.dataobject.accountentry.AccountEntryDO;

/**
 * 账户记账流水 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AccountEntryConvert {

    AccountEntryConvert INSTANCE = Mappers.getMapper(AccountEntryConvert.class);

    AccountEntryDO convert(AccountEntryCreateReqVO bean);

    AccountEntryDO convert(AccountEntryUpdateReqVO bean);

    AccountEntryRespVO convert(AccountEntryDO bean);

    List<AccountEntryRespVO> convertList(List<AccountEntryDO> list);

    PageResult<AccountEntryRespVO> convertPage(PageResult<AccountEntryDO> page);

    List<AccountEntryExcelVO> convertList02(List<AccountEntryDO> list);

}
