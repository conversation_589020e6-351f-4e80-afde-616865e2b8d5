package com.yitong.octopus.module.account.controller.admin.account;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;
import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.account.controller.admin.account.vo.*;
import com.yitong.octopus.module.account.dal.dataobject.account.AccountDO;
import com.yitong.octopus.module.account.convert.account.AccountConvert;
import com.yitong.octopus.module.account.service.account.AccountService;

@Tag(name = "管理后台 - 账户")
@RestController
@RequestMapping("/account/")
@Validated
public class AccountController {

    @Resource
    private AccountService Service;

    @PostMapping("/create")
    @Operation(summary = "创建账户")
    @PreAuthorize("@ss.hasPermission('account::create')")
    public CommonResult<String> create(@Valid @RequestBody AccountCreateReqVO createReqVO) {
        return success(Service.create(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新账户")
    @PreAuthorize("@ss.hasPermission('account::update')")
    public CommonResult<Boolean> update(@Valid @RequestBody AccountUpdateReqVO updateReqVO) {
        Service.update(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除账户")
    @Parameter(name = "codes", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('account::delete')")
    public CommonResult<Boolean> delete(@RequestParam("codes") String codes) {
        Service.delete(codes);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得账户")
    @Parameter(name = "codes", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('account::query')")
    public CommonResult<AccountRespVO> get(@RequestParam("code") String code) {
        AccountDO  accountDO= Service.get(code);
        return success(AccountConvert.INSTANCE.convert(accountDO));
    }

    @GetMapping("/list")
    @Operation(summary = "获得账户列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('account::query')")
    public CommonResult<List<AccountRespVO>> getList(@RequestParam("codes") Collection<String> codes) {
        List<AccountDO> list = Service.getList(codes);
        return success(AccountConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得账户分页")
    @PreAuthorize("@ss.hasPermission('account::query')")
    public CommonResult<PageResult<AccountRespVO>> getPage(@Valid AccountPageReqVO pageVO) {
        PageResult<AccountDO> pageResult = Service.getPage(pageVO);
        return success(AccountConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出账户 Excel")
    @PreAuthorize("@ss.hasPermission('account::export')")
    public void exportExcel(@Valid AccountExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<AccountDO> list = Service.getList(exportReqVO);
        // 导出 Excel
        List<AccountExcelVO> datas = AccountConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "账户.xls", "数据", AccountExcelVO.class, datas);
    }

}
