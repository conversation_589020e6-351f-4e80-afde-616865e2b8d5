package com.yitong.octopus.module.account.dal.dataobject.account;

import lombok.*;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 账户 DO
 *
 * <AUTHOR>
 */
@TableName("t_account")
@KeySequence("t_account_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountDO {

    /**
     * 账户编码
     */
    @TableId(type = IdType.INPUT)
    private String code;
    /**
     * 账户名
     */
    private String name;
    /**
     * 父账户代码
     */
    private String parentCode;
    /**
     * 余额（包含冻结部分)
     */
    private Long balance;
    /**
     * 冻结余额
     */
    private Long frozenBalance;
    /**
     * 是否允许透支 0-不允许(默认)  1-允许
     */
    private Boolean overdraft;
    /**
     * 账户所有者类型 01-服务商 02-商户 03-渠道商
     */
    private String ownerType;
    /**
     * 账户所有者ID
     */
    private String ownerId;
    /**
     * 创建时间
     */
    private LocalDateTime crtTime;
    /**
     * 更新时间
     */
    private LocalDateTime updTime;
    /**
     * 版本号
     */
    @Version
    private Long version;

}
