package com.yitong.octopus.module.account.controller.admin.account.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SettleOrderDto {

    @Schema(description = "商家ID/商户ID")
    private Long spId;

    @Schema(description = "金额")
    private Long amount;

    @Schema(description = "业务订单Id")
    private String bizOrderId;

}
