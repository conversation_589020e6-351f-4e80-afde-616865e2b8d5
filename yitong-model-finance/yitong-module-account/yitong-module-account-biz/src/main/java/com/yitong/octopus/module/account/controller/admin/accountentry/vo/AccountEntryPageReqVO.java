package com.yitong.octopus.module.account.controller.admin.accountentry.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 账户记账流水分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccountEntryPageReqVO extends PageParam {

    @Schema(description = "记账流水号")
    private String tradeNo;

    @Schema(description = "账户代码")
    private String acctCode;

    @Schema(description = "变更类型 1:进 -1:出 0: 其他", example = "1")
    private Integer opType;

    @Schema(description = "变动金额")
    private Long amount;

    @Schema(description = "是否操作冻结金额 0-否 1-是")
    private Integer frozen;

    @Schema(description = "变更后的余额")
    private Long postBalance;

    @Schema(description = "变更后的冻结余额")
    private Long postFrozenBalance;

    @Schema(description = "是否被冲正 0-未冲正（默认) 1-冲正")
    private Integer reversed;

    @Schema(description = "冲正源记账流水")
    private String originTradeNo;

    @Schema(description = "记账规则代码 ", example = "2")
    private String bizType;

    @Schema(description = "记账凭证号", example = "31028")
    private String bizOrderId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] crtTime;

    @Schema(description = "更新时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] updTime;

    @Schema(description = "版本号")
    private Long version;

}
