package com.yitong.octopus.module.account.controller.admin.accountentry.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 账户记账流水 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccountEntryRespVO extends AccountEntryBaseVO {

    @Schema(description = "主键", required = true, example = "14021")
    private Long id;

}
