package com.yitong.octopus.module.account.service.account;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yitong.octopus.framework.common.util.string.StringUtils;
import com.yitong.octopus.module.account.controller.admin.account.dto.SettleOrderDto;
import com.yitong.octopus.module.account.controller.admin.accountentry.vo.AccountEntryCreateReqVO;
import com.yitong.octopus.module.account.controller.admin.accountentry.vo.AccountEntryUpdateReqVO;
import com.yitong.octopus.module.account.dal.dataobject.accountentry.AccountEntryDO;
import com.yitong.octopus.module.account.enums.AcctBizType;
import com.yitong.octopus.module.account.enums.AcctCodeType;
import com.yitong.octopus.module.account.enums.AcctOpType;
import com.yitong.octopus.module.account.enums.AcctOwnerType;
import com.yitong.octopus.module.account.service.accountentry.AccountEntryService;
import com.yitong.octopus.module.sp.controller.admin.mainplatformcontract.vo.SpMainPlatformContractViewRespVO;
import com.yitong.octopus.module.sp.enums.SpTypeEnum;
import com.yitong.octopus.module.sp.service.maininfo.SpMainInfoService;
import com.yitong.octopus.module.sp.service.mainplatformcontract.SpMainPlatformContractService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

import com.yitong.octopus.module.account.controller.admin.account.vo.*;
import com.yitong.octopus.module.account.dal.dataobject.account.AccountDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.account.convert.account.AccountConvert;
import com.yitong.octopus.module.account.dal.mysql.account.AccountMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.account.enums.AcctBizType.SETTLE;
import static com.yitong.octopus.module.account.enums.AcctBizType.WITHDRAW;
import static com.yitong.octopus.module.account.enums.AcctCodeType.*;
import static com.yitong.octopus.module.account.enums.AcctOpType.IN;
import static com.yitong.octopus.module.account.enums.AcctOpType.OUT;
import static com.yitong.octopus.module.account.enums.ErrorCodeConstants.*;

/**
 * 账户 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class AccountServiceImpl implements AccountService {

    @Resource
    private AccountMapper accountMapper;

    @Resource
    private AccountEntryService accountEntryService;

    /**
     * 开子账户
     * @param type 账户类型
     * @param ownerId 商家/商户/渠道ID
     * @param parentCodes 子账户类型
     * @return
     */
    private List<AccountDO> openSubAcct(AcctOwnerType type, String ownerId, String... parentCodes) {
        List<AccountDO> list = new ArrayList<>();
        for (String parentCode : parentCodes) {
            AccountDO account = accountMapper.getByOwnerId(parentCode, ownerId);
            if (account == null) {
                account = new AccountDO();
                account.setCode(parentCode + type.code() + org.apache.commons.lang3.StringUtils.leftPad(ownerId, 19, '0'));
                account.setBalance(0L);
                account.setFrozenBalance(0L);
                account.setParentCode(parentCode);
                account.setOwnerType(type.code());
                account.setOwnerId(ownerId);
                accountMapper.insert(account);
            }
            list.add(account);
        }
        return list;
    }

    @Override
    public String create(AccountCreateReqVO createReqVO) {
        // 插入
        AccountDO accountDO = AccountConvert.INSTANCE.convert(createReqVO);
        accountMapper.insert(accountDO);
        // 返回
        return accountDO.getCode();
    }

    @Override
    public void update(AccountUpdateReqVO updateReqVO) {
        // 校验存在
        validateExists(updateReqVO.getCode());
        // 更新
        AccountDO updateObj = AccountConvert.INSTANCE.convert(updateReqVO);
        accountMapper.updateById(updateObj);
    }

    @Override
    public void delete(String code) {
        // 校验存在
        validateExists(code);
        // 删除
        accountMapper.deleteById(code);
    }

    private void validateExists(String code) {
        if (accountMapper.selectById(code) == null) {
            throw exception(ACC_NOT_EXISTS);
        }
    }

    @Override
    public AccountDO get(String code) {
        return accountMapper.selectById(code);
    }

    @Override
    public List<AccountDO> getList(Collection<String> codes) {
        return accountMapper.selectBatchIds(codes);
    }

    @Override
    public PageResult<AccountDO> getPage(AccountPageReqVO pageReqVO) {
        return accountMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AccountDO> getList(AccountExportReqVO exportReqVO) {
        return accountMapper.selectList(exportReqVO);
    }

    /**
     * 结算成功记账
     * 目标待结算账户- 目标余额账户+
     * @param order 结算订单信息
     */
    @Transactional
    public String onPaySuccessfully(SettleOrderDto order) {
        String tradeNo = IdUtil.getSnowflakeNextIdStr();
        String spId = order.getSpId().toString();
        this.changeBalance(tradeNo, spId, ACCTCODE_TOBE_SETTLED, OUT, order.getAmount(), false, SETTLE, order.getBizOrderId());
        this.changeBalance(tradeNo, spId, ACCTCODE_SETTLED, IN, order.getAmount(), false, SETTLE, order.getBizOrderId());
        return tradeNo;
    }

    /**
     * 提现申请受理成功记账
     *
     * 目标余额账户-, 银行存款-, 目标已提现账户+
     */
    @Transactional
    public String onWithdrawApply(SettleOrderDto order) {
        String tradeNo = IdUtil.getSnowflakeNextIdStr();
        String payerId = order.getSpId().toString();
//		this.changeBalance(tradeNo, ACCTCODE_BANK_DEPOSITS, OUT, order.getSettleAmount(), false, bizCode, orderId);
        this.changeBalance(tradeNo, payerId, ACCTCODE_SETTLED, OUT, order.getAmount(), false, WITHDRAW, order.getBizOrderId());
        this.changeBalance(tradeNo, payerId, ACCTCODE_WITHDRAWED, IN, order.getAmount(), false, WITHDRAW, order.getBizOrderId());
        return tradeNo;
    }

    /**
     * 提现失败记账
     * @param withdrawApplyAccountingNo 提现交易账户号
     */
    @Override
    @Transactional
    public String onWithdrawFailed(String withdrawApplyAccountingNo) {
        return this.reverse(withdrawApplyAccountingNo);
    }

    /**
     * 记账冲正
     * @param tradeNo 交易单号
     */
    @Transactional
    public String reverse(String tradeNo) {
        String reverseTradeNo = IdUtil.getSnowflakeNextIdStr();
        accountEntryService.findByTradeNo(tradeNo).forEach(entry -> this.reverse(reverseTradeNo, entry));
        return reverseTradeNo;
    }

    private void reverse(String reverseTradeNo, AccountEntryDO entry) {
        if (entry.getReversed()) {
            log.error("The account entry was be reversed already! tradeNo={}", entry.getTradeNo());
            throw exception(ACCT_OPERATE_FAIL);
        }
        entry.setReversed(true);

        AccountEntryUpdateReqVO updateReqVO = BeanUtil.copyProperties(entry,AccountEntryUpdateReqVO.class);
        accountEntryService.updateEntry(updateReqVO);

        Long actualAmt = -entry.getAmount();
        switch (AcctOpType.resolveByCode(entry.getOpType())) {
            case OUT:
                actualAmt = -actualAmt;
                break;
            default:
        }
        AccountDO acct = changeBalance(entry.getAcctCode(), actualAmt,entry.getFrozen() ? actualAmt : 0);
        if (!acct.getOverdraft() && acct.getBalance() < 0) {
            log.error("Reverse failed: Not enough balance! acctCode={}, bizType={}, bizOrderId={}", acct.getCode(), entry.getBizType(), entry.getBizOrderId());
            throw exception(NOT_ENOUGH_BALANCE);
        }

        AccountEntryCreateReqVO reverseEntry = new AccountEntryCreateReqVO();
        reverseEntry.setTradeNo(reverseTradeNo);
        reverseEntry.setOriginTradeNo(entry.getTradeNo());
        reverseEntry.setAcctCode(entry.getAcctCode());
        reverseEntry.setAmount(-entry.getAmount());
        reverseEntry.setOpType(entry.getOpType());
        reverseEntry.setFrozen(entry.getFrozen());
        reverseEntry.setPostBalance(acct.getBalance());
        reverseEntry.setPostFrozenBalance(acct.getFrozenBalance());
        reverseEntry.setBizType(entry.getBizType());
        reverseEntry.setBizOrderId(entry.getBizOrderId());
        accountEntryService.createEntry(reverseEntry);
    }
    /**
     * 交易余额变更
     * @param tradeNo 交易单号
     * @param ownerId 所有者
     * @param parentAcctCode 父交易编码
     * @param opType 操作类型
     * @param amount 金额
     * @param isFrozenBalance 是否冻结
     * @param bizType 业务类型
     * @param bizOrderId 业务订单ID
     */
    private void changeBalance(String tradeNo, String ownerId, AcctCodeType parentAcctCode, AcctOpType opType, long amount, boolean isFrozenBalance, AcctBizType bizType, String bizOrderId) {
        if (amount == 0) {
            return;
        }
        AccountDO acct = accountMapper.getByOwnerId(parentAcctCode.code(), ownerId);
        this.changeBalance(tradeNo, acct.getCode(), opType, amount, isFrozenBalance, bizType, bizOrderId);
    }

    /**
     * 交易余额变更
     * @param tradeNo 交易单号
     * @param acctCode 交易编码
     * @param opType 操作类型
     * @param amount 金额
     * @param isFrozenBalance 是否冻结
     * @param bizType 业务类型
     * @param bizOrderId 业务订单ID
     * @return
     */
    private void changeBalance(String tradeNo, String acctCode, AcctOpType opType, long amount, boolean isFrozenBalance, AcctBizType bizType, String bizOrderId) {
        if (amount == 0) {
            return ;
        }
        Long actualAmt = amount;
        switch (opType) {
            case OUT:
                actualAmt = -amount;
                break;
            default:
        }
        AccountDO acct = changeBalance(acctCode, actualAmt, isFrozenBalance ? actualAmt : 0);
        if (!acct.getOverdraft() && acct.getBalance() < 0) {
            log.error("Not enough balance! acctCode={}, ruleCode={}, orderId={}", acct.getCode(), bizType, bizOrderId);
            throw exception(NOT_ENOUGH_BALANCE);
        }

        AccountEntryCreateReqVO  entry= new AccountEntryCreateReqVO();
        entry.setTradeNo(tradeNo);
        entry.setAcctCode(acct.getCode());
        entry.setAmount(amount);
        entry.setFrozen(isFrozenBalance);
        entry.setOpType(opType.code());
        entry.setPostBalance(acct.getBalance());
        entry.setPostFrozenBalance(acct.getFrozenBalance());
        entry.setReversed(false);
        entry.setBizType(bizType.code());
        entry.setBizOrderId(bizOrderId);
        accountEntryService.createEntry(entry);
    }

    private void reverse(String reverseTradeNo, AccountEntryUpdateReqVO entry) {

        if (entry.getReversed()) {
            log.error("The account entry was be reversed already! tradeNo={}", entry.getTradeNo());
            throw exception(ACCT_OPERATE_FAIL);
        }
        entry.setReversed(true);
        accountEntryService.updateEntry(entry);

        Long actualAmt = -entry.getAmount();
        switch (AcctOpType.resolveByCode(entry.getOpType())) {
            case OUT:
                actualAmt = -actualAmt;
                break;
            default:
        }
        AccountDO acct = changeBalance(entry.getAcctCode(), actualAmt, entry.getFrozen() ? actualAmt : 0);
        if (!acct.getOverdraft() && acct.getBalance() < 0) {
            log.error("Reverse failed: Not enough balance! acctCode={}, bizType={}, bizOrderId={}", acct.getCode(),
                    entry.getBizType(), entry.getBizOrderId());
            throw exception(NOT_ENOUGH_BALANCE);
        }

        AccountEntryCreateReqVO reverseEntry = new AccountEntryCreateReqVO();
        reverseEntry.setTradeNo(reverseTradeNo);
        reverseEntry.setOriginTradeNo(entry.getTradeNo());
        reverseEntry.setAcctCode(entry.getAcctCode());
        reverseEntry.setAmount(-entry.getAmount());
        reverseEntry.setOpType(entry.getOpType());
        reverseEntry.setFrozen(entry.getFrozen());
        reverseEntry.setPostBalance(acct.getBalance());
        reverseEntry.setPostFrozenBalance(acct.getFrozenBalance());
        reverseEntry.setBizType(entry.getBizType());
        reverseEntry.setBizOrderId(entry.getBizOrderId());
        accountEntryService.createEntry(reverseEntry);
    }

    /**
     * 账户余额变更
     * @param accountCode 账户Code
     * @param amt 金额
     * @param frozenAmt 冻结金额
     * @return
     */
    public AccountDO changeBalance(String accountCode, Long amt, Long frozenAmt) {
        accountMapper.changeBalance(accountCode, amt, frozenAmt);
        return this.get(accountCode);
    }
}
