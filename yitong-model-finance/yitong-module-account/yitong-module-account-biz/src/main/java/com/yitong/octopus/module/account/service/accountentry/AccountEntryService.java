package com.yitong.octopus.module.account.service.accountentry;

import java.util.*;
import javax.validation.*;
import com.yitong.octopus.module.account.controller.admin.accountentry.vo.*;
import com.yitong.octopus.module.account.dal.dataobject.accountentry.AccountEntryDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

/**
 * 账户记账流水 Service 接口
 *
 * <AUTHOR>
 */
public interface AccountEntryService {

    /**
     * 创建账户记账流水
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createEntry(@Valid AccountEntryCreateReqVO createReqVO);

    /**
     * 更新账户记账流水
     *
     * @param updateReqVO 更新信息
     */
    void updateEntry(@Valid AccountEntryUpdateReqVO updateReqVO);

    /**
     * 删除账户记账流水
     *
     * @param id 编号
     */
    void deleteEntry(Long id);

    /**
     * 获得账户记账流水
     *
     * @param id 编号
     * @return 账户记账流水
     */
    AccountEntryDO getEntry(Long id);

    /**
     * 获得账户记账流水列表
     *
     * @param ids 编号
     * @return 账户记账流水列表
     */
    List<AccountEntryDO> getEntryList(Collection<Long> ids);

    /**
     * 获得账户记账流水分页
     *
     * @param pageReqVO 分页查询
     * @return 账户记账流水分页
     */
    PageResult<AccountEntryDO> getEntryPage(AccountEntryPageReqVO pageReqVO);

    /**
     * 获得账户记账流水列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 账户记账流水列表
     */
    List<AccountEntryDO> getEntryList(AccountEntryExportReqVO exportReqVO);

    /**
     * 根据交易号查询交易记录
     * @param tradeNo 交易号
     * @return
     */
    List<AccountEntryDO>  findByTradeNo(String tradeNo);

}
