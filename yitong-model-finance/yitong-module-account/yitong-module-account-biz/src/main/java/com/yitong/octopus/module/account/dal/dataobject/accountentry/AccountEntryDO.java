package com.yitong.octopus.module.account.dal.dataobject.accountentry;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;

/**
 * 账户记账流水 DO
 *
 * <AUTHOR>
 */
@TableName("t_account_entry")
@KeySequence("t_account_entry_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountEntryDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 记账流水号
     */
    private String tradeNo;
    /**
     * 账户代码
     */
    private String acctCode;
    /**
     * 变更类型 1:进 -1:出 0: 其他
     */
    private Integer opType;
    /**
     * 变动金额
     */
    private Long amount;
    /**
     * 是否操作冻结金额 0-否 1-是
     */
    private Boolean frozen;
    /**
     * 变更后的余额
     */
    private Long postBalance;
    /**
     * 变更后的冻结余额
     */
    private Long postFrozenBalance;
    /**
     * 是否被冲正 0-未冲正（默认) 1-冲正
     */
    private Boolean reversed;
    /**
     * 冲正源记账流水
     */
    private String originTradeNo;
    /**
     * 记账规则代码 
     */
    private String bizType;
    /**
     * 记账凭证号
     */
    private String bizOrderId;
    /**
     * 创建时间
     */
    private LocalDateTime crtTime;
    /**
     * 更新时间
     */
    private LocalDateTime updTime;
    /**
     * 版本号
     */
    private Long version;

}
