package com.yitong.octopus.module.account.controller.admin.account.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 账户 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class AccountExcelVO {

    @ExcelProperty("账户编码")
    private String code;

    @ExcelProperty("账户名")
    private String name;

    @ExcelProperty("父账户代码")
    private String parentCode;

    @ExcelProperty("余额（包含冻结部分)")
    private Long balance;

    @ExcelProperty("冻结余额")
    private Long frozenBalance;

    @ExcelProperty("是否允许透支 0-不允许(默认)  1-允许")
    private Boolean overdraft;

    @ExcelProperty("账户所有者类型 01-服务商 02-商户 03-渠道商")
    private String ownerType;

    @ExcelProperty("账户所有者ID")
    private String ownerId;

    @ExcelProperty("创建时间")
    private LocalDateTime crtTime;

    @ExcelProperty("更新时间")
    private LocalDateTime updTime;

    @ExcelProperty("版本号")
    private Long version;

}
