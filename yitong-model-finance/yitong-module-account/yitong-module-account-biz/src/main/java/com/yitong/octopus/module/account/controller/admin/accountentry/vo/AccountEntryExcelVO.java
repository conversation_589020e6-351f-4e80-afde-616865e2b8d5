package com.yitong.octopus.module.account.controller.admin.accountentry.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 账户记账流水 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class AccountEntryExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("记账流水号")
    private String tradeNo;

    @ExcelProperty("账户代码")
    private String acctCode;

    @ExcelProperty("变更类型 1:进 -1:出 0: 其他")
    private Integer opType;

    @ExcelProperty("变动金额")
    private Long amount;

    @ExcelProperty("是否操作冻结金额 0-否 1-是")
    private Boolean frozen;

    @ExcelProperty("变更后的余额")
    private Long postBalance;

    @ExcelProperty("变更后的冻结余额")
    private Long postFrozenBalance;

    @ExcelProperty("是否被冲正 0-未冲正（默认) 1-冲正")
    private Boolean reversed;

    @ExcelProperty("冲正源记账流水")
    private String originTradeNo;

    @ExcelProperty("记账规则代码 ")
    private String bizType;

    @ExcelProperty("记账凭证号")
    private String bizOrderId;

    @ExcelProperty("创建时间")
    private LocalDateTime crtTime;

    @ExcelProperty("更新时间")
    private LocalDateTime updTime;

    @ExcelProperty("版本号")
    private Long version;

}
