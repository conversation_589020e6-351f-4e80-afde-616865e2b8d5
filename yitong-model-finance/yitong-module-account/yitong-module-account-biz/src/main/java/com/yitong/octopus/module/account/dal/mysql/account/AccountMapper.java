package com.yitong.octopus.module.account.dal.mysql.account;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.account.dal.dataobject.account.AccountDO;
import org.apache.ibatis.annotations.Mapper;
import com.yitong.octopus.module.account.controller.admin.account.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.transaction.annotation.Transactional;

/**
 * 账户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AccountMapper extends BaseMapperX<AccountDO> {

    default PageResult<AccountDO> selectPage(AccountPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccountDO>()
                .likeIfPresent(AccountDO::getName, reqVO.getName())
                .eqIfPresent(AccountDO::getParentCode, reqVO.getParentCode())
                .eqIfPresent(AccountDO::getBalance, reqVO.getBalance())
                .eqIfPresent(AccountDO::getFrozenBalance, reqVO.getFrozenBalance())
                .eqIfPresent(AccountDO::getOverdraft, reqVO.getOverdraft())
                .eqIfPresent(AccountDO::getOwnerType, reqVO.getOwnerType())
                .eqIfPresent(AccountDO::getOwnerId, reqVO.getOwnerId())
                .betweenIfPresent(AccountDO::getCrtTime, reqVO.getCrtTime())
                .betweenIfPresent(AccountDO::getUpdTime, reqVO.getUpdTime())
                .eqIfPresent(AccountDO::getVersion, reqVO.getVersion())
                .orderByDesc(AccountDO::getCode));
    }

    default List<AccountDO> selectList(AccountExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<AccountDO>()
                .likeIfPresent(AccountDO::getName, reqVO.getName())
                .eqIfPresent(AccountDO::getParentCode, reqVO.getParentCode())
                .eqIfPresent(AccountDO::getBalance, reqVO.getBalance())
                .eqIfPresent(AccountDO::getFrozenBalance, reqVO.getFrozenBalance())
                .eqIfPresent(AccountDO::getOverdraft, reqVO.getOverdraft())
                .eqIfPresent(AccountDO::getOwnerType, reqVO.getOwnerType())
                .eqIfPresent(AccountDO::getOwnerId, reqVO.getOwnerId())
                .betweenIfPresent(AccountDO::getCrtTime, reqVO.getCrtTime())
                .betweenIfPresent(AccountDO::getUpdTime, reqVO.getUpdTime())
                .eqIfPresent(AccountDO::getVersion, reqVO.getVersion())
                .orderByDesc(AccountDO::getCode));
    }

    default AccountDO getByOwnerId(String parentAcctCode, String ownerId) {
        return selectOne(new LambdaQueryWrapperX<AccountDO>()
                .eq(AccountDO::getParentCode,parentAcctCode)
                .eq(AccountDO::getOwnerId,ownerId));
    }

    /**
     * 冻结金额
     * @param acctCode 账户code
     * @param amt  金额
     * @param frozenAmt 冻结金额
     * @return
     */
    int changeBalance(@Param("acctCode") String acctCode, @Param("amt") Long amt, @Param("frozenAmt") Long frozenAmt);

}
