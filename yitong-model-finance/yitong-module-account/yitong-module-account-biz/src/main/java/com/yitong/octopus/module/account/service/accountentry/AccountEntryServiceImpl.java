package com.yitong.octopus.module.account.service.accountentry;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.yitong.octopus.module.account.controller.admin.accountentry.vo.*;
import com.yitong.octopus.module.account.dal.dataobject.accountentry.AccountEntryDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.account.convert.accountentry.AccountEntryConvert;
import com.yitong.octopus.module.account.dal.mysql.accountentry.AccountEntryMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.account.enums.ErrorCodeConstants.*;

/**
 * 账户记账流水 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AccountEntryServiceImpl implements AccountEntryService {

    @Resource
    private AccountEntryMapper entryMapper;

    @Override
    public Long createEntry(AccountEntryCreateReqVO createReqVO) {
        // 插入
        AccountEntryDO entry = AccountEntryConvert.INSTANCE.convert(createReqVO);
        entryMapper.insert(entry);
        // 返回
        return entry.getId();
    }

    @Override
    public void updateEntry(AccountEntryUpdateReqVO updateReqVO) {
        // 校验存在
        validateEntryExists(updateReqVO.getId());
        // 更新
        AccountEntryDO updateObj = AccountEntryConvert.INSTANCE.convert(updateReqVO);
        entryMapper.updateById(updateObj);
    }

    @Override
    public void deleteEntry(Long id) {
        // 校验存在
        validateEntryExists(id);
        // 删除
        entryMapper.deleteById(id);
    }

    private void validateEntryExists(Long id) {
        if (entryMapper.selectById(id) == null) {
            throw exception(ENTRY_NOT_EXISTS);
        }
    }

    @Override
    public AccountEntryDO getEntry(Long id) {
        return entryMapper.selectById(id);
    }

    @Override
    public List<AccountEntryDO> getEntryList(Collection<Long> ids) {
        return entryMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<AccountEntryDO> getEntryPage(AccountEntryPageReqVO pageReqVO) {
        return entryMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AccountEntryDO> getEntryList(AccountEntryExportReqVO exportReqVO) {
        return entryMapper.selectList(exportReqVO);
    }

    @Override
    public List<AccountEntryDO> findByTradeNo(String tradeNo) {
        return getEntryList(new AccountEntryExportReqVO().setTradeNo(tradeNo));
    }

}
