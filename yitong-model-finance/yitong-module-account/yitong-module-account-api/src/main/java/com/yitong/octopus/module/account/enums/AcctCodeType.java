package com.yitong.octopus.module.account.enums;

/**
 * 
 * 
 * <AUTHOR>
 *
 */
public enum AcctCodeType {

	//@formatter:off
	ACCTCODE_TOBE_SETTLED("********", "应付待结算"),
	ACCTCODE_SETTLED("********", "应付可结算"),
	ACCTCODE_WITHDRAWED("999901", "实付"),
	ACCTCODE_RECEIVABLE("112201", "应收账款"),
	;
	//@formatter:on
	private String code;

	private String desc;

	private AcctCodeType(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public String code() {
		return code;
	}

	public String desc() {
		return desc;
	}

	public static AcctCodeType resolveByCode(String code) {
		if (code != null) {
			for (AcctCodeType rtnCd : values()) {
				if (rtnCd.code().equals(code)) {
					return rtnCd;
				}
			}
		}
		return null;
	}

	/**
	 * // 服务商/商户 应付待结算，可提现, "已提现"
	 * @return
	 */
	public static String[] getCustomerAccounts(){
		return new String[]{ACCTCODE_TOBE_SETTLED.code(),ACCTCODE_SETTLED.code(),ACCTCODE_WITHDRAWED.code()};
	}

	/**
	 * 渠道：应收账款
	 * @return
	 */
	public static String[] getChannelAccounts(){
		return new String[]{ACCTCODE_RECEIVABLE.code()};
	}

}
