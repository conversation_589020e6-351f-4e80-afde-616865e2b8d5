package com.yitong.octopus.module.account.enums;

/**
 * 
 * 
 * <AUTHOR>
 *
 */
public enum AcctOwnerType {

	//@formatter:off
	AGENT("01", "服务商"),
	MERCHANT("02", "商户"),
	CHANNEL("03", "渠道商"),
	;
	//@formatter:on
	
	public final static String AGENT_CODE = "01";
	public final static String MERCHANT_CODE = "02";
	public final static String CHANNEL_CODE = "03";
	public final String code;

	public final String desc;

	private AcctOwnerType(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public String code() {
		return code;
	}

	public String desc() {
		return desc;
	}

	public static AcctOwnerType resolveByCode(String code) {
		if (code != null) {
			for (AcctOwnerType rtnCd : values()) {
				if (rtnCd.code().equals(code)) {
					return rtnCd;
				}
			}
		}
		return null;
	}

}
