package com.yitong.octopus.module.account.enums;

/**
 * 
 * 
 * <AUTHOR>
 *
 */
public enum AcctTemplate{

	//@formatter:off
	ACCT_TOBE_SETTLED("********", "待结算账户"),
	ACCT_SETTLED("********", "余额账户"),
	ACCT_WITHDRAWED("999901", "已提现账户"),
	ACCT_RECEIVABLE("112201", "渠道应收账款"),
	ACCT_BANK_DEPOSITS("100201", "备付金账户"),
	;
	//@formatter:on
	private String code;

	private String desc;

	private AcctTemplate(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public String code() {
		return code;
	}

	public String desc() {
		return desc;
	}

	public static AcctTemplate resolveByCode(String code) {
		if (code != null) {
			for (AcctTemplate rtnCd : values()) {
				if (rtnCd.code().equals(code)) {
					return rtnCd;
				}
			}
		}
		return null;
	}

}
