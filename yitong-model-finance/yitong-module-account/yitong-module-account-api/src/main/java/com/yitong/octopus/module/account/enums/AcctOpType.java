package com.yitong.octopus.module.account.enums;

/**
 * 
 * 
 * <AUTHOR>
 *
 */
public enum AcctOpType {

	//@formatter:off
	IN(1, "入"),
	OUT(-1, "出"),
	;
	//@formatter:on
	private int code;

	private String desc;

	private AcctOpType(int code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public Integer code() {
		return code;
	}

	public String desc() {
		return desc;
	}

	public static AcctOpType resolveByCode(Integer code) {
		if (code != null) {
			for (AcctOpType rtnCd : values()) {
				if (rtnCd.code().intValue() == code) {
					return rtnCd;
				}
			}
		}
		return null;
	}

}
