package com.yitong.octopus.module.account.enums;

import com.yitong.octopus.framework.common.exception.ErrorCode;

/**
 * <AUTHOR>
 */
public interface ErrorCodeConstants {

    ErrorCode ACC_NOT_EXISTS = new ErrorCode(**********, "账户不存在");
    ErrorCode NOT_ENOUGH_BALANCE = new ErrorCode(**********, "账户余额不足");
    ErrorCode ACCT_OPERATE_FAIL = new ErrorCode(**********, "账户操作异常");
    ErrorCode ENTRY_NOT_EXISTS = new ErrorCode(**********, "账户记账流水不存在");

}

