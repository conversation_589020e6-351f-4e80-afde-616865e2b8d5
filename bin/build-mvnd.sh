#!/bin/bash
set -e

# Maven Daemon 构建脚本
# 用于替代传统的 mvn 命令，提供更快的构建速度

echo "[build] 使用 Maven Daemon (mvnd) 进行构建"

# 尝试查找 mvnd
MVND_CMD=""
if command -v mvnd &> /dev/null; then
    MVND_CMD="mvnd"
elif [ -x "$HOME/.sdkman/candidates/mvnd/current/bin/mvnd" ]; then
    MVND_CMD="$HOME/.sdkman/candidates/mvnd/current/bin/mvnd"
else
    echo "[error] mvnd 未安装！请先安装 mvnd"
    echo "[info] 安装方法："
    echo "  - macOS: brew install mvndaemon/tap/mvnd"
    echo "  - SDKMAN: sdk install mvnd"
    echo "  - Linux/Windows: 从 https://github.com/apache/maven-mvnd/releases 下载"
    exit 1
fi

# 显示 mvnd 版本
echo "[info] mvnd 版本: $($MVND_CMD --version | head -n 1)"

# 构建参数
PROFILES=${1:-""}
SKIP_TESTS=${2:-"true"}

# 构建命令
BUILD_CMD="$MVND_CMD clean package"

# 添加跳过测试参数
if [ "$SKIP_TESTS" == "true" ]; then
    BUILD_CMD="$BUILD_CMD -DskipTests"
fi

# 添加 profile 参数
if [ -n "$PROFILES" ]; then
    BUILD_CMD="$BUILD_CMD -P$PROFILES"
fi

# 执行构建
echo "[build] 执行命令: $BUILD_CMD"
$BUILD_CMD

# 构建成功
if [ $? -eq 0 ]; then
    echo "[build] 构建成功！"
    echo "[info] JAR 文件位置: yitong-server/target/yitong-server.jar"
else
    echo "[error] 构建失败！"
    exit 1
fi