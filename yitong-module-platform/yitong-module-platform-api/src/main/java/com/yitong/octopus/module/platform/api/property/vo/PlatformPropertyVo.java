package com.yitong.octopus.module.platform.api.property.vo;

import lombok.*;

import java.util.List;

/**
 * 平台属性
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlatformPropertyVo {
    /**
     * 主键
     */
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;

    /**
     * 属性值
     */
    private List<PlatformPropertyValueVo> values;

}
