package com.yitong.octopus.module.platform.api.property;

import com.yitong.octopus.module.platform.api.property.dto.PlatformPropertyValueDto;
import com.yitong.octopus.module.platform.api.property.vo.PlatformPropertyValueVo;
import com.yitong.octopus.module.platform.api.property.vo.PlatformPropertyVo;
import com.yitong.octopus.module.platform.api.tag.vo.PlatformTagGroupVo;
import com.yitong.octopus.module.platform.api.tag.vo.PlatformTagVo;

import java.util.Collection;
import java.util.List;

/**
 * 平台属性接口
 */
public interface PlatformPropertyApi {

    /**
     * 根据属性项的编号的集合，获得对应的属性项数组
     *
     * @param ids 属性项的编号的集合
     * @return 属性项数组
     */
    List<PlatformPropertyVo> getPropertyList(Collection<Long> ids);

    /**
     * 根据属性项编号数组，获得属性值列表
     *
     * @param propertyIds 属性项目编号数组
     * @return 属性值列表
     */
    List<PlatformPropertyValueVo> getPropertyValueListByPropertyId(Collection<Long> propertyIds);

    /**
     * 校验属性和属性值
     * @param properties
     */
    void checkProperties(List<PlatformPropertyValueDto> properties);

    /**
     * 根据属性值Id获取属性信息
     * @param id 属性值Id
     * @return
     */
    PlatformPropertyValueVo getPlatformPropertyById(Long id);


    /**
     * 根据属性值Id列表返回属性及属性值
     * @param ids 属性值Id
     * @param propertyId 属性ID
     * @return
     */
    List<PlatformPropertyVo> getPropertyValueListByValueIdPropertyId(List<Long> ids, Long propertyId);
}
