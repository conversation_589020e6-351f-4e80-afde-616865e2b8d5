package com.yitong.octopus.module.platform.api.utils.jackson;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.yitong.octopus.module.platform.api.tag.PlatformTagApi;
import com.yitong.octopus.module.platform.api.tag.vo.PlatformTagVo;

import java.io.IOException;
import java.util.List;

public class PlatformTageListJsonSerializer extends JsonSerializer<List<Long>> {

    @Override
    public void serialize(List<Long> tagIds, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (CollectionUtil.isNotEmpty(tagIds)) {
            jsonGenerator.writeStartArray();
            for (Long tag: tagIds) {
                jsonGenerator.writeString(String.valueOf(tag));
            }
            jsonGenerator.writeEndArray();
            List<PlatformTagVo> tagList = SpringUtil.getBean(PlatformTagApi.class).getTagByIds(tagIds);
            if (CollectionUtil.isNotEmpty(tagList)) {
                jsonGenerator.writeFieldName("tagList");
                jsonGenerator.writeStartArray();
                for (PlatformTagVo tag: tagList) {
                    jsonGenerator.writeObject(tag);
                }
                jsonGenerator.writeEndArray();
            }
        }else {
            jsonGenerator.writeNull();
        }
    }
}
