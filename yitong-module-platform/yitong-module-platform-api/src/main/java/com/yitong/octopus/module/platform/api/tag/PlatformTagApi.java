package com.yitong.octopus.module.platform.api.tag;

import com.yitong.octopus.module.platform.api.tag.vo.PlatformTagGroupVo;
import com.yitong.octopus.module.platform.api.tag.vo.PlatformTagVo;

import java.util.List;

/**
 * 平台品牌接口
 */
public interface PlatformTagApi {

    /**
     * 根据标签Id获取标签信息
     * @param id 标签Id
     * @return
     */
    PlatformTagVo getTagById(Long id);

    /**
     * 根据标签ID列表查询标签
     * @param ids 品牌列表
     * @return
     */
    List<PlatformTagVo> getTagByIds(List<Long> ids);

    /**
     * 根据标签列表返回标签组及其标签
     * @param ids 标签Id
     * @param groupId 分组ID
     * @return
     */
    List<PlatformTagGroupVo> getTagGroupByIds(List<Long> ids, Long groupId);

}
