package com.yitong.octopus.module.platform.api.utils.mybatis;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.yitong.octopus.module.platform.api.property.dto.PlatformPropertyValueDto;

import java.lang.reflect.Field;
import java.util.List;

/**
 * 自定义Rule转换器
 */
public class JsonPlatformPropertyValueDtoTypeHandler extends AbstractJsonTypeHandler<List<PlatformPropertyValueDto>> {
    public JsonPlatformPropertyValueDtoTypeHandler(Class<?> type) {
        super(type);
    }

    public JsonPlatformPropertyValueDtoTypeHandler(Class<?> type, Field field) {
        super(type, field);
    }

    @Override
    public List<PlatformPropertyValueDto> parse(String json) {
        return JSONUtil.toList(json,PlatformPropertyValueDto.class);
    }

    @Override
    public String toJson(List<PlatformPropertyValueDto> obj) {
        return JSONUtil.toJsonStr(obj);
    }
}
