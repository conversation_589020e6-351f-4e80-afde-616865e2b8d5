package com.yitong.octopus.module.platform.enums;

import com.yitong.octopus.framework.common.exception.ErrorCode;

public interface ErrorCodeConstants {
    ErrorCode BILL_ITEM_NOT_EXISTS = new ErrorCode(1013100001, "平台结算项不存在");
    ErrorCode BRAND_NOT_EXISTS = new ErrorCode(1013200001, "平台品牌不存在");

    // ========== 商品分类相关 1013100000 ============
    ErrorCode CATEGORY_NOT_EXISTS = new ErrorCode(1013300000, "商品分类不存在");
    ErrorCode CATEGORY_PARENT_NOT_EXISTS = new ErrorCode(1013300001, "父分类不存在");
    ErrorCode CATEGORY_PARENT_NOT_FIRST_LEVEL = new ErrorCode(1013300002, "父分类不能是二级分类");
    ErrorCode CATEGORY_EXISTS_CHILDREN = new ErrorCode(1013300003, "存在子分类，无法删除");
    ErrorCode CATEGORY_DISABLED = new ErrorCode(1*********, "商品分类({})已禁用，无法使用");

    // ========== 平台地图渠道相关 ********** ============
    ErrorCode MAP_CHANNEL_NOT_EXISTS = new ErrorCode(**********, "平台地图渠道不存在");

    ErrorCode LABEL_NOT_EXISTS = new ErrorCode(**********, "平台标签不存在");

    ErrorCode PARAMETERS_NOT_EXISTS = new ErrorCode(**********, "平台属性不存在");

    ErrorCode PARAMETER_GROUP_NOT_EXISTS = new ErrorCode(**********, "平台属性组不存在");

    ErrorCode BANK_NOT_EXISTS = new ErrorCode(**********, "平台商业银行列不存在");

    ErrorCode BANK_EXISTS = new ErrorCode(**********, "平台商业银行列已存在");

    ErrorCode TOPIC_NOT_EXISTS = new ErrorCode(**********, "话题不存在");

    ErrorCode PROPERTY_VALUE_EXISTS = new ErrorCode(**********, "属性值已存在");
    ErrorCode PROPERTY_VALUE_NOT_EXISTS = new ErrorCode(**********, "属性值不存在");
    ErrorCode PROPERTY_EXISTS = new ErrorCode(**********, "属性已存在");
    ErrorCode PROPERTY_NOT_EXISTS = new ErrorCode(**********, "属性不存在");
    ErrorCode PROPERTY_DELETE_FAIL_VALUE_EXISTS = new ErrorCode(**********, "删除属性失败，已经存在属性值");
    ErrorCode PROPERTIES_DUPLICATED = new ErrorCode(1_008_006_001, "属性组合存在重复");
    ErrorCode PROPERTIES_SELECT_DELETED = new ErrorCode(1_008_006_002, "当前属性值中存在已删除的属性值");

    ErrorCode TAG_GROUP_NOT_EXISTS = new ErrorCode(**********, "标签组不存在");
    ErrorCode TAG_GROUP_NAME_EXISTS = new ErrorCode(**********, "标签组名称不可重复");
    ErrorCode TAG_GROUP_SAME = new ErrorCode(**********, "标签组名称与标签名不可重复");
    ErrorCode TAG_NOT_EXISTS = new ErrorCode(1016000001, "标签不存在");
    ErrorCode TAG_EXISTS = new ErrorCode(1016000002, "标签名称不可重复");
    ErrorCode TAG_SELECT_DELETED = new ErrorCode(1016000003, "当前标签中存在已删除的标签");
    ErrorCode TAG_GROUP_CONTAINS_TAGS = new ErrorCode(1016000004, "当前标签组存在标签，请先删除标签");
}

