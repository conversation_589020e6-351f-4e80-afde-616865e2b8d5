package com.yitong.octopus.module.platform.api.brand;

import com.yitong.octopus.module.platform.api.brand.vo.PlatformBrandVo;

import java.util.List;

/**
 * 平台品牌接口
 */
public interface PlatformBrandApi {

    /**
     * 根据话题ID查询话题详情
     * @param brandId 品牌ID
     * @return
     */
    PlatformBrandVo getPlatformBrandByBrandId(Long brandId);

    /**
     * 根据话题ID列表查询话题
     * @param brandIds 品牌列表
     * @return
     */
    List<PlatformBrandVo> getPlatformBrandByTopicIds(List<Long> brandIds);

}
