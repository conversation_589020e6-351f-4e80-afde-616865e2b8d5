package com.yitong.octopus.module.platform.enums;

import com.yitong.octopus.framework.common.core.EnumKeyArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 标签分组类型(1:商家标签;2:商品签)
 */
@Getter
@AllArgsConstructor
public enum PlatformTagGroupTypeEnum implements EnumKeyArrayValuable {

    SP(1, "商家标签"),
    SP_GOODS(2, "商品标签"),
    SP_MATERIALS(3, "商家素材标签");

    public static final Object[] ARRAYS = Arrays.stream(values()).map(PlatformTagGroupTypeEnum::getType).toArray();

    private final Integer type;
    private final String name;

    @Override
    public Object[] array() {
        return ARRAYS;
    }

}
