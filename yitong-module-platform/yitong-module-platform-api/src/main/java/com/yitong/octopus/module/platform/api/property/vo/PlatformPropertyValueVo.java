package com.yitong.octopus.module.platform.api.property.vo;

import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlatformPropertyValueVo {
    /**
     * 主键
     */
    private Long id;
    /**
     * 属性项的编号
     *
     * 关联 {@link PlatformPropertyVo#getId()}
     */
    @NotNull(message = "属性不能为空")
    private Long propertyId;
    /**
     * 名称
     */
    @NotEmpty(message = "属性值名称不能为空")
    private String name;
    /**
     * 备注
     *
     */
    private String remark;
}
