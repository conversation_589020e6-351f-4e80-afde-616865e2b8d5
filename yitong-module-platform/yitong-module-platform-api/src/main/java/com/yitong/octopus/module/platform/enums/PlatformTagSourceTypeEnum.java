package com.yitong.octopus.module.platform.enums;

import com.yitong.octopus.framework.common.core.EnumKeyArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 标签来源(1:应用自建;2:企业微信后台建立)
 */
@Getter
@AllArgsConstructor
public enum PlatformTagSourceTypeEnum implements EnumKeyArrayValuable {

    APP(1, "应用自建"),
    WX_QW(2, "企业微信后台建立");

    public static final Object[] ARRAYS = Arrays.stream(values()).map(PlatformTagSourceTypeEnum::getType).toArray();

    private final Integer type;
    private final String name;

    @Override
    public Object[] array() {
        return ARRAYS;
    }

}
