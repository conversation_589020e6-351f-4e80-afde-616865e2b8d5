package com.yitong.octopus.module.platform.api.topic;

import com.yitong.octopus.module.platform.api.topic.vo.PlatformTopicVo;

import java.util.List;

/**
 * 平台话题接口
 */
public interface PlatformTopicApi {

    /**
     * 根据话题ID查询话题详情
     * @param platfrom 平台
     * @param topicId 话题ID
     * @return
     */
    PlatformTopicVo getPlatformTopicVoByTopicId(String platfrom,String topicId);

    /**
     * 根据话题ID列表查询话题
     * @param platfrom 平台
     * @param topicIds 话题列表
     * @return
     */
    List<PlatformTopicVo> getPlatformTopicVoByTopicIds(String platfrom,List<String> topicIds);

}
