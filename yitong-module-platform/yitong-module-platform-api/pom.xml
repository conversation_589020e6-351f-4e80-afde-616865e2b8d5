<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yitong.octopus</groupId>
        <artifactId>yitong-module-platform</artifactId>
        <version>${revision}</version> <!-- 1. 修改 version 为 ${revision} -->
    </parent>

    <artifactId>yitong-module-platform-api</artifactId>
    <packaging>jar</packaging> <!-- 2. 新增 packaging 为 jar -->
    <name>${project.artifactId}</name> <!-- 3. 新增 name 为 ${project.artifactId} -->
    <description> <!-- 4. 新增 description 为该模块的描述 -->
        服务商相关服务
    </description>
    <dependencies>  <!-- 5. 新增 yitong-common 依赖 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

    </dependencies>
</project>