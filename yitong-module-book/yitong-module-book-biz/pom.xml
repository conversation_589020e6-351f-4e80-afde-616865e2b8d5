<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yitong.octopus</groupId>
        <artifactId>yitong-module-book</artifactId>
        <version>${revision}</version> <!-- 1. 修改 version 为 ${revision} -->
    </parent>
    <packaging>jar</packaging> <!-- 2. 新增 packaging 为 jar -->

    <artifactId>yitong-module-book-biz</artifactId>

    <name>${project.artifactId}</name> <!-- 3. 新增 name 为 ${project.artifactId} -->
    <description> <!-- 4. 新增 description 为该模块的描述 -->
        book详细模块
    </description>

    <dependencies>  <!-- 5. 新增依赖，这里引入的都是比较常用的业务组件、技术组件 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-module-book-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-module-platform-biz</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-module-sp-biz</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-module-system-biz</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-module-trade-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-module-channel-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
<!--        <dependency>-->
<!--            <groupId>com.yitong.octopus</groupId>-->
<!--            <artifactId>yitong-spring-boot-starter-biz-operatelog</artifactId>-->
<!--        </dependency>-->

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-biz-tenant</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-test</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>


    </dependencies>
</project>