
create table  book_config
(
    id                            bigint  comment '主键'
        primary key,
    plan_name                     varchar(300)                       not null comment '方案名称',
    state                      tinyint                            not null comment '状态（1.启用、2.禁用）',
    book_examine                  tinyint                            not null comment '预约审核（1 提交预约后,自动审核通过,并占用库存、2 提交预约后,占用库存资源,订单需审核,审核通过代表预约成功）',
    initiate_reservation          tinyint                            null comment '发起预约配置参数（1 无限制、2 需要提前N 小时、3  需要提前N 天）',
    initiate_reservation_specific int                                null comment '发起预约具体配置参数（小时或天数）',
    cancel_reservation            tinyint                            null comment '取消预约（1 无限制、2 需要提前N 小时、3  需要提前N 天）',
    cancel_reservation_specific   int                                null comment '取消预约具体配置参数（小时或天数）',
    change_reservation            tinyint                            null comment '更改预约（1 无限制、2 需要提前N 小时、3  需要提前N 天、 0不允许更改）',
    change_reservation_specific   int                                null comment '更改预约具体参数（小时或天数）',
    reservation_term              tinyint                            null comment '预约项（1 可多选,最少选一个,最多选N个 、0 仅可单选）',
    reservation_usable            tinyint                            null comment '预约日期（0 全部日期可用、1 指定日期可用）',
    reservation_usable_date       varchar(2000)                      null comment '预约可用具体日期',
    reservation_unable            varchar(100)                       null comment '不可预约日期（1 每周不可用、2 节假日不可以、3指定某天不可用）',
    reservation_unable_date       varchar(1000)                      null comment '不可预约具体日期',
    reservation_unable_week       varchar(200)                       null comment '每周不可用(具体周几)',
    reservation_unable_festival   varchar(500)                       null comment '不可使用具体节假日',
    calendar_start_date           datetime                           null comment '日历开始日期',
    calendar_end_date             datetime                           null comment '日历结束日期',
    reservation_time              tinyint                            null comment '预约时段（0 不分时间段、 1 指定时间段）',
    reservation_time_state        int                                null comment '预约时段限制是否可多选（0 仅单选、1可多选【以数量代表可选数量】）',
    reservation_num               int                                null comment '预约次数限制（下单人手机号控制 0 无限制、同一日期内根据数量限制）',
    serve_staff                   int                                null comment '服务人员',
    serve_term                    int                                null comment '服务项',
    serve_site                    int                                null comment '服务场地',
    prize                         tinyint                            null comment '奖品是否有（0 没有、1 有）',
    pe_id                         bigint                             null comment '奖品id',
    prize_limitation              tinyint                            null comment '奖品限制（0 无限制、1 限制数）',
    prize_num                     int                                null comment '奖品数量',
    create_time                   datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time                   datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    creator                       varchar(64)                        null,
    updater                       varchar(64)                        null,
    deleted                       bit      default b'0'              not null comment '是否删除',
    tenant_id                     bigint   default 0                 not null comment '租户编号',
    reservation_type              bigint                             null comment '预约时段类型 1.预约项目、2.预约时间段'
);

create table book_group
(
    id            bigint auto_increment comment '主键'
        primary key,
    bc_id         bigint                             not null comment '预约方案id',
    st_id bigint                             null comment '服务项id',
    group_name    varchar(500)                       not null comment '组局名称',
    create_time   datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time   datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    creator       varchar(64)                        null,
    updater       varchar(64)                        null,
    deleted       bit      default b'0'              not null comment '是否删除',
    tenant_id     bigint   default 0                 not null comment '租户编号'
);

create table book_time_config
(
    id                              bigint  comment '主键'
        primary key,
    bc_id                           bigint                             not null comment '预约方案id',
    gp_id                           bigint                             not null comment '预约组配置id',
    now_date                        datetime                           not null comment '日期排版',
    time_paragraph                  varchar(100)                       null comment '时间段',
    reservation_time_interval       tinyint                            null comment '预约时段 时间间隔 （1小时、30分钟、15分钟、10分钟）',
    reservation_time_interval_split varchar(50)                        null comment '拆分具体时间 ',
    order_num                       int                                not null comment '序号',
    total_num                       int                                not null comment '总人数',
    surplus_num                     int                                not null comment '剩余预约人数',
    create_time                     datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time                     datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    creator                         varchar(64)                        null,
    updater                         varchar(64)                        null,
    deleted                         bit      default b'0'              not null comment '是否删除',
    tenant_id                       bigint   default 0                 not null comment '租户编号'
);

create table book_user_list
(
    id                bigint auto_increment comment '主键'
        primary key,
    sp_id             bigint                             not null comment '商家Id',
    store_id          bigint                             not null comment '商家门店Id',
    spu_id            bigint                             not null comment '商品Id',
    tc_id             bigint                             null comment '预约时间配置表Id',
    member_id         bigint                             not null comment '用户的唯一标识',
    serve_staff       varchar(1024)                      null comment '服务人员',
    serve_term_id     bigint                             not null comment '服务项',
    reservation_date  datetime                           not null comment '预约日期',
    user_name         varchar(1024)                      not null comment '用户名',
    reservation_num   int                                not null comment '预约人数',
    reservation_phone varchar(100)                       not null comment '预约电话',
    reservation_type  tinyint                            not null comment '预约类型（1.用户预约、 2.代用户预约）',
    reservation_state tinyint                            not null comment '预约状态（1.待确认、2.已确认、3.用户取消、4.商家取消、5.已完成）',
    confirm_date      datetime                           null comment '确认时间',
    confirm_people    varchar(100)                       null comment '确认人',
    cancel_date       datetime                           null comment '取消时间',
    cancel_people     varchar(100)                       null comment '取消人',
    cancel_reason     tinyint                            null comment '取消原因（1.更改时间、2.不想约了、3.计划有变、4.其他）',
    cancel_describe   varchar(2000)                      null comment '取消描述',
    create_time       datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time       datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    creator           varchar(64)                        null,
    updater           varchar(64)                        null,
    deleted           bit      default b'0'              not null comment '是否删除',
    tenant_id         bigint   default 0                 not null comment '租户编号',
    pe_id             bigint                             null comment '奖品Id'
);

create table book_site
(
    id           bigint auto_increment comment '主键'
        primary key,
    site_name    varchar(300)                       not null comment '场地名',
    site_address varchar(300)                       null comment '场地地址',
    create_time  datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time  datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    creator      varchar(64)                        null,
    updater      varchar(64)                        null,
    deleted      bit      default b'0'              not null comment '是否删除',
    tenant_id    bigint   default 0                 not null comment '租户编号'
);

create table book_staff
(
    id             bigint auto_increment comment '主键'
        primary key,
    staff_name     varchar(300)                       not null comment '人员名',
    staff_phone    varchar(300)                       null comment '人员电话',
    staff_sex      tinyint                            null comment '性别（1.男、 2.女）',
    staff_address  varchar(300)                       null comment '人员住址',
    staff_document varchar(300)                       null comment '人员证件',
    create_time    datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time    datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    creator        varchar(64)                        null,
    updater        varchar(64)                        null,
    deleted        bit      default b'0'              not null comment '是否删除',
    tenant_id      bigint   default 0                 not null comment '租户编号'
);

create table book_serve
(
    id          bigint auto_increment comment '主键'
        primary key,
    serve_name  varchar(300)                       not null comment '服务项名',
    create_time datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    creator     varchar(64)                        null,
    updater     varchar(64)                        null,
    deleted     bit      default b'0'              not null comment '是否删除',
    tenant_id   bigint   default 0                 not null comment '租户编号'
);

create table book_prize
(
    id                  bigint auto_increment comment '主键'
        primary key,
    prize_name          varchar(300)                       not null comment '奖品名',
    prize_market_amount int                                null comment '奖品市场价格',
    prize_sale_amount   int                                null comment '奖品售价格',
    prize_image         varchar(500)                       null comment '奖品图像',
    create_time         datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time         datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    creator             varchar(64)                        null,
    updater             varchar(64)                        null,
    deleted             bit      default b'0'              not null comment '是否删除',
    tenant_id           bigint   default 0                 not null comment '租户编号'
);


create table book_join_serve
(
    id          bigint auto_increment comment '主键'
        primary key,
    bc_id       bigint                             null comment '预约配置项id',
    serve_id    bigint                             null comment '(服务人员、服务项、服务场地) id',
    serve_type  tinyint                            null comment '类型 (1.服务人员、2.服务项、3.服务场地)',
    create_time datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    creator     varchar(64)                        null,
    updater     varchar(64)                        null,
    deleted     bit      default b'0'              not null comment '是否删除',
    tenant_id   bigint   default 0                 not null comment '租户编号'
);


create table book_join_spu
(
    id          bigint auto_increment comment '主键'
        primary key,
    bc_id       bigint                             not null comment '预约方案Id',
    spu_id      bigint                             not null comment '商品id',
    status      tinyint                            not null comment '是否指定门店 【1.开启、2.关闭】',
    create_time datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    creator     varchar(64)                        null,
    updater     varchar(64)                        null,
    deleted     bit      default b'0'              not null comment '是否删除',
    tenant_id   bigint   default 0                 not null comment '租户编号'
);


create table book_spu_store
(
    id          bigint auto_increment comment '主键'
        primary key,
    js_id       bigint                             not null comment '商品关联预约方案表 id',
    store_id    bigint                             null comment '商家门店Id',
    create_time datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    creator     varchar(64)                        null,
    updater     varchar(64)                        null,
    deleted     bit      default b'0'              not null comment '是否删除',
    tenant_id   bigint   default 0                 not null comment '租户编号'
);
