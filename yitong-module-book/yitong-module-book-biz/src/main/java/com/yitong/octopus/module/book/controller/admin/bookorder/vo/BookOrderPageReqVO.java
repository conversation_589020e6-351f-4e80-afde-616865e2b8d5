package com.yitong.octopus.module.book.controller.admin.bookorder.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 用户预约订单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BookOrderPageReqVO extends PageParam {

    @Schema(description = "状态", example = "26764")
    private Integer status;

    @Schema(description = "应用Id", example = "31056")
    private Long appId;

    @Schema(description = "商家Id", example = "31056")
    private Long spId;

    @Schema(description = "商家门店Id", example = "26764")
    private Long storeId;

    @Schema(description = "商品Id", example = "29542")
    private Long spuId;

    @Schema(description = "预约配置表Id", example = "16281")
    private Long bookConfigId;

    @Schema(description = "奖品id", example = "25858")
    private Long prizeId;

    @Schema(description = "会员Id")
    private Long memberId;

    @Schema(description = "预约日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] bookDate;

    @Schema(description = "取消描述")
    private String cancelDescribe;

}