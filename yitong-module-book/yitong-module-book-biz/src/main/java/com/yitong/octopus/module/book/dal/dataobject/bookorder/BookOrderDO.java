package com.yitong.octopus.module.book.dal.dataobject.bookorder;

import com.github.yulichang.annotation.EntityMapping;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;
import lombok.experimental.FieldNameConstants;

/**
 * 用户预约订单 DO
 *
 * <AUTHOR>
 */
@FieldNameConstants
@TableName("yt_book_order")
@KeySequence("yt_book_order_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BookOrderDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 应用id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long appId;
    /**
     * 商家Id
     */
    private Long spId;
    /**
     * 商家门店Id
     */
    private Long storeId;
    /**
     * 商品Id
     */
    private Long spuId;
    /**
     * 预约配置表Id
     */
    private Long bookConfigId;
    /**
     * 奖品id
     */
    private Long prizeId;
    /**
     * 用户的唯一标识
     */
    private Long memberId;

    /**
     * 预约日期
     */
    private String bookDate;
    /**
     * 预约人数
     */
    private Integer reservationNum;
    /**
     * 预约类型（1.用户预约、 2.代用户预约）
     */
    private Integer reservationType;
    /**
     * 预约状态（1.待确认、2.已确认、3.用户取消、4.商家取消、5.已完成）
     */
    private Integer status;
    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;
    /**
     * 确认人
     */
    private String confirmPeople;
    /**
     * 取消时间
     */
    private LocalDateTime cancelTime;
    /**
     * 取消原因（1.更改时间、2.不想约了、3.计划有变、4.其他）
     */
    private Integer cancelReason;
    /**
     * 取消描述
     */
    private String cancelDescribe;
    /**
     * 核销时间
     */
    private LocalDateTime redeemTime;
    /**
     * 商家门店Id
     */
    private Long redeemStoreId;
    /**
     * 商家Id
     */
    private Long redeemSpId;
    /**
     * 预约用户信息
     */
    @TableField(exist = false)
    @EntityMapping(thisField = Fields.id, joinField = BookOrderMemberDO.Fields.bookOrderId)
    private List<BookOrderMemberDO> bookMemberList;

}