package com.yitong.octopus.module.book.service.bookconfig;

import com.yitong.octopus.module.book.controller.admin.bookconfig.vo.BookConfigSpStorePageReqVO;
import com.yitong.octopus.module.book.controller.admin.bookconfig.vo.BookConfigSpStoreSaveReqVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yitong.octopus.module.book.dal.dataobject.bookconfig.BookConfigSpStoreDO;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;

import com.yitong.octopus.module.book.dal.mysql.bookconfig.BookConfigSpStoreMapper;

/**
 * 预约商家与门店 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BookConfigSpStoreServiceImpl extends ServiceImpl<BookConfigSpStoreMapper,BookConfigSpStoreDO>  implements BookConfigSpStoreService {

    @Override
    public Long createConfigSpStore(BookConfigSpStoreSaveReqVO createReqVO) {
        // 插入
        BookConfigSpStoreDO configSpStore = BeanUtils.toBean(createReqVO, BookConfigSpStoreDO.class);
        getBaseMapper().insert(configSpStore);
        // 返回
        return configSpStore.getId();
    }

    @Override
    public void updateConfigSpStore(BookConfigSpStoreSaveReqVO updateReqVO) {
        // 更新
        BookConfigSpStoreDO updateObj = BeanUtils.toBean(updateReqVO, BookConfigSpStoreDO.class);
        getBaseMapper().updateById(updateObj);
    }

    @Override
    public void deleteConfigSpStore(Long id) {
        // 删除
        getBaseMapper().deleteById(id);
    }

    @Override
    public BookConfigSpStoreDO getConfigSpStore(Long id) {
        return getBaseMapper().selectById(id);
    }

    @Override
    public PageResult<BookConfigSpStoreDO> getConfigSpStorePage(BookConfigSpStorePageReqVO pageReqVO) {
        return getBaseMapper().selectPage(pageReqVO);
    }

}