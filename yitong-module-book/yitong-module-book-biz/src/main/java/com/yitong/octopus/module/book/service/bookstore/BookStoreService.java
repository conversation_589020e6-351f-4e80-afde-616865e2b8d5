package com.yitong.octopus.module.book.service.bookstore;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.module.book.controller.app.bookstore.vo.BookStorePageReqVO;
import com.yitong.octopus.module.book.controller.app.bookstore.vo.BookStoreRespVO;
import com.yitong.octopus.module.book.controller.app.bookstore.vo.BookStoreTopReqVO;
import org.w3c.dom.stylesheets.LinkStyle;

import java.util.List;

/**
 * 预约门店服务
 */
public interface BookStoreService {

    /**
     * 获取预约门店信息
     * @param pageReqVO
     * @return
     */
    PageResult<BookStoreRespVO> getBookStoreByPage(BookStorePageReqVO pageReqVO);


    /**
     * 获取预约门店信息,根据门店预约数量汇总，排序
     * @param reqVO
     * @return
     */
    List<BookStoreRespVO> getBookStoreTopList(BookStoreTopReqVO reqVO);
}
