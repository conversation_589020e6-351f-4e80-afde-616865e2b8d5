package com.yitong.octopus.module.book.dal.mysql.bookorder;

import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.book.dal.dataobject.bookorder.BookOrderMemberDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户预约订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BookOrderMemberMapper extends BaseMapperX<BookOrderMemberDO> {

    default List<BookOrderMemberDO> getBookOrderMemberByBookOrderId(Long bookOrderId){
        return this.selectList(BookOrderMemberDO::getBookOrderId,bookOrderId);
    }

}