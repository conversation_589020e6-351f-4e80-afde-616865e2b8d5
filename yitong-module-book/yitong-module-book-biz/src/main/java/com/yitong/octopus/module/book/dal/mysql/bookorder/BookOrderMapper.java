package com.yitong.octopus.module.book.dal.mysql.bookorder;

import com.github.yulichang.extension.mapping.config.DeepConfig;
import com.github.yulichang.extension.mapping.relation.Relation;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.book.dal.dataobject.bookorder.BookOrderDO;
import org.apache.ibatis.annotations.Mapper;
import com.yitong.octopus.module.book.controller.admin.bookorder.vo.*;

/**
 * 用户预约订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BookOrderMapper extends BaseMapperX<BookOrderDO> {

    default PageResult<BookOrderDO> selectPage(BookOrderPageReqVO reqVO) {
        PageResult<BookOrderDO> result = selectPage(reqVO, new LambdaQueryWrapperX<BookOrderDO>()
                .eqIfPresent(BookOrderDO::getAppId, reqVO.getAppId())
                .eqIfPresent(BookOrderDO::getMemberId, reqVO.getMemberId())
                .eqIfPresent(BookOrderDO::getStatus, reqVO.getStatus())
                .eqIfPresent(BookOrderDO::getSpId, reqVO.getSpId())
                .eqIfPresent(BookOrderDO::getStoreId, reqVO.getStoreId())
                .eqIfPresent(BookOrderDO::getSpuId, reqVO.getSpuId())
                .eqIfPresent(BookOrderDO::getBookConfigId, reqVO.getBookConfigId())
                .eqIfPresent(BookOrderDO::getPrizeId, reqVO.getPrizeId())
                .betweenIfPresent(BookOrderDO::getBookDate, reqVO.getBookDate())
                .eqIfPresent(BookOrderDO::getCancelDescribe, reqVO.getCancelDescribe())
                .orderByDesc(BookOrderDO::getId));
        Relation.mpjGetRelation(result.getList(), DeepConfig.defaultConfig());
        return result;
    }

}