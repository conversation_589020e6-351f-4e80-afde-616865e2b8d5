package com.yitong.octopus.module.book.controller.app.bookstore.vo;

import com.yitong.octopus.module.sp.api.store.vo.SpStoreLbsPageReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 用户预约订单热门 Request VO")
@Data
@ToString(callSuper = true)
public class BookStoreTopReqVO extends SpStoreLbsPageReqVO {

    /**
     * 数量
     */
    @Schema(description = "数量")
    private  Integer topSize = 5;
}