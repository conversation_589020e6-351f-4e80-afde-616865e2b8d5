package com.yitong.octopus.module.book.controller.admin.bookconfig.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yitong.octopus.module.sp.api.utils.jackson.SpStoreNameListJsonSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 预订配置详情 Response VO")
@Data
@ExcelIgnoreUnannotated
public class BookConfigInfoVO extends BookConfigPageRespVO {

    // ############# 归属商家和商品 ###########
    @Schema(description = "商品门店列表")
    @JsonSerialize(using = SpStoreNameListJsonSerializer.class)
    private List<Long> storeIds;
    // ############  归属商家和商品 ############
}