package com.yitong.octopus.module.book.service.bookorder;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yitong.octopus.module.book.controller.admin.bookorder.vo.*;
import com.yitong.octopus.module.book.dal.dataobject.bookorder.BookOrderDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import javax.validation.Valid;

/**
 * 用户预约订单 Service 接口
 *
 * <AUTHOR>
 */
public interface BookOrderService extends IService<BookOrderDO> {

    /**
     * 创建用户预约订单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createOrder(@Valid BookOrderSaveReqVO createReqVO);

    /**
     * 更新用户预约订单
     *
     * @param updateReqVO 更新信息
     */
    void updateOrder(@Valid BookOrderSaveReqVO updateReqVO);

    /**
     * 删除用户预约订单
     *
     * @param id 编号
     */
    void deleteOrder(Long id);

    /**
     * 获得用户预约订单
     *
     * @param id 编号
     * @return 用户预约订单
     */
    BookOrderDO getOrder(Long id);

    /**
     * 获得用户预约订单
     *
     * @param id 编号
     * @param appId 应用id
     * @return 用户预约订单
     */
    BookOrderDO getOrderInfoByIdAndAppId(Long id, Long appId);

    /**
     * 获得用户预约订单分页
     *
     * @param pageReqVO 分页查询
     * @return 用户预约订单分页
     */
    PageResult<BookOrderDO> getOrderPage(BookOrderPageReqVO pageReqVO);

    /**
     * 取消预约订单
     * @param reqVO 取消请求
     * @param appId 应用ID
     */
    void cancelBookOrder(@Valid BookOrderCancelReqVO reqVO, Long appId);

    /**
     * 获取有效的用户预约数量
     * @param appId
     * @return
     */
    Long getValidBookOrderCountByMember(Long appId);

}