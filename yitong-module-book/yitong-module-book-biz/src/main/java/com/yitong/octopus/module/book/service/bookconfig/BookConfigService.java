package com.yitong.octopus.module.book.service.bookconfig;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yitong.octopus.module.book.controller.admin.bookconfig.vo.BookConfigInfoVO;
import com.yitong.octopus.module.book.controller.admin.bookconfig.vo.BookConfigPageReqVO;
import com.yitong.octopus.module.book.controller.admin.bookconfig.vo.BookConfigPageRespVO;
import com.yitong.octopus.module.book.controller.admin.bookconfig.vo.BookConfigSaveReqVO;
import com.yitong.octopus.module.book.dal.dataobject.bookconfig.BookConfigDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import javax.validation.Valid;

/**
 * 预订配置 Service 接口
 *
 * <AUTHOR>
 */
public interface BookConfigService extends IService<BookConfigDO> {

    /**
     * 创建预订配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createConfig(@Valid BookConfigSaveReqVO createReqVO);

    /**
     * 更新预订配置
     *
     * @param updateReqVO 更新信息
     */
    void updateConfig(@Valid BookConfigSaveReqVO updateReqVO);

    /**
     * 删除预订配置
     *
     * @param id 编号
     */
    void deleteConfig(Long id);

    /**
     * 获得预订配置
     *
     * @param id 编号
     * @return 预订配置
     */
    BookConfigDO getConfig(Long id);

    /**
     * 获得预订配置
     *
     * @param id 编号
     * @return 预订配置
     */
    BookConfigDO getConfigByIdAndSpuId(Long id,Long spuId);

    /**
     * 获得预订配置
     *
     * @param id 编号
     * @return 预订配置
     */
    BookConfigInfoVO getConfigInfoVoById(Long id);

    /**
     * 获得预订配置分页
     *
     * @param pageReqVO 分页查询
     * @return 预订配置分页
     */
    PageResult<BookConfigDO> getConfigPage(BookConfigPageReqVO pageReqVO);

}