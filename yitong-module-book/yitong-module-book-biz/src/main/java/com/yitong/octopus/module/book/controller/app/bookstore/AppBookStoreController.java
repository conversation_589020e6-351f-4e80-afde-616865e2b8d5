//package com.yitong.octopus.module.book.controller.app.bookstore;
//
//import com.yitong.octopus.framework.common.pojo.CommonResult;
//import com.yitong.octopus.framework.common.pojo.PageResult;
//import com.yitong.octopus.module.book.controller.app.bookstore.vo.BookStorePageReqVO;
//import com.yitong.octopus.module.book.controller.app.bookstore.vo.BookStoreRespVO;
//import com.yitong.octopus.module.book.service.bookstore.BookStoreService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import javax.annotation.Resource;
//import javax.validation.Valid;
//
//import static com.yitong.octopus.framework.common.pojo.CommonResult.success;
//
//@Tag(name = "[有点生活] - 用户预约订单")
//@RestController
//@RequestMapping("/book/spu")
//@Validated
//public class AppBookStoreController {
//
//    @Resource
//    private BookStoreService bookStoreService;
//
//    @GetMapping("/store-page")
//    @Operation(summary = "获得可预约的门店列表")
//    public CommonResult<PageResult<BookStoreRespVO>> getBookStoreByPage(@Valid BookStorePageReqVO pageReqVO) {
//        PageResult<BookStoreRespVO> pageResult = bookStoreService.getBookStoreByPage(pageReqVO);
//        return success(pageResult);
//    }
//
//}