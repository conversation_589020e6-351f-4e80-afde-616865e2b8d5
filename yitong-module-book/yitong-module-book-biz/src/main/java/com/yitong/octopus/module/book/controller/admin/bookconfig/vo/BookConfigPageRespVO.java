package com.yitong.octopus.module.book.controller.admin.bookconfig.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yitong.octopus.module.sp.api.utils.jackson.SpGoodsSpuNameJsonSerializer;
import com.yitong.octopus.module.sp.api.utils.jackson.SpStoreNameJsonSerializer;
import com.yitong.octopus.module.sp.api.utils.jackson.SpStoreNameListJsonSerializer;
import com.yitong.octopus.module.sp.api.utils.jackson.SpMainNameJsonSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 预订配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class BookConfigPageRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "24213")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "方案名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("方案名称")
    private String name;

    @Schema(description = "状态（1.启用、2.禁用）", example = "2")
    @ExcelProperty("状态（1.启用、2.禁用）")
    private Integer status;

    @Schema(description = "预约审核（1 提交预约后,自动审核通过,并占用库存、2 提交预约后,占用库存资源,订单需审核,审核通过代表预约成功）", example = "1")
    @ExcelProperty("预约审核（1 提交预约后,自动审核通过,并占用库存、2 提交预约后,占用库存资源,订单需审核,审核通过代表预约成功）")
    private Integer applyAuditType;

    @Schema(description = "发起预约配置参数（1 无限制、2 需要提前N 小时、3  需要提前N 天）", example = "2")
    @ExcelProperty("发起预约配置参数（1 无限制、2 需要提前N 小时、3  需要提前N 天）")
    private Integer applyType;

    @Schema(description = "发起预约具体配置参数（小时或天数）")
    @ExcelProperty("发起预约具体配置参数（小时或天数）")
    private Integer applyTimes;

    @Schema(description = "取消预约（1 无限制、2 需要提前N 小时、3  需要提前N 天）", example = "1")
    @ExcelProperty("取消预约（1 无限制、2 需要提前N 小时、3  需要提前N 天）")
    private Integer cancelType;

    @Schema(description = "取消预约具体配置参数（小时或天数）")
    @ExcelProperty("取消预约具体配置参数（小时或天数）")
    private Integer cancelTimes;

    @Schema(description = "更改预约（1 无限制、2 需要提前N 小时、3  需要提前N 天、 0不允许更改）", example = "2")
    @ExcelProperty("更改预约（1 无限制、2 需要提前N 小时、3  需要提前N 天、 0不允许更改）")
    private Integer changeType;

    @Schema(description = "更改预约具体参数（小时或天数）")
    @ExcelProperty("更改预约具体参数（小时或天数）")
    private Integer changeTimes;

    @Schema(description = "预约项（1 可多选,最少选一个,最多选N个 、0 仅可单选）")
    @ExcelProperty("预约项（1 可多选,最少选一个,最多选N个 、0 仅可单选）")
    private Integer bookingService;

    @Schema(description = "预约日期（0 全部日期可用、1 指定日期可用）", example = "1")
    @ExcelProperty("预约日期（0 全部日期可用、1 指定日期可用）")
    private Integer bookingDateType;

    @Schema(description = "预约可用具体日期")
    @ExcelProperty("预约可用具体日期")
    private String bookingDate;

    @Schema(description = "不可预约日期（ 0，不设定 1 每周不可用、2 节假日不可以、3指定某天不可用）", example = "2")
    @ExcelProperty("不可预约日期（ 0，不设定 1 每周不可用、2 节假日不可以、3指定某天不可用）")
    private Integer bookingUnableType;

    @Schema(description = "不可预约具体日期")
    @ExcelProperty("不可预约具体日期")
    private String bookingUnableDate;

    @Schema(description = "每周不可用(具体周几)")
    @ExcelProperty("每周不可用(具体周几)")
    private String bookingUnableWeek;

    @Schema(description = "不可使用具体节假日")
    @ExcelProperty("不可使用具体节假日")
    private String bookingUnableFestival;

    @Schema(description = "日历开始日期")
    @ExcelProperty("日历开始日期")
    private LocalDateTime bookingStartDate;

    @Schema(description = "日历结束日期")
    @ExcelProperty("日历结束日期")
    private LocalDateTime bookingEndDate;

    @Schema(description = "预约时段（0 不分时间段、 1 指定时间段）", example = "2")
    @ExcelProperty("预约时段（0 不分时间段、 1 指定时间段）")
    private Integer bookingTimeType;

    @Schema(description = "预约时段限制是否可多选（0 仅单选、1可多选【以数量代表可选数量】）")
    @ExcelProperty("预约时段限制是否可多选（0 仅单选、1可多选【以数量代表可选数量】）")
    private Integer bookingTimeState;

    @Schema(description = "预约次数限制（下单人手机号控制 0 无限制、同一日期内根据数量限制）")
    @ExcelProperty("预约次数限制（下单人手机号控制 0 无限制、同一日期内根据数量限制）")
    private Integer bookingNum;

    @Schema(description = "奖品是否有（0 没有、1 有）", example = "1")
    @ExcelProperty("奖品是否有（0 没有、1 有）")
    private Integer prizeType;

    @Schema(description = "奖品id", example = "28423")
    @ExcelProperty("奖品id")
    private Long prizeId;

    @Schema(description = "奖品限制（0 无限制、1 限制数）")
    @ExcelProperty("奖品限制（0 无限制、1 限制数）")
    private Integer prizeTakeLimit;

    @Schema(description = "奖品数量")
    @ExcelProperty("奖品数量")
    private Integer prizeTotalNum;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    // ############# 归属商家和商品 ###########
    @Schema(description = "商家")
    @JsonSerialize(using = SpMainNameJsonSerializer.class)
    private Long spId;

    @Schema(description = "商品")
    @JsonSerialize(using = SpGoodsSpuNameJsonSerializer.class)
    private Long spuId;

    @Schema(description = "商品门店限制")
    private Boolean storeLimit;

    @Schema(description = "商品门店列表")
    private List<Long> storeIds;
    // ############  归属商家和商品 ############
}