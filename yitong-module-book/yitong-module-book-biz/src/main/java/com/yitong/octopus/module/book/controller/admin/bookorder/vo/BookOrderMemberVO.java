package com.yitong.octopus.module.book.controller.admin.bookorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 用户预约订单用户详情Request VO")
@Data
public class BookOrderMemberVO {

    @Schema(description = "会员名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "会员名称不能为空")
    private String memberName;

    @Schema(description = "会员手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "会员手机号不能为空")
    private String memberMobile;

}