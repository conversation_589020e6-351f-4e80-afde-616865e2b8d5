package com.yitong.octopus.module.book.controller.admin.bookorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 用户预约订单新增/修改 Request VO")
@Data
public class BookOrderSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "3027")
    private Long id;

    @Schema(description = "appId", requiredMode = Schema.RequiredMode.REQUIRED, example = "26764")
    @NotNull(message = "appId不能为空")
    private Long appId;

    @Schema(description = "商家Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31056")
    @NotNull(message = "商家Id不能为空")
    private Long spId;

    @Schema(description = "商家门店Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26764")
    @NotNull(message = "商家门店Id不能为空")
    private Long storeId;

    @Schema(description = "商品Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "29542")
    @NotNull(message = "商品Id不能为空")
    private Long spuId;

    @Schema(description = "预约配置表Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "16281")
    @NotNull(message = "预约配置表Id不能为空")
    private Long bookConfigId;

    @Schema(description = "奖品id", example = "25858")
    private Long prizeId;

    @Schema(description = "用户的唯一标识", requiredMode = Schema.RequiredMode.REQUIRED, example = "24742")
    @NotNull(message = "用户的唯一标识不能为空")
    private Long memberId;


    @Schema(description = "预约日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "预约日期不能为空")
    private String bookDate;

    @Schema(description = "预约人数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "预约人数不能为空")
    private Integer reservationNum;

    @Schema(description = "预约类型（1.用户预约、 2.代用户预约）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "预约类型（1.用户预约、 2.代用户预约）不能为空")
    private Integer reservationType;

    @Schema(description = "预约状态（1.待确认、2.已确认、3.用户取消、4.商家取消、5.已完成）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "预约状态（1.待确认、2.已确认、3.用户取消、4.商家取消、5.已完成）不能为空")
    private Integer state;

    @Schema(description = "确认人")
    private String confirmPeople;

    @Schema(description = "取消时间")
    private LocalDateTime cancelTime;

    @Schema(description = "取消原因（1.更改时间、2.不想约了、3.计划有变、4.其他）", example = "不好")
    private Integer cancelReason;

    @Schema(description = "取消描述")
    private String cancelDescribe;

    @Schema(description = "核销时间")
    private LocalDateTime redeemTime;

    @Schema(description = "商家门店Id", example = "18125")
    private Long redeemStoreId;

    @Schema(description = "商家Id", example = "6977")
    private Long redeemSpId;

    @Schema(description = "预约用户信息", example = "6977")
    private List<BookOrderMemberVO> memberList;

//    @Data
//    @NoArgsConstructor
//    @AllArgsConstructor
//    public static class BookOrderMember{
//
//        @Schema(description = "会员名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
//        @NotEmpty(message = "会员名称不能为空")
//        private String memberName;
//
//        @Schema(description = "会员手机号", requiredMode = Schema.RequiredMode.REQUIRED)
//        @NotEmpty(message = "会员手机号不能为空")
//        private String memberMobile;
//
//    }
}