package com.yitong.octopus.module.book.controller.admin.bookconfig.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 预订配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BookConfigPageReqVO extends PageParam {

    @Schema(description = "方案名称", example = "芋艿")
    private String name;

}