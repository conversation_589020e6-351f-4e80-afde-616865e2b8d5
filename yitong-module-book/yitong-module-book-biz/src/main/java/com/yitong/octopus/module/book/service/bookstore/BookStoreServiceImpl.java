package com.yitong.octopus.module.book.service.bookstore;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.util.MyBatisUtils;
import com.yitong.octopus.module.book.controller.app.bookstore.vo.BookStorePageReqVO;
import com.yitong.octopus.module.book.controller.app.bookstore.vo.BookStoreRespVO;
import com.yitong.octopus.module.book.controller.app.bookstore.vo.BookStoreTopReqVO;
import com.yitong.octopus.module.book.dal.mysql.bookconfig.BookConfigSpStoreMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class BookStoreServiceImpl implements BookStoreService{

    @Resource
    private BookConfigSpStoreMapper bookConfigSpStoreMapper;

    @Override
    public PageResult<BookStoreRespVO> getBookStoreByPage(BookStorePageReqVO pageReqVO) {
        IPage<BookStoreRespVO> page =  bookConfigSpStoreMapper.getBookStoreByPage(MyBatisUtils.buildPage(pageReqVO),pageReqVO);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<BookStoreRespVO> getBookStoreTopList(BookStoreTopReqVO reqVO) {
        return bookConfigSpStoreMapper.getBookStoreTopList(reqVO);
    }
}
