package com.yitong.octopus.module.book.dal.mysql.bookconfig;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.book.controller.admin.bookconfig.vo.BookConfigSpStorePageReqVO;
import com.yitong.octopus.module.book.controller.app.bookstore.vo.BookStorePageReqVO;
import com.yitong.octopus.module.book.controller.app.bookstore.vo.BookStoreRespVO;
import com.yitong.octopus.module.book.controller.app.bookstore.vo.BookStoreTopReqVO;
import com.yitong.octopus.module.book.dal.dataobject.bookconfig.BookConfigSpStoreDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 预约商家与门店 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BookConfigSpStoreMapper extends BaseMapperX<BookConfigSpStoreDO> {

    default PageResult<BookConfigSpStoreDO> selectPage(BookConfigSpStorePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BookConfigSpStoreDO>()
                .eqIfPresent(BookConfigSpStoreDO::getSpId, reqVO.getSpId())
                .eqIfPresent(BookConfigSpStoreDO::getStoreId, reqVO.getStoreId())
                .betweenIfPresent(BookConfigSpStoreDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BookConfigSpStoreDO::getId));
    }

    default int deleteByBookConfigId(Long bookConfigId){
        return delete(BookConfigSpStoreDO::getBookConfigId,bookConfigId);
    }

    default List<Long> getStoreIdsByBookConfigId(Long bookConfigId){
        return selectList(BookConfigSpStoreDO::getBookConfigId,bookConfigId).stream().map(s ->s.getStoreId()).collect(Collectors.toList());
    }

    /**
     * 获取预约可适用的门店列表
     * @param pageReqVO
     * @return
     */
    IPage<BookStoreRespVO> getBookStoreByPage(IPage<BookStorePageReqVO> page,@Param("vo") BookStorePageReqVO pageReqVO);

    /**
     * 获取预约可适用的门店列表,根据门店预约数量
     * @param reqVO
     * @return
     */
    List<BookStoreRespVO> getBookStoreTopList(@Param("vo") BookStoreTopReqVO reqVO);

}