package com.yitong.octopus.module.book.service.bookorder;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.security.core.util.SecurityFrameworkUtils;
import com.yitong.octopus.module.book.dal.dataobject.bookconfig.BookConfigDO;
import com.yitong.octopus.module.book.dal.dataobject.bookorder.BookOrderMemberDO;
import com.yitong.octopus.module.book.dal.mysql.bookorder.BookOrderMemberMapper;
import com.yitong.octopus.module.book.enums.BookAuditTypeEnum;
import com.yitong.octopus.module.book.enums.BookOrderStateEnum;
import com.yitong.octopus.module.book.service.bookconfig.BookConfigService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yitong.octopus.module.book.controller.admin.bookorder.vo.*;
import com.yitong.octopus.module.book.dal.dataobject.bookorder.BookOrderDO;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;

import com.yitong.octopus.module.book.dal.mysql.bookorder.BookOrderMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.book.enums.ErrorCodeConstants.*;

/**
 * 用户预约订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BookOrderServiceImpl extends ServiceImpl<BookOrderMapper,BookOrderDO>  implements BookOrderService {

    @Resource
    private BookConfigService bookConfigService;

    @Resource
    private BookOrderMemberMapper bookOrderMemberMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createOrder(BookOrderSaveReqVO reqVO) {
        BookConfigDO bookConfig = bookConfigService.getConfigByIdAndSpuId(reqVO.getBookConfigId(),reqVO.getSpuId());
        if (ObjectUtil.isNull(bookConfig)) {
            throw exception(BOOKING_CONFIG_NOT_EXISTS);
        }
        // 插入
        BookOrderDO order = BeanUtils.toBean(reqVO, BookOrderDO.class);
        if (BookAuditTypeEnum.AUTO_AUDIT.getType().equals(bookConfig.getApplyAuditType())){
            order.setConfirmTime(LocalDateTime.now());
            order.setConfirmPeople("SYS");
            order.setStatus(BookOrderStateEnum.CONFIRM.getStatus());
        }
        getBaseMapper().insert(order);
        if (CollectionUtil.isNotEmpty(reqVO.getMemberList())){
            List<BookOrderMemberDO> bookOrderMemberList = reqVO.getMemberList().stream()
                    .map(m -> BeanUtils.toBean(m,BookOrderMemberDO.class).setBookOrderId(order.getId()))
                    .collect(Collectors.toList());
            bookOrderMemberMapper.insertBatch(bookOrderMemberList);
        }
        // 返回
        return order.getId();
    }

    @Override
    public void updateOrder(BookOrderSaveReqVO reqVO) {
        // 校验存在
        validateOrderExists(reqVO.getId());
        // 更新
        BookOrderDO updateObj = BeanUtils.toBean(reqVO, BookOrderDO.class);
        getBaseMapper().updateById(updateObj);
    }

    @Override
    public void deleteOrder(Long id) {
        // 校验存在
        validateOrderExists(id);
        // 删除
        getBaseMapper().deleteById(id);
    }

    private void validateOrderExists(Long id) {
        if (getBaseMapper().selectById(id) == null) {
            throw exception(BOOKING_ORDER_NOT_EXISTS);
        }
    }

    @Override
    public BookOrderDO getOrder(Long id) {
        return getBaseMapper().selectById(id);
    }

    @Override
    public BookOrderDO getOrderInfoByIdAndAppId(Long id, Long appId) {
        BookOrderDO bookOrder = getBaseMapper().selectOne(BookOrderDO::getId,id,BookOrderDO::getAppId,appId);
        if (ObjectUtil.isNotNull(bookOrder)){
            bookOrder.setBookMemberList(bookOrderMemberMapper.getBookOrderMemberByBookOrderId(id));
        }
        return bookOrder;
    }

    @Override
    public PageResult<BookOrderDO> getOrderPage(BookOrderPageReqVO pageReqVO) {
        return getBaseMapper().selectPage(pageReqVO);
    }

    @Override
    public void cancelBookOrder(BookOrderCancelReqVO reqVO, Long appId) {
        BookOrderDO bookOrder = getOrderInfoByIdAndAppId(reqVO.getBookOrderId(),appId);
        if (ObjectUtil.isNull(bookOrder)){
            throw exception(BOOKING_ORDER_NOT_EXISTS);
        }

        if (!BookOrderStateEnum.WAITE.getStatus().equals(bookOrder.getStatus()) && !BookOrderStateEnum.CONFIRM.getStatus().equals(bookOrder.getStatus())){
            throw exception(BOOKING_ORDER_STATUS_NOT_CANCEL,BookOrderStateEnum.getBookOrderStateEnumByStatus(bookOrder.getStatus()).getName());
        }

        BookConfigDO bookConfig  =  bookConfigService.getConfig(bookOrder.getBookConfigId());
        if (ObjectUtil.isNull(bookConfig)){
            throw exception(BOOKING_CONFIG_NOT_EXISTS);
        }
        // TODO 校验取消限制
//        if (BookCancelTypeEnum.N_HOUR.getType().equals(bookConfig.getCancelType()) && LocalDateTimeUtils.between(bookOrder.getCreateTime(),LocalDateTime.now(), ChronoUnit.HOURS) <){
//            bookConfig.getCancelTimes();
//        }
//        if (BookCancelTypeEnum.N_HOUR.getType().equals(bookConfig.getCancelType())){
//
//        }
        bookOrder.setCancelTime(LocalDateTime.now());
        bookOrder.setCancelReason(reqVO.getCancelReason());
        bookOrder.setCancelDescribe(reqVO.getCancelDescribe());
        bookOrder.setStatus(reqVO.getCancelFrom());
        updateById(bookOrder);
    }

    @Override
    public Long getValidBookOrderCountByMember(Long appId) {
        return this.getBaseMapper().selectCount(new LambdaQueryWrapperX<BookOrderDO>()
                .eq(BookOrderDO::getMemberId, SecurityFrameworkUtils.getLoginUserId())
                .eq(BookOrderDO::getAppId, appId)
                .in(BookOrderDO::getStatus, BookOrderStateEnum.WAITE.getStatus(),BookOrderStateEnum.CONFIRM.getStatus())
        );
    }

}