package com.yitong.octopus.module.book.dal.dataobject.bookconfig;

import lombok.*;

import java.io.Serializable;
import java.util.*;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;
import org.apache.ibatis.type.JdbcType;

/**
 * 预约商家与门店 DO
 *
 * <AUTHOR>
 */
@TableName("yt_book_config_sp_store")
@KeySequence("yt_book_config_sp_store_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BookConfigSpStoreDO implements Serializable {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 预约配置Id
     */
    private Long bookConfigId;
    /**
     * 商家ID
     */
    private Long spId;
    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建者，目前使用 SysUser 的 id 编号
     *
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    @TableField(fill = FieldFill.INSERT, jdbcType = JdbcType.VARCHAR)
    private String creator;
}