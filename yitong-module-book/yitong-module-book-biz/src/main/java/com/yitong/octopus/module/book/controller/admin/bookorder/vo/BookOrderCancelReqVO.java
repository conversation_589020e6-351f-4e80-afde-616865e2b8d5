package com.yitong.octopus.module.book.controller.admin.bookorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 用户预约订单取消 Request VO")
@Data
public class BookOrderCancelReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "3027")
    @NotNull(message = "预约单不能为空")
    private Long bookOrderId;

    @Schema(description = "取消原因（1.更改时间、2.不想约了、3.计划有变、4.其他）", example = "不好")
    @NotNull(message = "取消原因不能为空")
    private Integer cancelReason;

    @Schema(description = "取消描述")
    private String cancelDescribe;

    @NotNull(message = "取消方")
    private Integer cancelFrom;

}