package com.yitong.octopus.module.book.service.bookconfig;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yitong.octopus.module.book.controller.admin.bookconfig.vo.BookConfigSpStorePageReqVO;
import com.yitong.octopus.module.book.controller.admin.bookconfig.vo.BookConfigSpStoreSaveReqVO;
import com.yitong.octopus.module.book.dal.dataobject.bookconfig.BookConfigSpStoreDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import javax.validation.Valid;

/**
 * 预约商家与门店 Service 接口
 *
 * <AUTHOR>
 */
public interface BookConfigSpStoreService extends IService<BookConfigSpStoreDO> {

    /**
     * 创建预约商家与门店
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createConfigSpStore(@Valid BookConfigSpStoreSaveReqVO createReqVO);

    /**
     * 更新预约商家与门店
     *
     * @param updateReqVO 更新信息
     */
    void updateConfigSpStore(@Valid BookConfigSpStoreSaveReqVO updateReqVO);

    /**
     * 删除预约商家与门店
     *
     * @param id 编号
     */
    void deleteConfigSpStore(Long id);

    /**
     * 获得预约商家与门店
     *
     * @param id 编号
     * @return 预约商家与门店
     */
    BookConfigSpStoreDO getConfigSpStore(Long id);

    /**
     * 获得预约商家与门店分页
     *
     * @param pageReqVO 分页查询
     * @return 预约商家与门店分页
     */
    PageResult<BookConfigSpStoreDO> getConfigSpStorePage(BookConfigSpStorePageReqVO pageReqVO);

}