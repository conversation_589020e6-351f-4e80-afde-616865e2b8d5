package com.yitong.octopus.module.book.service.bookconfig;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yitong.octopus.framework.security.core.util.SecurityFrameworkUtils;
import com.yitong.octopus.module.book.controller.admin.bookconfig.vo.BookConfigInfoVO;
import com.yitong.octopus.module.book.controller.admin.bookconfig.vo.BookConfigPageReqVO;
import com.yitong.octopus.module.book.controller.admin.bookconfig.vo.BookConfigPageRespVO;
import com.yitong.octopus.module.book.controller.admin.bookconfig.vo.BookConfigSaveReqVO;
import com.yitong.octopus.module.book.dal.dataobject.bookconfig.BookConfigDO;
import com.yitong.octopus.module.book.dal.dataobject.bookconfig.BookConfigSpStoreDO;
import com.yitong.octopus.module.book.dal.mysql.bookconfig.BookConfigMapper;
import com.yitong.octopus.module.book.dal.mysql.bookconfig.BookConfigSpStoreMapper;
import com.yitong.octopus.module.sp.dal.dataobject.goodsspu.SpGoodsSpuDO;
import com.yitong.octopus.module.sp.service.goodsspu.SpGoodsSpuService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.book.enums.ErrorCodeConstants.*;
import static com.yitong.octopus.module.sp.enums.ErrorCodeConstants.GOODS_SPU_NOT_EXISTS;

/**
 * 预订配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BookConfigServiceImpl extends ServiceImpl<BookConfigMapper, BookConfigDO>  implements BookConfigService {

    @Resource
    private SpGoodsSpuService spGoodsSpuService;
    @Resource
    private BookConfigSpStoreMapper spStoreMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createConfig(BookConfigSaveReqVO reqVO) {
        // 插入
        BookConfigDO config = BeanUtils.toBean(reqVO, BookConfigDO.class);
        SpGoodsSpuDO spGoodsSpu = spGoodsSpuService.getGoodsSpu(reqVO.getSpuId());
        if (ObjectUtil.isNull(spGoodsSpu)){
            throw exception(GOODS_SPU_NOT_EXISTS);
        }
        config.setSpId(spGoodsSpu.getSpId());
        getBaseMapper().insert(config);
        //初始化预约配置与门店
        initBookConfigSpStore(false, reqVO.getStoreLimit(), reqVO.getStoreIds(), config.getId(),spGoodsSpu.getSpId());
        // 返回
        return config.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateConfig(BookConfigSaveReqVO reqVO) {
        // 校验存在
        validateConfigExists(reqVO.getId());
        BookConfigDO dbObj = getConfig(reqVO.getId());
        // 更新
        BookConfigDO updateObj = BeanUtils.toBean(reqVO, BookConfigDO.class);
        SpGoodsSpuDO spGoodsSpu = spGoodsSpuService.getGoodsSpu(reqVO.getSpuId());
        if (ObjectUtil.isNull(spGoodsSpu)){
            throw exception(GOODS_SPU_NOT_EXISTS);
        }
        initBookConfigSpStore(dbObj.getStoreLimit(), reqVO.getStoreLimit(), reqVO.getStoreIds(), reqVO.getId(),spGoodsSpu.getSpId());
        getBaseMapper().updateById(updateObj);
    }

    /**
     * 初始化预约配置与门店
     * @param bdStoreLimit 元记录是否开启门店限制
     * @param nowStoreLimit 当前是否开启门店限制
     * @param storeIds 门店列表
     * @param bookConfigId 配置Id
     * @param spId 商家Id
     */
    private void initBookConfigSpStore(Boolean bdStoreLimit, Boolean nowStoreLimit,List<Long> storeIds, Long bookConfigId, Long spId) {
        if (nowStoreLimit){
            if (CollectionUtil.isEmpty(storeIds)){
                throw exception(BOOKING_CONFIG_STORE_LIMIT_EMPTY);
            }
            //删除之前记录
            if (bdStoreLimit){
                spStoreMapper.deleteByBookConfigId(bookConfigId);
            }
            // 创建人
            Long systemUserId = SecurityFrameworkUtils.getLoginUserId();
            //插入新记录
            List<BookConfigSpStoreDO> spStoreList = storeIds.stream().map(s ->
                            new BookConfigSpStoreDO()
                                    .setBookConfigId(bookConfigId)
                                    .setSpId(spId)
                                    .setStoreId(s)
                                    .setCreateTime(LocalDateTime.now())
                                    .setCreator(String.valueOf(systemUserId))
                    ).collect(Collectors.toList());
            spStoreMapper.insertBatch(spStoreList);
        }else {
            //删除之前记录
            if (bdStoreLimit){
                spStoreMapper.deleteByBookConfigId(bookConfigId);
            }
        }
    }

    @Override
    public void deleteConfig(Long id) {
        // 校验存在
        validateConfigExists(id);
        // 删除
        getBaseMapper().deleteById(id);
    }

    private void validateConfigExists(Long id) {
        if (getBaseMapper().selectById(id) == null) {
            throw exception(BOOKING_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public BookConfigDO getConfig(Long id) {
        return  getBaseMapper().selectById(id);
    }

    @Override
    public BookConfigDO getConfigByIdAndSpuId(Long id, Long spuId) {
        return getBaseMapper().selectOne(BookConfigDO::getId,id,BookConfigDO::getSpuId,spuId);
    }

    @Override
    public BookConfigInfoVO getConfigInfoVoById(Long id) {
        BookConfigDO bookConfig = getBaseMapper().selectById(id);
        if (ObjectUtil.isNotNull(bookConfig)) {
            BookConfigInfoVO respVO = BeanUtils.toBean(bookConfig, BookConfigInfoVO.class);
            respVO.setStoreIds(spStoreMapper.getStoreIdsByBookConfigId(id));
            return respVO;
        }
        return  null;
    }

    @Override
    public PageResult<BookConfigDO> getConfigPage(BookConfigPageReqVO pageReqVO) {
        return getBaseMapper().selectPage(pageReqVO);
    }

}