package com.yitong.octopus.module.book.controller.admin.bookorder.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yitong.octopus.module.sp.api.utils.jackson.SpGoodsSpuNameJsonSerializer;
import com.yitong.octopus.module.sp.api.utils.jackson.SpMainNameJsonSerializer;
import com.yitong.octopus.module.sp.api.utils.jackson.SpStoreNameJsonSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 用户预约订单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class BookOrderRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "3027")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "商家Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31056")
    @ExcelProperty("商家Id")
    @JsonSerialize(using = SpMainNameJsonSerializer.class)
    private Long spId;

    @Schema(description = "商家门店Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26764")
    @ExcelProperty("商家门店Id")
    @JsonSerialize(using = SpStoreNameJsonSerializer.class)
    private Long storeId;

    @Schema(description = "商品Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "29542")
    @ExcelProperty("商品Id")
    @JsonSerialize(using = SpGoodsSpuNameJsonSerializer.class)
    private Long spuId;

    @Schema(description = "预约配置表Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "16281")
    @ExcelProperty("预约配置表Id")
    private Long bookingConfigId;

    @Schema(description = "奖品id", example = "25858")
    @ExcelProperty("奖品id")
    private Long prizeId;

    @Schema(description = "用户的唯一标识", requiredMode = Schema.RequiredMode.REQUIRED, example = "24742")
    @ExcelProperty("用户的唯一标识")
    private Long memberId;

    @Schema(description = "预约日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预约日期")
    private String bookDate;

    @Schema(description = "预约人数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预约人数")
    private Integer reservationNum;

    @Schema(description = "预约类型（1.用户预约、 2.代用户预约）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("预约类型（1.用户预约、 2.代用户预约）")
    private Integer reservationType;

    @Schema(description = "预约状态（1.待确认、2.已确认、3.用户取消、4.商家取消、5.已完成）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预约状态（1.待确认、2.已确认、3.用户取消、4.商家取消、5.已完成）")
    private Integer status;

    @Schema(description = "确认人")
    @ExcelProperty("确认人")
    private String confirmPeople;

    @Schema(description = "取消时间")
    @ExcelProperty("取消时间")
    private LocalDateTime cancelTime;

    @Schema(description = "取消原因（1.更改时间、2.不想约了、3.计划有变、4.其他）", example = "不好")
    @ExcelProperty("取消原因（1.更改时间、2.不想约了、3.计划有变、4.其他）")
    private Integer cancelReason;

    @Schema(description = "取消描述")
    @ExcelProperty("取消描述")
    private String cancelDescribe;

    @Schema(description = "核销时间")
    @ExcelProperty("核销时间")
    private LocalDateTime redeemTime;

    @Schema(description = "商家门店Id", example = "18125")
    @ExcelProperty("商家门店Id")
    private Long redeemStoreId;

    @Schema(description = "商家Id", example = "6977")
    @ExcelProperty("商家Id")
    private Long redeemSpId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "预约用户", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<BookOrderMemberVO> bookMemberList;

}