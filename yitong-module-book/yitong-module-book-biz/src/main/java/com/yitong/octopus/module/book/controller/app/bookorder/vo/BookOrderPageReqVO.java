package com.yitong.octopus.module.book.controller.app.bookorder.vo;

import com.yitong.octopus.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 用户预约订单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BookOrderPageReqVO extends PageParam {

    @Schema(description = "商家Id", example = "31056")
    private Long spId;

    @Schema(description = "商家门店Id", example = "26764")
    private Long storeId;

    @Schema(description = "商品Id", example = "29542")
    private Long spuId;

    @Schema(description = "预约配置表Id", example = "16281")
    private Long bookingConfigId;

    @Schema(description = "奖品id", example = "25858")
    private Long prizeId;

    @Schema(description = "会员名称", example = "赵六")
    private String memberName;

    @Schema(description = "会员手机号")
    private String memberMobile;

    @Schema(description = "预约日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] bookDate;

    @Schema(description = "取消描述")
    private String cancelDescribe;

}