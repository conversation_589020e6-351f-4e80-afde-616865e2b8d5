package com.yitong.octopus.module.book.controller.app.bookorder;

import com.yitong.octopus.framework.common.pojo.CommonResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;
import com.yitong.octopus.module.book.controller.app.bookorder.vo.BookOrderRespVO;
import com.yitong.octopus.module.book.dal.dataobject.bookorder.BookOrderDO;
import com.yitong.octopus.module.book.service.bookorder.BookOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 用户预约订单")
@RestController
@RequestMapping("/book/order")
@Validated
public class AppBookOrderController {

    @Resource
    private BookOrderService orderService;

    @GetMapping("/get")
    @Operation(summary = "获得用户预约订单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('book:order:query')")
    public CommonResult<BookOrderRespVO> getOrder(@RequestParam("id") Long id) {
        BookOrderDO order = orderService.getOrder(id);
        return success(BeanUtils.toBean(order, BookOrderRespVO.class));
    }

//    @GetMapping("/page")
//    @Operation(summary = "获得用户预约订单分页")
//    @PreAuthorize("@ss.hasPermission('book:order:query')")
//    public CommonResult<PageResult<BookOrderRespVO>> getOrderPage(@Valid BookOrderPageReqVO pageReqVO) {
//        PageResult<BookOrderDO> pageResult = orderService.getOrderPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, BookOrderRespVO.class));
//    }

}