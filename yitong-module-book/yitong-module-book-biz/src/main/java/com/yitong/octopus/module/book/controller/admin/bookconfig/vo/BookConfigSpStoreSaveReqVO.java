package com.yitong.octopus.module.book.controller.admin.bookconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;

@Schema(description = "管理后台 - 预约商家与门店新增/修改 Request VO")
@Data
public class BookConfigSpStoreSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10679")
    private Long id;

    @Schema(description = "商家ID", example = "16831")
    private Long spId;

    @Schema(description = "门店ID", example = "7482")
    private Long storeId;

}