package com.yitong.octopus.module.book.controller.app.bookstore.vo;

import com.yitong.octopus.module.sp.api.store.vo.SpStoreLbsPageReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 用户预约订单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BookStorePageReqVO extends SpStoreLbsPageReqVO {

}