package com.yitong.octopus.module.book.dal.dataobject.bookorder;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;
import lombok.experimental.FieldNameConstants;

import java.time.LocalDateTime;

/**
 * 用户预约订单 DO
 *
 * <AUTHOR>
 */
@FieldNameConstants
@TableName("yt_book_order_member")
@KeySequence("yt_book_order_member_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BookOrderMemberDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 应用id
     */
    private Long bookOrderId;

    /**
     * 会员名称
     */
    private String memberName;
    /**
     * 会员手机号
     */
    private String memberMobile;

}