package com.yitong.octopus.module.book.controller.admin.bookorder;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageParam;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.book.controller.admin.bookorder.vo.*;
import com.yitong.octopus.module.book.dal.dataobject.bookorder.BookOrderDO;
import com.yitong.octopus.module.book.service.bookorder.BookOrderService;

@Tag(name = "管理后台 - 用户预约订单")
@RestController
@RequestMapping("/book/order")
@Validated
public class BookOrderController {

    @Resource
    private BookOrderService orderService;

    @PostMapping("/create")
    @Operation(summary = "创建用户预约订单")
    @PreAuthorize("@ss.hasPermission('book:order:create')")
    public CommonResult<Long> createOrder(@Valid @RequestBody BookOrderSaveReqVO createReqVO) {
        return success(orderService.createOrder(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户预约订单")
    @PreAuthorize("@ss.hasPermission('book:order:update')")
    public CommonResult<Boolean> updateOrder(@Valid @RequestBody BookOrderSaveReqVO updateReqVO) {
        orderService.updateOrder(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户预约订单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('book:order:delete')")
    public CommonResult<Boolean> deleteOrder(@RequestParam("id") Long id) {
        orderService.deleteOrder(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户预约订单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('book:order:query')")
    public CommonResult<BookOrderRespVO> getOrder(@RequestParam("id") Long id) {
        BookOrderDO order = orderService.getOrder(id);
        return success(BeanUtils.toBean(order, BookOrderRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得用户预约订单分页")
    @PreAuthorize("@ss.hasPermission('book:order:query')")
    public CommonResult<PageResult<BookOrderRespVO>> getOrderPage(@Valid BookOrderPageReqVO pageReqVO) {
        PageResult<BookOrderDO> pageResult = orderService.getOrderPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BookOrderRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出用户预约订单 Excel")
    @PreAuthorize("@ss.hasPermission('book:order:export')")
    public void exportOrderExcel(@Valid BookOrderPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<BookOrderDO> list = orderService.getOrderPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "用户预约订单.xls", "数据", BookOrderRespVO.class,
                        BeanUtils.toBean(list, BookOrderRespVO.class));
    }

}