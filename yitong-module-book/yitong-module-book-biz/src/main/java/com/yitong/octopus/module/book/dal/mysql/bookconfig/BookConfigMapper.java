package com.yitong.octopus.module.book.dal.mysql.bookconfig;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.book.controller.admin.bookconfig.vo.BookConfigPageReqVO;
import com.yitong.octopus.module.book.dal.dataobject.bookconfig.BookConfigDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 预订配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BookConfigMapper extends BaseMapperX<BookConfigDO> {

    default PageResult<BookConfigDO> selectPage(BookConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BookConfigDO>()
                .likeIfPresent(BookConfigDO::getName, reqVO.getName())
                .orderByDesc(BookConfigDO::getId));
    }

}