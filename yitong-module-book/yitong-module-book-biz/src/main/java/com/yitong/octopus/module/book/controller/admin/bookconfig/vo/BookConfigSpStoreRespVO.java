package com.yitong.octopus.module.book.controller.admin.bookconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 预约商家与门店 Response VO")
@Data
@ExcelIgnoreUnannotated
public class BookConfigSpStoreRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10679")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "商家ID", example = "16831")
    @ExcelProperty("商家ID")
    private Long spId;

    @Schema(description = "门店ID", example = "7482")
    @ExcelProperty("门店ID")
    private Long storeId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}