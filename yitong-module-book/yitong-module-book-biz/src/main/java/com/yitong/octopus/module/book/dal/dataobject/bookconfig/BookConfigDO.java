package com.yitong.octopus.module.book.dal.dataobject.bookconfig;

import lombok.*;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;
import org.apache.ibatis.type.JdbcType;

/**
 * 预订配置 DO
 *
 * <AUTHOR>
 */
@TableName("yt_book_config")
@KeySequence("yt_booking_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BookConfigDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 方案名称
     */
    private String name;
    /**
     * 状态（1.启用、2.禁用）
     */
    private Integer status;
    /**
     * 预约审核（1 提交预约后,自动审核通过,并占用库存、2 提交预约后,占用库存资源,订单需审核,审核通过代表预约成功）
     */
    private Integer applyAuditType;
    /**
     * 发起预约配置参数（1 无限制、2 需要提前N 小时、3  需要提前N 天）
     */
    private Integer applyType;
    /**
     * 发起预约具体配置参数（小时或天数）
     */
    private Integer applyTimes;
    /**
     * 取消预约（1 无限制、2 需要提前N 小时、3  需要提前N 天）
     */
    private Integer cancelType;
    /**
     * 取消预约具体配置参数（小时或天数）
     */
    private Integer cancelTimes;
    /**
     * 更改预约（1 无限制、2 需要提前N 小时、3  需要提前N 天、 0不允许更改）
     */
    private Integer changeType;
    /**
     * 更改预约具体参数（小时或天数）
     */
    private Integer changeTimes;
    /**
     * 预约项（1 可多选,最少选一个,最多选N个 、0 仅可单选）
     */
    private Integer bookingService;
    /**
     * 预约日期（0 全部日期可用、1 指定日期可用）
     */
    private Integer bookingDateType;
    /**
     * 预约可用具体日期
     */
    private String bookingDate;
    /**
     * 不可预约日期（ 0，不设定 1 每周不可用、2 节假日不可以、3指定某天不可用）
     */
    private Integer bookingUnableType;
    /**
     * 不可预约具体日期
     */
    private String bookingUnableDate;
    /**
     * 每周不可用(具体周几)
     */
    private String bookingUnableWeek;
    /**
     * 不可使用具体节假日
     */
    private String bookingUnableFestival;
    /**
     * 日历开始日期
     */
    private LocalDateTime bookingStartDate;
    /**
     * 日历结束日期
     */
    private LocalDateTime bookingEndDate;
    /**
     * 预约时段（0 不分时间段、 1 指定时间段）
     */
    private Integer bookingTimeType;
    /**
     * 预约时段限制是否可多选（0 仅单选、1可多选【以数量代表可选数量】）
     */
    private Integer bookingTimeState;
    /**
     * 预约次数限制（下单人手机号控制 0 无限制、同一日期内根据数量限制）
     */
    private Integer bookingNum;
    /**
     * 奖品是否有（0 没有、1 有）
     */
    private Integer prizeType;
    /**
     * 奖品id
     */
    private Long prizeId;
    /**
     * 奖品限制（0 无限制、1 限制数）
     */
    private Integer prizeTakeLimit;
    /**
     * 奖品数量
     */
    private Integer prizeTotalNum;

     // ############# 归属商家和商品 ###########

    private Long spId;

    private Long spuId;

    private Boolean storeLimit;

    // ############  归属商家和商品 ############

}