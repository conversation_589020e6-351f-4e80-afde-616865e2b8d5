package com.yitong.octopus.module.book.controller.admin.bookconfig;

import com.yitong.octopus.module.book.controller.admin.bookconfig.vo.BookConfigInfoVO;
import com.yitong.octopus.module.book.controller.admin.bookconfig.vo.BookConfigPageReqVO;
import com.yitong.octopus.module.book.controller.admin.bookconfig.vo.BookConfigPageRespVO;
import com.yitong.octopus.module.book.controller.admin.bookconfig.vo.BookConfigSaveReqVO;
import com.yitong.octopus.module.book.dal.dataobject.bookconfig.BookConfigDO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageParam;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.book.service.bookconfig.BookConfigService;

@Tag(name = "管理后台 - 预订配置")
@RestController
@RequestMapping("/book/config")
@Validated
public class BookConfigController {

    @Resource
    private BookConfigService configService;

    @PostMapping("/create")
    @Operation(summary = "创建预订配置")
    @PreAuthorize("@ss.hasPermission('booking:config:create')")
    public CommonResult<Long> createConfig(@Valid @RequestBody BookConfigSaveReqVO createReqVO) {
        return success(configService.createConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新预订配置")
    @PreAuthorize("@ss.hasPermission('booking:config:update')")
    public CommonResult<Boolean> updateConfig(@Valid @RequestBody BookConfigSaveReqVO updateReqVO) {
        configService.updateConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除预订配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('booking:config:delete')")
    public CommonResult<Boolean> deleteConfig(@RequestParam("id") Long id) {
        configService.deleteConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得预订配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('booking:config:query')")
    public CommonResult<BookConfigInfoVO> getConfig(@RequestParam("id") Long id) {
        return success(configService.getConfigInfoVoById(id));
    }

    @GetMapping("/page")
    @Operation(summary = "获得预订配置分页")
    @PreAuthorize("@ss.hasPermission('booking:config:query')")
    public CommonResult<PageResult<BookConfigPageRespVO>> getConfigPage(@Valid BookConfigPageReqVO pageReqVO) {
        PageResult<BookConfigDO> pageResult = configService.getConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BookConfigPageRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出预订配置 Excel")
    @PreAuthorize("@ss.hasPermission('booking:config:export')")
    public void exportConfigExcel(@Valid BookConfigPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<BookConfigDO> list = configService.getConfigPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "预订配置.xls", "数据", BookConfigPageRespVO.class,
                        BeanUtils.toBean(list, BookConfigPageRespVO.class));
    }

}