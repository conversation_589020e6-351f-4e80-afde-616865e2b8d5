<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yitong.octopus.module.book.dal.mysql.bookconfig.BookConfigSpStoreMapper">

    <select id="getBookStoreByPage"
            resultType="com.yitong.octopus.module.book.controller.app.bookstore.vo.BookStoreRespVO">
        SELECT
            t.*
        FROM (
            SELECT
                ssi.id store_id
                ,ssi.store_name
                ,ssi.store_logo
                ,ssi.sp_add
                ,ssi.sp_city
                ,ssi.rating
                ,(SELECT ssig.image_url FROM sp_store_info_gallery ssig WHERE ssig.store_id = ssi.id AND ssig.image_type =1 AND ssig.deleted = 0 LIMIT 1 ) store_main_image
                ,FLOOR(st_distance_sphere(point(sim.longitude,sim.latitude),point(${vo.longitude},${vo.latitude}))) as distance
            FROM sp_store_info ssi
            JOIN sp_store_info_map sim ON sim.store_id = ssi.id AND sim.deleted = 0
            LEFT JOIN sp_store_info_gallery  ssig ON ssig.store_id = ssi.id  AND ssig.image_type =1 AND ssig.deleted = 0
            JOIN (
                SELECT
                    ss.store_id
                FROM yt_book_config bc
                JOIN sp_goods_spu_store ss ON bc.spu_id = ss.spu_id  AND ss.deleted = 0
                WHERE bc.store_limit = 0 AND bc.`status` = 1  AND bc.deleted = 0
                UNION
                SELECT
                    bcss.store_id
                FROM yt_book_config bc
                JOIN yt_book_config_sp_store bcss ON bcss.book_config_id = bc.id
                WHERE bc.store_limit = 1 AND bc.`status` = 1  AND bc.deleted = 0
            ) t ON t.store_id = ssi.id
            WHERE  1=1 AND ssi.deleted = 0
            <if test="vo.radius != null ">
                HAVING  distance &lt;= #{vo.radius}
            </if>
            ORDER BY distance
        ) t
    </select>

    <select id="getBookStoreTopList"
            resultType="com.yitong.octopus.module.book.controller.app.bookstore.vo.BookStoreRespVO">
        SELECT
            ssi.id store_id
             ,ssi.store_name
             ,ssi.store_logo
             ,ssi.sp_add
             ,ssi.sp_city
             ,ssi.rating
             ,(SELECT ssig.image_url FROM sp_store_info_gallery ssig WHERE ssig.store_id = ssi.id AND ssig.image_type =1 AND ssig.deleted = 0 LIMIT 1 ) store_main_image
            ,FLOOR(st_distance_sphere(point(sim.longitude,sim.latitude),point(${vo.longitude},${vo.latitude}))) as distance
		    ,ss.store_book_num
        FROM sp_store_info ssi
        JOIN sp_store_info_map sim ON sim.store_id = ssi.id AND sim.deleted = 0
        LEFT JOIN sp_store_info_gallery  ssig ON ssig.store_id = ssi.id  AND ssig.image_type =1 AND ssig.deleted = 0
        JOIN (
            SELECT
                ss.store_id
            FROM yt_book_config bc
            JOIN sp_goods_spu_store ss ON bc.spu_id = ss.spu_id  AND ss.deleted = 0
            WHERE bc.store_limit = 0 AND bc.`status` = 1  AND bc.deleted = 0
            UNION
            SELECT
                bcss.store_id
            FROM yt_book_config bc
            JOIN yt_book_config_sp_store bcss ON bcss.book_config_id = bc.id
            WHERE bc.store_limit = 1 AND bc.`status` = 1  AND bc.deleted = 0
        ) t ON t.store_id = ssi.id
            JOIN(
            SELECT
                store_id
                ,count(1) store_book_num
            FROM yt_book_order
            WHERE deleted = 0 AND `status` IN (1,2,5)
            GROUP BY store_id
        ) ss ON ss.store_id = ssi.id
        WHERE  1=1 AND ssi.deleted = 0
        <if test="vo.radius != null ">
            HAVING  distance &lt;= #{vo.radius}
        </if>
        ORDER BY distance,ss.store_book_num DESC
        LIMIT #{vo.topSize}
    </select>
</mapper>