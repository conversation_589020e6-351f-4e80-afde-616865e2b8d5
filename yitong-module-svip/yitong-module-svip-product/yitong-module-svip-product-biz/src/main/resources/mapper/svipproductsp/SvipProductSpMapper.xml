<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yitong.octopus.module.svip.dal.mysql.svipproductsp.SvipProductSpMapper">

    <select id="getSpTotalPrivilegeNumBySp" resultType="java.lang.Integer">
        SELECT
            IFNULL(SUM(sp.privilege_num-sp.privilege_used_num),0) privilege_num
        FROM yt_svip_product_sp  sp
         JOIN yt_svip_product p ON sp.product_id = p.id
        WHERE p.type =  #{productType}
          AND sp.sp_id= #{spId}
          AND sp.deleted =0
          AND sp.`status` IN (1,2)
    </select>

    <update id="usePrivilegeById">
        UPDATE yt_svip_product_sp
        SET privilege_used_num = privilege_used_num + ${privilegeUsedNum}
            ,privilege_start_time = IFNULL(privilege_start_time,NOW())
            ,`status` = CASE `status`
                WHEN 1 THEN
                    2
                WHEN 2 THEN
                    IF(privilege_num &lt; (privilege_used_num + ${privilegeUsedNum}),3,2)
                ELSE
                    `status`
                END
            ,update_time = NOW()
        WHERE id = #{id}
          AND deleted =0
          AND `status` IN (1,2)
          AND privilege_num >= (privilege_used_num + ${privilegeUsedNum})
    </update>

    <update id="expireBySystem">
        UPDATE yt_svip_product_sp
        SET `status` = 3
        WHERE  `status` IN (1,2)
         AND deleted =0
         AND end_time &lt; NOW()
    </update>

</mapper>