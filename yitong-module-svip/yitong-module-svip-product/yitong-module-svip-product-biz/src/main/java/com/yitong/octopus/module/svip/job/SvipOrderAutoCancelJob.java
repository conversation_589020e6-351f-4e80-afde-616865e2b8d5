package com.yitong.octopus.module.svip.job;

import com.yitong.octopus.framework.quartz.core.handler.JobHandler;
import com.yitong.octopus.framework.tenant.core.context.TenantContextHolder;
import com.yitong.octopus.framework.tenant.core.job.TenantJob;
import com.yitong.octopus.module.svip.service.sviporder.SvipOrderService;
import com.yitong.octopus.module.system.dal.dataobject.user.AdminUserDO;
import com.yitong.octopus.module.system.dal.mysql.user.AdminUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 订单支付取消
 */
@Slf4j
@Component("SvipOrderAutoCancelJob")
public class SvipOrderAutoCancelJob implements JobHandler {

    @Resource
    private SvipOrderService svipOrderService;

    @Override
    @TenantJob // 标记多租户
    public String execute(String param) throws Exception {
        Integer count = svipOrderService.cancelOrderBySystem();
        return String.format("过期订单 %s 个", count);
    }

}
