package com.yitong.octopus.module.svip.controller.admin.sviporder.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 增值服务产品订单项分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SvipOrderItemPageReqVO extends PageParam {

    @Schema(description = "订单Id", example = "29696")
    private Long orderId;

    @Schema(description = "产品Id", example = "5934")
    private Long productId;

}