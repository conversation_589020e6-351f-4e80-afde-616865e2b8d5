package com.yitong.octopus.module.svip.dal.mysql.svipproductsp;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.yitong.octopus.module.svip.dal.dataobject.svipproduct.SvipProductDO;
import com.yitong.octopus.module.svip.dal.dataobject.svipproductsp.SvipProductSpDO;
import org.apache.ibatis.annotations.Mapper;
import com.yitong.octopus.module.svip.controller.admin.svipproductsp.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 增值服务产品商家订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SvipProductSpMapper extends BaseMapperX<SvipProductSpDO> {

    default PageResult<SvipProductSpDO> selectPage(SvipProductSpPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SvipProductSpDO>()
                .eqIfPresent(SvipProductSpDO::getProductId, reqVO.getProductId())
                .eqIfPresent(SvipProductSpDO::getOrderId, reqVO.getOrderId())
                .likeIfPresent(SvipProductSpDO::getProductName, reqVO.getProductName())
                .eqIfPresent(SvipProductSpDO::getSpId, reqVO.getSpId())
                .eqIfPresent(SvipProductSpDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(SvipProductSpDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SvipProductSpDO::getId));
    }

    /**
     * 根据商家获取商家权益可用总数
     * @param spId 商家Id
     * @param productType 权益类型
     * @return
     */
    int getSpTotalPrivilegeNumBySp(@Param("spId") Long spId, @Param("productType") Integer productType);

    /**
     * 根据商家获取商家权益可用权益最新的2组
     * @param spId 商家Id
     * @param productType 权益类型
     * @return
     */
    default  List<SvipProductSpDO> getSpTotalPrivilegeBySp(@Param("spId") Long spId, @Param("productType") Integer productType){
        return selectJoinList(SvipProductSpDO.class,new MPJLambdaWrapperX<SvipProductSpDO>()
                .selectAll(SvipProductSpDO.class)
                .innerJoin(SvipProductDO.class,SvipProductDO::getId,SvipProductSpDO::getProductId)
                .eq(SvipProductSpDO::getSpId,spId)
                .eq(SvipProductDO::getType,productType)
                .in(SvipProductSpDO::getStatus,1,2)
                .orderByDesc(SvipProductSpDO::getStatus)
                .orderByAsc(SvipProductSpDO::getId)
        );
    }

    /**
     * 使用权益数量
     * @param id 权益Id
     * @param privilegeUsedNum 权益数量
     * @return
     */
    Boolean usePrivilegeById(@Param("id") Long id, @Param("privilegeUsedNum") Integer privilegeUsedNum);

    /**
     * 自动过期未使用的权益
     * @return
     */
    int expireBySystem();
}