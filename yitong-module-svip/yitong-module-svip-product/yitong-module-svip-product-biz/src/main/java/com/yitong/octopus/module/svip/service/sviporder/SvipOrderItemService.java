package com.yitong.octopus.module.svip.service.sviporder;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yitong.octopus.module.svip.controller.admin.sviporder.vo.SvipOrderItemPageReqVO;
import com.yitong.octopus.module.svip.controller.admin.sviporder.vo.SvipOrderItemSaveReqVO;
import com.yitong.octopus.module.svip.dal.dataobject.sviporder.SvipOrderItemDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import javax.validation.Valid;
import java.util.List;

/**
 * 增值服务产品订单项 Service 接口
 *
 * <AUTHOR>
 */
public interface SvipOrderItemService extends IService<SvipOrderItemDO> {

    /**
     * 创建增值服务产品订单项
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createOrderItem(@Valid SvipOrderItemSaveReqVO createReqVO);

    /**
     * 更新增值服务产品订单项
     *
     * @param updateReqVO 更新信息
     */
    void updateOrderItem(@Valid SvipOrderItemSaveReqVO updateReqVO);

    /**
     * 删除增值服务产品订单项
     *
     * @param id 编号
     */
    void deleteOrderItem(Long id);

    /**
     * 获得增值服务产品订单项
     *
     * @param id 编号
     * @return 增值服务产品订单项
     */
    SvipOrderItemDO getOrderItem(Long id);

    /**
     * 获得增值服务产品订单项分页
     *
     * @param pageReqVO 分页查询
     * @return 增值服务产品订单项分页
     */
    PageResult<SvipOrderItemDO> getOrderItemPage(SvipOrderItemPageReqVO pageReqVO);


    /**
     * 根据订单号获得增值服务产品订单项
     *
     * @param orderId 订单编号
     * @return 增值服务产品订单项
     */
    List<SvipOrderItemDO> getOrderItemByOrderId(Long orderId);

}