package com.yitong.octopus.module.svip.controller.admin.svipproduct.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 增值服务产品新增/修改 Request VO")
@Data
public class SvipProductSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "5975")
    private Long id;

    @Schema(description = "类型", example = "2")
    private Integer type;

    @Schema(description = "产品名称", example = "王五")
    private String name;

    @Schema(description = "产品图片", example = "https://www.xxxx.com")
    private String  imageUrl;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "状态", example = "2")
    private Boolean status;

    @Schema(description = "活动规则")
    private String ruleInfo;

    @Schema(description = "权益类型", example = "1")
    private Integer privilegeType;

    @Schema(description = "权益数量")
    private Integer privilegeNum;

    @Schema(description = "赠送数量")
    private Integer giveawayNum;

    @Schema(description = "生效日期类型 1 固定日期 2 领取之后", example = "1")
    private Integer validityType;

    @Schema(description = "固定日期-生效开始时间")
    private LocalDateTime validStartTime;

    @Schema(description = "固定日期-生效结束时间")
    private LocalDateTime validEndTime;

    @Schema(description = "领取日期-开始天数")
    private Integer fixedStartTerm;

    @Schema(description = "领取日期-结束天数")
    private Integer fixedEndTerm;

    @Schema(description = "市场价")
    private Long marketAmount;

    @Schema(description = "销售价")
    private Long saleAmount;

    @Schema(description = "月个数")
    private Integer monthNum;

    @Schema(description = "备注", example = "你猜")
    private String remark;

}