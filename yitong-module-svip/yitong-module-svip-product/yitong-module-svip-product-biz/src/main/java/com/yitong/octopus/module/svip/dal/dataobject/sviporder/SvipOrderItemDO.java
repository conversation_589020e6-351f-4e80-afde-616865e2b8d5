package com.yitong.octopus.module.svip.dal.dataobject.sviporder;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;

/**
 * 增值服务产品订单项 DO
 *
 * <AUTHOR>
 */
@TableName("yt_svip_order_item")
@KeySequence("yt_svip_order_item_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SvipOrderItemDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 订单Id
     */
    private Long orderId;
    /**
     * 产品Id
     */
    private Long productId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 市场价
     */
    private Long marketAmount;
    /**
     * 销售价
     */
    private Long saleAmount;
    /**
     * 总价
     */
    private Long totalAmount;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 优惠价格
     */
    private Long discountAmount;
    /**
     * 备注
     */
    private String remark;

}