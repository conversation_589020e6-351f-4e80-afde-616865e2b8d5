package com.yitong.octopus.module.svip.mq.producer.sviporder;

import com.yitong.octopus.module.svip.mq.message.sviporder.SvipOrderSuccessMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 会员用户 Producer
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SvipOrderSuccessProducer {

    @Resource
    private ApplicationContext applicationContext;

    /**
     * 发送 {@link SvipOrderSuccessMessage} 消息
     *
     * @param orderId Svip订单编号
     */
    public void sendOrderSuccessMessage(Long orderId) {
        applicationContext.publishEvent(new SvipOrderSuccessMessage().setOrderId(orderId));
    }

}
