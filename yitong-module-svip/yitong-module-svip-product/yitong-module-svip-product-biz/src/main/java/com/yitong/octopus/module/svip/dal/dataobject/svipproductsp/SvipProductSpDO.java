package com.yitong.octopus.module.svip.dal.dataobject.svipproductsp;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;

/**
 * 增值服务产品商家订单 DO
 *
 * <AUTHOR>
 */
@TableName("yt_svip_product_sp")
@KeySequence("yt_svip_product_sp_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SvipProductSpDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 订单Id
     */
    private Long orderId;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 商家ID
     */
    private Long spId;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 权益数量
     */
    private Integer privilegeNum;
    /**
     * 权益开始时间
     */
    private LocalDateTime privilegeStartTime;
    /**
     * 权益使用数量
     */
    private Integer privilegeUsedNum;
    /**
     * 备注
     */
    private String remark;

}