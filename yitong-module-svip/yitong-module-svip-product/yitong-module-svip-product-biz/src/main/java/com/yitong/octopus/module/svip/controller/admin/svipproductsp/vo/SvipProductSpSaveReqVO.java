package com.yitong.octopus.module.svip.controller.admin.svipproductsp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 增值服务产品商家订单新增/修改 Request VO")
@Data
public class SvipProductSpSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27054")
    private Long id;

    /**
     * 订单Id
     */
    @Schema(description = "订单Id", example = "7182")
    private Long orderId;

    @Schema(description = "商品ID", example = "7182")
    private Long productId;

    @Schema(description = "产品名称", example = "芋艿")
    private String productName;

    @Schema(description = "商家ID", example = "10909")
    private Long spId;

    @Schema(description = "状态", example = "2")
    private Boolean status;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "权益数量")
    private Integer privilegeNum;

    @Schema(description = "权益开始时间")
    private LocalDateTime privilegeStartTime;

    @Schema(description = "权益使用数量")
    private Integer privilegeUsedNum;

    @Schema(description = "备注", example = "你猜")
    private String remark;

}