package com.yitong.octopus.module.svip.controller.admin.svipproductsp.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;
import com.yitong.octopus.framework.excel.core.convert.StringConvert;
import com.yitong.octopus.module.sp.api.utils.jackson.SpMainNameJsonSerializer;
import com.yitong.octopus.module.sp.utils.excel.SpNameConvert;
import com.yitong.octopus.module.svip.enums.DictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


@Schema(description = "管理后台 - 增值服务产品商家订单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SvipProductSpExcelExportVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27054")
    @ExcelProperty(value = "id",converter = StringConvert.class)
    private Long id;

    @Schema(description = "商品ID", example = "7182")
    @ExcelProperty(value = "商品ID",converter = StringConvert.class)
    private Long productId;

    /**
     * 订单Id
     */
    @Schema(description = "订单Id", example = "7182")
    @ExcelProperty(value = "订单Id",converter = StringConvert.class)
    private Long orderId;

    @Schema(description = "产品名称", example = "芋艿")
    @ExcelProperty("产品名称")
    private String productName;

    @Schema(description = "商家ID", example = "10909")
    @ExcelProperty(value = "商家ID",converter = StringConvert.class)
    @JsonSerialize(using = SpMainNameJsonSerializer.class)
    private Long spId;

    @ExcelProperty(value = "商家名称",converter = SpNameConvert.class)
    private Long spName;

    @Schema(description = "状态", example = "2")
    @ExcelProperty(value = "状态",converter = DictConvert.class)
    @DictFormat(DictTypeConstants.YT_SVIP_ORDER_STATUS)
    private Integer status;

    @Schema(description = "开始时间")
    @ExcelProperty("开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @ExcelProperty("结束时间")
    private LocalDateTime endTime;

    @Schema(description = "权益数量")
    @ExcelProperty("权益数量")
    private Integer privilegeNum;

    @Schema(description = "权益开始时间")
    @ExcelProperty("权益开始时间")
    private LocalDateTime privilegeStartTime;

    @Schema(description = "权益使用数量")
    @ExcelProperty("权益使用数量")
    private Integer privilegeUsedNum;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}