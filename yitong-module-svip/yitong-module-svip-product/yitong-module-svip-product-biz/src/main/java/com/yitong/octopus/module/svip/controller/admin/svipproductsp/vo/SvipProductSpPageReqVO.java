package com.yitong.octopus.module.svip.controller.admin.svipproductsp.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 增值服务产品商家订单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SvipProductSpPageReqVO extends PageParam {

    @Schema(description = "商品ID", example = "7182")
    private Long productId;

    /**
     * 订单Id
     */
    @Schema(description = "订单Id", example = "7182")
    private Long orderId;

    @Schema(description = "产品名称", example = "芋艿")
    private String productName;

    @Schema(description = "商家ID", example = "10909")
    private Long spId;

    @Schema(description = "状态", example = "2")
    private Boolean status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}