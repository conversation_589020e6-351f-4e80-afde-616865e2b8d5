package com.yitong.octopus.module.svip.api.productsp;

import com.yitong.octopus.module.svip.api.productsp.vo.SvipProductSpApiVO;
import com.yitong.octopus.module.svip.dal.dataobject.svipproductsp.SvipProductSpDO;
import com.yitong.octopus.module.svip.service.svipproductsp.SvipProductSpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.svip.enums.ErrorCodeConstants.SVIP_PRODUCT_SP_PRIVILEGE_NOT_ENOUGH;

/**
 * 增值服务产品 Service 接口
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SvipProductSpApiImpl implements SvipProductSpApi {

    @Resource
    private SvipProductSpService svipProductSpService;

    @Override
    public SvipProductSpApiVO usePrivilegeBySp(Long spId,Integer productType,Integer privilegeUsedNum) {
        SvipProductSpDO svipProductSp = svipProductSpService.usePrivilegeBySp(spId,productType,privilegeUsedNum);
        return new SvipProductSpApiVO()
                .setProductId(svipProductSp.getProductId())
                .setProductName(svipProductSp.getProductName())
                .setProductSpId(svipProductSp.getId())
                .setPrivilegeUsedNum(privilegeUsedNum)
                .setSpId(spId)
            ;
    }

    @Override
    public void checkPrivilegeBySp(Long spId, Integer productType, Integer privilegeUsedNum) {
        int privilegeTotalNum = svipProductSpService.getSpTotalPrivilegeNumBySp(spId,productType);
        log.info("usePrivilegeBySp spId:{},productType:{},privilegeTotalNum:{}",spId,productType,privilegeTotalNum);
        if (privilegeTotalNum < privilegeUsedNum){
            log.info("usePrivilegeBySp not enough error spId:{},productType:{},privilegeTotalNum:{},privilegeUsedNum:{}",spId,productType,privilegeTotalNum,privilegeUsedNum);
            throw exception(SVIP_PRODUCT_SP_PRIVILEGE_NOT_ENOUGH);
        }
    }
}