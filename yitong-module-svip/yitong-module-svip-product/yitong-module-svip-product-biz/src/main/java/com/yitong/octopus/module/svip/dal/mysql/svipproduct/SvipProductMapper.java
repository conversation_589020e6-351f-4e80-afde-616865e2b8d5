package com.yitong.octopus.module.svip.dal.mysql.svipproduct;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.svip.dal.dataobject.svipproduct.SvipProductDO;
import org.apache.ibatis.annotations.Mapper;
import com.yitong.octopus.module.svip.controller.admin.svipproduct.vo.*;

/**
 * 增值服务产品 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SvipProductMapper extends BaseMapperX<SvipProductDO> {

    default PageResult<SvipProductDO> selectPage(SvipProductPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SvipProductDO>()
                .eqIfPresent(SvipProductDO::getType, reqVO.getType())
                .likeIfPresent(SvipProductDO::getName, reqVO.getName())
                .eqIfPresent(SvipProductDO::getStatus, reqVO.getStatus())
                .eqIfPresent(SvipProductDO::getPrivilegeType, reqVO.getPrivilegeType())
                .orderByDesc(SvipProductDO::getId));
    }

}