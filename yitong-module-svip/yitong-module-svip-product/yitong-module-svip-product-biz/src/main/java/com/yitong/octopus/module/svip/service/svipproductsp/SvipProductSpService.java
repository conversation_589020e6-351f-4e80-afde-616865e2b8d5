package com.yitong.octopus.module.svip.service.svipproductsp;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yitong.octopus.module.svip.controller.admin.svipproductsp.vo.*;
import com.yitong.octopus.module.svip.dal.dataobject.svipproductsp.SvipProductSpDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import javax.validation.Valid;

/**
 * 增值服务产品商家订单 Service 接口
 *
 * <AUTHOR>
 */
public interface SvipProductSpService extends IService<SvipProductSpDO> {

    /**
     * 创建增值服务产品商家订单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProductSp(@Valid SvipProductSpSaveReqVO createReqVO);

    /**
     * 更新增值服务产品商家订单
     *
     * @param updateReqVO 更新信息
     */
    void updateProductSp(@Valid SvipProductSpSaveReqVO updateReqVO);

    /**
     * 删除增值服务产品商家订单
     *
     * @param id 编号
     */
    void deleteProductSp(Long id);

    /**
     * 获得增值服务产品商家订单
     *
     * @param id 编号
     * @return 增值服务产品商家订单
     */
    SvipProductSpDO getProductSp(Long id);

    /**
     * 获得增值服务产品商家订单分页
     *
     * @param pageReqVO 分页查询
     * @return 增值服务产品商家订单分页
     */
    PageResult<SvipProductSpDO> getProductSpPage(SvipProductSpPageReqVO pageReqVO);


    /**
     * 使用商家权益
     * @param spId 商家
     * @param productType 产品类型
     * @param privilegeUsedNum 权益数量
     * @return
     */
    SvipProductSpDO usePrivilegeBySp(Long spId,Integer productType,Integer privilegeUsedNum);

    /**
     * 根据商家获取商家权益可用总数
     * @param spId 商家Id
     * @param productType 权益类型
     * @return
     */
    int getSpTotalPrivilegeNumBySp(Long spId, Integer productType);

    /**
     * 【系统】给商家发送权益
     *
     * @param orderId 订单编号
     */
    void takeSvipProductByOrder(Long orderId);

    /**
     * 【系统】商家权益自动过期
     * @return
     */
    int expireBySystem();
}