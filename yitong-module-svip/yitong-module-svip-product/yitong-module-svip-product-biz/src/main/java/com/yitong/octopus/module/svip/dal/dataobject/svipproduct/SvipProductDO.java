package com.yitong.octopus.module.svip.dal.dataobject.svipproduct;

import com.yitong.octopus.framework.mybatis.core.type.StringListTypeHandler;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;

/**
 * 增值服务产品 DO
 *
 * <AUTHOR>
 */
@TableName("yt_svip_product")
@KeySequence("yt_svip_product_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SvipProductDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 类型
     */
    private Integer type;
    /**
     * 产品名称
     */
    private String name;
    /**
     * 产品图片
     */
    private String  imageUrl;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 状态
     *
     * 枚举 {@link TODO yt_common_status 对应的类}
     */
    private Integer status;
    /**
     * 活动规则
     */
    private String ruleInfo;
    /**
     * 权益类型
     */
    private Integer privilegeType;
    /**
     * 权益数量
     */
    private Integer privilegeNum;
    /**
     * 赠送数量
     */
    private Integer giveawayNum;
    /**
     * 生效日期类型 1 固定日期 2 领取之后
     */
    private Integer validityType;
    /**
     * 固定日期-生效开始时间
     */
    private LocalDateTime validStartTime;
    /**
     * 固定日期-生效结束时间
     */
    private LocalDateTime validEndTime;
    /**
     * 领取日期-开始天数
     */
    private Integer fixedStartTerm;
    /**
     * 领取日期-结束天数
     */
    private Integer fixedEndTerm;
    /**
     * 月个数【仅在按月时候生效】
     */
    private Integer monthNum;
    /**
     * 市场价
     */
    private Long marketAmount;
    /**
     * 销售价
     */
    private Long saleAmount;
    /**
     * 备注
     */
    private String remark;

}