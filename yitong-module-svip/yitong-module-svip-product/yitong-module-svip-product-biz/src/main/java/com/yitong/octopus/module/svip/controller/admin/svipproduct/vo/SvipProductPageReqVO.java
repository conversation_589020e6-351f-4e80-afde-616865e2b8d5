package com.yitong.octopus.module.svip.controller.admin.svipproduct.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 增值服务产品分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SvipProductPageReqVO extends PageParam {

    @Schema(description = "类型", example = "2")
    private Integer type;

    @Schema(description = "产品名称", example = "王五")
    private String name;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "权益类型", example = "1")
    private Integer privilegeType;

}