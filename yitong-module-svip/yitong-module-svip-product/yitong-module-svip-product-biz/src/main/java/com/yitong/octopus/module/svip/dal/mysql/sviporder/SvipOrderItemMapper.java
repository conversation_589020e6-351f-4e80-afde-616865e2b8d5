package com.yitong.octopus.module.svip.dal.mysql.sviporder;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.svip.controller.admin.sviporder.vo.SvipOrderItemPageReqVO;
import com.yitong.octopus.module.svip.dal.dataobject.sviporder.SvipOrderItemDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 增值服务产品订单项 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SvipOrderItemMapper extends BaseMapperX<SvipOrderItemDO> {

    default PageResult<SvipOrderItemDO> selectPage(SvipOrderItemPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SvipOrderItemDO>()
                .eqIfPresent(SvipOrderItemDO::getOrderId, reqVO.getOrderId())
                .eqIfPresent(SvipOrderItemDO::getProductId, reqVO.getProductId())
                .orderByDesc(SvipOrderItemDO::getId));
    }

}