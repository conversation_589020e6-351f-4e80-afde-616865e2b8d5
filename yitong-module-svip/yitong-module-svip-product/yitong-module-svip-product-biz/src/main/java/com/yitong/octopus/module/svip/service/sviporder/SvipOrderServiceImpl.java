package com.yitong.octopus.module.svip.service.sviporder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.yitong.octopus.framework.common.util.string.StringUtils;
import com.yitong.octopus.module.sp.dal.dataobject.maininfo.SpMainInfoDO;
import com.yitong.octopus.module.sp.service.maininfo.SpMainInfoService;
import com.yitong.octopus.module.svip.dal.dataobject.sviporder.SvipOrderItemDO;
import com.yitong.octopus.module.svip.dal.dataobject.svipproduct.SvipProductDO;
import com.yitong.octopus.module.svip.enums.order.SvipOrderStatusEnum;
import com.yitong.octopus.module.svip.enums.order.SvipOrderCancelTypeEnum;
import com.yitong.octopus.module.svip.mq.producer.sviporder.SvipOrderSuccessProducer;
import com.yitong.octopus.module.svip.service.svipproduct.SvipProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yitong.octopus.module.svip.controller.admin.sviporder.vo.*;
import com.yitong.octopus.module.svip.dal.dataobject.sviporder.SvipOrderDO;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;

import com.yitong.octopus.module.svip.dal.mysql.sviporder.SvipOrderMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.sp.enums.ErrorCodeConstants.MAIN_INFO_NOT_EXISTS;
import static com.yitong.octopus.module.svip.enums.ErrorCodeConstants.*;

/**
 * 增值服务产品订单 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class SvipOrderServiceImpl extends ServiceImpl<SvipOrderMapper,SvipOrderDO>  implements SvipOrderService {

    @Resource
    private SvipOrderItemService svipOrderItemService;

    @Resource
    private SvipProductService svipProductService;

    @Resource
    private SpMainInfoService spMainInfoService;

    @Resource
    private SvipOrderSuccessProducer svipOrderSuccessProducer;

    @Override
    public Long createOrder(SvipOrderSaveReqVO reqVO) {
        // 插入
        SvipOrderDO order = BeanUtils.toBean(reqVO, SvipOrderDO.class);
        getBaseMapper().insert(order);
        // 返回
        return order.getId();
    }

    @Override
    public void updateOrder(SvipOrderSaveReqVO reqVO) {
        // 校验存在
        validateOrderExists(reqVO.getId());
        // 更新
        SvipOrderDO updateObj = BeanUtils.toBean(reqVO, SvipOrderDO.class);
        getBaseMapper().updateById(updateObj);
    }

    @Override
    public void deleteOrder(Long id) {
        // 校验存在
        validateOrderExists(id);
        // 删除
        getBaseMapper().deleteById(id);
    }

    private void validateOrderExists(Long id) {
        if (getBaseMapper().selectById(id) == null) {
            throw exception(SVIP_ORDER_NOT_EXISTS);
        }
    }

    @Override
    public SvipOrderDO getOrder(Long id) {
        return getBaseMapper().selectById(id);
    }

    @Override
    public PageResult<SvipOrderDO> getOrderPage(SvipOrderPageReqVO pageReqVO) {
        return getBaseMapper().selectPage(pageReqVO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createOrderBySp(SvipOrderSaveSpReqVO reqVO) {
        SvipProductDO svipProductDO = svipProductService.getProduct(reqVO.getProductId());
        if (ObjectUtil.isNull(svipProductDO)){
            throw exception(SVIP_PRODUCT_NOT_EXISTS);
        }

        SpMainInfoDO spMainInfoDO = spMainInfoService.getMainInfo(reqVO.getSpId());
        if (ObjectUtil.isNull(spMainInfoDO)){
            throw exception(MAIN_INFO_NOT_EXISTS);
        }

        Long totalAmount = svipProductDO.getSaleAmount() * reqVO.getQuantity();
        Long payAmount = totalAmount - reqVO.getDiscountAmount();

        if (!totalAmount.equals(reqVO.getTotalAmount()) || !payAmount.equals(reqVO.getPayAmount())){
            throw exception(SVIP_ORDER_VALID_AMOUNT_ERROR);
        }

        //计算总金额
        SvipOrderDO svipOrderDO = new SvipOrderDO()
                .setOrderNum(IdUtil.getSnowflakeNextIdStr())
                .setOrderTime(LocalDateTime.now())
                .setPayExpireTime(LocalDateTime.now().plusMinutes(30))
                .setSpId(reqVO.getSpId())
                .setQuantity(reqVO.getQuantity())
                .setDiscountAmount(reqVO.getDiscountAmount())
                .setTotalAmount(reqVO.getTotalAmount())
                .setPayAmount(reqVO.getPayAmount())
                .setProductAmount(svipProductDO.getSaleAmount())
                .setPayChannel(reqVO.getPayChannel())
                .setPayChannelAmount(0L)
                .setPayTime(LocalDateTime.now())
                .setRemark(reqVO.getRemark())
                .setStatus(SvipOrderStatusEnum.UNPAID.getStatus());

        this.save(svipOrderDO);

        SvipOrderItemDO svipOrderItemDO = new SvipOrderItemDO()
                .setOrderId(svipOrderDO.getId())
                .setTotalAmount(reqVO.getTotalAmount())
                .setDiscountAmount(reqVO.getDiscountAmount())
                .setMarketAmount(svipProductDO.getMarketAmount())
                .setSaleAmount(svipProductDO.getSaleAmount())
                .setQuantity(reqVO.getQuantity())
                .setProductId(svipProductDO.getId())
                .setProductName(svipProductDO.getName());
        svipOrderItemService.save(svipOrderItemDO);
        //直接支付成功，给商家发送权益
        if (svipOrderDO.getPayAmount() == 0 || StringUtils.isNotEmpty(reqVO.getOutOrderNum())) {
            svipOrderDO.setStatus(SvipOrderStatusEnum.PAID.getStatus());
            svipOrderDO.setOutOrderNum(StringUtils.defaultString(reqVO.getOutOrderNum(),IdUtil.getSnowflakeNextIdStr()));
            svipOrderDO.setPayTime(LocalDateTime.now());
            this.updateById(svipOrderDO);
            // 通知商家获取权益
            svipOrderSuccessProducer.sendOrderSuccessMessage(svipOrderDO.getId());
        }
        return svipOrderDO.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void payOrderBySp(SvipOrderPayReqVO reqVO) {
        SvipOrderDO svipOrderDO = getOrder(reqVO.getId());
        if (ObjectUtil.isNull(svipOrderDO)){
            throw exception(SVIP_ORDER_NOT_EXISTS);
        }
        if (!SvipOrderStatusEnum.UNPAID.getStatus().equals(svipOrderDO.getStatus())){
            throw exception(SVIP_ORDER_VALID_STATUS_ERROR);
        }
        //复制属性值
        BeanUtils.copyProperties(reqVO,svipOrderDO);
        svipOrderDO.setStatus(SvipOrderStatusEnum.PAID.getStatus());
        svipOrderDO.setPayTime(LocalDateTime.now());

        this.updateById(svipOrderDO);

        // 通知商家获取权益
        svipOrderSuccessProducer.sendOrderSuccessMessage(svipOrderDO.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelOrderBySp(SvipOrderCancelReqVO reqVO) {
        SvipOrderDO svipOrderDO = getOrder(reqVO.getId());
        if (ObjectUtil.isNull(svipOrderDO)){
            throw exception(SVIP_ORDER_NOT_EXISTS);
        }
        if (!SvipOrderStatusEnum.UNPAID.getStatus().equals(svipOrderDO.getStatus())){
            throw exception(SVIP_ORDER_VALID_STATUS_ERROR);
        }
        svipOrderDO.setCancelTime(LocalDateTime.now());
        svipOrderDO.setCancelType(reqVO.getCancelType());
        svipOrderDO.setStatus(SvipOrderStatusEnum.CANCELED.getStatus());
        this.updateById(svipOrderDO);
    }

    @Override
    public int cancelOrderBySystem() {
        List<SvipOrderDO> orders = getBaseMapper().selectListByStatusAndCreateTimeLt(SvipOrderStatusEnum.UNPAID.getStatus(), LocalDateTime.now());
        if (CollUtil.isEmpty(orders)) {
            return 0;
        }

        // 2. 遍历执行，逐个取消
        int count = 0;
        for (SvipOrderDO order : orders) {
            try {
                getSelf().cancelOrderBySystem(order);
                count++;
            } catch (Throwable e) {
                log.error("[cancelOrderBySystem][order({}) 过期订单异常]", order.getId(), e);
            }
        }
        return count;
    }

    /**
     * 自动取消单个订单
     *
     * @param order 订单
     */
    @Transactional(rollbackFor = Exception.class)
//    @TradeOrderLog(operateType = TradeOrderOperateTypeEnum.SYSTEM_CANCEL)
    public void cancelOrderBySystem(SvipOrderDO order) {
        cancelOrder0(order, SvipOrderCancelTypeEnum.PAY_TIMEOUT);
    }

    /**
     * 取消订单的核心实现
     *
     * @param order      订单
     * @param cancelType 取消类型
     */
    private void cancelOrder0(SvipOrderDO order, SvipOrderCancelTypeEnum cancelType) {
        // 1. 更新 TradeOrderDO 状态为已取消
        int updateCount = getBaseMapper().updateByIdAndStatus(order.getId(), order.getStatus(),
                new SvipOrderDO().setStatus(SvipOrderStatusEnum.CANCELED.getStatus())
                        .setCancelType(cancelType.getType()).setCancelTime(LocalDateTime.now()));
        if (updateCount == 0) {
            throw exception(SVIP_ORDER_CANCEL_FAIL_STATUS_NOT_UNPAID);
        }
    }

    // =================== 营销相关的操作 ===================

    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private SvipOrderServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }
}