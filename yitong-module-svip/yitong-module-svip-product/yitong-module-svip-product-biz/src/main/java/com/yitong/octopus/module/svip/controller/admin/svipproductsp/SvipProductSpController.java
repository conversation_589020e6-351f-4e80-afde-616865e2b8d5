package com.yitong.octopus.module.svip.controller.admin.svipproductsp;

import com.yitong.octopus.framework.common.util.collection.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import java.util.*;
import java.io.IOException;
import com.yitong.octopus.framework.common.pojo.PageParam;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.svip.controller.admin.svipproductsp.vo.*;
import com.yitong.octopus.module.svip.dal.dataobject.svipproductsp.SvipProductSpDO;
import com.yitong.octopus.module.svip.service.svipproductsp.SvipProductSpService;

@Tag(name = "管理后台 - 增值服务产品商家订单")
@RestController
@RequestMapping("/svip/product-sp")
@Validated
public class SvipProductSpController {

    @Resource
    private SvipProductSpService productSpService;

    @PostMapping("/create")
    @Operation(summary = "创建增值服务产品商家订单")
    @PreAuthorize("@ss.hasPermission('svip:product-sp:create')")
    public CommonResult<Long> createProductSp(@Valid @RequestBody SvipProductSpSaveReqVO createReqVO) {
        return success(productSpService.createProductSp(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新增值服务产品商家订单")
    @PreAuthorize("@ss.hasPermission('svip:product-sp:update')")
    public CommonResult<Boolean> updateProductSp(@Valid @RequestBody SvipProductSpSaveReqVO updateReqVO) {
        productSpService.updateProductSp(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除增值服务产品商家订单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('svip:product-sp:delete')")
    public CommonResult<Boolean> deleteProductSp(@RequestParam("id") Long id) {
        productSpService.deleteProductSp(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得增值服务产品商家订单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('svip:product-sp:query')")
    public CommonResult<SvipProductSpRespVO> getProductSp(@RequestParam("id") Long id) {
        SvipProductSpDO productSp = productSpService.getProductSp(id);
        return success(BeanUtils.toBean(productSp, SvipProductSpRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得增值服务产品商家订单分页")
    @PreAuthorize("@ss.hasPermission('svip:product-sp:query')")
    public CommonResult<PageResult<SvipProductSpRespVO>> getProductSpPage(@Valid SvipProductSpPageReqVO pageReqVO) {
        PageResult<SvipProductSpDO> pageResult = productSpService.getProductSpPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SvipProductSpRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出增值服务产品商家订单 Excel")
    @PreAuthorize("@ss.hasPermission('svip:product-sp:export')")
    public void exportProductSpExcel(@Valid SvipProductSpPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SvipProductSpDO> list = productSpService.getProductSpPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "增值服务产品商家订单.xls", "数据", SvipProductSpExcelExportVO.class,
                CollectionUtils.convertList(list, s ->
                    BeanUtils.toBean(s,SvipProductSpExcelExportVO.class).setSpName(s.getSpId())
                ));
    }

}