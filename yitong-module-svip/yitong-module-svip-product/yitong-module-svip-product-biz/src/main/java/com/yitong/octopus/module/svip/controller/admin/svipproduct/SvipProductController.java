package com.yitong.octopus.module.svip.controller.admin.svipproduct;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageParam;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.svip.controller.admin.svipproduct.vo.*;
import com.yitong.octopus.module.svip.dal.dataobject.svipproduct.SvipProductDO;
import com.yitong.octopus.module.svip.service.svipproduct.SvipProductService;

@Tag(name = "管理后台 - 增值服务产品")
@RestController
@RequestMapping("/svip/product")
@Validated
public class SvipProductController {

    @Resource
    private SvipProductService productService;

    @PostMapping("/create")
    @Operation(summary = "创建增值服务产品")
    @PreAuthorize("@ss.hasPermission('svip:product:create')")
    public CommonResult<Long> createProduct(@Valid @RequestBody SvipProductSaveReqVO createReqVO) {
        return success(productService.createProduct(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新增值服务产品")
    @PreAuthorize("@ss.hasPermission('svip:product:update')")
    public CommonResult<Boolean> updateProduct(@Valid @RequestBody SvipProductSaveReqVO updateReqVO) {
        productService.updateProduct(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除增值服务产品")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('svip:product:delete')")
    public CommonResult<Boolean> deleteProduct(@RequestParam("id") Long id) {
        productService.deleteProduct(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得增值服务产品")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('svip:product:query')")
    public CommonResult<SvipProductRespVO> getProduct(@RequestParam("id") Long id) {
        SvipProductDO product = productService.getProduct(id);
        return success(BeanUtils.toBean(product, SvipProductRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得增值服务产品分页")
    @PreAuthorize("@ss.hasPermission('svip:product:query')")
    public CommonResult<PageResult<SvipProductRespVO>> getProductPage(@Valid SvipProductPageReqVO pageReqVO) {
        PageResult<SvipProductDO> pageResult = productService.getProductPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SvipProductRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出增值服务产品 Excel")
    @PreAuthorize("@ss.hasPermission('svip:product:export')")
    public void exportProductExcel(@Valid SvipProductPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SvipProductDO> list = productService.getProductPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "增值服务产品.xls", "数据", SvipProductRespVO.class,
                        BeanUtils.toBean(list, SvipProductRespVO.class));
    }

}