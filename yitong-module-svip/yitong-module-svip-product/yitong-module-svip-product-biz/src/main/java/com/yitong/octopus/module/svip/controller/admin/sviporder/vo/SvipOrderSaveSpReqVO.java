package com.yitong.octopus.module.svip.controller.admin.sviporder.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.yitong.octopus.framework.common.validation.InEnum;
import com.yitong.octopus.framework.excel.core.convert.MoneyConvert;
import com.yitong.octopus.framework.jackson.core.databind.MoneyY2FDeserializer;
import com.yitong.octopus.module.svip.enums.order.SvipOrderPayChannelEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * {"spId":"1822910692478353409","spName":"开县徐妈串串","productId":"1803696482521268226","productName":"笔记畅享100篇","productAmount":"100.00","quantity":1,"totalAmount":100,"discountAmount":0,"payAmount":100,"payChannel":999}
 */
@Schema(description = "管理后台 - 增值服务产品商家订单 Request VO")
@Data
public class SvipOrderSaveSpReqVO {

    @Schema(description = "商家ID", example = "13907")
    private Long spId;

    @Schema(description = "商品Id")
    private Long productId;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "总金额")
    @JsonDeserialize(using = MoneyY2FDeserializer.class)
    private Long totalAmount;

    @Schema(description = "付款金额")
    @JsonDeserialize(using = MoneyY2FDeserializer.class)
    private Long payAmount;

    @Schema(description = "平台和商家优惠")
    @JsonDeserialize(using = MoneyY2FDeserializer.class)
    private Long discountAmount = 0L;

    @Schema(description = "数量")
    private Integer quantity;

    @Schema(description = "支付渠道")
    @InEnum(SvipOrderPayChannelEnum.class)
    private Integer payChannel;

    @Schema(description = "支付渠道费")
    @JsonDeserialize(using = MoneyY2FDeserializer.class)
    private Long payChannelAmount;

    @Schema(description = "三方订单编码")
    private String outOrderNum;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

}