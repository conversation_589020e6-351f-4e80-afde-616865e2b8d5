package com.yitong.octopus.module.svip.controller.admin.sviporder;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageParam;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.svip.controller.admin.sviporder.vo.*;
import com.yitong.octopus.module.svip.dal.dataobject.sviporder.SvipOrderDO;
import com.yitong.octopus.module.svip.service.sviporder.SvipOrderService;

@Tag(name = "管理后台 - 增值服务产品订单")
@RestController
@RequestMapping("/svip/order")
@Validated
public class SvipOrderController {

    @Resource
    private SvipOrderService orderService;

    @PostMapping("/create")
    @Operation(summary = "创建增值服务产品订单")
    @PreAuthorize("@ss.hasPermission('svip:order:create')")
    public CommonResult<Long> createOrder(@Valid @RequestBody SvipOrderSaveReqVO createReqVO) {
        return success(orderService.createOrder(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新增值服务产品订单")
    @PreAuthorize("@ss.hasPermission('svip:order:update')")
    public CommonResult<Boolean> updateOrder(@Valid @RequestBody SvipOrderSaveReqVO updateReqVO) {
        orderService.updateOrder(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除增值服务产品订单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('svip:order:delete')")
    public CommonResult<Boolean> deleteOrder(@RequestParam("id") Long id) {
        orderService.deleteOrder(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得增值服务产品订单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('svip:order:query')")
    public CommonResult<SvipOrderRespVO> getOrder(@RequestParam("id") Long id) {
        SvipOrderDO order = orderService.getOrder(id);
        return success(BeanUtils.toBean(order, SvipOrderRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得增值服务产品订单分页")
    @PreAuthorize("@ss.hasPermission('svip:order:query')")
    public CommonResult<PageResult<SvipOrderRespVO>> getOrderPage(@Valid SvipOrderPageReqVO pageReqVO) {
        PageResult<SvipOrderDO> pageResult = orderService.getOrderPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SvipOrderRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出增值服务产品订单 Excel")
    @PreAuthorize("@ss.hasPermission('svip:order:export')")
    public void exportOrderExcel(@Valid SvipOrderPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SvipOrderDO> list = orderService.getOrderPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "增值服务产品订单.xls", "数据", SvipOrderRespVO.class,
                        BeanUtils.toBean(list, SvipOrderRespVO.class));
    }

    @PostMapping("/create-sp")
    @Operation(summary = "创建增值服务产品订单商品按照商家")
    @PreAuthorize("@ss.hasPermission('svip:order:create')")
    public CommonResult<Long> createOrderBySp(@Valid @RequestBody SvipOrderSaveSpReqVO reqVO) {
        return success(orderService.createOrderBySp(reqVO));
    }

    @PostMapping("/pay")
    @Operation(summary = "支付产品")
    @PreAuthorize("@ss.hasPermission('svip:order:update')")
    public CommonResult<Boolean> payOrderBySp(@Valid @RequestBody SvipOrderPayReqVO reqVO) {
        orderService.payOrderBySp(reqVO);
        return success(true);
    }

    @PostMapping("/cancel")
    @Operation(summary = "取消产品")
    @PreAuthorize("@ss.hasPermission('svip:order:update')")
    public CommonResult<Boolean> cancelOrderBySp(@Valid @RequestBody SvipOrderCancelReqVO reqVO) {
        orderService.cancelOrderBySp(reqVO);
        return success(true);
    }



}