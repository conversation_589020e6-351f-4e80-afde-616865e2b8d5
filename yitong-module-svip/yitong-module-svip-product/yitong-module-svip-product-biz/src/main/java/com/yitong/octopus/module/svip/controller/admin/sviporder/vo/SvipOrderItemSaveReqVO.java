package com.yitong.octopus.module.svip.controller.admin.sviporder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;

@Schema(description = "管理后台 - 增值服务产品订单项新增/修改 Request VO")
@Data
public class SvipOrderItemSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "22064")
    private Long id;

    @Schema(description = "订单Id", example = "29696")
    private Long orderId;

    @Schema(description = "产品Id", example = "5934")
    private Long productId;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "市场价")
    private Long marketAmount;

    @Schema(description = "市场价")
    private Long total;

    @Schema(description = "销售价")
    private Long saleAmount;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

}