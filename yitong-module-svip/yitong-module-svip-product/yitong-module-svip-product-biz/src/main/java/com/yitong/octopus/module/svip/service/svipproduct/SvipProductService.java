package com.yitong.octopus.module.svip.service.svipproduct;

import java.util.*;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yitong.octopus.module.svip.controller.admin.svipproduct.vo.*;
import com.yitong.octopus.module.svip.dal.dataobject.svipproduct.SvipProductDO;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.PageParam;

import javax.validation.Valid;

/**
 * 增值服务产品 Service 接口
 *
 * <AUTHOR>
 */
public interface SvipProductService extends IService<SvipProductDO> {

    /**
     * 创建增值服务产品
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProduct(@Valid SvipProductSaveReqVO createReqVO);

    /**
     * 更新增值服务产品
     *
     * @param updateReqVO 更新信息
     */
    void updateProduct(@Valid SvipProductSaveReqVO updateReqVO);

    /**
     * 删除增值服务产品
     *
     * @param id 编号
     */
    void deleteProduct(Long id);

    /**
     * 获得增值服务产品
     *
     * @param id 编号
     * @return 增值服务产品
     */
    SvipProductDO getProduct(Long id);

    /**
     * 获得增值服务产品分页
     *
     * @param pageReqVO 分页查询
     * @return 增值服务产品分页
     */
    PageResult<SvipProductDO> getProductPage(SvipProductPageReqVO pageReqVO);

}