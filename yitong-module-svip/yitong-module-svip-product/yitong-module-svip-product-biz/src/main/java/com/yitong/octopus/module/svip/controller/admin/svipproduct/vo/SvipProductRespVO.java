package com.yitong.octopus.module.svip.controller.admin.svipproduct.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 增值服务产品 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SvipProductRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "5975")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "类型", example = "2")
    @ExcelProperty("类型")
    private Integer type;

    @Schema(description = "产品名称", example = "王五")
    @ExcelProperty("产品名称")
    private String name;

    @Schema(description = "产品图片", example = "https://www.xxxx.com")
    @ExcelProperty("产品图片")
    private String imageUrl;

    @Schema(description = "开始时间")
    @ExcelProperty("开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @ExcelProperty("结束时间")
    private LocalDateTime endTime;

    @Schema(description = "状态", example = "2")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("yt_common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "活动规则")
    @ExcelProperty("活动规则")
    private String ruleInfo;

    @Schema(description = "权益类型", example = "1")
    @ExcelProperty("权益类型")
    private Integer privilegeType;

    @Schema(description = "权益数量")
    @ExcelProperty("权益数量")
    private Integer privilegeNum;

    @Schema(description = "赠送数量")
    @ExcelProperty("赠送数量")
    private Integer giveawayNum;

    @Schema(description = "生效日期类型 1 固定日期 2 领取之后", example = "1")
    @ExcelProperty("生效日期类型 1 固定日期 2 领取之后")
    private Integer validityType;

    @Schema(description = "固定日期-生效开始时间")
    @ExcelProperty("固定日期-生效开始时间")
    private LocalDateTime validStartTime;

    @Schema(description = "固定日期-生效结束时间")
    @ExcelProperty("固定日期-生效结束时间")
    private LocalDateTime validEndTime;

    @Schema(description = "领取日期-开始天数")
    @ExcelProperty("领取日期-开始天数")
    private Integer fixedStartTerm;

    @Schema(description = "领取日期-结束天数")
    @ExcelProperty("领取日期-结束天数")
    private Integer fixedEndTerm;

    @Schema(description = "市场价")
    @ExcelProperty("市场价")
    private Long marketAmount;
    /**
     * 月个数【仅在按月时候生效】
     */
    @Schema(description = "月个数")
    private Integer monthNum;

    @Schema(description = "销售价")
    @ExcelProperty("销售价")
    private Long saleAmount;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "备注", example = "你猜")
    private String remark;

}