package com.yitong.octopus.module.svip.controller.admin.sviporder.vo;

import com.yitong.octopus.framework.common.validation.InEnum;
import com.yitong.octopus.module.svip.enums.order.SvipOrderPayChannelEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 增值服务产品订单支付 Request VO")
@Data
public class SvipOrderPayReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1553")
    @NotNull(message = "订单编号不能为空")
    private Long id;

    @Schema(description = "三方订单交易")
    @NotNull(message = "支付渠道不能为空")
    @InEnum(SvipOrderPayChannelEnum.class)
    private Integer payChannel;

    @Schema(description = "三方订单交易")
    @NotEmpty(message = "三方交易编码不能为空")
    private String outOrderNum;

    @Schema(description = "支付渠道费")
    private Long payChannelAmount;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

}