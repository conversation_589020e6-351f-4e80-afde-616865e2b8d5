package com.yitong.octopus.module.svip.service.sviporder;

import java.util.*;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yitong.octopus.module.svip.controller.admin.sviporder.vo.*;
import com.yitong.octopus.module.svip.dal.dataobject.sviporder.SvipOrderDO;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.PageParam;

import javax.validation.Valid;

/**
 * 增值服务产品订单 Service 接口
 *
 * <AUTHOR>
 */
public interface SvipOrderService extends IService<SvipOrderDO> {

    /**
     * 创建增值服务产品订单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long createOrder(@Valid SvipOrderSaveReqVO reqVO);

    /**
     * 更新增值服务产品订单
     *
     * @param reqVO 更新信息
     */
    void updateOrder(@Valid SvipOrderSaveReqVO reqVO);

    /**
     * 删除增值服务产品订单
     *
     * @param id 编号
     */
    void deleteOrder(Long id);

    /**
     * 获得增值服务产品订单
     *
     * @param id 编号
     * @return 增值服务产品订单
     */
    SvipOrderDO getOrder(Long id);

    /**
     * 获得增值服务产品订单分页
     *
     * @param pageReqVO 分页查询
     * @return 增值服务产品订单分页
     */
    PageResult<SvipOrderDO> getOrderPage(SvipOrderPageReqVO pageReqVO);

    /**
     * 创建增值服务产品订单
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long createOrderBySp(@Valid SvipOrderSaveSpReqVO reqVO);

    /**
     * 支付值服务产品订单
     *
     * @param reqVO 支付产品信息
     * @return 编号
     */
    void payOrderBySp(@Valid SvipOrderPayReqVO reqVO);

    /**
     * 取消值服务产品订单
     *
     * @param reqVO 支付产品信息
     * @return 编号
     */
    void cancelOrderBySp(@Valid SvipOrderCancelReqVO reqVO);


    /**
     * 【系统】自动取消订单
     *
     * @return 取消数量
     */
    int cancelOrderBySystem();
}