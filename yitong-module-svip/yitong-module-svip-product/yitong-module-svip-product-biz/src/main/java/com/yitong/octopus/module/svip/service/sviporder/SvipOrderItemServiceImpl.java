package com.yitong.octopus.module.svip.service.sviporder;

import com.yitong.octopus.module.svip.controller.admin.sviporder.vo.SvipOrderItemPageReqVO;
import com.yitong.octopus.module.svip.controller.admin.sviporder.vo.SvipOrderItemSaveReqVO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yitong.octopus.module.svip.dal.dataobject.sviporder.SvipOrderItemDO;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;

import com.yitong.octopus.module.svip.dal.mysql.sviporder.SvipOrderItemMapper;

import java.util.List;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.svip.enums.ErrorCodeConstants.*;

/**
 * 增值服务产品订单项 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SvipOrderItemServiceImpl extends ServiceImpl<SvipOrderItemMapper,SvipOrderItemDO>  implements SvipOrderItemService {

    @Override
    public Long createOrderItem(SvipOrderItemSaveReqVO createReqVO) {
        // 插入
        SvipOrderItemDO orderItem = BeanUtils.toBean(createReqVO, SvipOrderItemDO.class);
        getBaseMapper().insert(orderItem);
        // 返回
        return orderItem.getId();
    }

    @Override
    public void updateOrderItem(SvipOrderItemSaveReqVO updateReqVO) {
        // 校验存在
        validateOrderItemExists(updateReqVO.getId());
        // 更新
        SvipOrderItemDO updateObj = BeanUtils.toBean(updateReqVO, SvipOrderItemDO.class);
        getBaseMapper().updateById(updateObj);
    }

    @Override
    public void deleteOrderItem(Long id) {
        // 校验存在
        validateOrderItemExists(id);
        // 删除
        getBaseMapper().deleteById(id);
    }

    private void validateOrderItemExists(Long id) {
        if (getBaseMapper().selectById(id) == null) {
            throw exception(SVIP_ORDER_ITEM_NOT_EXISTS);
        }
    }

    @Override
    public SvipOrderItemDO getOrderItem(Long id) {
        return getBaseMapper().selectById(id);
    }

    @Override
    public PageResult<SvipOrderItemDO> getOrderItemPage(SvipOrderItemPageReqVO pageReqVO) {
        return getBaseMapper().selectPage(pageReqVO);
    }

    @Override
    public List<SvipOrderItemDO> getOrderItemByOrderId(Long orderId) {
        return getBaseMapper().selectList(SvipOrderItemDO::getOrderId,orderId);
    }

}