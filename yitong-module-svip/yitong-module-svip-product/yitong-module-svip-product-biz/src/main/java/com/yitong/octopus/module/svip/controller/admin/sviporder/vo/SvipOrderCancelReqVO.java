package com.yitong.octopus.module.svip.controller.admin.sviporder.vo;

import com.yitong.octopus.framework.common.validation.InEnum;
import com.yitong.octopus.module.svip.enums.order.SvipOrderCancelTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 增值服务产品订单取消 Request VO")
@Data
public class SvipOrderCancelReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1553")
    @NotNull(message = "订单编号不能为空")
    private Long id;

    @Schema(description = "取消类型")
    @NotEmpty(message = "取消类型")
    @InEnum(SvipOrderCancelTypeEnum.class)
    private Integer cancelType;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

}