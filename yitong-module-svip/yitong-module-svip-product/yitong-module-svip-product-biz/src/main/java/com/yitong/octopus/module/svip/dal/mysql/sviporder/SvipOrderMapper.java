package com.yitong.octopus.module.svip.dal.mysql.sviporder;

import java.time.LocalDateTime;
import java.util.*;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.svip.dal.dataobject.sviporder.SvipOrderDO;
import org.apache.ibatis.annotations.Mapper;
import com.yitong.octopus.module.svip.controller.admin.sviporder.vo.*;

/**
 * 增值服务产品订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SvipOrderMapper extends BaseMapperX<SvipOrderDO> {

    default PageResult<SvipOrderDO> selectPage(SvipOrderPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SvipOrderDO>()
                .eqIfPresent(SvipOrderDO::getSpId, reqVO.getSpId())
                .eqIfPresent(SvipOrderDO::getOrderNum, reqVO.getOrderNum())
                .betweenIfPresent(SvipOrderDO::getOrderTime, reqVO.getOrderTime())
                .betweenIfPresent(SvipOrderDO::getPayTime, reqVO.getPayTime())
                .likeIfPresent(SvipOrderDO::getOutOrderNum, reqVO.getOutOrderNum())
                .eqIfPresent(SvipOrderDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(SvipOrderDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SvipOrderDO::getId));
    }

    default int updateByIdAndStatus(Long id, Integer status, SvipOrderDO update) {
        return update(update, new LambdaUpdateWrapper<SvipOrderDO>()
                .eq(SvipOrderDO::getId, id).eq(SvipOrderDO::getStatus, status));
    }

    default List<SvipOrderDO> selectListByStatusAndCreateTimeLt(Integer status, LocalDateTime createTime) {
        return selectList(new LambdaUpdateWrapper<SvipOrderDO>()
                .eq(SvipOrderDO::getStatus, status)
                .lt(SvipOrderDO::getPayExpireTime, createTime));
    }


}