package com.yitong.octopus.module.svip.controller.admin.sviporder.vo;

import com.yitong.octopus.framework.common.validation.InEnum;
import com.yitong.octopus.module.svip.enums.order.SvipOrderStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 增值服务产品订单新增/修改 Request VO")
@Data
public class SvipOrderSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1553")
    private Long id;

    @Schema(description = "商家ID", example = "13907")
    private Long spId;

    @Schema(description = "订单编码")
    private String orderNum;

    @Schema(description = "下单时间")
    private LocalDateTime orderTime;

    @Schema(description = "支付时间")
    private LocalDateTime payTime;

    @Schema(description = "三方订单编码")
    private String outOrderNum;

    @Schema(description = "状态", example = "2")
    @InEnum(SvipOrderStatusEnum.class)
    private Integer status;

    @Schema(description = "商品金额")
    private Integer productAmount;

    @Schema(description = "总金额")
    private Integer totalAmount;

    @Schema(description = "付款金额")
    private Integer payAmount;

    @Schema(description = "平台和商家优惠")
    private Integer discountAmount;

    @Schema(description = "支付渠道")
    private Integer payChannel;

    @Schema(description = "支付渠道费")
    private Integer payChannelAmount;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

}