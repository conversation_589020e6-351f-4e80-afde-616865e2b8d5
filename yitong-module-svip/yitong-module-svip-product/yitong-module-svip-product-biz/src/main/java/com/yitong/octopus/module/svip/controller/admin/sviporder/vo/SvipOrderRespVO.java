package com.yitong.octopus.module.svip.controller.admin.sviporder.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yitong.octopus.framework.common.validation.InEnum;
import com.yitong.octopus.framework.jackson.core.databind.MoneyF2YSerializer;
import com.yitong.octopus.module.sp.api.utils.jackson.SpMainNameJsonSerializer;
import com.yitong.octopus.module.svip.enums.order.SvipOrderStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 增值服务产品订单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SvipOrderRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1553")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "商家ID", example = "13907")
    @ExcelProperty("商家ID")
    @JsonSerialize(using = SpMainNameJsonSerializer.class)
    private Long spId;

    @Schema(description = "订单编码")
    @ExcelProperty("订单编码")
    private String orderNum;

    @Schema(description = "下单时间")
    @ExcelProperty("下单时间")
    private LocalDateTime orderTime;

    @Schema(description = "支付时间")
    @ExcelProperty("支付时间")
    private LocalDateTime payTime;

    @Schema(description = "三方订单编码")
    @ExcelProperty("三方订单编码")
    private String outOrderNum;

    @Schema(description = "状态", example = "2")
    @ExcelProperty("状态")
    @InEnum(SvipOrderStatusEnum.class)
    private Integer status;

    @Schema(description = "数量")
    private Integer quantity;

    @Schema(description = "商品金额")
    @ExcelProperty("商品金额")
    @JsonSerialize(using = MoneyF2YSerializer.class)
    private Long productAmount;

    @Schema(description = "总金额")
    @ExcelProperty("总金额")
    @JsonSerialize(using = MoneyF2YSerializer.class)
    private Long totalAmount;

    @Schema(description = "付款金额")
    @ExcelProperty("付款金额")
    @JsonSerialize(using = MoneyF2YSerializer.class)
    private Long payAmount;

    @Schema(description = "平台和商家优惠")
    @ExcelProperty("平台和商家优惠")
    @JsonSerialize(using = MoneyF2YSerializer.class)
    private Long discountAmount;

    @Schema(description = "支付渠道")
    @ExcelProperty("支付渠道")
    private Integer payChannel;

    @Schema(description = "支付渠道费")
    @ExcelProperty("支付渠道费")
    @JsonSerialize(using = MoneyF2YSerializer.class)
    private Long payChannelAmount;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}