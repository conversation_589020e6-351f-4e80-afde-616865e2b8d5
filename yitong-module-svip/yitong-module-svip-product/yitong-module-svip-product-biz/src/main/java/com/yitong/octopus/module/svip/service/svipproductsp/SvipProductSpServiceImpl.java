package com.yitong.octopus.module.svip.service.svipproductsp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils;
import com.yitong.octopus.module.svip.dal.dataobject.sviporder.SvipOrderDO;
import com.yitong.octopus.module.svip.dal.dataobject.sviporder.SvipOrderItemDO;
import com.yitong.octopus.module.svip.dal.dataobject.svipproduct.SvipProductDO;
import com.yitong.octopus.module.svip.enums.order.SvipOrderStatusEnum;
import com.yitong.octopus.module.svip.enums.order.SvipProductSpStatusEnum;
import com.yitong.octopus.module.svip.service.sviporder.SvipOrderItemService;
import com.yitong.octopus.module.svip.service.sviporder.SvipOrderService;
import com.yitong.octopus.module.svip.service.svipproduct.SvipProductService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yitong.octopus.module.svip.controller.admin.svipproductsp.vo.*;
import com.yitong.octopus.module.svip.dal.dataobject.svipproductsp.SvipProductSpDO;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;

import com.yitong.octopus.module.svip.dal.mysql.svipproductsp.SvipProductSpMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.svip.enums.ErrorCodeConstants.*;
import static com.yitong.octopus.module.svip.enums.order.SvipProductValidityTypeEnum.FIX_DATE;
import static com.yitong.octopus.module.svip.enums.order.SvipProductValidityTypeEnum.ORDER_AFTER;

/**
 * 增值服务产品商家订单 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class SvipProductSpServiceImpl extends ServiceImpl<SvipProductSpMapper,SvipProductSpDO>  implements SvipProductSpService {

    @Resource
    private SvipOrderService svipOrderService;
    @Resource
    private SvipOrderItemService svipOrderItemService;

    @Resource
    private SvipProductService svipProductService;

    @Override
    public Long createProductSp(SvipProductSpSaveReqVO createReqVO) {
        // 插入
        SvipProductSpDO productSp = BeanUtils.toBean(createReqVO, SvipProductSpDO.class);
        getBaseMapper().insert(productSp);
        // 返回
        return productSp.getId();
    }

    @Override
    public void updateProductSp(SvipProductSpSaveReqVO updateReqVO) {
        // 校验存在
        validateProductSpExists(updateReqVO.getId());
        // 更新
        SvipProductSpDO updateObj = BeanUtils.toBean(updateReqVO, SvipProductSpDO.class);
        getBaseMapper().updateById(updateObj);
    }

    @Override
    public void deleteProductSp(Long id) {
        // 校验存在
        validateProductSpExists(id);
        // 删除
        getBaseMapper().deleteById(id);
    }

    private void validateProductSpExists(Long id) {
        if (getBaseMapper().selectById(id) == null) {
            throw exception(SVIP_PRODUCT_SP_NOT_EXISTS);
        }
    }

    @Override
    public SvipProductSpDO getProductSp(Long id) {
        return getBaseMapper().selectById(id);
    }

    @Override
    public PageResult<SvipProductSpDO> getProductSpPage(SvipProductSpPageReqVO pageReqVO) {
        return getBaseMapper().selectPage(pageReqVO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void takeSvipProductByOrder(Long orderId) {
          SvipOrderDO svipOrderDO = svipOrderService.getOrder(orderId);
          if (ObjectUtil.isNull(svipOrderDO)){
              throw exception(SVIP_ORDER_NOT_EXISTS);
          }
          if (!SvipOrderStatusEnum.PAID.getStatus().equals(svipOrderDO.getStatus())){
              throw exception(SVIP_ORDER_VALID_STATUS_ERROR);
          }
          List<SvipOrderItemDO> svipOrderItemList = svipOrderItemService.getOrderItemByOrderId(orderId);
          if (CollUtil.isEmpty(svipOrderItemList)){
              throw exception(SVIP_ORDER_ITEM_NOT_EXISTS);
          }
          List<SvipProductSpDO> svipProductSpList  = Lists.newArrayList();
          for (SvipOrderItemDO item:svipOrderItemList){
              SvipProductDO svipProduct = svipProductService.getProduct(item.getProductId());
              if (ObjectUtil.isNull(svipProduct)){
                  continue;
              }
              for (int index =0 ;index <item.getQuantity(); index++){
                  SvipProductSpDO svipProductSp = new SvipProductSpDO()
                          .setOrderId(orderId)
                          .setProductId(item.getProductId())
                          .setProductName(item.getProductName())
                          .setStatus(SvipProductSpStatusEnum.WAITE.getStatus())
                          .setSpId(svipOrderDO.getSpId())
                          .setPrivilegeNum(svipProduct.getPrivilegeNum()+svipProduct.getGiveawayNum())
                          .setPrivilegeUsedNum(0);
                  //生效日期类型 1 固定日期 2 领取之后
                  if (FIX_DATE.getType().equals(svipProduct.getValidityType())){
                      svipProductSp.setStartTime(LocalDateTimeUtils.initDayStart(svipProduct.getValidStartTime()));
                      svipProductSp.setEndTime(LocalDateTimeUtils.initDayEnd(svipProduct.getValidEndTime()));
                  }else if(ORDER_AFTER.getType().equals(svipProduct.getValidityType())){
                      LocalDateTime start = LocalDateTimeUtils.now().plusDays(svipProduct.getFixedStartTerm());
                      LocalDateTime end = start.plusDays(svipProduct.getFixedEndTerm());
                      svipProductSp.setStartTime(LocalDateTimeUtils.initDayStart(start));
                      svipProductSp.setEndTime(LocalDateTimeUtils.initDayEnd(end));
                  }
                  svipProductSpList.add(svipProductSp);
              }
          }
        this.saveBatch(svipProductSpList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int expireBySystem() {
        return getBaseMapper().expireBySystem();
    }

    @Override
    public int getSpTotalPrivilegeNumBySp(Long spId, Integer productType) {
        return getBaseMapper().getSpTotalPrivilegeNumBySp(spId,productType);
    }


    /**
     * 使用权益
     * @param spId 商家
     * @param productType 产品类型
     * @param privilegeUsedNum 权益数量
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SvipProductSpDO usePrivilegeBySp(Long spId,Integer productType,Integer privilegeUsedNum) {
        //检查商家总权益数是否足够，权益消耗。
        // 1. 足够，进行权益扣减
        // 2. 不足，提示需要充值权益
        int privilegeTotalNum = getSpTotalPrivilegeNumBySp(spId, productType);
        log.info("usePrivilegeBySp spId:{},productType:{},privilegeTotalNum:{}",spId,productType,privilegeTotalNum);
        if (privilegeTotalNum < privilegeUsedNum){
            log.info("usePrivilegeBySp not enough error spId:{},productType:{},privilegeTotalNum:{},privilegeUsedNum:{}",spId,productType,privilegeTotalNum,privilegeUsedNum);
            throw exception(SVIP_PRODUCT_SP_PRIVILEGE_NOT_ENOUGH);
        }
        // 先做一次统一扣减，看看是否成功，成功则返回，否则，则拆分。
        List<SvipProductSpDO>  svipProductSList = getBaseMapper().getSpTotalPrivilegeBySp(spId, productType);
        Integer privilegeUsedNumRel = privilegeUsedNum;
        Boolean useResult;
        for (SvipProductSpDO svipProductSp : svipProductSList){
            int privilegeNum = svipProductSp.getPrivilegeNum() - svipProductSp.getPrivilegeUsedNum();
            if (privilegeNum <= privilegeUsedNumRel){
                useResult =  getBaseMapper().usePrivilegeById(svipProductSp.getId(),privilegeNum);
                //权益充足
                if (useResult){
                    //扣除后返回检查
                    privilegeUsedNumRel = privilegeUsedNumRel - privilegeNum;
                    if (0 == privilegeUsedNumRel ){
                        return svipProductSp;
                    }
                }else{
                    log.info("usePrivilegeBySp for not enough error spId:{},spProductId:{},privilegeUsedNumRel:{}",spId,svipProductSp.getId(),privilegeUsedNumRel);
                    throw exception(SVIP_PRODUCT_SP_PRIVILEGE_NOT_ENOUGH);
                }
            }else {
                useResult = getBaseMapper().usePrivilegeById(svipProductSp.getId(),privilegeUsedNumRel);
                //权益充足
                if (useResult){
                    //扣除后返回
                    return svipProductSp;
                }else{
                    log.info("usePrivilegeBySp for not enough error spId:{},spProductId:{},privilegeUsedNumRel:{}",spId,svipProductSp.getId(),privilegeUsedNumRel);
                    throw exception(SVIP_PRODUCT_SP_PRIVILEGE_NOT_ENOUGH);
                }
            }
        }
        log.info("usePrivilegeBySp for end not enough error spId:{},productType:{},privilegeTotalNum:{}",spId,productType,privilegeTotalNum);
        throw exception(SVIP_PRODUCT_SP_PRIVILEGE_NOT_ENOUGH);
    }

}