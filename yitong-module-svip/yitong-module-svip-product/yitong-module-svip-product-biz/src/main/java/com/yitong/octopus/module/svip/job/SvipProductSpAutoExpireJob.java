package com.yitong.octopus.module.svip.job;

import com.yitong.octopus.framework.quartz.core.handler.JobHandler;
import com.yitong.octopus.framework.tenant.core.job.TenantJob;
import com.yitong.octopus.module.svip.service.sviporder.SvipOrderService;
import com.yitong.octopus.module.svip.service.svipproductsp.SvipProductSpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 商家权益自动过期
 */
@Slf4j
@Component("SvipProductSpAutoExpireJob")
public class SvipProductSpAutoExpireJob implements JobHandler {

    @Resource
    private SvipProductSpService svipProductSpService;

    @Override
    @TenantJob // 标记多租户
    public String execute(String param) throws Exception {
        Integer count = svipProductSpService.expireBySystem();
        log.info("[jobs] svip product sp expire {} 个",count);
        return String.format("过期权益 %s 个", count);
    }

}
