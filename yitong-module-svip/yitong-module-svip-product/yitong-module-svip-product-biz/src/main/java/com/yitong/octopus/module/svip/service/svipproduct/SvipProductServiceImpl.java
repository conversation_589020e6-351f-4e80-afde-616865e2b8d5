package com.yitong.octopus.module.svip.service.svipproduct;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yitong.octopus.module.svip.controller.admin.svipproduct.vo.*;
import com.yitong.octopus.module.svip.dal.dataobject.svipproduct.SvipProductDO;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;

import com.yitong.octopus.module.svip.dal.mysql.svipproduct.SvipProductMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.svip.enums.ErrorCodeConstants.SVIP_PRODUCT_NOT_EXISTS;

/**
 * 增值服务产品 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SvipProductServiceImpl extends ServiceImpl<SvipProductMapper,SvipProductDO>  implements SvipProductService {

    @Resource
    private SvipProductMapper productMapper;

    @Override
    public Long createProduct(SvipProductSaveReqVO createReqVO) {
        // 插入
        SvipProductDO product = BeanUtils.toBean(createReqVO, SvipProductDO.class);
        productMapper.insert(product);
        // 返回
        return product.getId();
    }

    @Override
    public void updateProduct(SvipProductSaveReqVO updateReqVO) {
        // 校验存在
        validateProductExists(updateReqVO.getId());
        // 更新
        SvipProductDO updateObj = BeanUtils.toBean(updateReqVO, SvipProductDO.class);
        productMapper.updateById(updateObj);
    }

    @Override
    public void deleteProduct(Long id) {
        // 校验存在
        validateProductExists(id);
        // 删除
        productMapper.deleteById(id);
    }

    private void validateProductExists(Long id) {
        if (productMapper.selectById(id) == null) {
            throw exception(SVIP_PRODUCT_NOT_EXISTS);
        }
    }

    @Override
    public SvipProductDO getProduct(Long id) {
        return productMapper.selectById(id);
    }

    @Override
    public PageResult<SvipProductDO> getProductPage(SvipProductPageReqVO pageReqVO) {
        return productMapper.selectPage(pageReqVO);
    }

}