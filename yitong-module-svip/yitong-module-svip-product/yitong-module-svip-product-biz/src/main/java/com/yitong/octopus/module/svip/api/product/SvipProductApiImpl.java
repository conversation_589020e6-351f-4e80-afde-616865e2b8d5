package com.yitong.octopus.module.svip.api.product;

import com.yitong.octopus.framework.common.util.object.BeanUtils;
import com.yitong.octopus.module.svip.api.product.vo.SvipProductApiVO;
import com.yitong.octopus.module.svip.dal.dataobject.svipproduct.SvipProductDO;
import com.yitong.octopus.module.svip.service.svipproduct.SvipProductService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 增值服务产品 Service 接口
 *
 * <AUTHOR>
 */
@Component
public class SvipProductApiImpl implements SvipProductApi{

    @Resource
    private SvipProductService svipProductService;

    @Override
    public SvipProductApiVO getProduct(Long id) {
        SvipProductDO  vo = svipProductService.getProduct(id);
        return BeanUtils.toBean(vo,SvipProductApiVO.class);
    }


}