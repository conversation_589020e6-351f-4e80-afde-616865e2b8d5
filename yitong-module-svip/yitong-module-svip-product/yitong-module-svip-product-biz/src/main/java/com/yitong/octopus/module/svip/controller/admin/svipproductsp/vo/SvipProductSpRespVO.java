package com.yitong.octopus.module.svip.controller.admin.svipproductsp.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yitong.octopus.module.sp.api.utils.jackson.SpMainNameJsonSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 增值服务产品商家订单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SvipProductSpRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27054")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "商品ID", example = "7182")
    @ExcelProperty("商品ID")
    private Long productId;

    /**
     * 订单Id
     */
    @Schema(description = "订单Id", example = "7182")
    private Long orderId;

    @Schema(description = "产品名称", example = "芋艿")
    @ExcelProperty("产品名称")
    private String productName;

    @Schema(description = "商家ID", example = "10909")
    @ExcelProperty("商家ID")
    @JsonSerialize(using = SpMainNameJsonSerializer.class)
    private Long spId;

    @Schema(description = "状态", example = "2")
    @ExcelProperty("状态")
    private Integer status;

    @Schema(description = "开始时间")
    @ExcelProperty("开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @ExcelProperty("结束时间")
    private LocalDateTime endTime;

    @Schema(description = "权益数量")
    @ExcelProperty("权益数量")
    private Integer privilegeNum;

    @Schema(description = "权益开始时间")
    @ExcelProperty("权益开始时间")
    private LocalDateTime privilegeStartTime;

    @Schema(description = "权益使用数量")
    @ExcelProperty("权益使用数量")
    private Integer privilegeUsedNum;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}