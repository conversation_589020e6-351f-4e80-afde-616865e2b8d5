package com.yitong.octopus.module.svip.dal.dataobject.sviporder;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;

/**
 * 增值服务产品订单 DO
 *
 * <AUTHOR>
 */
@TableName("yt_svip_order")
@KeySequence("yt_svip_order_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SvipOrderDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 商家ID
     */
    private Long spId;
    /**
     * 订单编码
     */
    private String orderNum;
    /**
     * 下单时间
     */
    private LocalDateTime orderTime;
    /**
     * 支付时间
     */
    private LocalDateTime payTime;
    /**
     * 支付超时时间
     */
    private LocalDateTime payExpireTime;
    /**
     * 取消时间
     */
    private LocalDateTime cancelTime;
    /**
     * 取消类型
     */
    private Integer cancelType;
    /**
     * 三方订单编码
     */
    private String outOrderNum;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 商品金额

     */
    private Long productAmount;
    /**
     * 总金额
     */
    private Long totalAmount;
    /**
     * 付款金额
     */
    private Long payAmount;
    /**
     * 平台和商家优惠
     */
    private Long discountAmount;
    /**
     * 支付渠道
     */
    private Integer payChannel;
    /**
     * 商品数量
     */
    private Integer quantity;
    /**
     * 支付渠道费
     */
    private Long payChannelAmount;
    /**
     * 备注
     */
    private String remark;

}