//package com.yitong.octopus.module.svip.controller.admin.sviporder;
//
//import com.yitong.octopus.module.svip.controller.admin.sviporder.vo.SvipOrderItemPageReqVO;
//import com.yitong.octopus.module.svip.controller.admin.sviporder.vo.SvipOrderItemRespVO;
//import com.yitong.octopus.module.svip.controller.admin.sviporder.vo.SvipOrderItemSaveReqVO;
//import org.springframework.web.bind.annotation.*;
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletResponse;
//import javax.validation.Valid;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import java.util.*;
//import java.io.IOException;
//
//import com.yitong.octopus.framework.common.pojo.PageParam;
//import com.yitong.octopus.framework.common.pojo.PageResult;
//import com.yitong.octopus.framework.common.pojo.CommonResult;
//import com.yitong.octopus.framework.common.util.object.BeanUtils;
//import static com.yitong.octopus.framework.common.pojo.CommonResult.success;
//
//import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
//
//import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
//import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;
//
//import com.yitong.octopus.module.svip.dal.dataobject.sviporder.SvipOrderItemDO;
//import com.yitong.octopus.module.svip.service.sviporder.SvipOrderItemService;
//
//@Tag(name = "管理后台 - 增值服务产品订单项")
//@RestController
//@RequestMapping("/svip/order-item")
//@Validated
//public class SvipOrderItemController {
//
//    @Resource
//    private SvipOrderItemService orderItemService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建增值服务产品订单项")
//    @PreAuthorize("@ss.hasPermission('svip:order-item:create')")
//    public CommonResult<Long> createOrderItem(@Valid @RequestBody SvipOrderItemSaveReqVO createReqVO) {
//        return success(orderItemService.createOrderItem(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新增值服务产品订单项")
//    @PreAuthorize("@ss.hasPermission('svip:order-item:update')")
//    public CommonResult<Boolean> updateOrderItem(@Valid @RequestBody SvipOrderItemSaveReqVO updateReqVO) {
//        orderItemService.updateOrderItem(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除增值服务产品订单项")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('svip:order-item:delete')")
//    public CommonResult<Boolean> deleteOrderItem(@RequestParam("id") Long id) {
//        orderItemService.deleteOrderItem(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得增值服务产品订单项")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('svip:order-item:query')")
//    public CommonResult<SvipOrderItemRespVO> getOrderItem(@RequestParam("id") Long id) {
//        SvipOrderItemDO orderItem = orderItemService.getOrderItem(id);
//        return success(BeanUtils.toBean(orderItem, SvipOrderItemRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得增值服务产品订单项分页")
//    @PreAuthorize("@ss.hasPermission('svip:order-item:query')")
//    public CommonResult<PageResult<SvipOrderItemRespVO>> getOrderItemPage(@Valid SvipOrderItemPageReqVO pageReqVO) {
//        PageResult<SvipOrderItemDO> pageResult = orderItemService.getOrderItemPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, SvipOrderItemRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出增值服务产品订单项 Excel")
//    @PreAuthorize("@ss.hasPermission('svip:order-item:export')")
//    @OperateLog(type = EXPORT)
//    public void exportOrderItemExcel(@Valid SvipOrderItemPageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<SvipOrderItemDO> list = orderItemService.getOrderItemPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "增值服务产品订单项.xls", "数据", SvipOrderItemRespVO.class,
//                        BeanUtils.toBean(list, SvipOrderItemRespVO.class));
//    }
//
//}