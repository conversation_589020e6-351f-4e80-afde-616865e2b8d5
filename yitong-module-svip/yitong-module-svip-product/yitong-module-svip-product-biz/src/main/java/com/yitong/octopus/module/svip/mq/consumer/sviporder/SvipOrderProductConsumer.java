package com.yitong.octopus.module.svip.mq.consumer.sviporder;

import com.yitong.octopus.module.svip.mq.message.sviporder.SvipOrderSuccessMessage;
import com.yitong.octopus.module.svip.service.svipproductsp.SvipProductSpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 商家订购订单成，发送权益，基 {@link SvipOrderSuccessMessage} 消息
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SvipOrderProductConsumer {

    @Resource
    private SvipProductSpService svipProductSpService;

    @EventListener
    @Async // Spring Event 默认在 Producer 发送的线程，通过 @Async 实现异步
    public void onMessage(SvipOrderSuccessMessage message) {
        log.info("[onMessage][消息内容({})]", message);
        svipProductSpService.takeSvipProductByOrder(message.getOrderId());
    }

}
