package com.yitong.octopus.module.svip.controller.admin.sviporder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 增值服务产品订单项 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SvipOrderItemRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "22064")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "订单Id", example = "29696")
    @ExcelProperty("订单Id")
    private Long orderId;

    @Schema(description = "产品Id", example = "5934")
    @ExcelProperty("产品Id")
    private Long productId;

    @Schema(description = "产品名称")
    @ExcelProperty("产品名称")
    private String productName;

    @Schema(description = "市场价")
    @ExcelProperty("市场价")
    private Long marketAmount;

    @Schema(description = "市场价")
    @ExcelProperty("市场价")
    private Long total;

    @Schema(description = "销售价")
    @ExcelProperty("销售价")
    private Long saleAmount;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}