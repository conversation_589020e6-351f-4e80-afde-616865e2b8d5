//package com.yitong.octopus.module.activity.service.activity;
//
//import com.yitong.octopus.module.activity.controller.admin.activity.vo.redeem.ActivityCouponRedeemCreateReqVO;
//import com.yitong.octopus.module.activity.controller.admin.activity.vo.redeem.ActivityCouponRedeemExportReqVO;
//import com.yitong.octopus.module.activity.controller.admin.activity.vo.redeem.ActivityCouponRedeemPageReqVO;
//import com.yitong.octopus.module.activity.controller.admin.activity.vo.redeem.ActivityCouponRedeemSaveReqVO;
//import com.yitong.octopus.module.activity.service.admin.activity.ActivityCouponRedeemServiceImpl;
//import org.junit.jupiter.api.Disabled;
//import org.junit.jupiter.api.Test;
//
//import javax.annotation.Resource;
//
//import com.yitong.octopus.framework.test.core.ut.BaseDbUnitTest;
//
//import com.yitong.octopus.module.activity.dal.dataobject.activity.ActivityCouponRedeemDO;
//import com.yitong.octopus.module.activity.dal.mysql.activity.ActivityCouponRedeemMapper;
//import com.yitong.octopus.framework.common.pojo.PageResult;
//
//import org.springframework.context.annotation.Import;
//import java.util.*;
//
//import static com.yitong.octopus.module.activity.enums.ErrorCodeConstants.*;
//import static com.yitong.octopus.framework.test.core.util.AssertUtils.*;
//import static com.yitong.octopus.framework.test.core.util.RandomUtils.*;
//import static com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils.*;
//import static com.yitong.octopus.framework.common.util.object.ObjectUtils.*;
//import static org.junit.jupiter.api.Assertions.*;
//
///**
//* {@link ActivityCouponRedeemServiceImpl} 的单元测试类
//*
//* <AUTHOR>
//*/
//@Import(ActivityCouponRedeemServiceImpl.class)
//public class ActivityCouponRedeemServiceImplTest extends BaseDbUnitTest {
//
//    @Resource
//    private ActivityCouponRedeemServiceImpl couponRedeemService;
//
//    @Resource
//    private ActivityCouponRedeemMapper couponRedeemMapper;
//
//    @Test
//    public void testCreateCouponRedeem_success() {
//        // 准备参数
//        ActivityCouponRedeemCreateReqVO reqVO = randomPojo(ActivityCouponRedeemCreateReqVO.class);
//
//        // 调用
//        Long couponRedeemId = couponRedeemService.createCouponRedeem(reqVO);
//        // 断言
//        assertNotNull(couponRedeemId);
//        // 校验记录的属性是否正确
//        ActivityCouponRedeemDO couponRedeem = couponRedeemMapper.selectById(couponRedeemId);
//        assertPojoEquals(reqVO, couponRedeem);
//    }
//
//    @Test
//    public void testUpdateCouponRedeem_success() {
//        // mock 数据
//        ActivityCouponRedeemDO dbCouponRedeem = randomPojo(ActivityCouponRedeemDO.class);
//        couponRedeemMapper.insert(dbCouponRedeem);// @Sql: 先插入出一条存在的数据
//        // 准备参数
//        ActivityCouponRedeemSaveReqVO reqVO = randomPojo(ActivityCouponRedeemSaveReqVO.class, o -> {
//            o.setId(dbCouponRedeem.getId()); // 设置更新的 ID
//        });
//
//        // 调用
//        couponRedeemService.updateCouponRedeem(reqVO);
//        // 校验是否更新正确
//        ActivityCouponRedeemDO couponRedeem = couponRedeemMapper.selectById(reqVO.getId()); // 获取最新的
//        assertPojoEquals(reqVO, couponRedeem);
//    }
//
//    @Test
//    public void testUpdateCouponRedeem_notExists() {
//        // 准备参数
//        ActivityCouponRedeemSaveReqVO reqVO = randomPojo(ActivityCouponRedeemSaveReqVO.class);
//
//        // 调用, 并断言异常
//        assertServiceException(() -> couponRedeemService.updateCouponRedeem(reqVO), ACTIVITY_COUPON_REDEEM_NOT_EXISTS);
//    }
//
//    @Test
//    public void testDeleteCouponRedeem_success() {
//        // mock 数据
//        ActivityCouponRedeemDO dbCouponRedeem = randomPojo(ActivityCouponRedeemDO.class);
//        couponRedeemMapper.insert(dbCouponRedeem);// @Sql: 先插入出一条存在的数据
//        // 准备参数
//        Long id = dbCouponRedeem.getId();
//
//        // 调用
//        couponRedeemService.deleteCouponRedeem(id);
//       // 校验数据不存在了
//       assertNull(couponRedeemMapper.selectById(id));
//    }
//
//    @Test
//    public void testDeleteCouponRedeem_notExists() {
//        // 准备参数
//        Long id = randomLongId();
//
//        // 调用, 并断言异常
//        assertServiceException(() -> couponRedeemService.deleteCouponRedeem(id), ACTIVITY_COUPON_REDEEM_NOT_EXISTS);
//    }
//
//    @Test
//    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
//    public void testGetCouponRedeemPage() {
//       // mock 数据
//       ActivityCouponRedeemDO dbCouponRedeem = randomPojo(ActivityCouponRedeemDO.class, o -> { // 等会查询到
//           o.setActivityId(null);
//           o.setMemberId(null);
//           o.setRedeemTime(null);
//           o.setRedeemUserId(null);
//           o.setRedeemUserName(null);
//           o.setRedeemStoreId(null);
//           o.setRedeemStoreName(null);
//           o.setRedeemSpName(null);
//       });
//       couponRedeemMapper.insert(dbCouponRedeem);
//       // 测试 activityId 不匹配
//       couponRedeemMapper.insert(cloneIgnoreId(dbCouponRedeem, o -> o.setActivityId(null)));
//       // 测试 activityName 不匹配
//       // 测试 memberId 不匹配
//       couponRedeemMapper.insert(cloneIgnoreId(dbCouponRedeem, o -> o.setMemberId(null)));
//       // 测试 couponCode 不匹配
//       // 测试 redeemTime 不匹配
//       couponRedeemMapper.insert(cloneIgnoreId(dbCouponRedeem, o -> o.setRedeemTime(null)));
//       // 测试 redeemUserId 不匹配
//       couponRedeemMapper.insert(cloneIgnoreId(dbCouponRedeem, o -> o.setRedeemUserId(null)));
//       // 测试 redeemUserName 不匹配
//       couponRedeemMapper.insert(cloneIgnoreId(dbCouponRedeem, o -> o.setRedeemUserName(null)));
//       // 测试 redeemStoreId 不匹配
//       couponRedeemMapper.insert(cloneIgnoreId(dbCouponRedeem, o -> o.setRedeemStoreId(null)));
//       // 测试 redeemStoreName 不匹配
//       couponRedeemMapper.insert(cloneIgnoreId(dbCouponRedeem, o -> o.setRedeemStoreName(null)));
//       // 测试 redeemSpName 不匹配
//       couponRedeemMapper.insert(cloneIgnoreId(dbCouponRedeem, o -> o.setRedeemSpName(null)));
//       // 准备参数
//       ActivityCouponRedeemPageReqVO reqVO = new ActivityCouponRedeemPageReqVO();
//       reqVO.setActivityId(null);
//       reqVO.setMemberId(null);
//       reqVO.setRedeemTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
//       reqVO.setRedeemUserId(null);
//       reqVO.setRedeemUserName(null);
//       reqVO.setRedeemStoreId(null);
//       reqVO.setRedeemStoreName(null);
//       reqVO.setRedeemSpName(null);
//
//       // 调用
//       PageResult<ActivityCouponRedeemDO> pageResult = couponRedeemService.getCouponRedeemPage(reqVO);
//       // 断言
//       assertEquals(1, pageResult.getTotal());
//       assertEquals(1, pageResult.getList().size());
//       assertPojoEquals(dbCouponRedeem, pageResult.getList().get(0));
//    }
//
//    @Test
//    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
//    public void testGetCouponRedeemList() {
//       // mock 数据
//       ActivityCouponRedeemDO dbCouponRedeem = randomPojo(ActivityCouponRedeemDO.class, o -> { // 等会查询到
//           o.setActivityId(null);
//           o.setMemberId(null);
//           o.setRedeemTime(null);
//           o.setRedeemUserId(null);
//           o.setRedeemUserName(null);
//           o.setRedeemStoreId(null);
//           o.setRedeemStoreName(null);
//           o.setRedeemSpName(null);
//       });
//       couponRedeemMapper.insert(dbCouponRedeem);
//       // 测试 activityId 不匹配
//       couponRedeemMapper.insert(cloneIgnoreId(dbCouponRedeem, o -> o.setActivityId(null)));
//       // 测试 memberId 不匹配
//       couponRedeemMapper.insert(cloneIgnoreId(dbCouponRedeem, o -> o.setMemberId(null)));
//       // 测试 couponCode 不匹配
//       // 测试 redeemTime 不匹配
//       couponRedeemMapper.insert(cloneIgnoreId(dbCouponRedeem, o -> o.setRedeemTime(null)));
//       // 测试 redeemUserId 不匹配
//       couponRedeemMapper.insert(cloneIgnoreId(dbCouponRedeem, o -> o.setRedeemUserId(null)));
//       // 测试 redeemUserName 不匹配
//       couponRedeemMapper.insert(cloneIgnoreId(dbCouponRedeem, o -> o.setRedeemUserName(null)));
//       // 测试 redeemStoreId 不匹配
//       couponRedeemMapper.insert(cloneIgnoreId(dbCouponRedeem, o -> o.setRedeemStoreId(null)));
//       // 测试 redeemStoreName 不匹配
//       couponRedeemMapper.insert(cloneIgnoreId(dbCouponRedeem, o -> o.setRedeemStoreName(null)));
//       // 测试 redeemSpName 不匹配
//       couponRedeemMapper.insert(cloneIgnoreId(dbCouponRedeem, o -> o.setRedeemSpName(null)));
//       // 准备参数
//       ActivityCouponRedeemExportReqVO reqVO = new ActivityCouponRedeemExportReqVO();
//       reqVO.setActivityId(null);
//       reqVO.setMemberId(null);
//       reqVO.setRedeemTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
//       reqVO.setRedeemUserId(null);
//       reqVO.setRedeemUserName(null);
//       reqVO.setRedeemStoreId(null);
//       reqVO.setRedeemStoreName(null);
//       reqVO.setRedeemStoreChannelPoi(null);
//       reqVO.setRedeemSpName(null);
//
//       // 调用
//       List<ActivityCouponRedeemDO> list = couponRedeemService.getCouponRedeemList(reqVO);
//       // 断言
//       assertEquals(1, list.size());
//       assertPojoEquals(dbCouponRedeem, list.get(0));
//    }
//
//}
