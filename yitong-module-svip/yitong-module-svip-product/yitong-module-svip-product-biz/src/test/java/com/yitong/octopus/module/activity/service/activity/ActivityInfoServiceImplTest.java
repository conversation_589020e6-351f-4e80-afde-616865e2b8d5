//package com.yitong.octopus.module.activity.service.activity;
//
//import com.yitong.octopus.module.activity.controller.admin.activity.vo.info.ActivityInfoCreateReqVO;
//import com.yitong.octopus.module.activity.controller.admin.activity.vo.info.ActivityInfoExportReqVO;
//import com.yitong.octopus.module.activity.controller.admin.activity.vo.info.ActivityInfoPageReqVO;
//import com.yitong.octopus.module.activity.controller.admin.activity.vo.info.ActivityInfoUpdateReqVO;
//import com.yitong.octopus.module.activity.service.admin.activity.ActivityInfoServiceImpl;
//import org.junit.jupiter.api.Disabled;
//import org.junit.jupiter.api.Test;
//
//import javax.annotation.Resource;
//
//import com.yitong.octopus.framework.test.core.ut.BaseDbUnitTest;
//
//import com.yitong.octopus.module.activity.dal.dataobject.activity.ActivityInfoDO;
//import com.yitong.octopus.module.activity.dal.mysql.activity.ActivityInfoMapper;
//import com.yitong.octopus.framework.common.pojo.PageResult;
//
//import org.springframework.context.annotation.Import;
//import java.util.*;
//
//import static com.yitong.octopus.module.activity.enums.ErrorCodeConstants.*;
//import static com.yitong.octopus.framework.test.core.util.AssertUtils.*;
//import static com.yitong.octopus.framework.test.core.util.RandomUtils.*;
//import static com.yitong.octopus.framework.common.util.object.ObjectUtils.*;
//import static org.junit.jupiter.api.Assertions.*;
//
///**
//* {@link ActivityInfoServiceImpl} 的单元测试类
//*
//* <AUTHOR>
//*/
//@Import(ActivityInfoServiceImpl.class)
//public class ActivityInfoServiceImplTest extends BaseDbUnitTest {
//
//    @Resource
//    private ActivityInfoServiceImpl infoService;
//
//    @Resource
//    private ActivityInfoMapper infoMapper;
//
//    @Test
//    public void testCreateInfo_success() {
//        // 准备参数
//        ActivityInfoCreateReqVO reqVO = randomPojo(ActivityInfoCreateReqVO.class);
//
//        // 调用
//        Long infoId = infoService.createInfo(reqVO);
//        // 断言
//        assertNotNull(infoId);
//        // 校验记录的属性是否正确
//        ActivityInfoDO info = infoMapper.selectById(infoId);
//        assertPojoEquals(reqVO, info);
//    }
//
//    @Test
//    public void testUpdateInfo_success() {
//        // mock 数据
//        ActivityInfoDO dbInfo = randomPojo(ActivityInfoDO.class);
//        infoMapper.insert(dbInfo);// @Sql: 先插入出一条存在的数据
//        // 准备参数
//        ActivityInfoUpdateReqVO reqVO = randomPojo(ActivityInfoUpdateReqVO.class, o -> {
//            o.setId(dbInfo.getId()); // 设置更新的 ID
//        });
//
//        // 调用
//        infoService.updateInfo(reqVO);
//        // 校验是否更新正确
//        ActivityInfoDO info = infoMapper.selectById(reqVO.getId()); // 获取最新的
//        assertPojoEquals(reqVO, info);
//    }
//
//    @Test
//    public void testUpdateInfo_notExists() {
//        // 准备参数
//        ActivityInfoUpdateReqVO reqVO = randomPojo(ActivityInfoUpdateReqVO.class);
//
//        // 调用, 并断言异常
//        assertServiceException(() -> infoService.updateInfo(reqVO), INFO_NOT_EXISTS);
//    }
//
//    @Test
//    public void testDeleteInfo_success() {
//        // mock 数据
//        ActivityInfoDO dbInfo = randomPojo(ActivityInfoDO.class);
//        infoMapper.insert(dbInfo);// @Sql: 先插入出一条存在的数据
//        // 准备参数
//        Long id = dbInfo.getId();
//
//        // 调用
//        infoService.deleteInfo(id);
//       // 校验数据不存在了
//       assertNull(infoMapper.selectById(id));
//    }
//
//    @Test
//    public void testDeleteInfo_notExists() {
//        // 准备参数
//        Long id = randomLongId();
//
//        // 调用, 并断言异常
//        assertServiceException(() -> infoService.deleteInfo(id), INFO_NOT_EXISTS);
//    }
//
//    @Test
//    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
//    public void testGetInfoPage() {
//       // mock 数据
//       ActivityInfoDO dbInfo = randomPojo(ActivityInfoDO.class, o -> { // 等会查询到
//           o.setActivityName(null);
//           o.setActivityType(null);
//           o.setActivityStartTime(null);
//           o.setActivityEndTime(null);
//           o.setActivityStatus(null);
//           o.setApplyStartTime(null);
//           o.setApplyEndTime(null);
//           o.setApplyStatus(null);
//           o.setActivityDesc(null);
//           o.setActivityRuleInfo(null);
//           o.setMerchantApplyLimit(null);
//           o.setPublishChannels(null);
//       });
//       infoMapper.insert(dbInfo);
//       // 测试 activityName 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setActivityName(null)));
//       // 测试 activityType 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setActivityType(null)));
//       // 测试 activityStartTime 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setActivityStartTime(null)));
//       // 测试 activityEndTime 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setActivityEndTime(null)));
//       // 测试 activityStatus 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setActivityStatus(null)));
//       // 测试 applyStartTime 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setApplyStartTime(null)));
//       // 测试 applyEndTime 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setApplyEndTime(null)));
//       // 测试 applyStatus 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setApplyStatus(null)));
//       // 测试 activityDesc 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setActivityDesc(null)));
//       // 测试 activityRuleInfo 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setActivityRuleInfo(null)));
//       // 测试 merchantApplyLimit 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setMerchantApplyLimit(null)));
//       // 测试 publishChannels 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setPublishChannels(null)));
//       // 准备参数
//       ActivityInfoPageReqVO reqVO = new ActivityInfoPageReqVO();
//       reqVO.setActivityName(null);
//       reqVO.setActivityType(null);
//       reqVO.setActivityStatus(null);
//       reqVO.setApplyStatus(null);
//       reqVO.setPublishChannels(null);
//
//       // 调用
//       PageResult<ActivityInfoDO> pageResult = infoService.getInfoPage(reqVO);
//       // 断言
//       assertEquals(1, pageResult.getTotal());
//       assertEquals(1, pageResult.getList().size());
//       assertPojoEquals(dbInfo, pageResult.getList().get(0));
//    }
//
//    @Test
//    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
//    public void testGetInfoList() {
//       // mock 数据
//       ActivityInfoDO dbInfo = randomPojo(ActivityInfoDO.class, o -> { // 等会查询到
//           o.setActivityName(null);
//           o.setActivityType(null);
//           o.setActivityStartTime(null);
//           o.setActivityEndTime(null);
//           o.setActivityStatus(null);
//           o.setApplyStartTime(null);
//           o.setApplyEndTime(null);
//           o.setApplyStatus(null);
//           o.setActivityDesc(null);
//           o.setActivityRuleInfo(null);
//           o.setMerchantApplyLimit(null);
//           o.setPublishChannels(null);
//       });
//       infoMapper.insert(dbInfo);
//       // 测试 activityName 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setActivityName(null)));
//       // 测试 activityType 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setActivityType(null)));
//       // 测试 activityStartTime 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setActivityStartTime(null)));
//       // 测试 activityEndTime 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setActivityEndTime(null)));
//       // 测试 activityStatus 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setActivityStatus(null)));
//       // 测试 applyStartTime 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setApplyStartTime(null)));
//       // 测试 applyEndTime 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setApplyEndTime(null)));
//       // 测试 applyStatus 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setApplyStatus(null)));
//       // 测试 activityDesc 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setActivityDesc(null)));
//       // 测试 activityRuleInfo 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setActivityRuleInfo(null)));
//       // 测试 merchantApplyLimit 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setMerchantApplyLimit(null)));
//       // 测试 publishChannels 不匹配
//       infoMapper.insert(cloneIgnoreId(dbInfo, o -> o.setPublishChannels(null)));
//       // 准备参数
//       ActivityInfoExportReqVO reqVO = new ActivityInfoExportReqVO();
//       reqVO.setActivityName(null);
//       reqVO.setActivityType(null);
//       reqVO.setActivityStatus(null);
//       reqVO.setApplyStatus(null);
//       reqVO.setPublishChannels(null);
//
//       // 调用
//       List<ActivityInfoDO> list = infoService.getInfoList(reqVO);
//       // 断言
//       assertEquals(1, list.size());
//       assertPojoEquals(dbInfo, list.get(0));
//    }
//
//}
