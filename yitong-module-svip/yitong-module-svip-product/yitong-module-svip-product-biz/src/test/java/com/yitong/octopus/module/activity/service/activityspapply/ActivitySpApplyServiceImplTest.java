//package com.yitong.octopus.module.activity.service.activityspapply;
//
//import com.yitong.octopus.module.activity.controller.admin.activity.vo.spapply.ActivitySpApplyCreateReqVO;
//import com.yitong.octopus.module.activity.controller.admin.activity.vo.spapply.ActivitySpApplyExportReqVO;
//import com.yitong.octopus.module.activity.controller.admin.activity.vo.spapply.ActivitySpApplyPageReqVO;
//import com.yitong.octopus.module.activity.controller.admin.activity.vo.spapply.ActivitySpApplyUpdateReqVO;
//import com.yitong.octopus.module.activity.service.admin.activity.ActivitySpApplyServiceImpl;
//import org.junit.jupiter.api.Disabled;
//import org.junit.jupiter.api.Test;
//
//import javax.annotation.Resource;
//
//import com.yitong.octopus.framework.test.core.ut.BaseDbUnitTest;
//
//import com.yitong.octopus.module.activity.dal.dataobject.activity.ActivitySpApplyDO;
//import com.yitong.octopus.module.activity.dal.mysql.activity.ActivitySpApplyMapper;
//import com.yitong.octopus.framework.common.pojo.PageResult;
//
//import org.springframework.context.annotation.Import;
//import java.util.*;
//
//import static com.yitong.octopus.module.activity.enums.ErrorCodeConstants.*;
//import static com.yitong.octopus.framework.test.core.util.AssertUtils.*;
//import static com.yitong.octopus.framework.test.core.util.RandomUtils.*;
//import static com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils.*;
//import static com.yitong.octopus.framework.common.util.object.ObjectUtils.*;
//import static org.junit.jupiter.api.Assertions.*;
//
///**
//* {@link ActivitySpApplyServiceImpl} 的单元测试类
//*
//* <AUTHOR>
//*/
//@Import(ActivitySpApplyServiceImpl.class)
//public class ActivitySpApplyServiceImplTest extends BaseDbUnitTest {
//
//    @Resource
//    private ActivitySpApplyServiceImpl spApplyService;
//
//    @Resource
//    private ActivitySpApplyMapper spApplyMapper;
//
//    @Test
//    public void testCreateSpApply_success() {
//        // 准备参数
//        ActivitySpApplyCreateReqVO reqVO = randomPojo(ActivitySpApplyCreateReqVO.class);
//
//        // 调用
//        Long spApplyId = spApplyService.createSpApply(reqVO);
//        // 断言
//        assertNotNull(spApplyId);
//        // 校验记录的属性是否正确
//        ActivitySpApplyDO spApply = spApplyMapper.selectById(spApplyId);
//        assertPojoEquals(reqVO, spApply);
//    }
//
//    @Test
//    public void testUpdateSpApply_success() {
//        // mock 数据
//        ActivitySpApplyDO dbSpApply = randomPojo(ActivitySpApplyDO.class);
//        spApplyMapper.insert(dbSpApply);// @Sql: 先插入出一条存在的数据
//        // 准备参数
//        ActivitySpApplyUpdateReqVO reqVO = randomPojo(ActivitySpApplyUpdateReqVO.class, o -> {
//            o.setId(dbSpApply.getId()); // 设置更新的 ID
//        });
//
//        // 调用
//        spApplyService.updateSpApply(reqVO);
//        // 校验是否更新正确
//        ActivitySpApplyDO spApply = spApplyMapper.selectById(reqVO.getId()); // 获取最新的
//        assertPojoEquals(reqVO, spApply);
//    }
//
//    @Test
//    public void testUpdateSpApply_notExists() {
//        // 准备参数
//        ActivitySpApplyUpdateReqVO reqVO = randomPojo(ActivitySpApplyUpdateReqVO.class);
//
//        // 调用, 并断言异常
//        assertServiceException(() -> spApplyService.updateSpApply(reqVO), SP_APPLY_NOT_EXISTS);
//    }
//
//    @Test
//    public void testDeleteSpApply_success() {
//        // mock 数据
//        ActivitySpApplyDO dbSpApply = randomPojo(ActivitySpApplyDO.class);
//        spApplyMapper.insert(dbSpApply);// @Sql: 先插入出一条存在的数据
//        // 准备参数
//        Long id = dbSpApply.getId();
//
//        // 调用
//        spApplyService.deleteSpApply(id);
//       // 校验数据不存在了
//       assertNull(spApplyMapper.selectById(id));
//    }
//
//    @Test
//    public void testDeleteSpApply_notExists() {
//        // 准备参数
//        Long id = randomLongId();
//
//        // 调用, 并断言异常
//        assertServiceException(() -> spApplyService.deleteSpApply(id), SP_APPLY_NOT_EXISTS);
//    }
//
//    @Test
//    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
//    public void testGetSpApplyPage() {
//       // mock 数据
//       ActivitySpApplyDO dbSpApply = randomPojo(ActivitySpApplyDO.class, o -> { // 等会查询到
//           o.setActivityId(null);
//           o.setSpId(null);
//           o.setSpuId(null);
//           o.setApplyStatus(null);
//           o.setSubmitChannel(null);
//           o.setApplyTime(null);
//       });
//       spApplyMapper.insert(dbSpApply);
//       // 测试 activityId 不匹配
//       spApplyMapper.insert(cloneIgnoreId(dbSpApply, o -> o.setActivityId(null)));
//       // 测试 spId 不匹配
//       spApplyMapper.insert(cloneIgnoreId(dbSpApply, o -> o.setSpId(null)));
//       // 测试 spuId 不匹配
//       spApplyMapper.insert(cloneIgnoreId(dbSpApply, o -> o.setSpuId(null)));
//       // 测试 applyStatus 不匹配
//       spApplyMapper.insert(cloneIgnoreId(dbSpApply, o -> o.setApplyStatus(null)));
//       // 测试 submitChannel 不匹配
//       spApplyMapper.insert(cloneIgnoreId(dbSpApply, o -> o.setSubmitChannel(null)));
//       // 测试 applyTime 不匹配
//       spApplyMapper.insert(cloneIgnoreId(dbSpApply, o -> o.setApplyTime(null)));
//       // 准备参数
//       ActivitySpApplyPageReqVO reqVO = new ActivitySpApplyPageReqVO();
//       reqVO.setActivityId(null);
//       reqVO.setSpId(null);
//       reqVO.setSpuId(null);
//       reqVO.setApplyStatus(null);
//       reqVO.setSubmitChannel(null);
//       reqVO.setApplyTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
//
//       // 调用
//       PageResult<ActivitySpApplyDO> pageResult = spApplyService.getSpApplyPage(reqVO);
//       // 断言
//       assertEquals(1, pageResult.getTotal());
//       assertEquals(1, pageResult.getList().size());
//       assertPojoEquals(dbSpApply, pageResult.getList().get(0));
//    }
//
//    @Test
//    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
//    public void testGetSpApplyList() {
//       // mock 数据
//       ActivitySpApplyDO dbSpApply = randomPojo(ActivitySpApplyDO.class, o -> { // 等会查询到
//           o.setActivityId(null);
//           o.setSpId(null);
//           o.setSpuId(null);
//           o.setApplyStatus(null);
//           o.setSubmitChannel(null);
//           o.setApplyTime(null);
//       });
//       spApplyMapper.insert(dbSpApply);
//       // 测试 activityId 不匹配
//       spApplyMapper.insert(cloneIgnoreId(dbSpApply, o -> o.setActivityId(null)));
//       // 测试 spId 不匹配
//       spApplyMapper.insert(cloneIgnoreId(dbSpApply, o -> o.setSpId(null)));
//       // 测试 spuId 不匹配
//       spApplyMapper.insert(cloneIgnoreId(dbSpApply, o -> o.setSpuId(null)));
//       // 测试 applyStatus 不匹配
//       spApplyMapper.insert(cloneIgnoreId(dbSpApply, o -> o.setApplyStatus(null)));
//       // 测试 submitChannel 不匹配
//       spApplyMapper.insert(cloneIgnoreId(dbSpApply, o -> o.setSubmitChannel(null)));
//       // 测试 applyTime 不匹配
//       spApplyMapper.insert(cloneIgnoreId(dbSpApply, o -> o.setApplyTime(null)));
//       // 准备参数
//       ActivitySpApplyExportReqVO reqVO = new ActivitySpApplyExportReqVO();
//       reqVO.setActivityId(null);
//       reqVO.setSpId(null);
//       reqVO.setSpuId(null);
//       reqVO.setApplyStatus(null);
//       reqVO.setSubmitChannel(null);
//       reqVO.setApplyTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
//
//       // 调用
//       List<ActivitySpApplyDO> list = spApplyService.getSpApplyList(reqVO);
//       // 断言
//       assertEquals(1, list.size());
//       assertPojoEquals(dbSpApply, list.get(0));
//    }
//
//}
