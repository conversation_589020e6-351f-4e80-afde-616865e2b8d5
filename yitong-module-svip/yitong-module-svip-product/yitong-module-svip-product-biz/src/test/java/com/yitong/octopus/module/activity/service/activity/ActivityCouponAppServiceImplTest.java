//package com.yitong.octopus.module.activity.service.activity;
//
//import com.yitong.octopus.module.activity.controller.admin.activity.vo.coupon.ActivityCouponCreateReqVO;
//import com.yitong.octopus.module.activity.controller.admin.activity.vo.coupon.ActivityCouponExportReqVO;
//import com.yitong.octopus.module.activity.controller.admin.activity.vo.coupon.ActivityCouponPageReqVO;
//import com.yitong.octopus.module.activity.controller.admin.activity.vo.coupon.ActivityCouponUpdateReqVO;
//import com.yitong.octopus.module.activity.service.admin.activity.ActivityCouponServiceImpl;
//import org.junit.jupiter.api.Disabled;
//import org.junit.jupiter.api.Test;
//
//import javax.annotation.Resource;
//
//import com.yitong.octopus.framework.test.core.ut.BaseDbUnitTest;
//
//import com.yitong.octopus.module.activity.dal.dataobject.activity.ActivityCouponDO;
//import com.yitong.octopus.module.activity.dal.mysql.activity.ActivityCouponMapper;
//import com.yitong.octopus.framework.common.pojo.PageResult;
//
//import org.springframework.context.annotation.Import;
//import java.util.*;
//
//import static com.yitong.octopus.module.activity.enums.ErrorCodeConstants.*;
//import static com.yitong.octopus.framework.test.core.util.AssertUtils.*;
//import static com.yitong.octopus.framework.test.core.util.RandomUtils.*;
//import static com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils.*;
//import static com.yitong.octopus.framework.common.util.object.ObjectUtils.*;
//import static org.junit.jupiter.api.Assertions.*;
//
///**
//* {@link ActivityCouponServiceImpl} 的单元测试类
//*
//* <AUTHOR>
//*/
//@Import(ActivityCouponServiceImpl.class)
//public class ActivityCouponAppServiceImplTest extends BaseDbUnitTest {
//
//    @Resource
//    private ActivityCouponServiceImpl couponService;
//
//    @Resource
//    private ActivityCouponMapper couponMapper;
//
//    @Test
//    public void testCreateCoupon_success() {
//        // 准备参数
//        ActivityCouponCreateReqVO reqVO = randomPojo(ActivityCouponCreateReqVO.class);
//
//        // 调用
//        Long couponId = couponService.createCoupon(reqVO);
//        // 断言
//        assertNotNull(couponId);
//        // 校验记录的属性是否正确
//        ActivityCouponDO coupon = couponMapper.selectById(couponId);
//        assertPojoEquals(reqVO, coupon);
//    }
//
//    @Test
//    public void testUpdateCoupon_success() {
//        // mock 数据
//        ActivityCouponDO dbCoupon = randomPojo(ActivityCouponDO.class);
//        couponMapper.insert(dbCoupon);// @Sql: 先插入出一条存在的数据
//        // 准备参数
//        ActivityCouponUpdateReqVO reqVO = randomPojo(ActivityCouponUpdateReqVO.class, o -> {
//            o.setId(dbCoupon.getId()); // 设置更新的 ID
//        });
//
//        // 调用
//        couponService.updateCoupon(reqVO);
//        // 校验是否更新正确
//        ActivityCouponDO coupon = couponMapper.selectById(reqVO.getId()); // 获取最新的
//        assertPojoEquals(reqVO, coupon);
//    }
//
//    @Test
//    public void testUpdateCoupon_notExists() {
//        // 准备参数
//        ActivityCouponUpdateReqVO reqVO = randomPojo(ActivityCouponUpdateReqVO.class);
//
//        // 调用, 并断言异常
//        assertServiceException(() -> couponService.updateCoupon(reqVO), ACTIVITY_COUPON_NOT_EXISTS);
//    }
//
//    @Test
//    public void testDeleteCoupon_success() {
//        // mock 数据
//        ActivityCouponDO dbCoupon = randomPojo(ActivityCouponDO.class);
//        couponMapper.insert(dbCoupon);// @Sql: 先插入出一条存在的数据
//        // 准备参数
//        Long id = dbCoupon.getId();
//
//        // 调用
//        couponService.deleteCoupon(id);
//       // 校验数据不存在了
//       assertNull(couponMapper.selectById(id));
//    }
//
//    @Test
//    public void testDeleteCoupon_notExists() {
//        // 准备参数
//        Long id = randomLongId();
//
//        // 调用, 并断言异常
//        assertServiceException(() -> couponService.deleteCoupon(id), ACTIVITY_COUPON_NOT_EXISTS);
//    }
//
//    @Test
//    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
//    public void testGetCouponPage() {
//       // mock 数据
//       ActivityCouponDO dbCoupon = randomPojo(ActivityCouponDO.class, o -> { // 等会查询到
//           o.setActivityId(null);
//           o.setActivityName(null);
//           o.setMemberId(null);
//           o.setStatus(null);
//           o.setCouponCode(null);
//           o.setCouponSpName(null);
//           o.setCouponSkuName(null);
//           o.setChannelCode(null);
//           o.setChannelOrderId(null);
//           o.setCreateTime(null);
//       });
//       couponMapper.insert(dbCoupon);
//       // 测试 activityId 不匹配
//       couponMapper.insert(cloneIgnoreId(dbCoupon, o -> o.setActivityId(null)));
//       // 测试 activityName 不匹配
//       couponMapper.insert(cloneIgnoreId(dbCoupon, o -> o.setActivityName(null)));
//       // 测试 memberId 不匹配
//       couponMapper.insert(cloneIgnoreId(dbCoupon, o -> o.setMemberId(null)));
//       // 测试 status 不匹配
//       couponMapper.insert(cloneIgnoreId(dbCoupon, o -> o.setStatus(null)));
//       // 测试 couponCode 不匹配
//       couponMapper.insert(cloneIgnoreId(dbCoupon, o -> o.setCouponCode(null)));
//       // 测试 couponSpName 不匹配
//       couponMapper.insert(cloneIgnoreId(dbCoupon, o -> o.setCouponSpName(null)));
//       // 测试 couponSkuName 不匹配
//       couponMapper.insert(cloneIgnoreId(dbCoupon, o -> o.setCouponSkuName(null)));
//       // 测试 channelCode 不匹配
//       couponMapper.insert(cloneIgnoreId(dbCoupon, o -> o.setChannelCode(null)));
//       // 测试 channelOrderId 不匹配
//       couponMapper.insert(cloneIgnoreId(dbCoupon, o -> o.setChannelOrderId(null)));
//       // 测试 createTime 不匹配
//       couponMapper.insert(cloneIgnoreId(dbCoupon, o -> o.setCreateTime(null)));
//       // 准备参数
//       ActivityCouponPageReqVO reqVO = new ActivityCouponPageReqVO();
//       reqVO.setActivityId(null);
//       reqVO.setActivityName(null);
//       reqVO.setMemberId(null);
//       reqVO.setStatus(null);
//       reqVO.setCouponCode(null);
//       reqVO.setCouponSpName(null);
//       reqVO.setCouponSkuName(null);
//       reqVO.setChannelCode(null);
//       reqVO.setChannelOrderId(null);
//       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
//
//       // 调用
//       PageResult<ActivityCouponDO> pageResult = couponService.getCouponPage(reqVO);
//       // 断言
//       assertEquals(1, pageResult.getTotal());
//       assertEquals(1, pageResult.getList().size());
//       assertPojoEquals(dbCoupon, pageResult.getList().get(0));
//    }
//
//    @Test
//    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
//    public void testGetCouponList() {
//       // mock 数据
//       ActivityCouponDO dbCoupon = randomPojo(ActivityCouponDO.class, o -> { // 等会查询到
//           o.setActivityId(null);
//           o.setActivityName(null);
//           o.setMemberId(null);
//           o.setStatus(null);
//           o.setCouponCode(null);
//           o.setCouponSpName(null);
//           o.setCouponSkuName(null);
//           o.setChannelCode(null);
//           o.setChannelOrderId(null);
//           o.setCreateTime(null);
//       });
//       couponMapper.insert(dbCoupon);
//       // 测试 activityId 不匹配
//       couponMapper.insert(cloneIgnoreId(dbCoupon, o -> o.setActivityId(null)));
//       // 测试 activityName 不匹配
//       couponMapper.insert(cloneIgnoreId(dbCoupon, o -> o.setActivityName(null)));
//       // 测试 memberId 不匹配
//       couponMapper.insert(cloneIgnoreId(dbCoupon, o -> o.setMemberId(null)));
//       // 测试 status 不匹配
//       couponMapper.insert(cloneIgnoreId(dbCoupon, o -> o.setStatus(null)));
//       // 测试 couponCode 不匹配
//       couponMapper.insert(cloneIgnoreId(dbCoupon, o -> o.setCouponCode(null)));
//       // 测试 couponSpName 不匹配
//       couponMapper.insert(cloneIgnoreId(dbCoupon, o -> o.setCouponSpName(null)));
//       // 测试 couponSkuName 不匹配
//       couponMapper.insert(cloneIgnoreId(dbCoupon, o -> o.setCouponSkuName(null)));
//       // 测试 channelCode 不匹配
//       couponMapper.insert(cloneIgnoreId(dbCoupon, o -> o.setChannelCode(null)));
//       // 测试 channelOrderId 不匹配
//       couponMapper.insert(cloneIgnoreId(dbCoupon, o -> o.setChannelOrderId(null)));
//       // 测试 createTime 不匹配
//       couponMapper.insert(cloneIgnoreId(dbCoupon, o -> o.setCreateTime(null)));
//       // 准备参数
//       ActivityCouponExportReqVO reqVO = new ActivityCouponExportReqVO();
//       reqVO.setActivityId(null);
//       reqVO.setActivityName(null);
//       reqVO.setMemberId(null);
//       reqVO.setStatus(null);
//       reqVO.setCouponCode(null);
//       reqVO.setCouponSpName(null);
//       reqVO.setCouponSkuName(null);
//       reqVO.setChannelCode(null);
//       reqVO.setChannelOrderId(null);
//       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
//
//       // 调用
//       List<ActivityCouponDO> list = couponService.getCouponList(reqVO);
//       // 断言
//       assertEquals(1, list.size());
//       assertPojoEquals(dbCoupon, list.get(0));
//    }
//
//}
