package com.yitong.octopus;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import com.yitong.octopus.framework.common.util.date.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Slf4j
public class OpenApi2Test {
    //本地
//    private String url = "http://localhost:48090/openapi/v1";
    //正式
    private String url = "https://openapi.5ihuish.com/openapi/v1";
    //测试
//    private String url = "https://openapi.etongle.net/openapi/v1";
//    private final String appId = "2d46a14334759182787ede0766501e53";
//    private final String token = "b90b12ab56934a4ab719db18ef7c7e1a";
    // 美丽田园
    private final String appId = "22f93b565f677fe02bd8bbf3d0fe0fce";
    private final String token = "0c9ea13bbc924353aa0b4d9828c6c965";

    private final String accessToken = "93bd2cd68359464f95b21a679ca7260a";//********************************

    @Test
    public void getAuthTest(){
        Map<String,Object> params = Maps.newHashMap();
        params.put("app_id",appId);
        params.put("sign_method","MD5");
        params.put("nonce_str", RandomUtil.randomString(32));
        params.put("timestamp", DateUtils.date().toString(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
        params.put("sign",sign(params,token));
        String req = JSONUtil.toJsonStr(params);
        System.out.println("REP>>"+req);
        String resp = HttpUtil.post(url+"/auth/getToken", req);
        System.out.println(resp);
    }

    @Test
    public void queryOrderInfoTest(){
        Map<String,Object> params = Maps.newHashMap();
        params.put("app_id",appId);
        params.put("sign_method","MD5");
        params.put("nonce_str", RandomUtil.randomString(32));
        params.put("timestamp", DateUtils.date().toString(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
        params.put("access_token",accessToken);
        params.put("biz_content","{\"order_id\":\"1654669204399325184\"}");
        params.put("sign",sign(params,token));
        String req = JSONUtil.toJsonStr(params);
        System.out.println("REP>>"+req);
        String resp = HttpUtil.post(url+"/trade/order-info",req);
        System.out.println(resp);
    }

    @Test
    public void queryOrderCodeInfoTest(){
        Map<String,Object> params = Maps.newHashMap();
        params.put("app_id",appId);
        params.put("sign_method","MD5");
        params.put("nonce_str", RandomUtil.randomString(32));
        params.put("timestamp", DateUtils.date().toString(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
        params.put("access_token",accessToken);
        params.put("biz_content","{\"out_code\":\"ckpy22ljclq8\"}");
        params.put("sign",sign(params,token));
        String req = JSONUtil.toJsonStr(params);
        System.out.println("REP>>"+req);
        String resp = HttpUtil.post(url+"/trade/order-code-info",req);
        System.out.println(resp);
    }

    @Test
    public void couponRedeemTest(){
        Map<String,Object> params = Maps.newHashMap();
        params.put("app_id",appId);
        params.put("sign_method","MD5");
        params.put("nonce_str", RandomUtil.randomString(32));
        params.put("timestamp", DateUtils.date().toString(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
        params.put("access_token",accessToken);
        params.put("biz_content","{\"store_id\":\"1000000\",\"redeem_time\":\""+DateUtils.date().getTime()+"\",\"out_codes\":[\"cce0py22ljclq8\"]}");
        params.put("sign",sign(params,token));
        String req = JSONUtil.toJsonStr(params);
        System.out.println("REP>>"+req);
        String resp = HttpUtil.post(url+"/trade/coupon-redeem",req);
        System.out.println("resp>>"+resp);
    }

    /**
     * MD5签名
     * @param params 参数
     * @param token token
     * @return 签名结果
     */
    private String sign(Map<String,Object> params,String token){
        //删除签名字段
        params.remove("sign");
        Map<String,Object> sortParams = Maps.newTreeMap(String::compareTo);
        sortParams.putAll(params);
        //对象转换成map
        StringBuilder signStr = new StringBuilder();
        for (Map.Entry<String,Object> mapEntry:sortParams.entrySet()){
            signStr.append(mapEntry.getKey()).append("=").append(mapEntry.getValue()).append("&");
        }
        signStr.append("app_secret=").append(token);
        log.info("签名:{}", signStr);
        return SecureUtil.md5(signStr.toString()).toUpperCase();
    }


}
