package com.yitong.octopus;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import com.yitong.octopus.framework.common.util.date.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Slf4j
public class OpenApiTest {

    private String url = "https://openapi.5ihuish.com/apigateway/v1";
    private final String appId = "xxxxx";
    private final String token = "xxxxxxx";

    private final String accessToken = "xxxxx";//********************************

    @Test
    public void getAuthTest(){
        Map<String,Object> params = Maps.newHashMap();
        params.put("app_id",appId);
        params.put("sign_method","MD5");
        params.put("nonce_str", RandomUtil.randomString(32));
        params.put("timestamp", DateUtils.date().toString(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
        params.put("sign",sign(params,token));
        String req = JSONUtil.toJsonStr(params);
        System.out.println("REP>>"+req);
        String resp = HttpUtil.post(url+"/auth/getToken", req);
        System.out.println(resp);
    }

    @Test
    public void queryOrderInfoTest(){
        Map<String,Object> params = Maps.newHashMap();
        params.put("app_id",appId);
        params.put("sign_method","MD5");
        params.put("nonce_str", RandomUtil.randomString(32));
        params.put("timestamp", DateUtils.date().toString(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
        params.put("access_token",accessToken);
        params.put("biz_content","{\"order_id\":\"xxxxxxx\"}");
        params.put("sign",sign(params,token));
        String req = JSONUtil.toJsonStr(params);
        System.out.println("REP>>"+req);
        String resp = HttpUtil.post(url+"/trade/order-info",req);
        System.out.println(resp);
    }

    @Test
    public void queryOrderCodeInfoTest(){
        Map<String,Object> params = Maps.newHashMap();
        params.put("app_id",appId);
        params.put("sign_method","MD5");
        params.put("nonce_str", RandomUtil.randomString(32));
        params.put("timestamp", DateUtils.date().toString(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND));
        params.put("access_token",accessToken);
        params.put("biz_content","{\"out_code\":\"xxxxxxx\"}");
        params.put("sign",sign(params,token));
        String req = JSONUtil.toJsonStr(params);
        System.out.println("REP>>"+req);
        String resp = HttpUtil.post(url+"/trade/order-code-info",req);
        System.out.println(resp);
    }

    /**
     * MD5签名
     * @param params 参数
     * @param token token
     * @return 签名结果
     */
    private String sign(Map<String,Object> params,String token){
        //删除签名字段
        params.remove("sign");
        Map<String,Object> sortParams = Maps.newTreeMap(String::compareTo);
        sortParams.putAll(params);
        //对象转换成map
        StringBuilder signStr = new StringBuilder();
        for (Map.Entry<String,Object> mapEntry:sortParams.entrySet()){
            signStr.append(mapEntry.getKey()).append("=").append(mapEntry.getValue()).append("&");
        }
        signStr.append("app_secret=").append(token);
        log.info("签名:{}", signStr);
        return SecureUtil.md5(signStr.toString()).toUpperCase();
    }


}
