package com.yitong.octopus.server.aop;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.google.common.collect.Maps;
import com.yitong.octopus.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.yitong.octopus.framework.common.util.date.DateUtils;
import com.yitong.octopus.framework.common.util.json.JsonUtils;
import com.yitong.octopus.framework.common.util.string.StringUtils;
import com.yitong.octopus.module.openapi.controller.admin.openapiinfo.vo.OpenapiInfoVO;
import com.yitong.octopus.module.openapi.enums.ErrorCodeConstants;
import com.yitong.octopus.module.openapi.service.auth.OpenApiAuthService;
import com.yitong.octopus.module.openapi.service.openapiinfo.OpenapiInfoService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import com.yitong.octopus.module.openapi.dto.ApiBaseDto;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

import static com.yitong.octopus.framework.common.pojo.CommonResult.error;
import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 检查签名
 * <AUTHOR>
 */
@Slf4j
@Order(1)
@Aspect
@Component
public class OpenApiCheckSignAop {

    public static final String SIGN = "sign";
    public static final String APP_SECRET = "app_secret";
    public static final String EQ = "=";
    public static final String AND = "&";

    /**
     * 请求时间与服务器时间允许相差5分钟
     */
    private static final long ALLOW_TIME =  10 * 60 * 1000;

    @Resource
    private OpenApiAuthService openApiAuthService;

    @Resource
    private OpenapiInfoService openapiInfoService;

    /**
     * 授权接口校验
     * @param proceedingJoinPoint
     * @return
     * @throws Throwable
     */
    @Around("execution(* com.yitong.octopus.server.controller.v1.*.*(..))")
    public Object getTokenAround(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        Object[] args=proceedingJoinPoint.getArgs();
        ApiBaseDto apiBaseDto = (ApiBaseDto) args[0];
        return around(apiBaseDto,proceedingJoinPoint);
    }

    /**
     * 其他校验
     * 1.时间误差校验
     * 2.签名校验
     * @param apiBaseDto
     * @param proceedingJoinPoint
     * @return
     * @throws Throwable
     */
    private Object around(ApiBaseDto apiBaseDto,ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        //校验接口访问时间
        Date apiDate = DateUtils.parse(apiBaseDto.getTimestamp(),FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
        Long times = DateUtils.diff(apiDate, new Date());
        if (times>ALLOW_TIME && times >= BigDecimal.ZERO.intValue()){
            return error(GlobalErrorCodeConstants.BAD_REQUEST);
        }
        //存在accessToken，并校验
        if (StringUtils.isNotEmpty(apiBaseDto.getAccessToken()) && !openApiAuthService.checkToken(apiBaseDto)){
            return error(ErrorCodeConstants.UNAUTHORIZED);
        }
        //校验应用
        OpenapiInfoVO activeInfo = openapiInfoService.getActiveInfo(apiBaseDto.getAppId());
        if (ObjectUtil.isNull(activeInfo)){
            return error(ErrorCodeConstants.UNAUTHORIZED);
        }
        OpenapiInfoHolder.saveOpenapiInfoVO(activeInfo);
        //以下校验签名
        String reqSign = apiBaseDto.getSign();
        String checkSign = sign(JsonUtils.parseJsonObject(apiBaseDto),activeInfo.getToken());
        if (!reqSign.equals(checkSign)){
            return error(ErrorCodeConstants.UNAUTHORIZED);
        }
        try{
            Object result=proceedingJoinPoint.proceed(proceedingJoinPoint.getArgs());
            return result;
        }finally {
            OpenapiInfoHolder.removeOpenapiInfoVO();
        }
    }

    /**
     * MD5签名
     * @param params 参数
     * @param token token
     * @return 签名结果
     */
    private String sign(Map<String,Object> params,String token){
        //删除签名字段
        params.remove(SIGN);
        Map<String,Object> sortParams = Maps.newTreeMap(String::compareTo);
        sortParams.putAll(params);
        //对象转换成map
        StringBuilder signStr = new StringBuilder();
        for (Map.Entry<String,Object> mapEntry:sortParams.entrySet()){
            if (ObjectUtil.isNotEmpty(mapEntry.getValue())){
                signStr.append(mapEntry.getKey()).append(EQ).append(mapEntry.getValue()).append(AND);
            }
        }
        signStr.append(APP_SECRET).append(EQ).append(token);
        log.info("签名:{}",signStr.toString());
        return SecureUtil.md5(signStr.toString()).toUpperCase();
    }
}
