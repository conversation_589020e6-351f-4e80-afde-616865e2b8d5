package com.yitong.octopus.server;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 项目的启动类
 * <AUTHOR>
 */
@SuppressWarnings("SpringComponentScan") // 忽略 IDEA 无法识别 ${yitong.info.base-package}
@SpringBootApplication(scanBasePackages = {"${yitong.info.base-package}.server", "${yitong.info.base-package}.module"})
public class YitongOpenApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(YitongOpenApiApplication.class, args);
    }

}
