package com.yitong.octopus.server.aop;

import com.yitong.octopus.module.openapi.controller.admin.openapiinfo.vo.OpenapiInfoVO;

/**
 * 保存登录的应用信息
 * <AUTHOR>
 */
public class OpenapiInfoHolder {

    private static final ThreadLocal<OpenapiInfoVO> tl = new ThreadLocal<>();

    public static void saveOpenapiInfoVO(OpenapiInfoVO vo){
        tl.set(vo);
    }

    public static OpenapiInfoVO getOpenapiInfoVO(){
        return tl.get();
    }

    public static void removeOpenapiInfoVO(){
        tl.remove();
    }

}
