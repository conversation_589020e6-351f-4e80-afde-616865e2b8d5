package com.yitong.octopus.server.controller.v1.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class RedeemCouponReq {
	
	@NotNull
	private Long sp_id;

	@NotNull
	private Long store_id;

	@NotEmpty
	private List<OutCode> out_codes;

	@NotNull
	private Long redeem_time;

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public final static class OutCode {

		@NotEmpty
		private String out_code;
	}

}
