package com.yitong.octopus.server.controller.v1;

import com.yitong.octopus.module.openapi.dto.validator.Auth;
import com.yitong.octopus.module.openapi.service.auth.OpenApiAuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.yitong.octopus.module.openapi.dto.ApiAuthDto;
import com.yitong.octopus.module.openapi.dto.ApiBaseDto;
import com.yitong.octopus.framework.common.pojo.CommonResult;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;

import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

/**
 * 三方开发者接口
 * <AUTHOR>
 */
@Tag(name = "OpenApi - 开发者获取token")
@Slf4j
@RestController
@RequestMapping("/v1/auth")
public class OpenApiAuthController
{

    @Resource
    private OpenApiAuthService openApiAuthService;

    /**
     * 查询开发者秘钥列表
     */
    @Operation(summary = "开发者获取token")
    @PostMapping("/getToken")
    @PermitAll
    public CommonResult<ApiBaseDto> getToken(@Validated(Auth.class) @RequestBody ApiAuthDto apiAuthDto)
    {
        ApiBaseDto resp = new ApiBaseDto();
        resp.setAccessToken(openApiAuthService.generatorToken(apiAuthDto));
        return success(resp);
    }

}
