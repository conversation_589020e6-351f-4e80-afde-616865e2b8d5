package com.yitong.octopus.server.controller.v1;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yitong.octopus.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.yitong.octopus.framework.common.util.json.JsonUtils;
import com.yitong.octopus.framework.common.util.validation.ValidationUtils;
import com.yitong.octopus.module.openapi.dto.ApiAuthDto;
import com.yitong.octopus.module.openapi.dto.v1.*;
import com.yitong.octopus.module.openapi.dto.validator.Biz;
import com.yitong.octopus.module.trade.enums.ErrorCodeConstants;
import com.yitong.octopus.server.aop.OpenapiInfoHolder;
import com.yitong.octopus.server.controller.v1.dto.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.yitong.octopus.framework.common.pojo.CommonResult;

import javax.annotation.security.PermitAll;
import javax.validation.Validation;

import java.util.List;
import java.util.stream.Collectors;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.framework.common.pojo.CommonResult.*;
import static com.yitong.octopus.module.trade.enums.ErrorCodeConstants.TRADE_COUPON_CHANNEL_ERROR;

/**
 * 交易接口
 * <AUTHOR>
 */
@Tag(name = "OpenApi - 交易接口")
@Slf4j
@RestController
@RequestMapping("/v1/trade")
public class OpenApiTradeController {

    @Value("${channel.url:'http://127.0.0.1:8000'}")
    private String channelUrl;

    /**
     * 券码核销
     */
    @Operation(summary = "券码核销")
    @PostMapping("/coupon-redeem")
    @PermitAll
    public CommonResult<List<TradeOrderCodeRedeemRespVo>> redeem(@Validated(Biz.class) @RequestBody ApiAuthDto apiAuthDto)
    {
        TradeOrderCodeRedeemReqVo req = JSONUtil.toBean(apiAuthDto.getBizContent(),TradeOrderCodeRedeemReqVo.class);
        ValidationUtils.validate(Validation.buildDefaultValidatorFactory().getValidator(), req);
        if (ObjectUtil.isNotNull(req.getOutCodes())){
            RedeemCouponReq redeemCouponReq = new RedeemCouponReq();
            //获取spId
            redeemCouponReq.setSp_id(OpenapiInfoHolder.getOpenapiInfoVO().getSpId());
            redeemCouponReq.setRedeem_time(req.getRedeemTime());
            redeemCouponReq.setStore_id(Long.valueOf(req.getStoreId()));
            List<RedeemCouponReq.OutCode> outCodeList = req.getOutCodes().stream().map(i-> new RedeemCouponReq.OutCode(i)).collect(Collectors.toList());
            redeemCouponReq.setOut_codes(outCodeList);
            //接口请求10s超时
            try{
                //调用大海内部券码核销接口
                String result = HttpUtil.post(channelUrl+"/api/intra/coupon/redeem",JSONUtil.toJsonStr(redeemCouponReq),10000);
                JSONObject jsonResult = JSONUtil.parseObj(result);
                int code = jsonResult.getInt(CODE);
                String msg = jsonResult.getStr(MSG);
                if (GlobalErrorCodeConstants.SUCCESS.getCode().equals(code)){
                    List<RedeemResultData> redeemResultDataList = jsonResult.getBeanList(DATA,RedeemResultData.class);
                    List<TradeOrderCodeRedeemRespVo> list = redeemResultDataList.stream().map(i -> new TradeOrderCodeRedeemRespVo(i.getResult(),i.getMsg(),i.getOut_code())).collect(Collectors.toList());
                    return success(list);
                }else {
                    return error(code,msg);
                }
            }catch (Exception e){
                return error(TRADE_COUPON_CHANNEL_ERROR);
            }
        }
        throw exception(ErrorCodeConstants.TRADE_COUPON_NOT_EXISTS);
    }

    /**
     * 订单详情
     */
    @Operation(summary = "开发者获取订单详情")
    @PostMapping("/order-info")
    @PermitAll
    public CommonResult<TradeOrderInfoRespVo> getOrderInfo(@Validated(Biz.class) @RequestBody ApiAuthDto apiAuthDto)
    {
        TradeOrderInfoReqVo req = JSONUtil.toBean(apiAuthDto.getBizContent(),TradeOrderInfoReqVo.class);
        //TODO 查询订单
        ValidationUtils.validate(Validation.buildDefaultValidatorFactory().getValidator(), req);
        //接口请求10s超时
        try{
            OrderInfoReq orderCouponReq = new OrderInfoReq();
            orderCouponReq.setSp_id(OpenapiInfoHolder.getOpenapiInfoVO().getSpId());
            orderCouponReq.setOrder_id(String.valueOf(req.getOrderId()));
            //调用大海内部券码核销接口
            String result = HttpUtil.post(channelUrl+"/api/intra/order/detail",JSONUtil.toJsonStr(orderCouponReq),10000);
            JSONObject jsonResult = JSONUtil.parseObj(result);
            int code = jsonResult.getInt(CODE);
            String msg = jsonResult.getStr(MSG);
            if (GlobalErrorCodeConstants.SUCCESS.getCode().equals(code)){
                TradeOrderInfoRespVo redeemResultData = JsonUtils.parseObject(jsonResult.getStr(DATA),TradeOrderInfoRespVo.class);
                return success(redeemResultData);
            }else {
                return error(code,msg);
            }
        }catch (Exception e){
            return error(ErrorCodeConstants.TRADE_ORDER_NOT_EXISTS);
        }
    }


    /**
     * 券码详情
     */
    @Operation(summary = "开发者获取券码详情")
    @PostMapping("/order-code-info")
    @PermitAll
    public CommonResult<TradeOrderCodeInfoRespVo> getOrderCodeInfo(@Validated(Biz.class) @RequestBody ApiAuthDto apiAuthDto)
    {
        TradeOrderCodeInfoReqVo req = JSONUtil.toBean(apiAuthDto.getBizContent(),TradeOrderCodeInfoReqVo.class);
        ValidationUtils.validate(Validation.buildDefaultValidatorFactory().getValidator(), req);
        //接口请求10s超时
        try{
            OrderCouponReq orderCouponReq = new OrderCouponReq();
            orderCouponReq.setSp_id(OpenapiInfoHolder.getOpenapiInfoVO().getSpId());
            orderCouponReq.setOut_code(req.getOutCode());
            //调用大海内部券码核销接口
            String result = HttpUtil.post(channelUrl+"/api/intra/coupon/detail",JSONUtil.toJsonStr(orderCouponReq),10000);
            JSONObject jsonResult = JSONUtil.parseObj(result);
            int code = jsonResult.getInt(CODE);
            String msg = jsonResult.getStr(MSG);
            if (GlobalErrorCodeConstants.SUCCESS.getCode().equals(code)){
                TradeOrderCodeInfoRespVo redeemResultData = JsonUtils.parseObject(jsonResult.getStr(DATA),TradeOrderCodeInfoRespVo.class);
                return success(redeemResultData);
            }else {
                return error(code,msg);
            }
        }catch (Exception e){
            return error(ErrorCodeConstants.TRADE_COUPON_NOT_EXISTS);
        }
    }
}
