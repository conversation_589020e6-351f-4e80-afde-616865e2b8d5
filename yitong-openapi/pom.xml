<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.yitong.octopus</groupId>
        <artifactId>yitong</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yitong-openapi</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        后端 Server 的主项目，通过引入需要 yitong-module-xxx 的依赖，
        从而实现提供 RESTful API 给 yitong-ui-admin、yitong-ui-user 等前端项目。
        本质上来说，它就是个空壳（容器）！
    </description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

    <dependencies>
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-module-system-biz</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-module-infra-biz</artifactId>
            <version>${revision}</version>
        </dependency>

        <!--openapi相关 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-module-openapi-biz</artifactId>
            <version>${revision}</version>
        </dependency>

        <!--trade相关 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-module-trade-biz</artifactId>
            <version>${revision}</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.yitong.octopus</groupId>-->
<!--            <artifactId>yitong-spring-boot-starter-biz-error-code</artifactId>-->
<!--        </dependency>-->

        <!-- spring boot 配置所需依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-protection</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-test</artifactId>
        </dependency>
    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.10</version> <!-- 如果 spring.boot.version 版本修改，则这里也要跟着修改 -->
                <configuration>
                    <fork>true</fork>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
